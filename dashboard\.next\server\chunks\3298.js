"use strict";exports.id=3298,exports.ids=[3298],exports.modules={1934:(e,t,n)=>{n.d(t,{M:()=>r});let r=()=>"3.14.21"},4685:(e,t,n)=>{n.d(t,{$I:()=>h,Ru:()=>g,dv:()=>m,oL:()=>f,zN:()=>x});var r=n(76542),a=n(81144),s=n(34850),i=n(36643),o=n(54709);let u=Symbol.for("effect/Option"),c={...o.MS,[u]:{_A:e=>e},[s.FX](){return this.toJSON()},toString(){return(0,s.GP)(this.toJSON())}},l=Object.assign(Object.create(c),{_tag:"Some",_op:"Some",[r.HR](e){return f(e)&&g(e)&&r.aI(this.value,e.value)},[a.HR](){return a.PO(this,a.kg(a.tW(this._tag))(a.tW(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:(0,s.U2)(this.value)}}}),d=a.tW("None"),p=Object.assign(Object.create(c),{_tag:"None",_op:"None",[r.HR]:e=>f(e)&&h(e),[a.HR]:()=>d,toJSON(){return{_id:"Option",_tag:this._tag}}}),f=e=>(0,i.i5)(e,u),h=e=>"None"===e._tag,g=e=>"Some"===e._tag,m=Object.create(p),x=e=>{let t=Object.create(l);return t.value=e,t}},6856:(e,t,n)=>{let r;n.d(t,{D_:()=>i,Fs:()=>c,Mi:()=>o,Tn:()=>a,XY:()=>s,Yi:()=>u});let a=e=>"function"==typeof e,s=function(e,t){if("function"==typeof e)return function(){return e(arguments)?t.apply(this,arguments):e=>t(e,...arguments)};switch(e){case 0:case 1:throw RangeError(`Invalid arity ${e}`);case 2:return function(e,n){return arguments.length>=2?t(e,n):function(n){return t(n,e)}};case 3:return function(e,n,r){return arguments.length>=3?t(e,n,r):function(r){return t(r,e,n)}};case 4:return function(e,n,r,a){return arguments.length>=4?t(e,n,r,a):function(a){return t(a,e,n,r)}};case 5:return function(e,n,r,a,s){return arguments.length>=5?t(e,n,r,a,s):function(s){return t(s,e,n,r,a)}};default:return function(){if(arguments.length>=e)return t.apply(this,arguments);let n=arguments;return function(e){return t(e,...n)}}}},i=e=>e,o=i,u=(r=void 0,()=>r);function c(e,t,n,r,a,s,i,o,u){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return n(t(e));case 4:return r(n(t(e)));case 5:return a(r(n(t(e))));case 6:return s(a(r(n(t(e)))));case 7:return i(s(a(r(n(t(e))))));case 8:return o(i(s(a(r(n(t(e)))))));case 9:return u(o(i(s(a(r(n(t(e))))))));default:{let e=arguments[0];for(let t=1;t<arguments.length;t++)e=arguments[t](e);return e}}}},16023:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},21129:(e,t,n)=>{n.d(t,{$v:()=>U,A_:()=>E,Do:()=>D,Ie:()=>R,Jt:()=>A,KA:()=>x,L8:()=>T,Or:()=>b,Sr:()=>p,Um:()=>$,Up:()=>F,WQ:()=>C,aE:()=>k,cJ:()=>H,dz:()=>S,gw:()=>I,h1:()=>L,ii:()=>y,lN:()=>f,og:()=>_,om:()=>P,vw:()=>v});var r=n(76542),a=n(6856),s=n(29606),i=n(81144),o=n(34850),u=n(42424),c=n(36643),l=n(54709),d=n(4685);let p=Symbol.for("effect/Context/Tag"),f=Symbol.for("effect/Context/Reference"),h=Symbol.for("effect/STM"),g={...l.MS,_op:"Tag",[h]:l.Hs,[p]:{_Service:e=>e,_Identifier:e=>e},toString(){return(0,o.GP)(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[o.FX](){return this.toJSON()},of:e=>e,context(e){return T(this,e)}},m={...g,[f]:f},x=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let n=Error();Error.stackTraceLimit=t;let r=Object.create(g);return Object.defineProperty(r,"stack",{get:()=>n.stack}),r.key=e,r},v=e=>()=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let n=Error();function r(){}return Error.stackTraceLimit=t,Object.setPrototypeOf(r,g),r.key=e,Object.defineProperty(r,"stack",{get:()=>n.stack}),r},b=()=>(e,t)=>{let n=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();function a(){}return Error.stackTraceLimit=n,Object.setPrototypeOf(a,m),a.key=e,a.defaultValue=t.defaultValue,Object.defineProperty(a,"stack",{get:()=>r.stack}),a},y=Symbol.for("effect/Context"),w={[y]:{_Services:e=>e},[r.HR](e){if(k(e)&&this.unsafeMap.size===e.unsafeMap.size){for(let t of this.unsafeMap.keys())if(!e.unsafeMap.has(t)||!r.aI(this.unsafeMap.get(t),e.unsafeMap.get(t)))return!1;return!0}return!1},[i.HR](){return i.PO(this,i.ai(this.unsafeMap.size))},pipe(){return(0,u.t)(this,arguments)},toString(){return(0,o.GP)(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(o.U2)}},[o.FX](){return this.toJSON()}},_=e=>{let t=Object.create(w);return t.unsafeMap=e,t},j=e=>{let t=Error(`Service not found${e.key?`: ${String(e.key)}`:""}`);if(e.stack){let n=e.stack.split("\n");if(n.length>2){let e=n[2].match(/at (.*)/);e&&(t.message=t.message+` (defined at ${e[1]})`)}}if(t.stack){let e=t.stack.split("\n");e.splice(1,3),t.stack=e.join("\n")}return t},k=e=>(0,c.i5)(e,y),S=e=>(0,c.i5)(e,p),E=e=>(0,c.i5)(e,f),O=_(new Map),R=()=>O,T=(e,t)=>_(new Map([[e.key,t]])),C=(0,a.XY)(3,(e,t,n)=>{let r=new Map(e.unsafeMap);return r.set(t.key,n),_(r)}),N=(0,s.V)("effect/Context/defaultValueCache",()=>new Map),M=e=>{if(N.has(e.key))return N.get(e.key);let t=e.defaultValue();return N.set(e.key,t),t},I=(e,t)=>e.unsafeMap.has(t.key)?e.unsafeMap.get(t.key):M(t),U=(0,a.XY)(2,(e,t)=>{if(!e.unsafeMap.has(t.key)){if(f in t)return M(t);throw j(t)}return e.unsafeMap.get(t.key)}),A=U,D=(0,a.XY)(3,(e,t,n)=>e.unsafeMap.has(t.key)?e.unsafeMap.get(t.key):E(t)?M(t):n()),P=(0,a.XY)(2,(e,t)=>e.unsafeMap.has(t.key)?d.zN(e.unsafeMap.get(t.key)):E(t)?d.zN(M(t)):d.dv),L=(0,a.XY)(2,(e,t)=>{let n=new Map(e.unsafeMap);for(let[e,r]of t.unsafeMap)n.set(e,r);return _(n)}),$=(...e)=>{let t=new Map;for(let n of e)for(let[e,r]of n.unsafeMap)t.set(e,r);return _(t)},F=(...e)=>t=>{let n=new Set(e.map(e=>e.key)),r=new Map;for(let[e,a]of t.unsafeMap.entries())n.has(e)&&r.set(e,a);return _(r)},H=(...e)=>t=>{let n=new Map(t.unsafeMap);for(let t of e)n.delete(t.key);return _(n)}},29606:(e,t,n)=>{let r;n.d(t,{V:()=>i});var a=n(1934);let s=`effect/GlobalValue/globalStoreId/${a.M()}`,i=(e,t)=>(r||(globalThis[s]??=new Map,r=globalThis[s]),r.has(e)||r.set(e,t()),r.get(e))},34850:(e,t,n)=>{n.d(t,{FX:()=>s,GP:()=>o,U2:()=>i,ZK:()=>u});var r=n(29606),a=n(36643);let s=Symbol.for("nodejs.util.inspect.custom"),i=e=>{try{if((0,a.i5)(e,"toJSON")&&(0,a.Tn)(e.toJSON)&&0===e.toJSON.length)return e.toJSON();if(Array.isArray(e))return e.map(i)}catch(e){return{}}return f(e)},o=e=>JSON.stringify(e,null,2),u=(e,t=2)=>{if("string"==typeof e)return e;try{return"object"==typeof e?c(e,t):String(e)}catch(t){return String(e)}},c=(e,t)=>{let n=[],r=JSON.stringify(e,(e,t)=>"object"==typeof t&&null!==t?n.includes(t)?void 0:n.push(t)&&(void 0!==p.fiberRefs&&d(t)?t[l](p.fiberRefs):t):t,t);return n=void 0,r},l=Symbol.for("effect/Inspectable/Redactable"),d=e=>"object"==typeof e&&null!==e&&l in e,p=(0,r.V)("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),f=e=>d(e)&&void 0!==p.fiberRefs?e[l](p.fiberRefs):e},36643:(e,t,n)=>{n.d(t,{$J:()=>d,Et:()=>s,Kg:()=>a,Lm:()=>i,Tn:()=>o,i5:()=>l,u4:()=>p});var r=n(6856);let a=e=>"string"==typeof e,s=e=>"number"==typeof e,i=e=>"boolean"==typeof e,o=r.Tn,u=e=>"object"==typeof e&&null!==e,c=e=>u(e)||o(e),l=(0,r.XY)(2,(e,t)=>c(e)&&t in e),d=(0,r.XY)(2,(e,t)=>l(e,"_tag")&&e._tag===t),p=e=>u(e)&&!Array.isArray(e)},42424:(e,t,n)=>{n.d(t,{t:()=>r});let r=(e,t)=>{switch(t.length){case 0:return e;case 1:return t[0](e);case 2:return t[1](t[0](e));case 3:return t[2](t[1](t[0](e)));case 4:return t[3](t[2](t[1](t[0](e))));case 5:return t[4](t[3](t[2](t[1](t[0](e)))));case 6:return t[5](t[4](t[3](t[2](t[1](t[0](e))))));case 7:return t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))));case 8:return t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e))))))));case 9:return t[8](t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))))));default:{let n=e;for(let e=0,r=t.length;e<r;e++)n=t[e](n);return n}}}},45525:(e,t,n)=>{n.d(t,{D8:()=>d,Ts:()=>l}),n(6856);var r=n(76542),a=n(81144),s=n(34850),i=(n(36643),n(54709)),o=n(4685);i.MS,s.FX,r.HR,a.HR,r.HR,a.HR,o.zN,o.oL,o.$I,o.Ru,Symbol.iterator,()=>u;let u={next:()=>({done:!0,value:void 0})};Object.fromEntries,(e,t)=>{let n=[];for(let r of c(e))n.push(t(r,e[r]));return n};let c=e=>Object.keys(e),l=e=>Array.isArray(e)?e:Array.from(e),d=e=>Array.isArray(e)?e:[e]},51603:(e,t,n)=>{n.d(t,{BW:()=>s,WT:()=>o,Hi:()=>c,ku:()=>u}),n(6856);var r=n(29606);let a=e=>`BUG: ${e} - please report an issue at https://github.com/Effect-TS/effect/issues`;Symbol.iterator;class s{constructor(e){this.called=!1,this.self=e}next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new s(this.self)}}let i=Symbol.for("effect/Utils/YieldWrap");class o{#e;constructor(e){this.#e=e}[i](){return this.#e}}function u(e){if("object"==typeof e&&null!==e&&i in e)return e[i]();throw Error(a("yieldWrapGet"))}let c=(0,r.V)("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),l={effect_internal_function:e=>e()};l.effect_internal_function(()=>Error().stack)?.includes("effect_internal_function")===!0&&l.effect_internal_function},52861:(e,t,n)=>{n.d(t,{$v:()=>s,Or:()=>o,WQ:()=>a,vw:()=>i});var r=n(21129);r.Sr,r.lN,r.KA,r.ii,r.og,r.aE,r.dz,r.A_,r.Ie,r.L8;let a=r.WQ;r.Jt,r.Do;let s=r.$v;r.om,r.h1,r.Um,r.Up,r.cJ;let i=r.vw,o=r.Or},54709:(e,t,n)=>{n.d(t,{C6:()=>x,le:()=>d,Em:()=>g,MS:()=>f,$n:()=>u,TW:()=>l,O4:()=>c,bw:()=>v,Pe:()=>m,KE:()=>h,Hs:()=>p});var r=n(76542),a=n(81144),s=n(42424),i=n(51603),o=n(1934);let u=Symbol.for("effect/Effect"),c=Symbol.for("effect/Stream"),l=Symbol.for("effect/Sink"),d=Symbol.for("effect/Channel"),p={_R:e=>e,_E:e=>e,_A:e=>e,_V:o.M()},f={[u]:p,[c]:p,[l]:{_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e},[d]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},[r.HR](e){return this===e},[a.HR](){return a.PO(this,a.yT(this))},[Symbol.iterator](){return new i.BW(new i.WT(this))},pipe(){return(0,s.t)(this,arguments)}},h={[a.HR](){return a.PO(this,a.QK(this))},[r.HR](e){let t=Object.keys(this),n=Object.keys(e);if(t.length!==n.length)return!1;for(let n of t)if(!(n in e&&r.aI(this[n],e[n])))return!1;return!0}},g={...f,_op:"Commit"},m={...g,...h},x=function(){function e(){}return e.prototype=g,e}(),v=function(){function e(){}return e.prototype=m,e}()},67135:(e,t,n)=>{n.d(t,{r:()=>m,rE:()=>g});var r=n(45525),a=n(76275),s=n(94987);let i=()=>{let e,t;let n=new AbortController;return{promise:new Promise((n,r)=>{e=n,t=r}),ac:n,resolve:e,reject:t}};var o=n(6856),u=n(36643);let c=e=>{console.warn(`⚠️ [uploadthing][deprecated] ${e}`)},l=e=>{let t=new URL(e.url),n=new URLSearchParams(t.search);return n.set("actionType",e.actionType),n.set("slug",e.slug),t.search=n.toString(),t},d=e=>(t,n)=>a.Jk(function*(){let r=l({url:e.url,slug:e.endpoint,actionType:t}),i=new Headers((yield*a.iv(async()=>"function"==typeof e.headers?await e.headers():e.headers)));return e.package&&i.set("x-uploadthing-package",e.package),i.set("x-uploadthing-version","7.7.2"),i.set("Content-Type","application/json"),yield*(0,s.os)(r,{method:"POST",body:JSON.stringify(n),headers:i}).pipe(a.hg(s.de),a.Tj(o.Mi),a.Ku("FetchError",e=>a.fJ(new s.SD({code:"INTERNAL_CLIENT_ERROR",message:`Failed to report event "${t}" to UploadThing server`,cause:e}))),a.Ku("BadRequestError",e=>a.fJ(new s.SD({code:(0,s.Ci)(e.status),message:e.getMessage(),cause:e.json}))),a.Ku("InvalidJson",e=>a.fJ(new s.SD({code:"INTERNAL_CLIENT_ERROR",message:"Failed to parse response from UploadThing server",cause:e}))))}),p=(e,t,n,r)=>a.bI(i=>{let o=new XMLHttpRequest;o.open("PUT",n.url,!0),o.setRequestHeader("Range",`bytes=${t}-`),o.setRequestHeader("x-uploadthing-version","7.7.2"),o.responseType="json";let c=0;o.upload.addEventListener("progress",({loaded:e})=>{let t=e-c;r?.({loaded:e,delta:t}),c=e}),o.addEventListener("load",()=>{o.status>=200&&o.status<300&&(0,u.u4)(o.response)?(0,u.i5)(o.response,"error")?i(new s.SD({code:"UPLOAD_FAILED",message:String(o.response.error),data:o.response})):i(a.Py(o.response)):i(new s.SD({code:"UPLOAD_FAILED",message:`XHR failed ${o.status} ${o.statusText}`,data:o.response}))}),o.addEventListener("error",()=>{i(new s.SD({code:"UPLOAD_FAILED"}))});let l=new FormData;return"uri"in e?l.append("file",{uri:e.uri,type:e.type,name:e.name,...t>0&&{range:t}}):l.append("file",t>0?e.slice(t):e),o.send(l),a.OH(()=>o.abort())}),f=(e,t,n)=>(0,s.os)(t.url,{method:"HEAD"}).pipe(a.Tj(({headers:e})=>parseInt(e.get("x-ut-range-start")??"0",10)),a.Mi(e=>n.onUploadProgress?.({delta:e,loaded:e})),a.qI(r=>p(e,r,t,e=>n.onUploadProgress?.({delta:e.delta,loaded:e.loaded+r}))),a.Tj(o.Mi),a.Tj(n=>({name:e.name,size:e.size,key:t.key,lastModified:e.lastModified,serverData:n.serverData,get url(){return c("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),n.url},get appUrl(){return c("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),n.appUrl},ufsUrl:n.ufsUrl,customId:t.customId,type:e.type,fileHash:n.fileHash}))),h=(e,t)=>{let n=d({endpoint:String(e),package:t.package,url:t.url,headers:t.headers}),r=t.files.reduce((e,t)=>e+t.size,0),s=0;return a.qI(n("upload",{input:"input"in t?t.input:null,files:t.files.map(e=>({name:e.name,size:e.size,type:e.type,lastModified:e.lastModified}))}),e=>a.jJ(e,(e,n)=>a.qI(a.OH(()=>t.onUploadBegin?.({file:t.files[n].name})),()=>f(t.files[n],e,{onUploadProgress:e=>{s+=e.delta,t.onUploadProgress?.({file:t.files[n],progress:e.loaded/t.files[n].size*100,loaded:e.loaded,delta:e.delta,totalLoaded:s,totalProgress:s/r})}})),{concurrency:6}))},g="7.7.2",m=e=>{let t=(0,s.Lt)();return{uploadFiles:(n,r)=>{let i="function"==typeof n?n(t):n,o=e?.fetch??window.fetch;return h(i,{...r,skipPolling:{},url:(0,s.s2)(e?.url),package:e?.package??"uploadthing/client",input:r.input}).pipe(a.Pf(s.MC,o),e=>a.Np(e,r.signal&&{signal:r.signal})).then(e=>{if("Success"===e._tag)return e.value;if("Interrupt"===e.cause._tag)throw new s.tZ;throw a.Re(e.cause)})},createUpload:async(n,o)=>{let u=new Map,c=d({endpoint:String("function"==typeof n?n(t):n),package:e?.package??"uploadthing/client",url:(0,s.s2)(e?.url),headers:o.headers}),l=e?.fetch??window.fetch,p=await a.pR(c("upload",{input:"input"in o?o.input:null,files:o.files.map(e=>({name:e.name,size:e.size,type:e.type,lastModified:e.lastModified}))}).pipe(a.Pf(s.MC,l))),h=o.files.reduce((e,t)=>e+t.size,0),g=0,m=(e,t)=>f(e,t,{onUploadProgress:t=>{g+=t.delta,o.onUploadProgress?.({...t,file:e,progress:Math.round(t.loaded/e.size*100),totalLoaded:g,totalProgress:Math.round(g/h*100)})}}).pipe(a.Pf(s.MC,l));for(let[e,t]of p.entries()){let n=o.files[e];if(!n)continue;let r=i();u.set(n,{deferred:r,presigned:t}),a.Np(m(n,t),{signal:r.ac.signal}).then(e=>{if("Success"===e._tag)return r.resolve(e.value);if("Interrupt"===e.cause._tag)throw new s.oZ;throw a.Re(e.cause)}).catch(e=>{e instanceof s.oZ||r.reject(e)})}return{pauseUpload:e=>{for(let t of r.D8(e??o.files)){let e=u.get(t);if(!e)return;if(e.deferred.ac.signal.aborted)throw new s.tZ;e.deferred.ac.abort()}},resumeUpload:e=>{for(let t of r.D8(e??o.files)){let e=u.get(t);if(!e)throw"No upload found";e.deferred.ac=new AbortController,a.Np(m(t,e.presigned),{signal:e.deferred.ac.signal}).then(t=>{if("Success"===t._tag)return e.deferred.resolve(t.value);if("Interrupt"===t.cause._tag)throw new s.oZ;throw a.Re(t.cause)}).catch(t=>{t instanceof s.oZ||e.deferred.reject(t)})}},done:async e=>{let t=[];for(let n of r.D8(e??o.files)){let e=u.get(n);if(!e)throw"No upload found";t.push(e.deferred.promise)}let n=await Promise.all(t);return e?n[0]:n}}},routeRegistry:t}}},76275:(e,t,n)=>{n.d(t,{$D:()=>e5,rN:()=>e6,hg:()=>eh,bI:()=>el,Ku:()=>eF,Re:()=>C,fJ:()=>K,W$:()=>eI,qI:()=>em,jJ:()=>eZ,Jk:()=>ed,Tj:()=>ev,iv:()=>es,Pf:()=>eT,pR:()=>e1,Np:()=>e0,HZ:()=>e4,eu:()=>eO,Py:()=>q,OH:()=>Q,Mi:()=>eg,sF:()=>eL,Sv:()=>ea,$m:()=>ei,SZ:()=>eH});var r=n(45525),a=n(52861),s=n(54709);s.$n,s.O4,s.TW,s.le;let i=s.MS;s.Em,s.Pe,s.C6,s.bw;var o=n(76542),u=n(6856),c=n(29606),l=n(81144),d=n(34850),p=n(21129),f=n(42424),h=n(36643),g=n(51603);let m=Symbol.for("effect/Micro"),x=Symbol.for("effect/Micro/MicroExit"),v=e=>"object"==typeof e&&null!==e&&m in e,b=Symbol.for("effect/Micro/MicroCause"),y={_E:u.D_};class w extends globalThis.Error{constructor(e,t,n){let r,a,s;let i=`MicroCause.${e}`;if(t instanceof globalThis.Error){r=`(${i}) ${t.name}`;let e=(a=t.message).split("\n").length;s=t.stack?`(${i}) ${t.stack.split("\n").slice(0,e+3).join("\n")}`:`${r}: ${a}`}else r=i,a=(0,d.ZK)(t,0),s=`${r}: ${a}`;n.length>0&&(s+=`
    ${n.join("\n    ")}`),super(a),this._tag=e,this.traces=n,this[b]=y,this.name=r,this.stack=s}pipe(){return(0,f.t)(this,arguments)}toString(){return this.stack}[d.FX](){return this.stack}}class _ extends w{constructor(e,t=[]){super("Fail",e,t),this.error=e}}let j=(e,t=[])=>new _(e,t);class k extends w{constructor(e,t=[]){super("Die",e,t),this.defect=e}}let S=(e,t=[])=>new k(e,t);class E extends w{constructor(e=[]){super("Interrupt","interrupted",e)}}let O=(e=[])=>new E(e),R=e=>"Fail"===e._tag,T=e=>"Interrupt"===e._tag,C=e=>"Fail"===e._tag?e.error:"Die"===e._tag?e.defect:e,N=(0,u.XY)(2,(e,t)=>{let n=[...e.traces,t];switch(e._tag){case"Die":return S(e.defect,n);case"Interrupt":return O(n);case"Fail":return j(e.error,n)}}),M=Symbol.for("effect/Micro/MicroFiber"),I={_A:u.D_,_E:u.D_};class U{constructor(e,t=!0){this._stack=[],this._observers=[],this.currentOpCount=0,this._interrupted=!1,this._yielded=void 0,this.context=e,this.interruptible=t,this[M]=I}getRef(e){return p.gw(this.context,e)}addObserver(e){return this._exit?(e(this._exit),u.Yi):(this._observers.push(e),()=>{let t=this._observers.indexOf(e);t>=0&&this._observers.splice(t,1)})}unsafeInterrupt(){!this._exit&&(this._interrupted=!0,this.interruptible&&this.evaluate(ew))}unsafePoll(){return this._exit}evaluate(e){if(this._exit)return;if(void 0!==this._yielded){let e=this._yielded;this._yielded=void 0,e()}let t=this.runLoop(e);if(t===Y)return;let n=A.interruptChildren&&A.interruptChildren(this);if(void 0!==n)return this.evaluate(em(n,()=>t));this._exit=t;for(let e=0;e<this._observers.length;e++)this._observers[e](t);this._observers.length=0}runLoop(e){let t=!1,n=e;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!t&&this.getRef(eM).shouldYield(this)){t=!0;let e=n;n=em(et,()=>e)}if((n=n[$](this))===Y){let e=this._yielded;if(x in e)return this._yielded=void 0,e;return Y}}}catch(e){if(!(0,h.i5)(n,$))return e_(`MicroFiber.runLoop: Not a valid effect: ${String(n)}`);return e_(e)}}getCont(e){for(;;){let t=this._stack.pop();if(!t)return;let n=t[z]&&t[z](this);if(n)return{[e]:n};if(t[e])return t}}yieldWith(e){return this._yielded=e,Y}children(){return this._children??=new Set}}let A=(0,c.V)("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),D=e=>ee(()=>{for(let t of e)t.unsafeInterrupt();let t=e[Symbol.iterator](),n=ee(()=>{let e=t.next();for(;!e.done;){if(e.value.unsafePoll()){e=t.next();continue}let r=e.value;return el(e=>{r.addObserver(t=>{e(n)})})}return ek});return n}),P=Symbol.for("effect/Micro/identifier"),L=Symbol.for("effect/Micro/args"),$=Symbol.for("effect/Micro/evaluate"),F=Symbol.for("effect/Micro/successCont"),H=Symbol.for("effect/Micro/failureCont"),z=Symbol.for("effect/Micro/ensureCont"),Y=Symbol.for("effect/Micro/Yield"),J={_A:u.D_,_E:u.D_,_R:u.D_},W={...i,_op:"Micro",[m]:J,pipe(){return(0,f.t)(this,arguments)},[Symbol.iterator](){return new g.BW(new g.WT(this))},toJSON(){return{_id:"Micro",op:this[P],...L in this?{args:this[L]}:void 0}},toString(){return(0,d.GP)(this)},[d.FX](){return(0,d.GP)(this)}};function X(e){return e_("Micro.evaluate: Not implemented")}let B=e=>({...W,[P]:e.op,[$]:e.eval??X,[F]:e.contA,[H]:e.contE,[z]:e.ensure}),G=e=>{let t=B(e);return function(){let n=Object.create(t);return n[L]=!1===e.single?arguments:arguments[0],n}},V=e=>{let t={...B(e),[x]:x,_tag:e.op,get[e.prop](){return this[L]},toJSON(){return{_id:"MicroExit",_tag:e.op,[e.prop]:this[L]}},[o.HR](t){return eb(t)&&t._tag===e.op&&o.aI(this[L],t[L])},[l.HR](){return l.PO(this,l.kg(l.Yj(e.op))(l.tW(this[L])))}};return function(e){let n=Object.create(t);return n[L]=e,n[F]=void 0,n[H]=void 0,n[z]=void 0,n}},q=V({op:"Success",prop:"value",eval(e){let t=e.getCont(F);return t?t[F](this[L],e):e.yieldWith(this)}}),Z=V({op:"Failure",prop:"cause",eval(e){let t=e.getCont(H);for(;T(this[L])&&t&&e.interruptible;)t=e.getCont(H);return t?t[H](this[L],e):e.yieldWith(this)}}),K=e=>Z(j(e)),Q=G({op:"Sync",eval(e){let t=this[L](),n=e.getCont(F);return n?n[F](t,e):e.yieldWith(ey(t))}}),ee=G({op:"Suspend",eval(e){return this[L]()}}),et=G({op:"Yield",eval(e){let t=!1;return e.getRef(eM).scheduleTask(()=>{t||e.evaluate(ek)},this[L]??0),e.yieldWith(()=>{t=!0})}})(0),en=e=>e_(e),er=q(void 0),ea=e=>ee(()=>{try{return q(e.try())}catch(t){return K(e.catch(t))}}),es=e=>eu(function(t,n){e(n).then(e=>t(q(e)),e=>t(en(e)))},0!==e.length),ei=e=>eu(function(t,n){try{e.try(n).then(e=>t(q(e)),n=>t(K(e.catch(n))))}catch(n){t(K(e.catch(n)))}},0!==e.try.length),eo=G({op:"WithMicroFiber",eval(e){return this[L](e)}}),eu=G({op:"Async",single:!1,eval(e){let t=this[L][0],n=!1,r=!1,a=this[L][1]?new AbortController:void 0,s=t(t=>{n||(n=!0,r?e.evaluate(t):r=t)},a?.signal);return!1!==r?r:(r=!0,e._yielded=()=>{n=!0},void 0===a&&void 0===s||e._stack.push(ec(()=>(n=!0,a?.abort(),s??ek))),Y)}}),ec=G({op:"AsyncFinalizer",ensure(e){e.interruptible&&(e.interruptible=!1,e._stack.push(eB(!0)))},contE(e,t){return T(e)?em(this[L](),()=>Z(e)):Z(e)}}),el=e=>eu(e,e.length>=2),ed=(...e)=>ee(()=>ep(1===e.length?e[0]():e[1].call(e[0]))),ep=G({op:"Iterator",contA(e,t){let n=this[L].next(e);return n.done?q(n.value):(t._stack.push(this),(0,g.ku)(n.value))},eval(e){return this[F](void 0,e)}}),ef=(0,u.XY)(2,(e,t)=>ev(e,e=>t)),eh=(0,u.XY)(2,(e,t)=>em(e,e=>{let n=v(t)?t:"function"==typeof t?t(e):t;return v(n)?n:q(n)})),eg=(0,u.XY)(2,(e,t)=>em(e,e=>{let n=v(t)?t:"function"==typeof t?t(e):t;return v(n)?ef(n,e):q(e)})),em=(0,u.XY)(2,(e,t)=>{let n=Object.create(ex);return n[L]=e,n[F]=t,n}),ex=B({op:"OnSuccess",eval(e){return e._stack.push(this),this[L]}}),ev=(0,u.XY)(2,(e,t)=>em(e,e=>q(t(e)))),eb=e=>(0,h.i5)(e,x),ey=q,ew=Z(O()),e_=e=>Z(S(e)),ej=e=>"Failure"===e._tag,ek=ey(void 0),eS="setImmediate"in globalThis?globalThis.setImmediate:e=>setTimeout(e,0);class eE{scheduleTask(e,t){this.tasks.push(e),this.running||(this.running=!0,eS(this.afterScheduled))}runTasks(){let e=this.tasks;this.tasks=[];for(let t=0,n=e.length;t<n;t++)e[t]()}shouldYield(e){return e.currentOpCount>=e.getRef(eC)}flush(){for(;this.tasks.length>0;)this.runTasks()}constructor(){this.tasks=[],this.running=!1,this.afterScheduled=()=>{this.running=!1,this.runTasks()}}}let eO=e=>eo(t=>q(a.$v(t.context,e))),eR=(0,u.XY)(2,(e,t)=>eo(n=>{let r=n.context;return n.context=t(r),eJ(e,()=>(n.context=r,er))})),eT=(0,u.XY)(3,(e,t,n)=>eR(e,a.WQ(t,n)));class eC extends a.Or()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class eN extends a.Or()("effect/Micro/currentConcurrency",{defaultValue:()=>"unbounded"}){}class eM extends a.Or()("effect/Micro/currentScheduler",{defaultValue:()=>new eE}){}let eI=(0,u.XY)(e=>v(e[0]),(e,t,n)=>em(e,e=>t(e)?q(e):K(n(e)))),eU=(0,u.XY)(2,(e,t)=>{let n=Object.create(eA);return n[L]=e,n[H]=t,n}),eA=B({op:"OnFailure",eval(e){return e._stack.push(this),this[L]}}),eD=(0,u.XY)(3,(e,t,n)=>eU(e,e=>t(e)?n(e):Z(e))),eP=(0,u.XY)(3,(e,t,n)=>eD(e,t,e=>eh(n(e),Z(e)))),eL=(0,u.XY)(2,(e,t)=>eP(e,R,e=>t(e.error))),e$=(0,u.XY)(3,(e,t,n)=>eD(e,e=>R(e)&&t(e.error),e=>n(e.error))),eF=(0,u.XY)(3,(e,t,n)=>e$(e,(0,h.$J)(t),n)),eH=function(){let e=globalThis.Error.stackTraceLimit;globalThis.Error.stackTraceLimit=2;let t=new globalThis.Error;globalThis.Error.stackTraceLimit=e;let n=e=>n=>eX(n,n=>Z(function(e,n){let r=t.stack;if(!r)return n;let a=r.split("\n")[2]?.trim().replace(/^at /,"");if(!a)return n;let s=a.match(/\((.*)\)$/);return N(n,`at ${e} (${s?s[1]:a})`)}(e,n)));return 2==arguments.length?n(arguments[1])(arguments[0]):n(arguments[0])},ez=(0,u.XY)(2,(e,t)=>{let n=Object.create(eY);return n[L]=e,n[F]=t.onSuccess,n[H]=t.onFailure,n}),eY=B({op:"OnSuccessAndFailure",eval(e){return e._stack.push(this),this[L]}}),eJ=(0,u.XY)(2,(e,t)=>eV(n=>ez(n(e),{onFailure:e=>em(t(Z(e)),()=>Z(e)),onSuccess:e=>em(t(ey(e)),()=>q(e))}))),eW=(0,u.XY)(3,(e,t,n)=>eJ(e,e=>t(e)?n(e):ek)),eX=(0,u.XY)(2,(e,t)=>eW(e,ej,e=>t(e.cause))),eB=G({op:"SetInterruptible",ensure(e){if(e.interruptible=this[L],e._interrupted&&e.interruptible)return()=>ew}}),eG=e=>eo(t=>t.interruptible?e:(t.interruptible=!0,t._stack.push(eB(!1)),t._interrupted)?ew:e),eV=e=>eo(t=>t.interruptible?(t.interruptible=!1,t._stack.push(eB(!0)),e(eG)):e(u.D_)),eq=G({op:"While",contA(e,t){return(this[L].step(e),this[L].while())?(t._stack.push(this),this[L].body()):ek},eval(e){return this[L].while()?(e._stack.push(this),this[L].body()):ek}}),eZ=(e,t,n)=>eo(a=>{let s=n?.concurrency==="inherit"?a.getRef(eN):n?.concurrency??1,i="unbounded"===s?Number.POSITIVE_INFINITY:Math.max(1,s),o=r.Ts(e),u=o.length;if(0===u)return n?.discard?er:q([]);let c=n?.discard?void 0:Array(u),l=0;return 1===i?ef(eq({while:()=>l<o.length,body:()=>t(o[l],l),step:c?e=>c[l++]=e:e=>l++}),c):el(e=>{let n;let r=new Set,s=0,d=0,p=!1,f=!1;return function h(){for(p=!0;s<i&&l<u;){let g=l,m=o[g];l++,s++;try{let o=eK(a,t(m,g),!0,!0);r.add(o),o.addObserver(t=>{r.delete(o),!f&&("Failure"===t._tag?void 0===n&&(n=t,u=l,r.forEach(e=>e.unsafeInterrupt())):void 0!==c&&(c[g]=t.value),d++,s--,d===u?e(n??q(c)):!p&&s<i&&h())})}catch(e){n=e_(e),u=l,r.forEach(e=>e.unsafeInterrupt())}}p=!1}(),ee(()=>(f=!0,l=u,D(r)))})}),eK=(e,t,n=!1,r=!1)=>{let a=new U(e.context,e.interruptible);return r||(e.children().add(a),a.addObserver(()=>e.children().delete(a))),n?a.evaluate(t):e.getRef(eM).scheduleTask(()=>a.evaluate(t),0),a},eQ=(e,t)=>{let n=new U(eM.context(t?.scheduler??new eE));if(n.evaluate(e),t?.signal){if(t.signal.aborted)n.unsafeInterrupt();else{let e=()=>n.unsafeInterrupt();t.signal.addEventListener("abort",e,{once:!0}),n.addObserver(()=>t.signal.removeEventListener("abort",e))}}return n},e0=(e,t)=>new Promise((n,r)=>{eQ(e,t).addObserver(n)}),e1=(e,t)=>e0(e,t).then(e=>{if("Failure"===e._tag)throw e.cause;return e.value}),e2=e=>{let t=new eE,n=eQ(e,{scheduler:t});return t.flush(),n._exit??e_(n)},e4=e=>{let t=e2(e);if("Failure"===t._tag)throw t.cause;return t.value},e3=function(){class e extends globalThis.Error{}return Object.assign(e.prototype,W,s.KE,{[P]:"Failure",[$](){return K(this)},toString(){return this.message?`${this.name}: ${this.message}`:this.name},toJSON(){return{...this}},[d.FX](){let e=this.stack;return e?`${this.toString()}
${e.split("\n").slice(1).join("\n")}`:this.toString()}}),e}(),e5=class extends e3{constructor(e){super(),e&&Object.assign(this,e)}},e6=e=>{class t extends e5{constructor(...t){super(...t),this._tag=e}}return t.prototype.name=e,t}},76542:(e,t,n)=>{n.d(t,{HR:()=>i,aI:()=>o});var r=n(81144),a=n(36643),s=n(51603);let i=Symbol.for("effect/Equal");function o(){return 1==arguments.length?e=>u(e,arguments[0]):u(arguments[0],arguments[1])}function u(e,t){if(e===t)return!0;let n=typeof e;if(n!==typeof t)return!1;if("object"===n||"function"===n){if(null!==e&&null!==t){if(c(e)&&c(t))return!!(r.tW(e)===r.tW(t)&&e[i](t))||!!s.Hi.enabled&&!!s.Hi.tester&&s.Hi.tester(e,t);if(e instanceof Date&&t instanceof Date)return e.toISOString()===t.toISOString();if(e instanceof URL&&t instanceof URL)return e.href===t.href}if(s.Hi.enabled){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,n)=>u(e,t[n]));if(Object.getPrototypeOf(e)===Object.prototype&&Object.getPrototypeOf(e)===Object.prototype){let n=Object.keys(e),r=Object.keys(t);if(n.length===r.length){for(let r of n)if(!(r in t&&u(e[r],t[r])))return!!s.Hi.tester&&s.Hi.tester(e,t);return!0}}return!!s.Hi.tester&&s.Hi.tester(e,t)}}return!!s.Hi.enabled&&!!s.Hi.tester&&s.Hi.tester(e,t)}let c=e=>(0,a.i5)(e,i)},78148:(e,t,n)=>{n.d(t,{b:()=>o});var r=n(43210),a=n(14163),s=n(60687),i=r.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},81144:(e,t,n)=>{n.d(t,{HR:()=>u,PO:()=>v,QK:()=>x,Yj:()=>g,ai:()=>h,kg:()=>d,tW:()=>c,yT:()=>l});var r=n(6856),a=n(29606),s=n(36643),i=n(51603);let o=(0,a.V)(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),u=Symbol.for("effect/Hash"),c=e=>{if(!0===i.Hi.enabled)return 0;switch(typeof e){case"number":return h(e);case"bigint":return g(e.toString(10));case"boolean":case"symbol":return g(String(e));case"string":return g(e);case"undefined":return g("undefined");case"function":case"object":if(null===e)return g("null");if(e instanceof Date)return c(e.toISOString());if(e instanceof URL)return c(e.href);else if(f(e))return e[u]();else return l(e);default:throw Error(`BUG: unhandled typeof ${typeof e} - please report an issue at https://github.com/Effect-TS/effect/issues`)}},l=e=>(o.has(e)||o.set(e,h(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),o.get(e)),d=e=>t=>53*t^e,p=e=>0xbfffffff&e|e>>>1&0x40000000,f=e=>(0,s.i5)(e,u),h=e=>{if(e!=e||e===1/0)return 0;let t=0|e;for(t!==e&&(t^=0xffffffff*e);e>0xffffffff;)t^=e/=0xffffffff;return p(t)},g=e=>{let t=5381,n=e.length;for(;n;)t=33*t^e.charCodeAt(--n);return p(t)},m=(e,t)=>{let n=12289;for(let a=0;a<t.length;a++)n^=(0,r.Fs)(g(t[a]),d(c(e[t[a]])));return p(n)},x=e=>m(e,Object.keys(e)),v=function(){if(1==arguments.length){let e=arguments[0];return function(t){return Object.defineProperty(e,u,{value:()=>t,enumerable:!1}),t}}let e=arguments[0],t=arguments[1];return Object.defineProperty(e,u,{value:()=>t,enumerable:!1}),t}},91767:(e,t,n)=>{n.d(t,{Jt:()=>w,Wi:()=>_});var r=n(60687),a=n(94987),s=n(67135),i=n(98230),o=n(43210),u=n(4363),c=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function l(e,t){var n=function(e){var t=e.name;if(t&&-1!==t.lastIndexOf(".")&&!e.type){var n=t.split(".").pop().toLowerCase(),r=c.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}(e);if("string"!=typeof n.path){var r=e.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"==typeof t?t:"string"==typeof r&&r.length>0?r:e.name,writable:!1,configurable:!1,enumerable:!0})}return n}var d=[".DS_Store","Thumbs.db"];function p(e){return(0,u.sH)(this,void 0,void 0,function(){return(0,u.YH)(this,function(t){var n;if(f(e)&&f(e.dataTransfer))return[2,function(e,t){return(0,u.sH)(this,void 0,void 0,function(){var n;return(0,u.YH)(this,function(r){switch(r.label){case 0:if(!e.items)return[3,2];if(n=g(e.items).filter(function(e){return"file"===e.kind}),"drop"!==t)return[2,n];return[4,Promise.all(n.map(m))];case 1:return[2,h(function e(t){return t.reduce(function(t,n){return(0,u.fX)((0,u.fX)([],(0,u.zs)(t),!1),(0,u.zs)(Array.isArray(n)?e(n):[n]),!1)},[])}(r.sent()))];case 2:return[2,h(g(e.files).map(function(e){return l(e)}))]}})})}(e.dataTransfer,e.type)];if(f(n=e)&&f(n.target))return[2,g(e.target.files).map(function(e){return l(e)})];return Array.isArray(e)&&e.every(function(e){return"getFile"in e&&"function"==typeof e.getFile})?[2,function(e){return(0,u.sH)(this,void 0,void 0,function(){return(0,u.YH)(this,function(t){switch(t.label){case 0:return[4,Promise.all(e.map(function(e){return e.getFile()}))];case 1:return[2,t.sent().map(function(e){return l(e)})]}})})}(e)]:[2,[]]})})}function f(e){return"object"==typeof e&&null!==e}function h(e){return e.filter(function(e){return -1===d.indexOf(e.name)})}function g(e){if(null===e)return[];for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r)}return t}function m(e){if("function"!=typeof e.webkitGetAsEntry)return x(e);var t=e.webkitGetAsEntry();return t&&t.isDirectory?b(t):x(e)}function x(e){var t=e.getAsFile();return t?Promise.resolve(l(t)):Promise.reject("".concat(e," is not a File"))}function v(e){return(0,u.sH)(this,void 0,void 0,function(){return(0,u.YH)(this,function(t){return[2,e.isDirectory?b(e):function(e){return(0,u.sH)(this,void 0,void 0,function(){return(0,u.YH)(this,function(t){return[2,new Promise(function(t,n){e.file(function(n){t(l(n,e.fullPath))},function(e){n(e)})})]})})}(e)]})})}function b(e){var t=e.createReader();return new Promise(function(e,n){var r=[];!function a(){var s=this;t.readEntries(function(t){return(0,u.sH)(s,void 0,void 0,function(){var s;return(0,u.YH)(this,function(i){switch(i.label){case 0:if(t.length)return[3,5];i.label=1;case 1:return i.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return e(i.sent()),[3,4];case 3:return n(i.sent()),[3,4];case 4:return[3,6];case 5:s=Promise.all(t.map(v)),r.push(s),a(),i.label=6;case 6:return[2]}})})},function(e){n(e)})}()})}function y(e){let{mode:t="manual",appendOnPaste:n=!1,cn:s=a.yu}=e.config??{},u=(0,o.useRef)(new AbortController),[c,l]=(0,o.useState)([]),[d,f]=(0,o.useState)(e.__internal_upload_progress??0),{startUpload:h,isUploading:g,routeConfig:m}=(0,i._)((0,a.s2)(e.url),e.endpoint,e.fetch??globalThis.fetch,{signal:u.current.signal,headers:e.headers,onClientUploadComplete:t=>{l([]),e.onClientUploadComplete?.(t),f(0)},uploadProgressGranularity:e.uploadProgressGranularity,onUploadProgress:t=>{f(t),e.onUploadProgress?.(t)},onUploadError:e.onUploadError,onUploadBegin:e.onUploadBegin,onBeforeUploadBegin:e.onBeforeUploadBegin}),{fileTypes:x,multiple:v}=(0,a.dW)(m),b=!!(e.__internal_dropzone_disabled??e.disabled),y=(()=>{let t=e.__internal_ready??("ready"===e.__internal_state||x.length>0);return e.__internal_state?e.__internal_state:b?"disabled":t?g?"uploading":"ready":"readying"})(),w=(0,o.useCallback)(t=>{h(t,"input"in e?e.input:void 0).catch(t=>{if(t instanceof a.tZ)e.onUploadAborted?.();else throw t})},[e,h]),{getRootProps:_,getInputProps:j,isDragActive:k,rootRef:S}=function({accept:e,disabled:t=!1,maxSize:n=Number.POSITIVE_INFINITY,minSize:r=0,multiple:s=!0,maxFiles:i=0,onDrop:u}){let c=(0,o.useMemo)(()=>(0,a.DG)(e),[e]),l=(0,o.useRef)(null),d=(0,o.useRef)(null),f=(0,o.useRef)([]),[h,g]=(0,o.useReducer)(a.Ff,a.ue),m=(0,o.useCallback)(e=>{e.preventDefault(),e.persist(),f.current=[...f.current,e.target],(0,a.ax)(e)&&Promise.resolve(p(e)).then(t=>{if(e.isPropagationStopped())return;let o=t.length,u=o>0&&(0,a.gs)({files:t,accept:c,minSize:r,maxSize:n,multiple:s,maxFiles:i});g({type:"setDraggedFiles",payload:{isDragAccept:u,isDragReject:o>0&&!u,isDragActive:!0}})}).catch(a.lQ)},[c,i,n,r,s]),x=(0,o.useCallback)(e=>{if(e.preventDefault(),e.persist(),(0,a.ax)(e))try{e.dataTransfer.dropEffect="copy"}catch{(0,a.lQ)()}return!1},[]),v=(0,o.useCallback)(e=>{e.preventDefault(),e.persist();let t=f.current.filter(e=>l.current?.contains(e)),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),f.current=t,t.length>0||g({type:"setDraggedFiles",payload:{isDragActive:!1,isDragAccept:!1,isDragReject:!1}})},[]),b=(0,o.useCallback)(e=>{let t=[];e.forEach(e=>{let s=(0,a.S5)(e,c),i=(0,a.Mh)(e,r,n);s&&i&&t.push(e)}),(0,a.gH)(t,s,i)||t.splice(0),g({type:"setFiles",payload:{acceptedFiles:t}}),u(t)},[c,i,n,r,s,u]),y=(0,o.useCallback)(e=>{e.preventDefault(),e.persist(),f.current=[],(0,a.ax)(e)&&Promise.resolve(p(e)).then(t=>{e.isPropagationStopped()||b(t)}).catch(a.lQ),g({type:"reset"})},[b]),w=(0,o.useCallback)(()=>{d.current&&(g({type:"openDialog"}),d.current.value="",d.current.click())},[]),_=(0,o.useCallback)(e=>{l.current?.isEqualNode(e.target)&&(0,a.uB)(e)&&(e.preventDefault(),w())},[w]),j=(0,o.useCallback)(e=>{e.stopPropagation(),h.isFileDialogActive&&e.preventDefault()},[h.isFileDialogActive]),k=(0,o.useCallback)(()=>g({type:"focus"}),[]),S=(0,o.useCallback)(()=>g({type:"blur"}),[]),E=(0,o.useCallback)(()=>{(0,a.lP)()?setTimeout(w,0):w()},[w]),O=(0,o.useMemo)(()=>()=>({ref:l,role:"presentation",...t?{}:{tabIndex:0,onKeyDown:_,onFocus:k,onBlur:S,onClick:E,onDragEnter:m,onDragOver:x,onDragLeave:v,onDrop:y}}),[t,S,E,m,v,x,y,k,_]),R=(0,o.useMemo)(()=>()=>({ref:d,type:"file",style:{display:"none"},accept:c,multiple:s,tabIndex:-1,...t?{}:{onChange:y,onClick:j}}),[c,s,y,j,t]);return{...h,getRootProps:O,getInputProps:R,rootRef:l}}({onDrop:(0,o.useCallback)(n=>{e.onDrop?.(n),e.onChange?.(n),l(n),"auto"===t&&w(n)},[e,t,w]),multiple:v,accept:(0,a._z)(x),disabled:b});(0,i.u)(r=>{if(!n||document.activeElement!==S.current)return;let s=(0,a.Ur)(r);if(!s?.length)return;let i=s;l(t=>(i=[...t,...s],e.onChange?.(i),i)),e.onChange?.(i),"auto"===t&&w(i)});let E=(0,o.useMemo)(()=>({ready:"readying"!==y,isUploading:"uploading"===y,uploadProgress:d,fileTypes:x,files:c,isDragActive:k}),[x,c,y,d,k]);return(0,r.jsxs)("div",{className:s("mt-2 flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10 text-center",k&&"bg-blue-600/10",e.className,(0,a.I_)(e.appearance?.container,E)),..._(),style:(0,a.f1)(e.appearance?.container,E),"data-state":y,children:[(0,a._4)(e.content?.uploadIcon,E)??(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:s("mx-auto block h-12 w-12 align-middle text-gray-400",(0,a.I_)(e.appearance?.uploadIcon,E)),style:(0,a.f1)(e.appearance?.uploadIcon,E),"data-ut-element":"upload-icon","data-state":y,children:(0,r.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M5.5 17a4.5 4.5 0 0 1-1.44-8.765a4.5 4.5 0 0 1 8.302-3.046a3.5 3.5 0 0 1 4.504 4.272A4 4 0 0 1 15 17H5.5Zm3.75-2.75a.75.75 0 0 0 1.5 0V9.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0l-3.25 3.5a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z",clipRule:"evenodd"})}),(0,r.jsxs)("label",{className:s("relative mt-4 flex w-64 cursor-pointer items-center justify-center text-sm font-semibold leading-6 text-gray-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500","ready"===y?"text-blue-600":"text-gray-500",(0,a.I_)(e.appearance?.label,E)),style:(0,a.f1)(e.appearance?.label,E),"data-ut-element":"label","data-state":y,children:[(0,r.jsx)("input",{className:"sr-only",...j()}),(0,a._4)(e.content?.label,E)??("ready"===y?`Choose ${v?"file(s)":"a file"} or drag and drop`:"Loading...")]}),(0,r.jsx)("div",{className:s("m-0 h-[1.25rem] text-xs leading-5 text-gray-600",(0,a.I_)(e.appearance?.allowedContent,E)),style:(0,a.f1)(e.appearance?.allowedContent,E),"data-ut-element":"allowed-content","data-state":y,children:(0,a._4)(e.content?.allowedContent,E)??(0,a.wV)(m)}),(0,r.jsx)("button",{className:s("group relative mt-4 flex h-10 w-36 items-center justify-center overflow-hidden rounded-md border-none text-base text-white","after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:bg-blue-600 after:transition-[width] after:duration-500 after:content-['']","focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2","disabled:pointer-events-none","data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed","data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400",(0,a.I_)(e.appearance?.button,E)),style:{"--progress-width":`${d}%`,...(0,a.f1)(e.appearance?.button,E)},onClick:e=>{if("uploading"===y){e.preventDefault(),e.stopPropagation(),u.current.abort(),u.current=new AbortController;return}"manual"===t&&c.length>0&&(e.preventDefault(),e.stopPropagation(),w(c))},"data-ut-element":"button","data-state":y,type:"button",disabled:0===c.length||"disabled"===y,children:(()=>{let n=(0,a._4)(e.content?.button,E);if(n)return n;switch(y){case"readying":return"Loading...";case"uploading":if(d>=100)return(0,r.jsx)(i.S,{});return(0,r.jsxs)("span",{className:"z-50",children:[(0,r.jsxs)("span",{className:"block group-hover:hidden",children:[Math.round(d),"%"]}),(0,r.jsx)(i.C,{cn:s,className:"hidden size-4 group-hover:block"})]});default:if("manual"===t&&c.length>0)return`Upload ${c.length} file${1===c.length?"":"s"}`;return`Choose File${v?"(s)":""}`}})()})]})}let w=e=>{(0,a.GI)("@uploadthing/react",i.p.uploadthing,s.rE);let t=(0,a.s2)(e?.url),n=e?.fetch??globalThis.fetch;return e=>(0,r.jsx)(i.U,{...e,url:t,fetch:n})},_=e=>{(0,a.GI)("@uploadthing/react",i.p.uploadthing,s.rE);let t=(0,a.s2)(e?.url),n=e?.fetch??globalThis.fetch;return e=>(0,r.jsx)(y,{...e,url:t,fetch:n})}},94987:(e,t,n)=>{n.d(t,{MC:()=>R,fN:()=>O,tZ:()=>g,oZ:()=>h,SD:()=>S,DG:()=>Z,gs:()=>G,wV:()=>L,_4:()=>H,Lt:()=>_,yu:()=>z,os:()=>T,_z:()=>I,zG:()=>M,dW:()=>A,Ci:()=>E,Ur:()=>U,ue:()=>K,uB:()=>J,ax:()=>V,S5:()=>Y,lP:()=>q,gH:()=>B,Mh:()=>X,lQ:()=>w,de:()=>C,Ff:()=>Q,s2:()=>y,qs:()=>N,N8:()=>m,I_:()=>$,f1:()=>F,oA:()=>j,GI:()=>v});var r=n(76275),a=n(36643),s=n(52861);let i={"audio/3gpp":{source:"iana",extensions:["3gpp"]},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/basic":{source:"iana",extensions:["au","snd"]},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp4":{source:"iana",extensions:["m4a","mp4a"]},"audio/mpeg":{source:"iana",extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/ogg":{source:"iana",extensions:["oga","ogg","spx","opus"]},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/silk":{source:"apache",extensions:["sil"]},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/webm":{source:"apache",extensions:["weba"]},"audio/x-aac":{source:"apache",extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/x-gsm":{source:"apache",extensions:["gsm"]},"audio/xm":{source:"apache",extensions:["xm"]}},o={"image/aces":{source:"iana",extensions:["exr"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",extensions:["avif"]},"image/bmp":{source:"iana",extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",extensions:["jpeg","jpg","jpe","jfif","pjpeg","pjp"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",extensions:["jpm"]},"image/jpx":{source:"iana",extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/png":{source:"iana",extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.microsoft.icon":{source:"iana",extensions:["ico"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]}},u={"text/cache-manifest":{source:"iana",extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/css":{source:"iana",charset:"UTF-8",extensions:["css"]},"text/csv":{source:"iana",extensions:["csv"]},"text/html":{source:"iana",extensions:["html","htm","shtml"]},"text/markdown":{source:"iana",extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/n3":{source:"iana",charset:"UTF-8",extensions:["n3"]},"text/plain":{source:"iana",extensions:["txt","text","conf","def","list","log","in","ini"]},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/richtext":{source:"iana",extensions:["rtx"]},"text/rtf":{source:"iana",extensions:["rtf"]},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shex":{source:"iana",extensions:["shex"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/tab-separated-values":{source:"iana",extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/uri-list":{source:"iana",extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",extensions:["vcard"]},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",extensions:["xml"]}},c={"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h264":{source:"iana",extensions:["h264"]},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",extensions:["mp4","mp4v","mpg4"]},"video/mpeg":{source:"iana",extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/ogg":{source:"iana",extensions:["ogv"]},"video/quicktime":{source:"iana",extensions:["qt","mov"]},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/webm":{source:"apache",extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]}};class l extends r.rN("InvalidURL"){constructor(e){super({reason:`Failed to parse '${e}' as a URL.`})}}class d extends r.rN("FetchError"){}class p extends r.rN("InvalidJson"){}class f extends r.rN("BadRequestError"){getMessage(){return a.u4(this.json)&&"string"==typeof this.json.message?this.json.message:this.message}}class h extends r.rN("UploadAborted"){}class g extends r.rN("UploadAborted"){}async function m(e){let t=await e.text();try{return JSON.parse(t)}catch(e){return console.error(`Error parsing JSON, got '${t}'`,e),Error(`Error parsing JSON, got '${t}'`)}}function x(e){return Object.keys(e)}function v(e,t,n){!function(e,t){let n=/(\d+)\.?(\d+)?\.?(\d+)?/,r=n.exec(e);if(!r?.[0])throw Error(`Invalid semver requirement: ${e}`);let a=n.exec(t);if(!a?.[0])throw Error(`Invalid semver to check: ${t}`);let[s,i,o,u]=r,[c,l,d,p]=a;return e.startsWith("^")?i===l&&(!o||!d||!(o>d)):e.startsWith("~")?i===l&&o===d:i===l&&o===d&&u===p}(t,n)&&console.warn(`!!!WARNING::: ${e} requires "uploadthing@${t}", but version "${n}" is installed`)}let b=e=>r.Jk(function*(){let t="undefined"!=typeof window?window.location.origin:process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:"http://localhost:3000",n=yield*r.Sv({try:()=>new URL(e??"/api/uploadthing",t),catch:()=>new l(e??"/api/uploadthing")});return"/"===n.pathname&&(n.pathname="/api/uploadthing"),n}),y=e=>e instanceof URL?e:r.HZ(b(e));function w(){}function _(){return new Proxy(w,{get:(e,t)=>t})}function j(e,...t){return"function"==typeof e?e(...t):e}let k={BAD_REQUEST:400,NOT_FOUND:404,FORBIDDEN:403,INTERNAL_SERVER_ERROR:500,INTERNAL_CLIENT_ERROR:500,TOO_LARGE:413,TOO_SMALL:400,TOO_MANY_FILES:400,KEY_TOO_LONG:400,URL_GENERATION_FAILED:500,UPLOAD_FAILED:500,MISSING_ENV:500,INVALID_SERVER_CONFIG:500,FILE_LIMIT_EXCEEDED:500};class S extends r.$D{constructor(e){let t="string"==typeof e?{code:"INTERNAL_SERVER_ERROR",message:e}:e;super({message:t.message??function(e,t){return"string"==typeof e?e:e instanceof Error||e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:t??"An unknown error occurred"}(t.cause,t.code)}),this._tag="UploadThingError",this.name="UploadThingError",this.code=t.code,this.data=t.data,t.cause instanceof Error?this.cause=t.cause:a.u4(t.cause)&&a.Et(t.cause.status)&&a.Kg(t.cause.statusText)?this.cause=Error(`Response ${t.cause.status} ${t.cause.statusText}`):a.Kg(t.cause)?this.cause=Error(t.cause):this.cause=t.cause}static toObject(e){return{code:e.code,message:e.message,data:e.data}}static serialize(e){return JSON.stringify(S.toObject(e))}}function E(e){for(let[t,n]of Object.entries(k))if(n===e)return t;return"INTERNAL_SERVER_ERROR"}let O=e=>new S({code:"INTERNAL_CLIENT_ERROR",message:"Something went wrong. Please report this to UploadThing.",cause:e});class R extends s.vw("uploadthing/Fetch")(){}let T=(e,t)=>r.qI(r.eu(R),n=>{let a=new Headers(t?.headers??[]),s={url:e.toString(),method:t?.method,body:t?.body,headers:Object.fromEntries(a)};return r.$m({try:r=>n(e,{...t,headers:a,signal:r}),catch:e=>new d({error:e instanceof Error?{...e,name:e.name,message:e.message,stack:e.stack}:e,input:s})}).pipe(r.sF(e=>r.OH(()=>console.error(e.input))),r.Tj(e=>Object.assign(e,{requestUrl:s.url})),r.SZ("fetch"))}),C=e=>r.$m({try:async()=>({json:await e.json(),ok:e.ok,status:e.status}),catch:t=>new p({error:t,input:e.requestUrl})}).pipe(r.W$(({ok:e})=>e,({json:t,status:n})=>new f({status:n,message:`Request to ${e.requestUrl} failed with status ${n}`,json:t})),r.Tj(({json:e})=>e),r.SZ("parseJson")),N=(e,t)=>"all"===t?e:"fine"===t?Math.round(e):10*Math.floor(e/10),M=e=>{let t=Array.isArray(e)?e:x(e);return t.includes("blob")?[]:t.map(e=>"pdf"===e?"application/pdf":e.includes("/")?e:"audio"===e?["audio/*",...x(i)].join(", "):"image"===e?["image/*",...x(o)].join(", "):"text"===e?["text/*",...x(u)].join(", "):"video"===e?["video/*",...x(c)].join(", "):`${e}/*`)},I=e=>Object.fromEntries(M(e).map(e=>[e,[]]));function U(e){let t=e.clipboardData?.items;if(t)return Array.from(t).reduce((e,t)=>{let n=t.getAsFile();return n?[...e,n]:e},[])}let A=e=>({fileTypes:e?x(e):[],multiple:(e?Object.values(e).map(e=>e.maxFileCount):[]).some(e=>e&&e>1)}),D=e=>e.charAt(0).toUpperCase()+e.slice(1),P=e=>{if(!e)return"";let t=x(e),n=t.map(e=>"blob"===e?"file":e);if(n.length>1){let e=n.pop();return`${n.join("s, ")} and ${e}s`}let r=t[0],a=n[0];if(!r||!a)return"";let{maxFileSize:s,maxFileCount:i,minFileCount:o}=e[r];return i&&i>1?o>1?`${o} - ${i} ${a}s up to ${s}`:`${a}s up to ${s}, max ${i}`:`${a} (${s})`},L=e=>D(P(e)),$=(e,t)=>{if("string"==typeof e)return e;if("function"==typeof e){let n=e(t);if("string"==typeof n)return n}return""},F=(e,t)=>{if("object"==typeof e)return e;if("function"==typeof e){let n=e(t);if("object"==typeof n)return n}return{}},H=(e,t)=>e?"function"!=typeof e?e:"function"==typeof e?e(t):void 0:null,z=(...e)=>e.filter(Boolean).join(" ");function Y(e,t){return"application/x-moz-file"===e.type||function(e,t){if(t){let n=Array.isArray(t)?t:t.split(","),r=e.name,a=e.type.toLowerCase(),s=a.replace(/\/.*$/,"");return n.some(e=>{let t=e.trim().toLowerCase();return t.startsWith(".")?r.toLowerCase().endsWith(t):t.endsWith("/*")?s===t.replace(/\/.*$/,""):a===t})}return!0}(e,t)}function J(e){return"key"in e&&(" "===e.key||"Enter"===e.key)||"keyCode"in e&&(32===e.keyCode||13===e.keyCode)}new TextEncoder;let W=e=>null!=e;function X(e,t,n){return!W(e.size)||(W(t)&&W(n)?e.size>=t&&e.size<=n:!(W(t)&&e.size<t||W(n)&&e.size>n))}function B(e,t,n){return(!!t||!(e.length>1))&&(!t||!(n>=1)||!(e.length>n))}function G({files:e,accept:t,minSize:n,maxSize:r,multiple:a,maxFiles:s}){return!!B(e,a,s)&&e.every(e=>Y(e,t)&&X(e,n,r))}function V(e){return"dataTransfer"in e&&null!==e.dataTransfer?Array.prototype.some.call(e.dataTransfer?.types,e=>"Files"===e||"application/x-moz-file"===e):!!e.target&&"files"in e.target&&!!e.target.files}function q(e=window.navigator.userAgent){return e.includes("MSIE ")||e.includes("Trident/")||e.includes("Edge/")}function Z(e){if(W(e))return Object.entries(e).reduce((e,[t,n])=>[...e,t,...n],[]).filter(e=>"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)||/^.*\.[\w]+$/.test(e)).join(",")}let K={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[]};function Q(e,t){switch(t.type){case"focus":return{...e,isFocused:!0};case"blur":return{...e,isFocused:!1};case"openDialog":return{...K,isFileDialogActive:!0};case"closeDialog":return{...e,isFileDialogActive:!1};case"setDraggedFiles":case"setFiles":return{...e,...t.payload};case"reset":return K;default:return e}}},98230:(e,t,n)=>{n.d(t,{C:()=>m,S:()=>g,U:()=>x,_:()=>p,g:()=>f,p:()=>o,u:()=>h});var r=n(60687),a=n(43210),s=n(94987),i=n(67135),o={uploadthing:"^7.2.0"};let u=()=>void 0;function c(e){let t=a.useRef(l);u(()=>{t.current=e},[e]);let n=a.useRef(null);return n.current??=function(){return t.current.apply(this,arguments)},n.current}function l(){throw Error("INVALID_USEEVENT_INVOCATION: the callback from useEvent cannot be invoked before the component has mounted.")}let d=(e,t,n)=>{let r=globalThis.__UPLOADTHING,{data:s}=function(e,t,n){(0,a.useRef)({}),(0,a.useRef)(!1);let r={error:void 0,data:void 0},[s,i]=(0,a.useReducer)((e,t)=>{switch(t.type){case"loading":return{...r};case"fetched":return{...r,data:t.payload};case"error":return{...r,error:t.payload};default:return e}},r);return s}(0,r||t.href);return(r??s)?.find(e=>e.slug===n)?.config},p=function(e,t,n,r){let o=r?.uploadProgressGranularity??"coarse",{uploadFiles:u,routeRegistry:l}=(0,i.r)({fetch:n,url:e,package:"@uploadthing/react"}),[p,f]=(0,a.useState)(!1),h=(0,a.useRef)(0),g=(0,a.useRef)(new Map);return{startUpload:c(async(...e)=>{let n=await r?.onBeforeUploadBegin?.(e[0])??e[0],a=e[1];f(!0),n.forEach(e=>g.current.set(e,0)),r?.onUploadProgress?.(0);try{let e=await u(t,{signal:r?.signal,headers:r?.headers,files:n,onUploadProgress:e=>{if(!r?.onUploadProgress)return;g.current.set(e.file,e.progress);let t=0;g.current.forEach(e=>{t+=e});let n=(0,s.qs)(Math.min(100,t/g.current.size),o);n!==h.current&&(r.onUploadProgress(n),h.current=n)},onUploadBegin({file:e}){r?.onUploadBegin&&r.onUploadBegin(e)},input:a});return await r?.onClientUploadComplete?.(e),e}catch(t){let e;if(t instanceof s.tZ)throw t;t instanceof s.SD?e=t:console.error("Something went wrong. Please contact UploadThing and provide the following cause:",(e=(0,s.fN)(t)).cause instanceof Error?e.cause.toString():e.cause),await r?.onUploadError?.(e)}finally{f(!1),g.current=new Map,h.current=0}}),isUploading:p,routeConfig:d(n,e,(0,s.oA)(t,l))}},f=e=>{(0,s.GI)("@uploadthing/react",o.uploadthing,i.rE);let t=e?.fetch??globalThis.fetch,n=(0,s.s2)(e?.url),r=(0,i.r)({fetch:t,url:n,package:"@uploadthing/react"});return{useUploadThing:function(e,r){return p(n,e,t,r)},...r,getRouteConfig:function(e){let t=globalThis.__UPLOADTHING,n=(0,s.oA)(e,r.routeRegistry),a=t?.find(e=>e.slug===n)?.config;if(!a)throw Error(`No config found for endpoint "${n.toString()}". Please make sure to use the NextSSRPlugin in your Next.js app.`);return a}}},h=e=>{let t=c(e);(0,a.useEffect)(()=>{let e=new AbortController;return window.addEventListener("paste",t,{signal:e.signal}),()=>{e.abort()}},[t])};function g(){return(0,r.jsx)("svg",{className:"z-10 block h-5 w-5 animate-spin align-middle text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 576 512",children:(0,r.jsx)("path",{fill:"currentColor",d:"M256 32C256 14.33 270.3 0 288 0C429.4 0 544 114.6 544 256C544 302.6 531.5 346.4 509.7 384C500.9 399.3 481.3 404.6 465.1 395.7C450.7 386.9 445.5 367.3 454.3 351.1C470.6 323.8 480 291 480 255.1C480 149.1 394 63.1 288 63.1C270.3 63.1 256 49.67 256 31.1V32z"})})}function m({className:e,cn:t,...n}){return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",strokeLinecap:"round",strokeLinejoin:"round",className:t("fill-none stroke-current stroke-2",e),...n,children:[(0,r.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,r.jsx)("path",{d:"m4.9 4.9 14.2 14.2"})]})}function x(e){let{mode:t="auto",appendOnPaste:n=!1,cn:i=s.yu}=e.config??{},o=(0,a.useRef)(new AbortController),u=(0,a.useRef)(null),[c,l]=(0,a.useState)(e.__internal_upload_progress??0),[d,f]=(0,a.useState)([]),{startUpload:x,isUploading:v,routeConfig:b}=p((0,s.s2)(e.url),e.endpoint,e.fetch??globalThis.fetch,{signal:o.current.signal,headers:e.headers,onClientUploadComplete:t=>{u.current&&(u.current.value=""),f([]),e.onClientUploadComplete?.(t),l(0)},uploadProgressGranularity:e.uploadProgressGranularity,onUploadProgress:t=>{l(t),e.onUploadProgress?.(t)},onUploadError:e.onUploadError,onUploadBegin:e.onUploadBegin,onBeforeUploadBegin:e.onBeforeUploadBegin}),{fileTypes:y,multiple:w}=(0,s.dW)(b),_=!!(e.__internal_button_disabled??e.disabled),j=(()=>{let t="ready"===e.__internal_state||y.length>0;return e.__internal_state?e.__internal_state:_?"disabled":t?v?"uploading":"ready":"readying"})(),k=(0,a.useCallback)(t=>{x(t,"input"in e?e.input:void 0).catch(t=>{if(t instanceof s.tZ)e.onUploadAborted?.();else throw t})},[e,x]),S=(0,a.useMemo)(()=>({type:"file",ref:u,multiple:w,accept:(0,s.zG)(y).join(", "),onChange:n=>{if(!n.target.files)return;let r=Array.from(n.target.files);if(e.onChange?.(r),"manual"===t){f(r);return}k(r)},disabled:_,tabIndex:_?-1:0}),[e,_,y,t,w,k]);h(r=>{if(!n||document.activeElement!==u.current)return;let a=(0,s.Ur)(r);if(!a)return;let i=a;f(t=>(i=[...t,...a],e.onChange?.(i),i)),"auto"===t&&k(d)});let E=(0,a.useMemo)(()=>({ready:"readying"!==j,isUploading:"uploading"===j,uploadProgress:c,fileTypes:y,files:d}),[y,d,j,c]);return(0,r.jsxs)("div",{className:i("flex flex-col items-center justify-center gap-1",e.className,(0,s.I_)(e.appearance?.container,E)),style:{"--progress-width":`${c}%`,...(0,s.f1)(e.appearance?.container,E)},"data-state":j,children:[(0,r.jsxs)("label",{className:i("group relative flex h-10 w-36 cursor-pointer items-center justify-center overflow-hidden rounded-md text-white after:transition-[width] after:duration-500 focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2","disabled:pointer-events-none","data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed","data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400","after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:content-[''] data-[state=uploading]:after:bg-blue-600",(0,s.I_)(e.appearance?.button,E)),style:(0,s.f1)(e.appearance?.button,E),"data-state":j,"data-ut-element":"button",onClick:e=>{if("uploading"===j){e.preventDefault(),e.stopPropagation(),o.current.abort(),o.current=new AbortController;return}"manual"===t&&d.length>0&&(e.preventDefault(),e.stopPropagation(),k(d))},children:[(0,r.jsx)("input",{...S,className:"sr-only"}),(()=>{let n=(0,s._4)(e.content?.button,E);if(n)return n;switch(j){case"readying":return"Loading...";case"uploading":if(c>=100)return(0,r.jsx)(g,{});return(0,r.jsxs)("span",{className:"z-50",children:[(0,r.jsxs)("span",{className:"block group-hover:hidden",children:[Math.round(c),"%"]}),(0,r.jsx)(m,{cn:i,className:"hidden size-4 group-hover:block"})]});default:if("manual"===t&&d.length>0)return`Upload ${d.length} file${1===d.length?"":"s"}`;return`Choose File${S.multiple?"(s)":""}`}})()]}),"manual"===t&&d.length>0?(0,r.jsx)("button",{onClick:()=>{f([]),u.current&&(u.current.value=""),e.onChange?.([])},className:i("h-[1.25rem] cursor-pointer rounded border-none bg-transparent text-gray-500 transition-colors hover:bg-slate-200 hover:text-gray-600",(0,s.I_)(e.appearance?.clearBtn,E)),style:(0,s.f1)(e.appearance?.clearBtn,E),"data-state":j,"data-ut-element":"clear-btn",children:(0,s._4)(e.content?.clearBtn,E)??"Clear"}):(0,r.jsx)("div",{className:i("h-[1.25rem] text-xs leading-5 text-gray-600",(0,s.I_)(e.appearance?.allowedContent,E)),style:(0,s.f1)(e.appearance?.allowedContent,E),"data-state":j,"data-ut-element":"allowed-content",children:(0,s._4)(e.content?.allowedContent,E)??(0,s.wV)(b)})]})}}};