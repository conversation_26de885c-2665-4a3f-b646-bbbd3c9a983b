'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Search, Filter, Grid, List, Eye, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { formatPrice } from '@/lib/utils/formatPrice';
// PropertyFormDialog removed - now using dedicated pages

interface Property {
  id: string;
  title: string;
  titleAr?: string;
  description: string;
  descriptionAr?: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  location: string;
  locationAr?: string;
  address: string;
  addressAr?: string;
  city: string;
  cityAr?: string;
  images: string[];
  features: string[];
  featuresAr: string[];
  amenities: string[];
  amenitiesAr: string[];
  isFeatured: boolean;
  isActive: boolean;
  viewCount: number;
  createdAt: string;
  agent?: {
    id: string;
    name: string;
    email: string;
  };
}

interface PropertyStats {
  total: number;
  available: number;
  sold: number;
  rented: number;
  featured: number;
  byType: Record<string, number>;
}

export default function PropertiesPage() {
  const router = useRouter();
  const { language } = useSimpleLanguage();
  const [properties, setProperties] = useState<Property[]>([]);
  const [stats, setStats] = useState<PropertyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('الكل');
  const [filterStatus, setFilterStatus] = useState('الكل');
  // Removed dialog states - now using dedicated pages

  // Fetch properties and stats
  useEffect(() => {
    fetchProperties();
    fetchStats();
  }, [searchTerm, filterType, filterStatus]);

  const fetchProperties = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (filterType && filterType !== 'الكل' && filterType !== 'ALL') params.append('type', filterType);
      if (filterStatus && filterStatus !== 'الكل' && filterStatus !== 'ALL') params.append('status', filterStatus);
      params.append('isActive', 'true');

      const response = await fetch(`/api/v1/properties?${params}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.properties && data.data.properties.length > 0) {
          setProperties(data.data.properties);
          console.log('API data loaded:', data.data.properties.length, 'properties');
          return; // Exit early if API data is successful
        }
      }

      // Always use mock data as fallback
      console.log('Using mock data (API status:', response.status, ')');
      setProperties([
          {
            id: 'prop-available-1',
            title: 'Luxury Villa in Dubai Marina',
            titleAr: 'فيلا فاخرة في دبي مارينا',
            description: 'Beautiful 4-bedroom villa with sea view',
            descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',
            price: 2500000,
            currency: 'AED',
            type: 'VILLA',
            status: 'AVAILABLE',
            bedrooms: 4,
            bathrooms: 3,
            area: 350,
            location: 'Dubai Marina',
            locationAr: 'دبي مارينا',
            address: '123 Marina Walk',
            addressAr: '123 ممشى المارينا',
            city: 'Dubai',
            cityAr: 'دبي',
            images: ['https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop'],
            features: ['Swimming Pool', 'Gym', 'Parking'],
            featuresAr: ['مسبح', 'صالة رياضية', 'موقف سيارات'],
            amenities: ['24/7 Security', 'Concierge'],
            amenitiesAr: ['أمن 24/7', 'خدمة الكونسيرج'],
            isFeatured: true,
            isActive: true,
            viewCount: 125,
            createdAt: new Date().toISOString(),
          },
          {
            id: 'prop-sold-1',
            title: 'SOLD - Apartment in Burj Khalifa',
            titleAr: 'مباع - شقة في برج خليفة',
            description: 'Luxury apartment that was recently sold',
            descriptionAr: 'شقة فاخرة تم بيعها مؤخراً',
            price: 3200000,
            currency: 'AED',
            type: 'APARTMENT',
            status: 'SOLD',
            bedrooms: 3,
            bathrooms: 2,
            area: 180,
            location: 'Downtown Dubai',
            locationAr: 'وسط مدينة دبي',
            address: 'Burj Khalifa, Floor 45',
            addressAr: 'برج خليفة، الطابق 45',
            city: 'Dubai',
            cityAr: 'دبي',
            images: ['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop'],
            features: ['Burj Khalifa View', 'High Speed Elevator'],
            featuresAr: ['إطلالة على برج خليفة', 'مصعد عالي السرعة'],
            amenities: ['Gym', 'Pool', 'Spa'],
            amenitiesAr: ['صالة رياضية', 'مسبح', 'سبا'],
            isFeatured: false,
            isActive: false,
            viewCount: 89,
            createdAt: new Date().toISOString(),
          },
          {
            id: 'prop-rented-1',
            title: 'RENTED - Family House in Jumeirah',
            titleAr: 'مؤجر - بيت عائلي في جميرا',
            description: 'Family house currently rented',
            descriptionAr: 'بيت عائلي مؤجر حالياً',
            price: 180000,
            currency: 'AED',
            type: 'HOUSE',
            status: 'RENTED',
            bedrooms: 5,
            bathrooms: 4,
            area: 280,
            location: 'Jumeirah',
            locationAr: 'جميرا',
            address: '456 Jumeirah Street',
            addressAr: '456 شارع جميرا',
            city: 'Dubai',
            cityAr: 'دبي',
            images: ['https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop'],
            features: ['Large Garden', 'Swimming Pool', '3 Car Parking'],
            featuresAr: ['حديقة كبيرة', 'مسبح', 'موقف 3 سيارات'],
            amenities: ['Near Schools', 'Quiet Area'],
            amenitiesAr: ['قريب من المدارس', 'منطقة هادئة'],
            isFeatured: true,
            isActive: true,
            viewCount: 67,
            createdAt: new Date().toISOString(),
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
      // Fallback to empty array on error
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/v1/properties/stats');

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setStats(data.data);
          console.log('API stats loaded');
          return; // Exit early if API stats are successful
        }
      }

      // Always use mock stats as fallback
      console.log('Using mock stats (API status:', response.status, ')');
      setStats({
          total: 3,
          available: 1,
          sold: 1,
          rented: 1,
          featured: 2,
          byType: {
            APARTMENT: 1,
            VILLA: 1,
            HOUSE: 1,
            TOWNHOUSE: 0,
            PENTHOUSE: 0,
            STUDIO: 0,
            OFFICE: 0,
            SHOP: 0,
            WAREHOUSE: 0,
            LAND: 0,
            BUILDING: 0,
          }
        });
    } catch (error) {
      console.error('Error fetching stats:', error);
      // Fallback to default stats on error
      setStats({
        total: 0,
        available: 0,
        sold: 0,
        rented: 0,
        featured: 0,
        byType: {
          APARTMENT: 0,
          VILLA: 0,
          TOWNHOUSE: 0,
          PENTHOUSE: 0,
          STUDIO: 0,
          OFFICE: 0,
          SHOP: 0,
          WAREHOUSE: 0,
          LAND: 0,
          BUILDING: 0,
        }
      });
    }
  };

  const handleDeleteProperty = async (id: string) => {
    if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?')) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/properties/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchProperties();
        fetchStats();
      }
    } catch (error) {
      console.error('Error deleting property:', error);
    }
  };

  const formatPriceLocal = (price: number, currency: string) => {
    return formatPrice(price, currency, language === 'ar' ? 'ar-AE' : 'en-US');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-600 text-white';
      case 'SOLD': return 'bg-red-600 text-white';
      case 'RENTED': return 'bg-blue-600 text-white';
      case 'PENDING': return 'bg-yellow-600 text-black';
      case 'RESERVED': return 'bg-orange-600 text-white';
      default: return 'bg-gray-600 text-white';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'متاح';
      case 'SOLD': return 'مباع';
      case 'RENTED': return 'مؤجر';
      case 'PENDING': return 'قيد المراجعة';
      case 'RESERVED': return 'محجوز';
      default: return status;
    }
  };

  const getPropertyTitle = (property: Property) => {
    return language === 'ar' && property.titleAr ? property.titleAr : property.title;
  };

  const getPropertyLocation = (property: Property) => {
    return language === 'ar' && property.locationAr ? property.locationAr : property.location;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {language === 'ar' ? 'العقارات' : 'Properties'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {language === 'ar' ? 'إدارة العقارات والممتلكات' : 'Manage properties and real estate'}
          </p>
        </div>
        <Button
          onClick={() => router.push('/dashboard/properties/create')}
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Plus className="h-4 w-4" />
          {language === 'ar' ? 'إضافة عقار' : 'Add Property'}
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'إجمالي العقارات' : 'Total Properties'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'متاح' : 'Available'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.available}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'مباع' : 'Sold'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.sold}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'مؤجر' : 'Rented'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.rented}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'مميز' : 'Featured'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{stats.featured}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={language === 'ar' ? 'البحث في العقارات...' : 'Search properties...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder={language === 'ar' ? 'نوع العقار' : 'Property Type'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="الكل">جميع الأنواع</SelectItem>
            <SelectItem value="APARTMENT">شقة</SelectItem>
            <SelectItem value="VILLA">فيلا</SelectItem>
            <SelectItem value="TOWNHOUSE">تاون هاوس</SelectItem>
            <SelectItem value="OFFICE">مكتب</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder={language === 'ar' ? 'الحالة' : 'Status'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="الكل">جميع الحالات</SelectItem>
            <SelectItem value="AVAILABLE">متاح</SelectItem>
            <SelectItem value="SOLD">مباع</SelectItem>
            <SelectItem value="RENTED">مؤجر</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Properties Grid/List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
        </div>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {properties.map((property) => (
            <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              {viewMode === 'grid' ? (
                <>
                  {property.images.length > 0 && (
                    <div className="relative h-48 bg-gray-200 dark:bg-gray-700">
                      <img
                        src={property.images[0]}
                        alt={getPropertyTitle(property)}
                        className="w-full h-full object-cover"
                      />
                      {property.isFeatured && (
                        <Badge className="absolute top-2 left-2 bg-purple-600">
                          {language === 'ar' ? 'مميز' : 'Featured'}
                        </Badge>
                      )}
                    </div>
                  )}
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg truncate">{getPropertyTitle(property)}</h3>
                      <Badge className={getStatusColor(property.status)}>
                        {getStatusText(property.status)}
                      </Badge>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                      {getPropertyLocation(property)}
                    </p>
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-xl font-bold text-blue-600">
                        {formatPriceLocal(property.price, property.currency)}
                      </span>
                      <div className="flex gap-2 text-sm text-gray-500">
                        {property.bedrooms && <span>{property.bedrooms} {language === 'ar' ? 'غرف' : 'bed'}</span>}
                        {property.bathrooms && <span>{property.bathrooms} {language === 'ar' ? 'حمام' : 'bath'}</span>}
                        {property.area && <span>{property.area}m²</span>}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => router.push(`/dashboard/properties/${property.id}`)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/dashboard/properties/${property.id}/edit`)}
                          className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeleteProperty(property.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <span className="text-xs text-gray-500">
                        {language === 'ar' ? 'المشاهدات:' : 'Views:'} {property.viewCount}
                      </span>
                    </div>
                  </CardContent>
                </>
              ) : (
                <CardContent className="p-4">
                  <div className="flex gap-4">
                    {property.images.length > 0 && (
                      <div className="relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded">
                        <img
                          src={property.images[0]}
                          alt={getPropertyTitle(property)}
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-lg">{getPropertyTitle(property)}</h3>
                        <div className="flex gap-2">
                          <Badge className={getStatusColor(property.status)}>
                            {getStatusText(property.status)}
                          </Badge>
                          {property.isFeatured && (
                            <Badge className="bg-purple-600">
                              {language === 'ar' ? 'مميز' : 'Featured'}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                        {getPropertyLocation(property)}
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold text-blue-600">
                          {formatPriceLocal(property.price, property.currency)}
                        </span>
                        <div className="flex gap-4 text-sm text-gray-500">
                          {property.bedrooms && <span>{property.bedrooms} {language === 'ar' ? 'غرف' : 'bed'}</span>}
                          {property.bathrooms && <span>{property.bathrooms} {language === 'ar' ? 'حمام' : 'bath'}</span>}
                          {property.area && <span>{property.area}m²</span>}
                          <span>{language === 'ar' ? 'المشاهدات:' : 'Views:'} {property.viewCount}</span>
                        </div>
                        <div className="flex gap-1">
                          <Button size="sm" variant="outline" onClick={() => router.push(`/dashboard/properties/${property.id}`)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/dashboard/properties/${property.id}/edit`)}
                            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleDeleteProperty(property.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Dialogs removed - now using dedicated pages */}
    </div>
  );
}
