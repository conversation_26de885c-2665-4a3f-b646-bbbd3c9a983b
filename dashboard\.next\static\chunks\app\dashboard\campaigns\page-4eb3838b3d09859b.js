(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5381],{5623:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14503:(e,t,s)=>{"use strict";s.d(t,{dj:()=>f,oR:()=>u});var a=s(12115);let r=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=a.useState(d);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},20076:(e,t,s)=>{Promise.resolve().then(s.bind(s,22868))},22868:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(95155),r=s(97168),n=s(89852),o=s(53904),i=s(49103),l=s(47924),d=s(12115),c=s(88145),u=s(88524),f=s(67133),m=s(16559),p=s(81497),h=s(5623),x=s(12486),g=s(13717),y=s(62525),b=s(20433),v=s(31886),w=s(14503),j=s(6874),N=s.n(j);function A(e){let{campaigns:t=[],loading:s,onRefresh:n}=e,[i,l]=(0,d.useState)(!1),[j,A]=(0,d.useState)(!1),[k,C]=(0,d.useState)(null),{toast:R}=(0,w.dj)(),E=Array.isArray(t)?t:[],T=e=>{C(e),l(!0)},S=e=>{C(e),A(!0)},_=async()=>{if(k)try{await v.A.delete("/marketing/campaigns/".concat(k.id)),R({title:"Success",description:"Campaign deleted successfully"}),n()}catch(e){R({title:"Error",description:"Failed to delete campaign",variant:"destructive"})}finally{l(!1),C(null)}},O=e=>{switch(e.toLowerCase()){case"active":return"bg-green-100 text-green-800";case"scheduled":return"bg-blue-100 text-blue-800";case"completed":return"bg-emerald-100 text-emerald-800";case"paused":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},I=e=>{switch(e.toLowerCase()){case"active":return"\uD83D\uDFE2";case"scheduled":return"\uD83D\uDD35";case"completed":return"✅";case"paused":return"⏸️";case"draft":return"\uD83D\uDCDD";default:return"⚪"}};return s?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,a.jsx)("span",{children:"Loading campaigns..."})]}):0===E.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"No campaigns found"}),(0,a.jsx)(N(),{href:"/dashboard/campaigns/create",children:(0,a.jsxs)(r.$,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Create Campaign"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"border rounded-lg",children:(0,a.jsxs)(u.XI,{children:[(0,a.jsx)(u.A0,{children:(0,a.jsxs)(u.Hj,{children:[(0,a.jsx)(u.nd,{children:"Campaign"}),(0,a.jsx)(u.nd,{children:"Status"}),(0,a.jsx)(u.nd,{children:"Type"}),(0,a.jsx)(u.nd,{children:"Recipients"}),(0,a.jsx)(u.nd,{children:"Created"}),(0,a.jsx)(u.nd,{className:"w-[70px]"})]})}),(0,a.jsx)(u.BF,{children:E.map(e=>(0,a.jsxs)(u.Hj,{children:[(0,a.jsx)(u.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground truncate max-w-[200px]",children:e.message||"No message"})]})}),(0,a.jsx)(u.nA,{children:(0,a.jsxs)(c.E,{className:O(e.status),children:[I(e.status)," ",e.status]})}),(0,a.jsx)(u.nA,{children:(0,a.jsx)(c.E,{variant:"outline",children:e.type})}),(0,a.jsxs)(u.nA,{children:[e.sentCount||0," / ",e.totalRecipients||0]}),(0,a.jsx)(u.nA,{className:"text-muted-foreground",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)(u.nA,{children:(0,a.jsxs)(f.rI,{children:[(0,a.jsx)(f.ty,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(f.SQ,{align:"end",children:[(0,a.jsxs)(f._2,{onClick:()=>S(e),children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Send"]}),(0,a.jsx)(f._2,{asChild:!0,children:(0,a.jsxs)(N(),{href:"/dashboard/campaigns/edit/".concat(e.id),children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,a.jsxs)(f._2,{onClick:()=>T(e),className:"text-destructive",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})}),(0,a.jsx)(b.T,{open:j,onOpenChange:A,campaign:k,onSuccess:()=>{A(!1),C(null),n()}}),(0,a.jsx)(m.Lt,{open:i,onOpenChange:l,children:(0,a.jsxs)(m.EO,{children:[(0,a.jsxs)(m.wd,{children:[(0,a.jsx)(m.r7,{children:"Delete Campaign"}),(0,a.jsxs)(m.$v,{children:['Are you sure you want to delete "',null==k?void 0:k.name,'"? This action cannot be undone.']})]}),(0,a.jsxs)(m.ck,{children:[(0,a.jsx)(m.Zr,{children:"Cancel"}),(0,a.jsx)(m.Rx,{onClick:_,className:"bg-destructive text-destructive-foreground",children:"Delete"})]})]})})]})}function k(){let{campaigns:e,loading:t,searchTerm:s,setSearchTerm:c,refreshCampaigns:u}=function(){let[e,t]=(0,d.useState)([]),[s,a]=(0,d.useState)(!0),[r,n]=(0,d.useState)(""),o=async()=>{try{a(!0);let e=await v.A.get("/marketing/campaigns");console.log("Campaigns API Response:",e);let s=[];Array.isArray(e)?s=e:e&&Array.isArray(e.data)?s=e.data:e&&e.campaigns&&Array.isArray(e.campaigns)?s=e.campaigns:console.warn("Unexpected campaigns response format:",e),console.log("Processed campaigns data:",s),t(s)}catch(e){console.error("Error fetching campaigns:",e),t([])}finally{a(!1)}},i=(0,d.useMemo)(()=>{if(!r)return e;let t=r.toLowerCase();return e.filter(e=>e.name.toLowerCase().includes(t)||e.status.toLowerCase().includes(t)||e.type.toLowerCase().includes(t)||e.message&&e.message.toLowerCase().includes(t))},[e,r]);return(0,d.useEffect)(()=>{o()},[]),{campaigns:i,allCampaigns:e,loading:s,searchTerm:r,setSearchTerm:n,refreshCampaigns:()=>{o()}}}();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold",children:["Campaigns (",e.length,")"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(r.$,{variant:"outline",onClick:u,disabled:t,children:(0,a.jsx)(o.A,{className:"h-4 w-4 ".concat(t?"animate-spin":"")})}),(0,a.jsx)(N(),{href:"/dashboard/campaigns/create",children:(0,a.jsxs)(r.$,{children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Create Campaign"]})})]})]}),(0,a.jsxs)("div",{className:"relative max-w-md",children:[(0,a.jsx)(l.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(n.p,{placeholder:"Search campaigns...",value:s,onChange:e=>c(e.target.value),className:"pl-10"})]}),(0,a.jsx)(A,{campaigns:e,loading:t,onRefresh:u})]})}},31886:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(23464);class r{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(t){if(t.response){let s=t.response.status;if(console.log("API Client: Request to ".concat(e," failed with status ").concat(s)),404===s){let t=Error("Resource not found: ".concat(e));throw t.status=404,t.isNotFound=!0,t}}throw t}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let s=await this.client.delete(e,t);if(204===s.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return s.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let a={...s,headers:{...null==s?void 0:s.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let n=new r},47924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49103:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(52596),r=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},62525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},67133:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>f,_2:()=>m,mB:()=>p,rI:()=>c,ty:()=>u});var a=s(95155),r=s(12115),n=s(48698),o=s(13052),i=s(5196),l=s(9428),d=s(53999);let c=n.bL,u=n.l9;n.YJ,n.ZL,n.Pb,n.z6,r.forwardRef((e,t)=>{let{className:s,inset:r,children:i,...l}=e;return(0,a.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",s),...l,children:[i,(0,a.jsx)(o.A,{className:"ml-auto"})]})}).displayName=n.ZP.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...r})}).displayName=n.G5.displayName;let f=r.forwardRef((e,t)=>{let{className:s,sideOffset:r=4,...o}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,sideOffset:r,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...o})})});f.displayName=n.UC.displayName;let m=r.forwardRef((e,t)=>{let{className:s,inset:r,...o}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",s),...o})});m.displayName=n.q7.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,checked:o,...l}=e;return(0,a.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:o,...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),r]})}).displayName=n.H_.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=n.hN.displayName,r.forwardRef((e,t)=>{let{className:s,inset:r,...o}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",s),...o})}).displayName=n.JU.displayName;let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...r})});p.displayName=n.wv.displayName},81497:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},82714:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var a=s(95155),r=s(12115),n=s(40968),o=s(74466),i=s(53999);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,i.cn)(l(),s),...r})});d.displayName=n.b.displayName},88145:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(95155);s(12115);var r=s(74466),n=s(53999);let o=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(o({variant:s}),t),...r})}},88524:(e,t,s)=>{"use strict";s.d(t,{A0:()=>i,BF:()=>l,Hj:()=>d,XI:()=>o,nA:()=>u,nd:()=>c});var a=s(95155),r=s(12115),n=s(53999);let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",s),...r})})});o.displayName="Table";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",s),...r})});i.displayName="TableHeader";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",s),...r})});l.displayName="TableBody",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});d.displayName="TableRow";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});c.displayName="TableHead";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});u.displayName="TableCell",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"},89852:(e,t,s)=>{"use strict";s.d(t,{p:()=>o});var a=s(95155),r=s(12115),n=s(53999);let o=r.forwardRef((e,t)=>{let{className:s,type:r,...o}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...o})});o.displayName="Input"},95784:(e,t,s)=>{"use strict";s.d(t,{bq:()=>f,eb:()=>x,gC:()=>h,l6:()=>c,yv:()=>u});var a=s(95155),r=s(12115),n=s(31992),o=s(66474),i=s(47863),l=s(5196),d=s(53999);let c=n.bL;n.YJ;let u=n.WT,f=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(n.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[r,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=n.l9.displayName;let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});m.displayName=n.PP.displayName;let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})});p.displayName=n.wn.displayName;let h=r.forwardRef((e,t)=>{let{className:s,children:r,position:o="popper",...i}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:o,...i,children:[(0,a.jsx)(m,{}),(0,a.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(p,{})]})})});h.displayName=n.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=n.JU.displayName;let x=r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsxs)(n.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(n.p4,{children:r})]})});x.displayName=n.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=n.wv.displayName},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>l});var a=s(95155),r=s(12115),n=s(99708),o=s(74466),i=s(53999);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:s,variant:r,size:o,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(l({variant:r,size:o,className:s})),ref:t,...c})});d.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6071,9509,3464,9855,6874,1071,1118,9078,6399,8441,1684,7358],()=>t(20076)),_N_E=e.O()}]);