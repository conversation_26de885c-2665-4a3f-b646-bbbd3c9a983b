"use client"

import { Bell, Globe, Moon, Sun, ShieldAlert, LogOut, User } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { useTranslation } from "@/lib/i18n/client"
import { languages } from "@/lib/i18n/settings"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useRouter } from "next/navigation"
import { toast } from "@/components/ui/use-toast"

interface TopbarProps {
  user?: {
    id: string;
    firstName?: string | null;
    lastName?: string | null;
    email: string;
    role: {
      name: string;
    };
  };
}

export function Topbar({ user }: TopbarProps) {
  const { setTheme } = useTheme()
  const { i18n, t } = useTranslation()
  const router = useRouter()

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
    document.documentElement.dir = lng === "ar" ? "rtl" : "ltr"
  }

  // Get user's display name
  const userName = user ?
    (user.firstName && user.lastName ?
      `${user.firstName} ${user.lastName}` :
      user.email.split('@')[0]) :
    'User'

  // Get user's initials for avatar
  const userInitials = user ?
    (user.firstName && user.lastName ?
      `${user.firstName[0]}${user.lastName[0]}`.toUpperCase() :
      user.email[0].toUpperCase()) :
    'U'

  // Handle logout
  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      })

      if (response.ok) {
        toast({
          title: t('auth.logoutSuccess'),
          description: t('auth.redirectingToLogin'),
        })
        router.push('/sign-in')
      } else {
        throw new Error('Logout failed')
      }
    } catch (error) {
      console.error('Logout error:', error)
      toast({
        variant: 'destructive',
        title: t('auth.logoutFailed'),
        description: error instanceof Error ? error.message : String(error),
      })
    }
  }

  return (
    <div className="h-16 border-b bg-card flex items-center px-6 justify-between">
      <div className="flex-1">
        {user && (
          <div className="hidden md:flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <ShieldAlert className="h-3.5 w-3.5" />
              <span className="capitalize">
                {user.role.name === 'ADMIN' ? 'مدير' :
                 user.role.name === 'AGENT' ? 'وكيل' :
                 user.role.name === 'CLIENT' ? 'عميل' :
                 user.role.name === 'USER' ? 'مستخدم' : user.role.name}
              </span>
            </Badge>
          </div>
        )}
      </div>
      <div className="flex items-center gap-4">
        {user && (
          <div className="hidden md:block text-sm mr-2">
            <span className="text-muted-foreground">أهلاً وسهلاً،</span>{" "}
            <span className="font-medium">{userName}</span>
          </div>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <Globe className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {languages.map((lng) => (
              <DropdownMenuItem key={lng} onClick={() => changeLanguage(lng)} className="cursor-pointer">
                {lng === "en" ? "English" : "العربية"}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setTheme("light")}>فاتح</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("dark")}>داكن</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("system")}>النظام</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant="ghost" size="icon">
          <Bell className="h-5 w-5" />
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-9 w-9 rounded-full">
              <Avatar>
                <AvatarImage src={user?.profileImage || ""} alt={userName} />
                <AvatarFallback>{userInitials}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem className="cursor-default">
              <User className="mr-2 h-4 w-4" />
              <span>{userName}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleLogout} className="cursor-pointer text-destructive">
              <LogOut className="mr-2 h-4 w-4" />
              <span>{t('auth.logout')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
