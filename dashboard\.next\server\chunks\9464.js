"use strict";exports.id=9464,exports.ids=[9464],exports.modules={33287:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let{PrismaClientKnownRequestError:n,PrismaClientUnknownRequestError:i,PrismaClientRustPanicError:s,PrismaClientInitializationError:a,PrismaClientValidationError:o,getPrismaClient:l,sqltag:u,empty:c,join:d,raw:h,skip:f,Decimal:p,Debug:m,objectEnumValues:g,makeStrictEnum:y,Extensions:w,warnOnce:b,defineDmmfProperty:v,Public:E,getRuntime:x,createParam:P}=r(89799),S={};t.Prisma=S,t.$Enums={},S.prismaVersion={client:"6.7.0",engine:"3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"},S.PrismaClientKnownRequestError=n,S.PrismaClientUnknownRequestError=i,S.PrismaClientRustPanicError=s,S.PrismaClientInitializationError=a,S.PrismaClientValidationError=o,S.Decimal=p,S.sql=u,S.empty=c,S.join=d,S.raw=h,S.validator=E.validator,S.getExtensionContext=w.getExtensionContext,S.defineExtension=w.defineExtension,S.DbNull=g.instances.DbNull,S.JsonNull=g.instances.JsonNull,S.AnyNull=g.instances.AnyNull,S.NullTypes={DbNull:g.classes.DbNull,JsonNull:g.classes.JsonNull,AnyNull:g.classes.AnyNull};let _=r(33873);t.Prisma.TransactionIsolationLevel=y({ReadUncommitted:"ReadUncommitted",ReadCommitted:"ReadCommitted",RepeatableRead:"RepeatableRead",Serializable:"Serializable"}),t.Prisma.UserScalarFieldEnum={id:"id",email:"email",password:"password",firstName:"firstName",lastName:"lastName",profileImage:"profileImage",createdAt:"createdAt",updatedAt:"updatedAt",lastActive:"lastActive",role:"role",status:"status"},t.Prisma.PermissionScalarFieldEnum={id:"id",name:"name",description:"description",resource:"resource",action:"action",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.PropertyScalarFieldEnum={id:"id",title:"title",price:"price",location:"location",bedrooms:"bedrooms",bathrooms:"bathrooms",area:"area",description:"description",status:"status",type:"type",images:"images",createdAt:"createdAt",updatedAt:"updatedAt",userId:"userId"},t.Prisma.CampaignScalarFieldEnum={id:"id",name:"name",templateId:"templateId",content:"content",clientTypes:"clientTypes",variables:"variables",status:"status",createdAt:"createdAt",updatedAt:"updatedAt",scheduledAt:"scheduledAt",sentAt:"sentAt",userId:"userId"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.NullableJsonNullValueInput={DbNull:S.DbNull,JsonNull:S.JsonNull},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.Prisma.NullsOrder={first:"first",last:"last"},t.Prisma.JsonNullValueFilter={DbNull:S.DbNull,JsonNull:S.JsonNull,AnyNull:S.AnyNull},t.UserRole=t.$Enums.UserRole={USER:"USER",ADMIN:"ADMIN",AGENT:"AGENT",CLIENT:"CLIENT"},t.UserStatus=t.$Enums.UserStatus={ACTIVE:"ACTIVE",INACTIVE:"INACTIVE",SUSPENDED:"SUSPENDED"},t.PropertyStatus=t.$Enums.PropertyStatus={ACTIVE:"ACTIVE",PENDING:"PENDING",SOLD:"SOLD"},t.CampaignStatus=t.$Enums.CampaignStatus={DRAFT:"DRAFT",SCHEDULED:"SCHEDULED",ACTIVE:"ACTIVE",COMPLETED:"COMPLETED",PAUSED:"PAUSED"},t.Prisma.ModelName={User:"User",Permission:"Permission",Property:"Property",Campaign:"Campaign"};let A={generator:{name:"client",provider:{fromEnvVar:null,value:"prisma-client-js"},output:{value:"E:\\a\\real-estate-dashboard (1)\\lib\\generated\\prisma",fromEnvVar:null},config:{engineType:"library"},binaryTargets:[{fromEnvVar:null,value:"windows",native:!0}],previewFeatures:[],sourceFilePath:"E:\\a\\real-estate-dashboard (1)\\prisma\\schema.prisma",isCustomOutput:!0},relativeEnvPaths:{rootEnvPath:null,schemaEnvPath:"../../../.env"},relativePath:"../../../prisma",clientVersion:"6.7.0",engineVersion:"3cff47a7f5d65c3ea74883f1d736e41d68ce91ed",datasourceNames:["db"],activeProvider:"postgresql",inlineDatasources:{db:{url:{fromEnvVar:"DATABASE_URL",value:"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"}}},inlineSchema:'generator client {\n  provider = "prisma-client-js"\n  output   = "../lib/generated/prisma"\n}\n\ndatasource db {\n  provider = "postgresql"\n  url      = env("DATABASE_URL")\n}\n\nmodel User {\n  id           String       @id @default(cuid())\n  email        String       @unique\n  password     String\n  firstName    String?\n  lastName     String?\n  profileImage String?\n  createdAt    DateTime     @default(now())\n  updatedAt    DateTime     @updatedAt\n  lastActive   DateTime     @default(now())\n  role         UserRole     @default(USER)\n  status       UserStatus   @default(ACTIVE)\n  campaigns    Campaign[]\n  properties   Property[]\n  permissions  Permission[] @relation("UserPermissions")\n}\n\nmodel Permission {\n  id          String   @id @default(cuid())\n  name        String   @unique\n  description String?\n  resource    String\n  action      String\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n  users       User[]   @relation("UserPermissions")\n}\n\nmodel Property {\n  id          String         @id @default(cuid())\n  title       String\n  price       Float\n  location    String\n  bedrooms    Int\n  bathrooms   Int\n  area        Float\n  description String?\n  status      PropertyStatus @default(ACTIVE)\n  type        String\n  images      String[]\n  createdAt   DateTime       @default(now())\n  updatedAt   DateTime       @updatedAt\n  userId      String\n  createdBy   User           @relation(fields: [userId], references: [id])\n}\n\nmodel Campaign {\n  id          String         @id @default(cuid())\n  name        String\n  templateId  String?\n  content     String?\n  clientTypes String[]\n  variables   Json?\n  status      CampaignStatus @default(DRAFT)\n  createdAt   DateTime       @default(now())\n  updatedAt   DateTime       @updatedAt\n  scheduledAt DateTime?\n  sentAt      DateTime?\n  userId      String\n  createdBy   User           @relation(fields: [userId], references: [id])\n}\n\nenum UserRole {\n  USER\n  ADMIN\n  AGENT\n  CLIENT\n}\n\nenum UserStatus {\n  ACTIVE\n  INACTIVE\n  SUSPENDED\n}\n\nenum PropertyStatus {\n  ACTIVE\n  PENDING\n  SOLD\n}\n\nenum CampaignStatus {\n  DRAFT\n  SCHEDULED\n  ACTIVE\n  COMPLETED\n  PAUSED\n}\n',inlineSchemaHash:"8bcd2e367b7fd28178cc6e2a6a7213fb93f62cad57f5aa010bcbd1860f5f5bfe",copyEngine:!0},N=r(29021);if(A.dirname=__dirname,!N.existsSync(_.join(__dirname,"schema.prisma"))){let e=["lib/generated/prisma","generated/prisma"],t=e.find(e=>N.existsSync(_.join(process.cwd(),e,"schema.prisma")))??e[0];A.dirname=_.join(process.cwd(),t),A.isBundled=!0}A.runtimeDataModel=JSON.parse('{"models":{"User":{"dbName":null,"schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"email","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"password","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"firstName","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"lastName","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"profileImage","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"lastActive","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"role","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"UserRole","nativeType":null,"default":"USER","isGenerated":false,"isUpdatedAt":false},{"name":"status","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"UserStatus","nativeType":null,"default":"ACTIVE","isGenerated":false,"isUpdatedAt":false},{"name":"campaigns","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Campaign","nativeType":null,"relationName":"CampaignToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"properties","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Property","nativeType":null,"relationName":"PropertyToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"permissions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Permission","nativeType":null,"relationName":"UserPermissions","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Permission":{"dbName":null,"schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"name","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"resource","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"action","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"users","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"UserPermissions","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Property":{"dbName":null,"schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"title","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"price","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Float","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"location","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"bedrooms","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"bathrooms","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"area","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Float","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"status","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"PropertyStatus","nativeType":null,"default":"ACTIVE","isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"images","kind":"scalar","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdBy","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"PropertyToUser","relationFromFields":["userId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Campaign":{"dbName":null,"schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"name","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"templateId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"content","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"clientTypes","kind":"scalar","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"variables","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"status","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"CampaignStatus","nativeType":null,"default":"DRAFT","isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"scheduledAt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"sentAt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdBy","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"CampaignToUser","relationFromFields":["userId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false}},"enums":{"UserRole":{"values":[{"name":"USER","dbName":null},{"name":"ADMIN","dbName":null},{"name":"AGENT","dbName":null},{"name":"CLIENT","dbName":null}],"dbName":null},"UserStatus":{"values":[{"name":"ACTIVE","dbName":null},{"name":"INACTIVE","dbName":null},{"name":"SUSPENDED","dbName":null}],"dbName":null},"PropertyStatus":{"values":[{"name":"ACTIVE","dbName":null},{"name":"PENDING","dbName":null},{"name":"SOLD","dbName":null}],"dbName":null},"CampaignStatus":{"values":[{"name":"DRAFT","dbName":null},{"name":"SCHEDULED","dbName":null},{"name":"ACTIVE","dbName":null},{"name":"COMPLETED","dbName":null},{"name":"PAUSED","dbName":null}],"dbName":null}},"types":{}}'),v(t.Prisma,A.runtimeDataModel),A.engineWasm=void 0,A.compilerWasm=void 0;let{warnEnvConflicts:T}=r(89799);T({rootEnvPath:A.relativeEnvPaths.rootEnvPath&&_.resolve(A.dirname,A.relativeEnvPaths.rootEnvPath),schemaEnvPath:A.relativeEnvPaths.schemaEnvPath&&_.resolve(A.dirname,A.relativeEnvPaths.schemaEnvPath)}),t.PrismaClient=l(A),Object.assign(t,S),_.join(__dirname,"query_engine-windows.dll.node"),_.join(process.cwd(),"lib/generated/prisma/query_engine-windows.dll.node"),_.join(__dirname,"schema.prisma"),_.join(process.cwd(),"lib/generated/prisma/schema.prisma")},79464:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(33287);let i=global.prisma||new n.PrismaClient},89799:(e,t,r)=>{var n=Object.create,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,u=(e,t)=>()=>(e&&(t=e(e=0)),t),c=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),d=(e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})},h=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of a(t))l.call(e,o)||o===r||i(e,o,{get:()=>t[o],enumerable:!(n=s(t,o))||n.enumerable});return e},f=(e,t,r)=>(r=null!=e?n(o(e)):{},h(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)),p=c((e,t)=>{t.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return -1!==n&&(-1===i||n<i)}}),m=c((e,t)=>{var n,i=r(48161),s=r(7066),a=p(),{env:o}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function u(e,t){if(0===n)return 0;if(a("color=16m")||a("color=full")||a("color=truecolor"))return 3;if(a("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let r=n||0;if("dumb"===o.TERM)return r;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in o)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in o)||"codeship"===o.CI_NAME?1:r;if("TEAMCITY_VERSION"in o)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION);if("truecolor"===o.COLORTERM)return 3;if("TERM_PROGRAM"in o){let e=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(o.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)||"COLORTERM"in o?1:r}a("no-color")||a("no-colors")||a("color=false")||a("color=never")?n=0:(a("color")||a("colors")||a("color=true")||a("color=always"))&&(n=1),"FORCE_COLOR"in o&&(n="true"===o.FORCE_COLOR?1:"false"===o.FORCE_COLOR?0:0===o.FORCE_COLOR.length?1:Math.min(parseInt(o.FORCE_COLOR,10),3)),t.exports={supportsColor:function(e){return l(u(e,e&&e.isTTY))},stdout:l(u(!0,s.isatty(1))),stderr:l(u(!0,s.isatty(2)))}}),g=c((e,t)=>{var r=m(),n=p();function i(e){if(/^\d{3,4}$/.test(e)){let t=/(\d{1,2})(\d{2})/.exec(e)||[];return{major:0,minor:parseInt(t[1],10),patch:parseInt(t[2],10)}}let t=(e||"").split(".").map(e=>parseInt(e,10));return{major:t[0],minor:t[1],patch:t[2]}}function s(e){let{CI:t,FORCE_HYPERLINK:s,NETLIFY:a,TEAMCITY_VERSION:o,TERM_PROGRAM:l,TERM_PROGRAM_VERSION:u,VTE_VERSION:c,TERM:d}=process.env;if(s)return!(s.length>0&&0===parseInt(s,10));if(n("no-hyperlink")||n("no-hyperlinks")||n("hyperlink=false")||n("hyperlink=never"))return!1;if(n("hyperlink=true")||n("hyperlink=always")||a)return!0;if(!r.supportsColor(e)||e&&!e.isTTY)return!1;if("WT_SESSION"in process.env)return!0;if("win32"===process.platform||t||o)return!1;if(l){let e=i(u||"");switch(l){case"iTerm.app":return 3===e.major?e.minor>=1:e.major>3;case"WezTerm":return e.major>=0x1343cac;case"vscode":return e.major>1||1===e.major&&e.minor>=72;case"ghostty":return!0}}if(c){if("0.50.0"===c)return!1;let e=i(c);return e.major>0||e.minor>=50}return"alacritty"===d}t.exports={supportsHyperlink:s,stdout:s(process.stdout),stderr:s(process.stderr)}}),y=c((e,t)=>{t.exports={name:"@prisma/internals",version:"6.7.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.4.7",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.7.0-36.3cff47a7f5d65c3ea74883f1d736e41d68ce91ed","@prisma/schema-engine-wasm":"6.7.0-36.3cff47a7f5d65c3ea74883f1d736e41d68ce91ed","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}}),w=c((e,t)=>{t.exports={name:"@prisma/engines-version",version:"6.7.0-36.3cff47a7f5d65c3ea74883f1d736e41d68ce91ed",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}}),b=c(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.enginesVersion=void 0,e.enginesVersion=w().prisma.enginesVersion}),v=c((e,t)=>{t.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((e,t)=>Math.min(e,t.length),1/0):0}}),E=c((e,t)=>{t.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},"string"!=typeof e)throw TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if("number"!=typeof t)throw TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if("string"!=typeof r.indent)throw TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(0===t)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}}),x=c((e,t)=>{t.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")}),P=c((e,t)=>{var r=x();t.exports=e=>"string"==typeof e?e.replace(r(),""):e}),S=c((e,t)=>{t.exports={name:"dotenv",version:"16.4.7",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}}),_=c((e,t)=>{var n=r(73024),i=r(76760),s=r(48161),a=r(77598),o=S().version,l=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function u(e){console.log(`[dotenv@${o}][DEBUG] ${e}`)}function c(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function d(e){let t=null;if(e&&e.path&&e.path.length>0){if(Array.isArray(e.path))for(let r of e.path)n.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`}else t=i.resolve(process.cwd(),".env.vault");return n.existsSync(t)?t:null}function h(e){return"~"===e[0]?i.join(s.homedir(),e.slice(1)):e}var f={configDotenv:function(e){let t=i.resolve(process.cwd(),".env"),r="utf8",s=!!(e&&e.debug);e&&e.encoding?r=e.encoding:s&&u("No encoding is specified. UTF-8 is used by default");let a=[t];if(e&&e.path){if(Array.isArray(e.path))for(let t of(a=[],e.path))a.push(h(t));else a=[h(e.path)]}let o,l={};for(let t of a)try{let i=f.parse(n.readFileSync(t,{encoding:r}));f.populate(l,i,e)}catch(e){s&&u(`Failed to load ${t} ${e.message}`),o=e}let c=process.env;return e&&null!=e.processEnv&&(c=e.processEnv),f.populate(c,l,e),o?{parsed:l,error:o}:{parsed:l}},_configVault:function(e){console.log(`[dotenv@${o}][INFO] Loading env from encrypted .env.vault`);let t=f._parseVault(e),r=process.env;return e&&null!=e.processEnv&&(r=e.processEnv),f.populate(r,t,e),{parsed:t}},_parseVault:function(e){let t=d(e),r=f.configDotenv({path:t});if(!r.parsed){let e=Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw e.code="MISSING_DATA",e}let n=c(e).split(","),i=n.length,s;for(let e=0;e<i;e++)try{let t=n[e].trim(),i=function(e,t){let r;try{r=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let n=r.password;if(!n){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let i=r.searchParams.get("environment");if(!i){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let s=`DOTENV_VAULT_${i.toUpperCase()}`,a=e.parsed[s];if(!a){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:a,key:n}}(r,t);s=f.decrypt(i.ciphertext,i.key);break}catch(t){if(e+1>=i)throw t}return f.parse(s)},config:function(e){var t;if(0===c(e).length)return f.configDotenv(e);let r=d(e);return r?f._configVault(e):(t=`You set DOTENV_KEY but you are missing a .env.vault file at ${r}. Did you forget to build it?`,console.log(`[dotenv@${o}][WARN] ${t}`),f.configDotenv(e))},decrypt:function(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),s=n.subarray(-16);n=n.subarray(12,-16);try{let e=a.createDecipheriv("aes-256-gcm",r,i);return e.setAuthTag(s),`${e.update(n)}${e.final()}`}catch(n){let e=n instanceof RangeError,t="Invalid key length"===n.message,r="Unsupported state or unable to authenticate data"===n.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(r){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw n}},parse:function(e){let t,r={},n=e.toString();for(n=n.replace(/\r\n?/mg,`
`);null!=(t=l.exec(n));){let e=t[1],n=t[2]||"",i=(n=n.trim())[0];n=n.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===i&&(n=(n=n.replace(/\\n/g,`
`)).replace(/\\r/g,"\r")),r[e]=n}return r},populate:function(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let r of Object.keys(t))Object.prototype.hasOwnProperty.call(e,r)?(!0===i&&(e[r]=t[r]),n&&u(!0===i?`"${r}" is already defined and WAS overwritten`:`"${r}" is already defined and was NOT overwritten`)):e[r]=t[r]}};t.exports.configDotenv=f.configDotenv,t.exports._configVault=f._configVault,t.exports._parseVault=f._parseVault,t.exports.config=f.config,t.exports.decrypt=f.decrypt,t.exports.parse=f.parse,t.exports.populate=f.populate,t.exports=f}),A=c((e,t)=>{t.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`);for(let t of["body","title","labels","template","milestone","assignee","projects"]){let n=e[t];if(void 0!==n){if("labels"===t||"projects"===t){if(!Array.isArray(n))throw TypeError(`The \`${t}\` option should be an array`);n=n.join(",")}r.searchParams.set(t,n)}}return r.toString()},t.exports.default=t.exports}),N=c((e,t)=>{t.exports=function(){function e(e,t,r,n,i){return e<t||r<t?e>r?r+1:e+1:n===i?t:t+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,s=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(s-1);)i--,s--;for(var a=0;a<i&&t.charCodeAt(a)===r.charCodeAt(a);)a++;if(i-=a,s-=a,0===i||s<3)return s;var o,l,u,c,d,h,f,p,m,g,y,w,b=0,v=[];for(o=0;o<i;o++)v.push(o+1),v.push(t.charCodeAt(a+o));for(var E=v.length-1;b<s-3;)for(m=r.charCodeAt(a+(l=b)),g=r.charCodeAt(a+(u=b+1)),y=r.charCodeAt(a+(c=b+2)),w=r.charCodeAt(a+(d=b+3)),h=b+=4,o=0;o<E;o+=2)l=e(f=v[o],l,u,m,p=v[o+1]),u=e(l,u,c,g,p),c=e(u,c,d,y,p),h=e(c,d,h,w,p),v[o]=h,d=c,c=u,u=l,l=f;for(;b<s;)for(m=r.charCodeAt(a+(l=b)),h=++b,o=0;o<E;o+=2)f=v[o],v[o]=h=e(f,l,h,m,v[o+1]),l=f;return h}}()}),T=u(()=>{}),$=u(()=>{}),R={};d(R,{DMMF:()=>nz,Debug:()=>ex,Decimal:()=>nU,Extensions:()=>O,MetricsClient:()=>i3,PrismaClientInitializationError:()=>rp,PrismaClientKnownRequestError:()=>rm,PrismaClientRustPanicError:()=>rg,PrismaClientUnknownRequestError:()=>ry,PrismaClientValidationError:()=>rw,Public:()=>I,Sql:()=>so,createParam:()=>iW,defineDmmfProperty:()=>i7,deserializeJsonResponse:()=>nL,deserializeRawResult:()=>oI,dmmfToRuntimeDataModel:()=>nW,empty:()=>sc,getPrismaClient:()=>oY,getRuntime:()=>s8,join:()=>sl,makeStrictEnum:()=>oX,makeTypedQueryFactory:()=>se,objectEnumValues:()=>iD,raw:()=>su,serializeJsonQuery:()=>iX,skip:()=>iQ,sqltag:()=>sd,warnEnvConflicts:()=>o0,warnOnce:()=>rf}),e.exports=h(i({},"__esModule",{value:!0}),R);var O={};function k(e){return"function"==typeof e?e:t=>t.$extends(e)}function D(e){return e}d(O,{defineExtension:()=>k,getExtensionContext:()=>D});var I={};function q(...e){return e=>e}d(I,{validator:()=>q});var C={};d(C,{$:()=>L,bgBlack:()=>el,bgBlue:()=>eh,bgCyan:()=>ep,bgGreen:()=>ec,bgMagenta:()=>ef,bgRed:()=>eu,bgWhite:()=>em,bgYellow:()=>ed,black:()=>Z,blue:()=>er,bold:()=>H,cyan:()=>ei,dim:()=>W,gray:()=>ea,green:()=>ee,grey:()=>eo,hidden:()=>Y,inverse:()=>Q,italic:()=>J,magenta:()=>en,red:()=>X,reset:()=>B,strikethrough:()=>z,underline:()=>K,white:()=>es,yellow:()=>et});var F,V,j,M,U=!0;"u">typeof process&&({FORCE_COLOR:F,NODE_DISABLE_COLORS:V,NO_COLOR:j,TERM:M}=process.env||{},U=process.stdout&&process.stdout.isTTY);var L={enabled:!V&&null==j&&"dumb"!==M&&(null!=F&&"0"!==F||U)};function G(e,t){let r=RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1b[${e}m`,i=`\x1b[${t}m`;return function(e){return L.enabled&&null!=e?n+(~(""+e).indexOf(i)?e.replace(r,i+n):e)+i:e}}var B=G(0,0),H=G(1,22),W=G(2,22),J=G(3,23),K=G(4,24),Q=G(7,27),Y=G(8,28),z=G(9,29),Z=G(30,39),X=G(31,39),ee=G(32,39),et=G(33,39),er=G(34,39),en=G(35,39),ei=G(36,39),es=G(37,39),ea=G(90,39),eo=G(90,39),el=G(40,49),eu=G(41,49),ec=G(42,49),ed=G(43,49),eh=G(44,49),ef=G(45,49),ep=G(46,49),em=G(47,49),eg=["green","yellow","blue","magenta","cyan","red"],ey=[],ew=Date.now(),eb=0,ev="u">typeof process?process.env:{};globalThis.DEBUG??=ev.DEBUG??"",globalThis.DEBUG_COLORS??=!ev.DEBUG_COLORS||"true"===ev.DEBUG_COLORS;var eE={enable(e){"string"==typeof e&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(e=>e.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(t=>""!==t&&"-"!==t[0]&&e.match(RegExp(t.split("*").join(".*")+"$"))),n=t.some(t=>""!==t&&"-"===t[0]&&e.match(RegExp(t.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}},ex=new Proxy(function(e){let t={color:eg[eb++%eg.length],enabled:eE.enabled(e),namespace:e,log:eE.log,extend:()=>{}};return new Proxy((...e)=>{let{enabled:r,namespace:n,color:i,log:s}=t;if(0!==e.length&&ey.push([n,...e]),ey.length>100&&ey.shift(),eE.enabled(n)||r){let t=e.map(e=>"string"==typeof e?e:function(e,t=2){let r=new Set;return JSON.stringify(e,(e,t)=>{if("object"==typeof t&&null!==t){if(r.has(t))return"[Circular *]";r.add(t)}else if("bigint"==typeof t)return t.toString();return t},t)}(e)),r=`+${Date.now()-ew}ms`;ew=Date.now(),globalThis.DEBUG_COLORS?s(C[i](H(n)),...t,C[i](r)):s(n,...t,r)}},{get:(e,r)=>t[r],set:(e,r,n)=>t[r]=n})},{get:(e,t)=>eE[t],set:(e,t,r)=>eE[t]=r}),eP=f(r(73024)),eS="libquery_engine",e_=f(r(31421)),eA=f(r(51455)),eN=f(r(48161)),eT=Symbol.for("@ts-pattern/matcher"),e$=Symbol.for("@ts-pattern/isVariadic"),eR="@ts-pattern/anonymous-select-key",eO=e=>!!(e&&"object"==typeof e),ek=e=>e&&!!e[eT],eD=(e,t,r)=>{if(ek(e)){let{matched:n,selections:i}=e[eT]().match(t);return n&&i&&Object.keys(i).forEach(e=>r(e,i[e])),n}if(eO(e)){if(!eO(t))return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let n=[],i=[],s=[];for(let t of e.keys()){let r=e[t];ek(r)&&r[e$]?s.push(r):s.length?i.push(r):n.push(r)}if(s.length){if(s.length>1)throw Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<n.length+i.length)return!1;let e=t.slice(0,n.length),a=0===i.length?[]:t.slice(-i.length),o=t.slice(n.length,0===i.length?1/0:-i.length);return n.every((t,n)=>eD(t,e[n],r))&&i.every((e,t)=>eD(e,a[t],r))&&(0===s.length||eD(s[0],o,r))}return e.length===t.length&&e.every((e,n)=>eD(e,t[n],r))}return Reflect.ownKeys(e).every(n=>{let i=e[n];return(n in t||ek(i)&&"optional"===i[eT]().matcherType)&&eD(i,t[n],r)})}return Object.is(t,e)},eI=e=>{var t,r,n;return eO(e)?ek(e)?null!=(t=null==(r=(n=e[eT]()).getSelectionKeys)?void 0:r.call(n))?t:[]:Array.isArray(e)?eq(e,eI):eq(Object.values(e),eI):[]},eq=(e,t)=>e.reduce((e,r)=>e.concat(t(r)),[]);function eC(e){return Object.assign(e,{optional:()=>{var t;return t=e,eC({[eT]:()=>({match:e=>{let r={},n=(e,t)=>{r[e]=t};return void 0===e?(eI(t).forEach(e=>n(e,void 0)),{matched:!0,selections:r}):{matched:eD(t,e,n),selections:r}},getSelectionKeys:()=>eI(t),matcherType:"optional"})})},and:t=>eF(e,t),or:t=>(function(...e){return eC({[eT]:()=>({match:t=>{let r={},n=(e,t)=>{r[e]=t};return eq(e,eI).forEach(e=>n(e,void 0)),{matched:e.some(e=>eD(e,t,n)),selections:r}},getSelectionKeys:()=>eq(e,eI),matcherType:"or"})})})(e,t),select:t=>void 0===t?ej(e):ej(t,e)})}function eF(...e){return eC({[eT]:()=>({match:t=>{let r={},n=(e,t)=>{r[e]=t};return{matched:e.every(e=>eD(e,t,n)),selections:r}},getSelectionKeys:()=>eq(e,eI),matcherType:"and"})})}function eV(e){return{[eT]:()=>({match:t=>({matched:!!e(t)})})}}function ej(...e){let t="string"==typeof e[0]?e[0]:void 0,r=2===e.length?e[1]:"string"==typeof e[0]?void 0:e[0];return eC({[eT]:()=>({match:e=>{let n={[t??eR]:e};return{matched:void 0===r||eD(r,e,(e,t)=>{n[e]=t}),selections:n}},getSelectionKeys:()=>[t??eR].concat(void 0===r?[]:eI(r))})})}function eM(e){return"number"==typeof e}function eU(e){return"string"==typeof e}function eL(e){return"bigint"==typeof e}eC(eV(function(e){return!0}));var eG=e=>Object.assign(eC(e),{startsWith:t=>eG(eF(e,eV(e=>eU(e)&&e.startsWith(t)))),endsWith:t=>eG(eF(e,eV(e=>eU(e)&&e.endsWith(t)))),minLength:t=>eG(eF(e,eV(e=>eU(e)&&e.length>=t))),length:t=>eG(eF(e,eV(e=>eU(e)&&e.length===t))),maxLength:t=>eG(eF(e,eV(e=>eU(e)&&e.length<=t))),includes:t=>eG(eF(e,eV(e=>eU(e)&&e.includes(t)))),regex:t=>eG(eF(e,eV(e=>eU(e)&&!!e.match(t))))}),eB=(eG(eV(eU)),e=>Object.assign(eC(e),{between:(t,r)=>eB(eF(e,eV(e=>eM(e)&&t<=e&&r>=e))),lt:t=>eB(eF(e,eV(e=>eM(e)&&e<t))),gt:t=>eB(eF(e,eV(e=>eM(e)&&e>t))),lte:t=>eB(eF(e,eV(e=>eM(e)&&e<=t))),gte:t=>eB(eF(e,eV(e=>eM(e)&&e>=t))),int:()=>eB(eF(e,eV(e=>eM(e)&&Number.isInteger(e)))),finite:()=>eB(eF(e,eV(e=>eM(e)&&Number.isFinite(e)))),positive:()=>eB(eF(e,eV(e=>eM(e)&&e>0))),negative:()=>eB(eF(e,eV(e=>eM(e)&&e<0)))})),eH=(eB(eV(eM)),e=>Object.assign(eC(e),{between:(t,r)=>eH(eF(e,eV(e=>eL(e)&&t<=e&&r>=e))),lt:t=>eH(eF(e,eV(e=>eL(e)&&e<t))),gt:t=>eH(eF(e,eV(e=>eL(e)&&e>t))),lte:t=>eH(eF(e,eV(e=>eL(e)&&e<=t))),gte:t=>eH(eF(e,eV(e=>eL(e)&&e>=t))),positive:()=>eH(eF(e,eV(e=>eL(e)&&e>0))),negative:()=>eH(eF(e,eV(e=>eL(e)&&e<0)))}));eH(eV(eL)),eC(eV(function(e){return"boolean"==typeof e})),eC(eV(function(e){return"symbol"==typeof e})),eC(eV(function(e){return null==e})),eC(eV(function(e){return null!=e}));var eW=class extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch{t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}},eJ={matched:!1,value:void 0};function eK(e){return new eQ(e,eJ)}var eQ=class e{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...t){if(this.state.matched)return this;let r=t[t.length-1],n=[t[0]],i;3===t.length&&"function"==typeof t[1]?i=t[1]:t.length>2&&n.push(...t.slice(1,t.length-1));let s=!1,a={},o=(e,t)=>{s=!0,a[e]=t},l=n.some(e=>eD(e,this.input,o))&&(!i||i(this.input))?{matched:!0,value:r(s?eR in a?a[eR]:a:this.input,this.input)}:eJ;return new e(this.input,l)}when(t,r){if(this.state.matched)return this;let n=!!t(this.input);return new e(this.input,n?{matched:!0,value:r(this.input,this.input)}:eJ)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new eW(this.input)}run(){return this.exhaustive()}returnType(){return this}},eY=r(57975),ez={warn:et("prisma:warn")},eZ={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function eX(e,...t){eZ.warn()&&console.warn(`${ez.warn} ${e}`,...t)}var e0=(0,eY.promisify)(e_.default.exec),e1=ex("prisma:get-platform"),e2=["1.0.x","1.1.x","3.0.x"];async function e4(){let e=eN.default.platform(),t=process.arch;if("freebsd"===e){let e=await ts("freebsd-version");if(e&&e.trim().length>0){let r=/^(\d+)\.?/.exec(e);if(r)return{platform:"freebsd",targetDistro:`freebsd${r[1]}`,arch:t}}}if("linux"!==e)return{platform:e,arch:t};let r=await e3(),n=await ta(),i=eK({arch:t,archFromUname:n,familyDistro:r.familyDistro}).with({familyDistro:"musl"},()=>(e1('Trying platform-specific paths for "alpine"'),["/lib","/usr/lib"])).with({familyDistro:"debian"},({archFromUname:e})=>(e1('Trying platform-specific paths for "debian" (and "ubuntu")'),[`/usr/lib/${e}-linux-gnu`,`/lib/${e}-linux-gnu`])).with({familyDistro:"rhel"},()=>(e1('Trying platform-specific paths for "rhel"'),["/lib64","/usr/lib64"])).otherwise(({familyDistro:e,arch:t,archFromUname:r})=>(e1(`Don't know any platform-specific paths for "${e}" on ${t} (${r})`),[])),{libssl:s}=await e9(i);return{platform:"linux",libssl:s,arch:t,archFromUname:n,...r}}async function e3(){try{var e;let t,r,n,i;return e=await eA.default.readFile("/etc/os-release",{encoding:"utf-8"}),r=(t=/^ID="?([^"\n]*)"?$/im.exec(e))&&t[1]&&t[1].toLowerCase()||"",n=/^ID_LIKE="?([^"\n]*)"?$/im.exec(e),i=eK({id:r,idLike:n&&n[1]&&n[1].toLowerCase()||""}).with({id:"alpine"},({id:e})=>({targetDistro:"musl",familyDistro:e,originalDistro:e})).with({id:"raspbian"},({id:e})=>({targetDistro:"arm",familyDistro:"debian",originalDistro:e})).with({id:"nixos"},({id:e})=>({targetDistro:"nixos",originalDistro:e,familyDistro:"nixos"})).with({id:"debian"},{id:"ubuntu"},({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).with({id:"rhel"},{id:"centos"},{id:"fedora"},({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).when(({idLike:e})=>e.includes("debian")||e.includes("ubuntu"),({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).when(({idLike:e})=>"arch"===r||e.includes("arch"),({id:e})=>({targetDistro:"debian",familyDistro:"arch",originalDistro:e})).when(({idLike:e})=>e.includes("centos")||e.includes("fedora")||e.includes("rhel")||e.includes("suse"),({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).otherwise(({id:e})=>({targetDistro:void 0,familyDistro:void 0,originalDistro:e})),e1(`Found distro info:
${JSON.stringify(i,null,2)}`),i}catch{return{targetDistro:void 0,familyDistro:void 0,originalDistro:void 0}}}function e7(e){let t=/libssl\.so\.(\d)(\.\d)?/.exec(e);if(t)return e6(`${t[1]}${t[2]??".0"}.x`)}function e6(e){let t=(()=>{if(to(e))return e;let t=e.split(".");return t[1]="0",t.join(".")})();if(e2.includes(t))return t}async function e9(e){let t=await e5(e);if(t){e1(`Found libssl.so file using platform-specific paths: ${t}`);let e=e7(t);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"libssl-specific-path"}}e1('Falling back to "ldconfig" and other generic paths');let r=await ts('ldconfig -p | sed "s/.*=>s*//" | sed "s|.*/||" | grep libssl | sort | grep -v "libssl.so.0"');if(r||(r=await e5(["/lib64","/usr/lib64","/lib","/usr/lib"])),r){e1(`Found libssl.so file using "ldconfig" or other generic paths: ${r}`);let e=e7(r);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"ldconfig"}}let n=await ts("openssl version -v");if(n){e1(`Found openssl binary with version: ${n}`);let e=function(e){let t=/^OpenSSL\s(\d+\.\d+)\.\d+/.exec(e);if(t)return e6(`${t[1]}.x`)}(n);if(e1(`The parsed openssl version is: ${e}`),e)return{libssl:e,strategy:"openssl-binary"}}return e1("Couldn't find any version of libssl or OpenSSL in the system"),{}}async function e5(e){for(let t of e){let e=await e8(t);if(e)return e}}async function e8(e){try{return(await eA.default.readdir(e)).find(e=>e.startsWith("libssl.so.")&&!e.startsWith("libssl.so.0"))}catch(e){if("ENOENT"===e.code)return;throw e}}async function te(){let{binaryTarget:e}=await tn();return e}async function tt(){let{memoized:e,...t}=await tn();return t}var tr={};async function tn(){if(void 0!==tr.binaryTarget)return Promise.resolve({...tr,memoized:!0});let e=await e4(),t=function(e){let{platform:t,arch:r,archFromUname:n,libssl:i,targetDistro:s,familyDistro:a,originalDistro:o}=e;"linux"!==t||["x64","arm64"].includes(r)||eX(`Prisma only officially supports Linux on amd64 (x86_64) and arm64 (aarch64) system architectures (detected "${r}" instead). If you are using your own custom Prisma engines, you can ignore this warning, as long as you've compiled the engines for your system architecture "${n}".`);let l="1.1.x";if("linux"===t&&void 0===i){let e=eK({familyDistro:a}).with({familyDistro:"debian"},()=>"Please manually install OpenSSL via `apt-get update -y && apt-get install -y openssl` and try installing Prisma again. If you're running Prisma on Docker, add this command to your Dockerfile, or switch to an image that already has OpenSSL installed.").otherwise(()=>"Please manually install OpenSSL and try installing Prisma again.");eX(`Prisma failed to detect the libssl/openssl version to use, and may not work as expected. Defaulting to "openssl-${l}".
${e}`)}let u="debian";if("linux"===t&&void 0===s&&e1(`Distro is "${o}". Falling back to Prisma engines built for "${u}".`),"darwin"===t&&"arm64"===r)return"darwin-arm64";if("darwin"===t)return"darwin";if("win32"===t)return"windows";if("freebsd"===t)return s;if("openbsd"===t)return"openbsd";if("netbsd"===t)return"netbsd";if("linux"===t&&"nixos"===s)return"linux-nixos";if("linux"===t&&"arm64"===r)return`${"musl"===s?"linux-musl-arm64":"linux-arm64"}-openssl-${i||l}`;if("linux"===t&&"arm"===r)return`linux-arm-openssl-${i||l}`;if("linux"===t&&"musl"===s){let e="linux-musl";return!i||to(i)?e:`${e}-openssl-${i}`}return"linux"===t&&s&&i?`${s}-openssl-${i}`:("linux"!==t&&eX(`Prisma detected unknown OS "${t}" and may not work as expected. Defaulting to "linux".`),i?`${u}-openssl-${i}`:s?`${s}-openssl-${l}`:`${u}-openssl-${l}`)}(e);return{...tr={...e,binaryTarget:t},memoized:!1}}async function ti(e){try{return await e()}catch{return}}function ts(e){return ti(async()=>{let t=await e0(e);return e1(`Command "${e}" successfully returned "${t.stdout}"`),t.stdout})}async function ta(){return"function"==typeof eN.default.machine?eN.default.machine():(await ts("uname -m"))?.trim()}function to(e){return e.startsWith("1.")}var tl={};d(tl,{beep:()=>tL,clearScreen:()=>tV,clearTerminal:()=>tj,cursorBackward:()=>tv,cursorDown:()=>tw,cursorForward:()=>tb,cursorGetPosition:()=>tS,cursorHide:()=>tN,cursorLeft:()=>tE,cursorMove:()=>tg,cursorNextLine:()=>t_,cursorPrevLine:()=>tA,cursorRestorePosition:()=>tP,cursorSavePosition:()=>tx,cursorShow:()=>tT,cursorTo:()=>tm,cursorUp:()=>ty,enterAlternativeScreen:()=>tM,eraseDown:()=>tD,eraseEndLine:()=>tR,eraseLine:()=>tk,eraseLines:()=>t$,eraseScreen:()=>tq,eraseStartLine:()=>tO,eraseUp:()=>tI,exitAlternativeScreen:()=>tU,iTerm:()=>tH,image:()=>tB,link:()=>tG,scrollDown:()=>tF,scrollUp:()=>tC});var tu=f(r(1708),1),tc=globalThis.window?.document!==void 0,td=(globalThis.process?.versions?.node,globalThis.process?.versions?.bun,globalThis.Deno?.version?.deno,globalThis.process?.versions?.electron,globalThis.navigator?.userAgent?.includes("jsdom"),"u">typeof WorkerGlobalScope&&WorkerGlobalScope,"u">typeof DedicatedWorkerGlobalScope&&DedicatedWorkerGlobalScope,"u">typeof SharedWorkerGlobalScope&&SharedWorkerGlobalScope,"u">typeof ServiceWorkerGlobalScope&&ServiceWorkerGlobalScope,globalThis.navigator?.userAgentData?.platform);"macOS"===td||globalThis.navigator?.platform==="MacIntel"||globalThis.navigator?.userAgent?.includes(" Mac ")===!0||globalThis.process?.platform,"Windows"===td||globalThis.navigator?.platform==="Win32"||globalThis.process?.platform,"Linux"===td||globalThis.navigator?.platform?.startsWith("Linux")===!0||globalThis.navigator?.userAgent?.includes(" Linux ")===!0||globalThis.process?.platform,"iOS"===td||globalThis.navigator?.platform==="MacIntel"&&globalThis.navigator?.maxTouchPoints>1||/iPad|iPhone|iPod/.test(globalThis.navigator?.platform),"Android"===td||globalThis.navigator?.platform==="Android"||globalThis.navigator?.userAgent?.includes(" Android ")===!0||globalThis.process?.platform;var th=!tc&&"Apple_Terminal"===tu.default.env.TERM_PROGRAM,tf=!tc&&"win32"===tu.default.platform,tp=tc?()=>{throw Error("`process.cwd()` only works in Node.js, not the browser.")}:tu.default.cwd,tm=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");return"number"!=typeof t?"\x1b["+(e+1)+"G":"\x1b["+(t+1)+";"+(e+1)+"H"},tg=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");let r="";return e<0?r+="\x1b["+-e+"D":e>0&&(r+="\x1b["+e+"C"),t<0?r+="\x1b["+-t+"A":t>0&&(r+="\x1b["+t+"B"),r},ty=(e=1)=>"\x1b["+e+"A",tw=(e=1)=>"\x1b["+e+"B",tb=(e=1)=>"\x1b["+e+"C",tv=(e=1)=>"\x1b["+e+"D",tE="\x1b[G",tx=th?"\x1b7":"\x1b[s",tP=th?"\x1b8":"\x1b[u",tS="\x1b[6n",t_="\x1b[E",tA="\x1b[F",tN="\x1b[?25l",tT="\x1b[?25h",t$=e=>{let t="";for(let r=0;r<e;r++)t+=tk+(r<e-1?ty():"");return e&&(t+=tE),t},tR="\x1b[K",tO="\x1b[1K",tk="\x1b[2K",tD="\x1b[J",tI="\x1b[1J",tq="\x1b[2J",tC="\x1b[S",tF="\x1b[T",tV="\x1bc",tj=tf?`${tq}\x1b[0f`:`${tq}\x1b[3J\x1b[H`,tM="\x1b[?1049h",tU="\x1b[?1049l",tL="\x07",tG=(e,t)=>["\x1b]","8",";",";",t,"\x07",e,"\x1b]","8",";",";","\x07"].join(""),tB=(e,t={})=>{let r=`\x1b]1337;File=inline=1`;return t.width&&(r+=`;width=${t.width}`),t.height&&(r+=`;height=${t.height}`),!1===t.preserveAspectRatio&&(r+=";preserveAspectRatio=0"),r+":"+Buffer.from(e).toString("base64")+"\x07"},tH={setCwd:(e=tp())=>`\x1b]50;CurrentDir=${e}\x07`,annotation(e,t={}){let r=`\x1b]1337;`,n=void 0!==t.x,i=void 0!==t.y;if((n||i)&&!(n&&i&&void 0!==t.length))throw Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");return e=e.replaceAll("|",""),r+=t.isHidden?"AddHiddenAnnotation=":"AddAnnotation=",t.length>0?r+=(n?[e,t.length,t.x,t.y]:[t.length,e]).join("|"):r+=e,r+"\x07"}},tW=f(g(),1);function tJ(e,t,{target:r="stdout",...n}={}){return tW.default[r]?tl.link(e,t):!1===n.fallback?e:"function"==typeof n.fallback?n.fallback(e,t):`${e} (\u200B${t}\u200B)`}tJ.isSupported=tW.default.stdout,tJ.stderr=(e,t,r={})=>tJ(e,t,{target:"stderr",...r}),tJ.stderr.isSupported=tW.default.stderr;var tK=y().version;function tQ(e){var t;let r;return("library"===(r=process.env.PRISMA_CLIENT_ENGINE_TYPE)?"library":"binary"===r?"binary":"client"===r?"client":void 0)||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":(t=e,t?.previewFeatures.includes("queryCompiler")?"client":"library"))}f(b());var tY=f(r(76760));f(b()),ex("prisma:engines"),tY.default.join(__dirname,"../query-engine-darwin"),tY.default.join(__dirname,"../query-engine-darwin-arm64"),tY.default.join(__dirname,"../query-engine-debian-openssl-1.0.x"),tY.default.join(__dirname,"../query-engine-debian-openssl-1.1.x"),tY.default.join(__dirname,"../query-engine-debian-openssl-3.0.x"),tY.default.join(__dirname,"../query-engine-linux-static-x64"),tY.default.join(__dirname,"../query-engine-linux-static-arm64"),tY.default.join(__dirname,"../query-engine-rhel-openssl-1.0.x"),tY.default.join(__dirname,"../query-engine-rhel-openssl-1.1.x"),tY.default.join(__dirname,"../query-engine-rhel-openssl-3.0.x"),tY.default.join(__dirname,"../libquery_engine-darwin.dylib.node"),tY.default.join(__dirname,"../libquery_engine-darwin-arm64.dylib.node"),tY.default.join(__dirname,"../libquery_engine-debian-openssl-1.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-debian-openssl-1.1.x.so.node"),tY.default.join(__dirname,"../libquery_engine-debian-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.1.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-musl.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-musl-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-rhel-openssl-1.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-rhel-openssl-1.1.x.so.node"),tY.default.join(__dirname,"../libquery_engine-rhel-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../query_engine-windows.dll.node");var tz=f(r(73024)),tZ=ex("chmodPlusX"),tX=(f(v(),1),"prisma+postgres:"),t0=f(E()),t1=class{constructor(e){this.config=e}toString(){var e;let t,{config:r}=this,n=JSON.parse(JSON.stringify({provider:r.provider.fromEnvVar?`env("${r.provider.fromEnvVar}")`:r.provider.value,binaryTargets:function(e){let t;if(e.length>0){let r=e.find(e=>null!==e.fromEnvVar);t=r?`env("${r.fromEnvVar}")`:e.map(e=>e.native?"native":e.value)}else t=void 0;return t}(r.binaryTargets)}));return`generator ${r.name} {
${(0,t0.default)((t=Object.keys(e=n).reduce((e,t)=>Math.max(e,t.length),0),Object.entries(e).map(([e,r])=>`${e.padEnd(t)} = ${JSON.parse(JSON.stringify(r,(e,t)=>Array.isArray(t)?`[${t.map(e=>JSON.stringify(e)).join(", ")}]`:JSON.stringify(t)))}`).join(`
`)),2)}
}`}},t2={};d(t2,{error:()=>t5,info:()=>t9,log:()=>t7,query:()=>t8,should:()=>t3,tags:()=>t4,warn:()=>t6});var t4={error:X("prisma:error"),warn:et("prisma:warn"),info:ei("prisma:info"),query:er("prisma:query")},t3={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function t7(...e){console.log(...e)}function t6(e,...t){t3.warn()&&console.warn(`${t4.warn} ${e}`,...t)}function t9(e,...t){console.info(`${t4.info} ${e}`,...t)}function t5(e,...t){console.error(`${t4.error} ${e}`,...t)}function t8(e,...t){console.log(`${t4.query} ${e}`,...t)}function re(e,t){if(!e)throw Error(`${t}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`)}function rt(e,t){throw Error(t)}var rr=f(r(76760)),rn=f(_()),ri=f(r(73024)),rs=f(r(76760)),ra=ex("prisma:tryLoadEnv");function ro({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=rl(e);"none"!==r.conflictCheck&&function(e,t,r){let n=e?.dotenvResult.parsed,i=!ru(e?.path,t);if(n&&t&&i&&ri.default.existsSync(t)){let i=rn.default.parse(ri.default.readFileSync(t)),s=[];for(let e in i)n[e]===i[e]&&s.push(e);if(s.length>0){let n=rs.default.relative(process.cwd(),e.path),i=rs.default.relative(process.cwd(),t);if("error"===r)throw Error(`There is a conflict between env var${s.length>1?"s":""} in ${K(n)} and ${K(i)}
Conflicting env vars:
${s.map(e=>`  ${H(e)}`).join(`
`)}

We suggest to move the contents of ${K(i)} to ${K(n)} to consolidate your env vars.
`);if("warn"===r){let e=`Conflict for env var${s.length>1?"s":""} ${s.map(e=>H(e)).join(", ")} in ${K(n)} and ${K(i)}
Env vars from ${K(i)} overwrite the ones from ${K(n)}
      `;console.warn(`${et("warn(prisma)")} ${e}`)}}}}(n,t,r.conflictCheck);let i=null;return ru(n?.path,t)||(i=rl(t)),n||i||ra("No Environment variables loaded"),i?.dotenvResult.error?console.error(X(H("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function rl(e){var t;return(t=e)&&ri.default.existsSync(t)?(ra(`Environment variables loaded from ${e}`),{dotenvResult:function(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(n,i){let s=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(i);if(!s)return n;let a=s[1],o,l;if("\\"===a)o=(l=s[0]).replace("\\$","$");else{let n=s[2];l=s[0].substring(a.length),o=r(o=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n]||"")}return n.replace(l,o)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let r in e.parsed)t[r]=e.parsed[r];return e}(rn.default.config({path:e,debug:!!process.env.DOTENV_CONFIG_DEBUG||void 0})),message:W(`Environment variables loaded from ${rs.default.relative(process.cwd(),e)}`),path:e}):(ra(`Environment variables not found at ${e}`),null)}function ru(e,t){return e&&t&&rs.default.resolve(e)===rs.default.resolve(t)}function rc(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function rd(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var rh=new Set,rf=(e,t,...r)=>{rh.has(e)||(rh.add(e),t6(t,...r))},rp=class e extends Error{constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};rd(rp,"PrismaClientInitializationError");var rm=class extends Error{constructor(e,{code:t,clientVersion:r,meta:n,batchRequestIdx:i}){super(e),this.name="PrismaClientKnownRequestError",this.code=t,this.clientVersion=r,this.meta=n,Object.defineProperty(this,"batchRequestIdx",{value:i,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};rd(rm,"PrismaClientKnownRequestError");var rg=class extends Error{constructor(e,t){super(e),this.name="PrismaClientRustPanicError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};rd(rg,"PrismaClientRustPanicError");var ry=class extends Error{constructor(e,{clientVersion:t,batchRequestIdx:r}){super(e),this.name="PrismaClientUnknownRequestError",this.clientVersion=t,Object.defineProperty(this,"batchRequestIdx",{value:r,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};rd(ry,"PrismaClientUnknownRequestError");var rw=class extends Error{constructor(e,{clientVersion:t}){super(e),this.name="PrismaClientValidationError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};rd(rw,"PrismaClientValidationError");var rb,rv,rE="0123456789abcdef",rx="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",rP="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",rS={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},r_=!0,rA="[DecimalError] ",rN=rA+"Invalid argument: ",rT=rA+"Precision limit exceeded",r$=rA+"crypto unavailable",rR="[object Decimal]",rO=Math.floor,rk=Math.pow,rD=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,rI=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,rq=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,rC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,rF=rx.length-1,rV=rP.length-1,rj={toStringTag:rR};function rM(e){var t,r,n,i=e.length-1,s="",a=e[0];if(i>0){for(s+=a,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(s+=rz(r)),s+=n;(r=7-(n=(a=e[t])+"").length)&&(s+=rz(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return s+a}function rU(e,t,r){if(e!==~~e||e<t||e>r)throw Error(rN+e)}function rL(e,t,r,n){var i,s,a,o;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=7,i=0):(i=Math.ceil((t+1)/7),t%=7),s=rk(10,7-t),o=e[i]%s|0,null==n?t<3?(0==t?o=o/100|0:1==t&&(o=o/10|0),a=r<4&&99999==o||r>3&&49999==o||5e4==o||0==o):a=(r<4&&o+1==s||r>3&&o+1==s/2)&&(e[i+1]/s/100|0)==rk(10,t-2)-1||(o==s/2||0==o)&&(e[i+1]/s/100|0)==0:t<4?(0==t?o=o/1e3|0:1==t?o=o/100|0:2==t&&(o=o/10|0),a=(n||r<4)&&9999==o||!n&&r>3&&4999==o):a=((n||r<4)&&o+1==s||!n&&r>3&&o+1==s/2)&&(e[i+1]/s/1e3|0)==rk(10,t-3)-1,a}function rG(e,t,r){for(var n,i,s=[0],a=0,o=e.length;a<o;){for(i=s.length;i--;)s[i]*=t;for(s[0]+=rE.indexOf(e.charAt(a++)),n=0;n<s.length;n++)s[n]>r-1&&(void 0===s[n+1]&&(s[n+1]=0),s[n+1]+=s[n]/r|0,s[n]%=r)}return s.reverse()}rj.absoluteValue=rj.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),rH(e)},rj.ceil=function(){return rH(new this.constructor(this),this.e+1,2)},rj.clampedTo=rj.clamp=function(e,t){var r=this.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(rN+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new r(this)},rj.comparedTo=rj.cmp=function(e){var t,r,n,i,s=this.d,a=(e=new this.constructor(e)).d,o=this.s,l=e.s;if(!s||!a)return o&&l?o!==l?o:s===a?0:!s^o<0?1:-1:NaN;if(!s[0]||!a[0])return s[0]?o:a[0]?-l:0;if(o!==l)return o;if(this.e!==e.e)return this.e>e.e^o<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^o<0?1:-1;return n===i?0:n>i^o<0?1:-1},rj.cosine=rj.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+7,n.rounding=1,r=function(e,t){var r,n,i;if(t.isZero())return t;(n=t.d.length)<32?i=(1/r6(4,r=Math.ceil(n/3))).toString():(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=r7(e,1,t.times(i),new e(1));for(var s=r;s--;){var a=t.times(t);t=a.times(a).minus(a).times(8).plus(1)}return e.precision-=r,t}(n,r9(n,r)),n.precision=e,n.rounding=t,rH(2==rv||3==rv?r.neg():r,e,t,!0)):new n(1):new n(NaN)},rj.cubeRoot=rj.cbrt=function(){var e,t,r,n,i,s,a,o,l,u,c=this.constructor;if(!this.isFinite()||this.isZero())return new c(this);for(r_=!1,(s=this.s*rk(this.s*this,1/3))&&Math.abs(s)!=1/0?n=new c(s.toString()):(r=rM(this.d),(s=((e=this.e)-r.length+1)%3)&&(r+=1==s||-2==s?"0":"00"),s=rk(r,1/3),e=rO((e+1)/3)-(e%3==(e<0?-1:2)),(n=new c(r=s==1/0?"5e"+e:(r=s.toExponential()).slice(0,r.indexOf("e")+1)+e)).s=this.s),a=(e=c.precision)+3;;)if(n=rB((u=(l=(o=n).times(o).times(o)).plus(this)).plus(this).times(o),u.plus(l),a+2,1),rM(o.d).slice(0,a)===(r=rM(n.d)).slice(0,a)){if("9999"!=(r=r.slice(a-3,a+1))&&(i||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(rH(n,e+1,1),t=!n.times(n).times(n).eq(this));break}if(!i&&(rH(o,e+1,0),o.times(o).times(o).eq(this))){n=o;break}a+=4,i=1}return r_=!0,rH(n,e,c.rounding,t)},rj.decimalPlaces=rj.dp=function(){var e,t=this.d,r=NaN;if(t){if(r=((e=t.length-1)-rO(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r},rj.dividedBy=rj.div=function(e){return rB(this,new this.constructor(e))},rj.dividedToIntegerBy=rj.divToInt=function(e){var t=this.constructor;return rH(rB(this,new t(e),0,1,1),t.precision,t.rounding)},rj.equals=rj.eq=function(e){return 0===this.cmp(e)},rj.floor=function(){return rH(new this.constructor(this),this.e+1,3)},rj.greaterThan=rj.gt=function(e){return this.cmp(e)>0},rj.greaterThanOrEqualTo=rj.gte=function(e){var t=this.cmp(e);return 1==t||0===t},rj.hyperbolicCosine=rj.cosh=function(){var e,t,r,n,i,s=this,a=s.constructor,o=new a(1);if(!s.isFinite())return new a(s.s?1/0:NaN);if(s.isZero())return o;r=a.precision,n=a.rounding,a.precision=r+Math.max(s.e,s.sd())+4,a.rounding=1,(i=s.d.length)<32?t=(1/r6(4,e=Math.ceil(i/3))).toString():(e=16,t="2.3283064365386962890625e-10"),s=r7(a,1,s.times(t),new a(1),!0);for(var l,u=e,c=new a(8);u--;)l=s.times(s),s=o.minus(l.times(c.minus(l.times(c))));return rH(s,a.precision=r,a.rounding=n,!0)},rj.hyperbolicSine=rj.sinh=function(){var e,t,r,n,i=this,s=i.constructor;if(!i.isFinite()||i.isZero())return new s(i);if(t=s.precision,r=s.rounding,s.precision=t+Math.max(i.e,i.sd())+4,s.rounding=1,(n=i.d.length)<3)i=r7(s,2,i,i,!0);else{e=(e=1.4*Math.sqrt(n))>16?16:0|e,i=r7(s,2,i=i.times(1/r6(5,e)),i,!0);for(var a,o=new s(5),l=new s(16),u=new s(20);e--;)a=i.times(i),i=i.times(o.plus(a.times(l.times(a).plus(u))))}return s.precision=t,s.rounding=r,rH(i,t,r,!0)},rj.hyperbolicTangent=rj.tanh=function(){var e,t,r=this.constructor;return this.isFinite()?this.isZero()?new r(this):(e=r.precision,t=r.rounding,r.precision=e+7,r.rounding=1,rB(this.sinh(),this.cosh(),r.precision=e,r.rounding=t)):new r(this.s)},rj.inverseCosine=rj.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return -1!==r?0===r?e.isNeg()?rQ(t,n,i):new t(0):new t(NaN):e.isZero()?rQ(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))},rj.inverseHyperbolicCosine=rj.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,r_=!1,r=r.times(r).minus(1).sqrt().plus(r),r_=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)},rj.inverseHyperbolicSine=rj.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,r_=!1,r=r.times(r).plus(1).sqrt().plus(r),r_=!0,n.precision=e,n.rounding=t,r.ln())},rj.inverseHyperbolicTangent=rj.atanh=function(){var e,t,r,n,i=this,s=i.constructor;return i.isFinite()?i.e>=0?new s(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=s.precision,t=s.rounding,Math.max(n=i.sd(),e)<-(2*i.e)-1?rH(new s(i),e,t,!0):(s.precision=r=n-i.e,i=rB(i.plus(1),new s(1).minus(i),r+e,1),s.precision=e+4,s.rounding=1,i=i.ln(),s.precision=e,s.rounding=t,i.times(.5))):new s(NaN)},rj.inverseSine=rj.asin=function(){var e,t,r,n,i=this,s=i.constructor;return i.isZero()?new s(i):(t=i.abs().cmp(1),r=s.precision,n=s.rounding,-1!==t?0===t?((e=rQ(s,r+4,n).times(.5)).s=i.s,e):new s(NaN):(s.precision=r+6,s.rounding=1,i=i.div(new s(1).minus(i.times(i)).sqrt().plus(1)).atan(),s.precision=r,s.rounding=n,i.times(2)))},rj.inverseTangent=rj.atan=function(){var e,t,r,n,i,s,a,o,l,u=this,c=u.constructor,d=c.precision,h=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&d+4<=rV)return(a=rQ(c,d+4,h).times(.25)).s=u.s,a}else{if(!u.s)return new c(NaN);if(d+4<=rV)return(a=rQ(c,d+4,h).times(.5)).s=u.s,a}for(c.precision=o=d+10,c.rounding=1,e=r=Math.min(28,o/7+2|0);e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(r_=!1,t=Math.ceil(o/7),n=1,l=u.times(u),a=new c(u),i=u;-1!==e;)if(i=i.times(l),s=a.minus(i.div(n+=2)),i=i.times(l),void 0!==(a=s.plus(i.div(n+=2))).d[t])for(e=t;a.d[e]===s.d[e]&&e--;);return r&&(a=a.times(2<<r-1)),r_=!0,rH(a,c.precision=d,c.rounding=h,!0)},rj.isFinite=function(){return!!this.d},rj.isInteger=rj.isInt=function(){return!!this.d&&rO(this.e/7)>this.d.length-2},rj.isNaN=function(){return!this.s},rj.isNegative=rj.isNeg=function(){return this.s<0},rj.isPositive=rj.isPos=function(){return this.s>0},rj.isZero=function(){return!!this.d&&0===this.d[0]},rj.lessThan=rj.lt=function(e){return 0>this.cmp(e)},rj.lessThanOrEqualTo=rj.lte=function(e){return 1>this.cmp(e)},rj.logarithm=rj.log=function(e){var t,r,n,i,s,a,o,l,u=this.constructor,c=u.precision,d=u.rounding;if(null==e)e=new u(10),t=!0;else{if(r=(e=new u(e)).d,e.s<0||!r||!r[0]||e.eq(1))return new u(NaN);t=e.eq(10)}if(r=this.d,this.s<0||!r||!r[0]||this.eq(1))return new u(r&&!r[0]?-1/0:1!=this.s?NaN:r?0:1/0);if(t){if(r.length>1)s=!0;else{for(i=r[0];i%10==0;)i/=10;s=1!==i}}if(r_=!1,rL((l=rB(a=r2(this,o=c+5),t?rK(u,o+10):r2(e,o),o,1)).d,i=c,d))do if(o+=10,l=rB(a=r2(this,o),t?rK(u,o+10):r2(e,o),o,1),!s){+rM(l.d).slice(i+1,i+15)+1==1e14&&(l=rH(l,c+1,0));break}while(rL(l.d,i+=10,d));return r_=!0,rH(l,c,d)},rj.minus=rj.sub=function(e){var t,r,n,i,s,a,o,l,u,c,d,h,f=this.constructor;if(e=new f(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new f(e.d||this.s!==e.s?this:NaN):e=new f(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(u=this.d,h=e.d,o=f.precision,l=f.rounding,!u[0]||!h[0]){if(h[0])e.s=-e.s;else{if(!u[0])return new f(3===l?-0:0);e=new f(this)}return r_?rH(e,o,l):e}if(r=rO(e.e/7),c=rO(this.e/7),u=u.slice(),s=c-r){for((d=s<0)?(t=u,s=-s,a=h.length):(t=h,r=c,a=u.length),s>(n=Math.max(Math.ceil(o/7),a)+2)&&(s=n,t.length=1),t.reverse(),n=s;n--;)t.push(0);t.reverse()}else{for((d=(n=u.length)<(a=h.length))&&(a=n),n=0;n<a;n++)if(u[n]!=h[n]){d=u[n]<h[n];break}s=0}for(d&&(t=u,u=h,h=t,e.s=-e.s),a=u.length,n=h.length-a;n>0;--n)u[a++]=0;for(n=h.length;n>s;){if(u[--n]<h[n]){for(i=n;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[n]+=1e7}u[n]-=h[n]}for(;0===u[--a];)u.pop();for(;0===u[0];u.shift())--r;return u[0]?(e.d=u,e.e=rJ(u,r),r_?rH(e,o,l):e):new f(3===l?-0:0)},rj.modulo=rj.mod=function(e){var t,r=this.constructor;return e=new r(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(r_=!1,9==r.modulo?(t=rB(this,e.abs(),0,3,1),t.s*=e.s):t=rB(this,e,0,r.modulo,1),t=t.times(e),r_=!0,this.minus(t)):rH(new r(this),r.precision,r.rounding):new r(NaN)},rj.naturalExponential=rj.exp=function(){return r1(this)},rj.naturalLogarithm=rj.ln=function(){return r2(this)},rj.negated=rj.neg=function(){var e=new this.constructor(this);return e.s=-e.s,rH(e)},rj.plus=rj.add=function(e){var t,r,n,i,s,a,o,l,u,c,d=this.constructor;if(e=new d(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new d(e.d||this.s===e.s?this:NaN)):e=new d(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(u=this.d,c=e.d,o=d.precision,l=d.rounding,!u[0]||!c[0])return c[0]||(e=new d(this)),r_?rH(e,o,l):e;if(s=rO(this.e/7),n=rO(e.e/7),u=u.slice(),i=s-n){for(i<0?(r=u,i=-i,a=c.length):(r=c,n=s,a=u.length),i>(a=(s=Math.ceil(o/7))>a?s+1:a+1)&&(i=a,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for((a=u.length)-(i=c.length)<0&&(i=a,r=c,c=u,u=r),t=0;i;)t=(u[--i]=u[i]+c[i]+t)/1e7|0,u[i]%=1e7;for(t&&(u.unshift(t),++n),a=u.length;0==u[--a];)u.pop();return e.d=u,e.e=rJ(u,n),r_?rH(e,o,l):e},rj.precision=rj.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(rN+e);return this.d?(t=rY(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},rj.round=function(){var e=this.constructor;return rH(new e(this),this.e+1,e.rounding)},rj.sine=rj.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+7,n.rounding=1,r=function(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:r7(e,2,t,t);r=(r=1.4*Math.sqrt(n))>16?16:0|r,t=r7(e,2,t=t.times(1/r6(5,r)),t);for(var i,s=new e(5),a=new e(16),o=new e(20);r--;)i=t.times(t),t=t.times(s.plus(i.times(a.times(i).minus(o))));return t}(n,r9(n,r)),n.precision=e,n.rounding=t,rH(rv>2?r.neg():r,e,t,!0)):new n(NaN)},rj.squareRoot=rj.sqrt=function(){var e,t,r,n,i,s,a=this.d,o=this.e,l=this.s,u=this.constructor;if(1!==l||!a||!a[0])return new u(!l||l<0&&(!a||a[0])?NaN:a?this:1/0);for(r_=!1,0==(l=Math.sqrt(+this))||l==1/0?(((t=rM(a)).length+o)%2==0&&(t+="0"),l=Math.sqrt(t),o=rO((o+1)/2)-(o<0||o%2),n=new u(t=l==1/0?"5e"+o:(t=l.toExponential()).slice(0,t.indexOf("e")+1)+o)):n=new u(l.toString()),r=(o=u.precision)+3;;)if(n=(s=n).plus(rB(this,s,r+2,1)).times(.5),rM(s.d).slice(0,r)===(t=rM(n.d)).slice(0,r)){if("9999"!=(t=t.slice(r-3,r+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(rH(n,o+1,1),e=!n.times(n).eq(this));break}if(!i&&(rH(s,o+1,0),s.times(s).eq(this))){n=s;break}r+=4,i=1}return r_=!0,rH(n,o,u.rounding,e)},rj.tangent=rj.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,(r=r.sin()).s=1,r=rB(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,rH(2==rv||4==rv?r.neg():r,e,t,!0)):new n(NaN)},rj.times=rj.mul=function(e){var t,r,n,i,s,a,o,l,u,c=this.constructor,d=this.d,h=(e=new c(e)).d;if(e.s*=this.s,!d||!d[0]||!h||!h[0])return new c(e.s&&(!d||d[0]||h)&&(!h||h[0]||d)?d&&h?0*e.s:e.s/0:NaN);for(r=rO(this.e/7)+rO(e.e/7),(l=d.length)<(u=h.length)&&(s=d,d=h,h=s,a=l,l=u,u=a),s=[],n=a=l+u;n--;)s.push(0);for(n=u;--n>=0;){for(t=0,i=l+n;i>n;)o=s[i]+h[n]*d[i-n-1]+t,s[i--]=o%1e7|0,t=o/1e7|0;s[i]=(s[i]+t)%1e7|0}for(;!s[--a];)s.pop();return t?++r:s.shift(),e.d=s,e.e=rJ(s,r),r_?rH(e,c.precision,c.rounding):e},rj.toBinary=function(e,t){return r5(this,2,e,t)},rj.toDecimalPlaces=rj.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(rU(e,0,1e9),void 0===t?t=n.rounding:rU(t,0,8),rH(r,e+r.e+1,t))},rj.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=rW(n,!0):(rU(e,0,1e9),void 0===t?t=i.rounding:rU(t,0,8),r=rW(n=rH(new i(n),e+1,t),!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r},rj.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?r=rW(this):(rU(e,0,1e9),void 0===t?t=i.rounding:rU(t,0,8),r=rW(n=rH(new i(this),e+this.e+1,t),!1,e+n.e+1)),this.isNeg()&&!this.isZero()?"-"+r:r},rj.toFraction=function(e){var t,r,n,i,s,a,o,l,u,c,d,h,f=this.d,p=this.constructor;if(!f)return new p(this);if(u=r=new p(1),n=l=new p(0),a=(s=(t=new p(n)).e=rY(f)-this.e-1)%7,t.d[0]=rk(10,a<0?7+a:a),null==e)e=s>0?t:u;else{if(!(o=new p(e)).isInt()||o.lt(u))throw Error(rN+o);e=o.gt(t)?s>0?t:u:o}for(r_=!1,o=new p(rM(f)),c=p.precision,p.precision=s=14*f.length;d=rB(o,t,0,1,1),1!=(i=r.plus(d.times(n))).cmp(e);)r=n,n=i,i=u,u=l.plus(d.times(i)),l=i,i=t,t=o.minus(d.times(i)),o=i;return i=rB(e.minus(r),n,0,1,1),l=l.plus(i.times(u)),r=r.plus(i.times(n)),l.s=u.s=this.s,h=1>rB(u,n,s,1).minus(this).abs().cmp(rB(l,r,s,1).minus(this).abs())?[u,n]:[l,r],p.precision=c,r_=!0,h},rj.toHexadecimal=rj.toHex=function(e,t){return r5(this,16,e,t)},rj.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),null==e){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),void 0===t?t=n.rounding:rU(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(r_=!1,r=rB(r,e,0,t,1).times(e),r_=!0,rH(r)):(e.s=r.s,r=e),r},rj.toNumber=function(){return+this},rj.toOctal=function(e,t){return r5(this,8,e,t)},rj.toPower=rj.pow=function(e){var t,r,n,i,s,a,o=this,l=o.constructor,u=+(e=new l(e));if(!o.d||!e.d||!o.d[0]||!e.d[0])return new l(rk(+o,u));if((o=new l(o)).eq(1))return o;if(n=l.precision,s=l.rounding,e.eq(1))return rH(o,n,s);if((t=rO(e.e/7))>=e.d.length-1&&(r=u<0?-u:u)<=0x1fffffffffffff)return i=rZ(l,o,r,n),e.s<0?new l(1).div(i):rH(i,n,s);if((a=o.s)<0){if(t<e.d.length-1)return new l(NaN);if((1&e.d[t])==0&&(a=1),0==o.e&&1==o.d[0]&&1==o.d.length)return o.s=a,o}return(t=0!=(r=rk(+o,u))&&isFinite(r)?new l(r+"").e:rO(u*(Math.log("0."+rM(o.d))/Math.LN10+o.e+1)))>l.maxE+1||t<l.minE-1?new l(t>0?a/0:0):(r_=!1,l.rounding=o.s=1,r=Math.min(12,(t+"").length),(i=r1(e.times(r2(o,n+r)),n)).d&&rL((i=rH(i,n+5,1)).d,n,s)&&(t=n+10,+rM((i=rH(r1(e.times(r2(o,t+r)),t),t+5,1)).d).slice(n+1,n+15)+1==1e14&&(i=rH(i,n+1,0))),i.s=a,r_=!0,l.rounding=s,rH(i,n,s))},rj.toPrecision=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=rW(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(rU(e,1,1e9),void 0===t?t=i.rounding:rU(t,0,8),r=rW(n=rH(new i(n),e,t),e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r},rj.toSignificantDigits=rj.toSD=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(rU(e,1,1e9),void 0===t?t=r.rounding:rU(t,0,8)),rH(new r(this),e,t)},rj.toString=function(){var e=this.constructor,t=rW(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},rj.truncated=rj.trunc=function(){return rH(new this.constructor(this),this.e+1,1)},rj.valueOf=rj.toJSON=function(){var e=this.constructor,t=rW(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var rB=function(){function e(e,t,r){var n,i=0,s=e.length;for(e=e.slice();s--;)n=e[s]*t+i,e[s]=n%r|0,i=n/r|0;return i&&e.unshift(i),e}function t(e,t,r,n){var i,s;if(r!=n)s=r>n?1:-1;else for(i=s=0;i<r;i++)if(e[i]!=t[i]){s=e[i]>t[i]?1:-1;break}return s}function r(e,t,r,n){for(var i=0;r--;)e[r]-=i,i=+(e[r]<t[r]),e[r]=i*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,s,a,o,l){var u,c,d,h,f,p,m,g,y,w,b,v,E,x,P,S,_,A,N,T,$=n.constructor,R=n.s==i.s?1:-1,O=n.d,k=i.d;if(!O||!O[0]||!k||!k[0])return new $(n.s&&i.s&&(O?!k||O[0]!=k[0]:k)?O&&0==O[0]||!k?0*R:R/0:NaN);for(l?(f=1,c=n.e-i.e):(l=1e7,f=7,c=rO(n.e/f)-rO(i.e/f)),N=k.length,_=O.length,w=(y=new $(R)).d=[],d=0;k[d]==(O[d]||0);d++);if(k[d]>(O[d]||0)&&c--,null==s?(x=s=$.precision,a=$.rounding):x=o?s+(n.e-i.e)+1:s,x<0)w.push(1),p=!0;else{if(x=x/f+2|0,d=0,1==N){for(h=0,k=k[0],x++;(d<_||h)&&x--;d++)P=h*l+(O[d]||0),w[d]=P/k|0,h=P%k|0;p=h||d<_}else{for((h=l/(k[0]+1)|0)>1&&(k=e(k,h,l),O=e(O,h,l),N=k.length,_=O.length),S=N,v=(b=O.slice(0,N)).length;v<N;)b[v++]=0;(T=k.slice()).unshift(0),A=k[0],k[1]>=l/2&&++A;do h=0,(u=t(k,b,N,v))<0?(E=b[0],N!=v&&(E=E*l+(b[1]||0)),(h=E/A|0)>1?(h>=l&&(h=l-1),g=(m=e(k,h,l)).length,v=b.length,1==(u=t(m,b,g,v))&&(h--,r(m,N<g?T:k,g,l))):(0==h&&(u=h=1),m=k.slice()),(g=m.length)<v&&m.unshift(0),r(b,m,v,l),-1==u&&(v=b.length,(u=t(k,b,N,v))<1&&(h++,r(b,N<v?T:k,v,l))),v=b.length):0===u&&(h++,b=[0]),w[d++]=h,u&&b[0]?b[v++]=O[S]||0:(b=[O[S]],v=1);while((S++<_||void 0!==b[0])&&x--);p=void 0!==b[0]}w[0]||w.shift()}if(1==f)y.e=c,rb=p;else{for(d=1,h=w[0];h>=10;h/=10)d++;y.e=d+c*f-1,rH(y,o?s+y.e+1:s,a,p)}return y}}();function rH(e,t,r,n){var i,s,a,o,l,u,c,d,h,f=e.constructor;e:if(null!=t){if(!(d=e.d))return e;for(i=1,o=d[0];o>=10;o/=10)i++;if((s=t-i)<0)s+=7,a=t,l=(c=d[h=0])/rk(10,i-a-1)%10|0;else if((h=Math.ceil((s+1)/7))>=(o=d.length)){if(n){for(;o++<=h;)d.push(0);c=l=0,i=1,s%=7,a=s-7+1}else break e}else{for(c=o=d[h],i=1;o>=10;o/=10)i++;s%=7,l=(a=s-7+i)<0?0:c/rk(10,i-a-1)%10|0}if(n=n||t<0||void 0!==d[h+1]||(a<0?c:c%rk(10,i-a-1)),u=r<4?(l||n)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||n||6==r&&(s>0?a>0?c/rk(10,i-a):0:d[h-1])%10&1||r==(e.s<0?8:7)),t<1||!d[0])return d.length=0,u?(t-=e.e+1,d[0]=rk(10,(7-t%7)%7),e.e=-t||0):d[0]=e.e=0,e;if(0==s?(d.length=h,o=1,h--):(d.length=h+1,o=rk(10,7-s),d[h]=a>0?(c/rk(10,i-a)%rk(10,a)|0)*o:0),u)for(;;)if(0==h){for(s=1,a=d[0];a>=10;a/=10)s++;for(a=d[0]+=o,o=1;a>=10;a/=10)o++;s!=o&&(e.e++,1e7==d[0]&&(d[0]=1));break}else{if(d[h]+=o,1e7!=d[h])break;d[h--]=0,o=1}for(s=d.length;0===d[--s];)d.pop()}return r_&&(e.e>f.maxE?(e.d=null,e.e=NaN):e.e<f.minE&&(e.e=0,e.d=[0])),e}function rW(e,t,r){if(!e.isFinite())return r4(e);var n,i=e.e,s=rM(e.d),a=s.length;return t?(r&&(n=r-a)>0?s=s.charAt(0)+"."+s.slice(1)+rz(n):a>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):i<0?(s="0."+rz(-i-1)+s,r&&(n=r-a)>0&&(s+=rz(n))):i>=a?(s+=rz(i+1-a),r&&(n=r-i-1)>0&&(s=s+"."+rz(n))):((n=i+1)<a&&(s=s.slice(0,n)+"."+s.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(s+="."),s+=rz(n))),s}function rJ(e,t){var r=e[0];for(t*=7;r>=10;r/=10)t++;return t}function rK(e,t,r){if(t>rF)throw r_=!0,r&&(e.precision=r),Error(rT);return rH(new e(rx),t,1,!0)}function rQ(e,t,r){if(t>rV)throw Error(rT);return rH(new e(rP),t,r,!0)}function rY(e){var t=e.length-1,r=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function rz(e){for(var t="";e--;)t+="0";return t}function rZ(e,t,r,n){var i,s=new e(1),a=Math.ceil(n/7+4);for(r_=!1;;){if(r%2&&r8((s=s.times(t)).d,a)&&(i=!0),0===(r=rO(r/2))){r=s.d.length-1,i&&0===s.d[r]&&++s.d[r];break}r8((t=t.times(t)).d,a)}return r_=!0,s}function rX(e){return 1&e.d[e.d.length-1]}function r0(e,t,r){for(var n,i,s=new e(t[0]),a=0;++a<t.length;){if(!(i=new e(t[a])).s){s=i;break}((n=s.cmp(i))===r||0===n&&s.s===r)&&(s=i)}return s}function r1(e,t){var r,n,i,s,a,o,l,u=0,c=0,d=0,h=e.constructor,f=h.rounding,p=h.precision;if(!e.d||!e.d[0]||e.e>17)return new h(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(r_=!1,l=p):l=t,o=new h(.03125);e.e>-2;)e=e.times(o),d+=5;for(l+=n=Math.log(rk(2,d))/Math.LN10*2+5|0,r=s=a=new h(1),h.precision=l;;){if(s=rH(s.times(e),l,1),r=r.times(++c),rM((o=a.plus(rB(s,r,l,1))).d).slice(0,l)===rM(a.d).slice(0,l)){for(i=d;i--;)a=rH(a.times(a),l,1);if(null!=t)return h.precision=p,a;if(!(u<3&&rL(a.d,l-n,f,u)))return rH(a,h.precision=p,f,r_=!0);h.precision=l+=10,r=s=o=new h(1),c=0,u++}a=o}}function r2(e,t){var r,n,i,s,a,o,l,u,c,d,h,f=1,p=e,m=p.d,g=p.constructor,y=g.rounding,w=g.precision;if(p.s<0||!m||!m[0]||!p.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=p.s?NaN:m?0:p);if(null==t?(r_=!1,c=w):c=t,g.precision=c+=10,n=(r=rM(m)).charAt(0),!(15e14>Math.abs(s=p.e)))return u=rK(g,c+2,w).times(s+""),p=r2(new g(n+"."+r.slice(1)),c-10).plus(u),g.precision=w,null==t?rH(p,w,y,r_=!0):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=rM((p=p.times(e)).d)).charAt(0),f++;for(s=p.e,n>1?(p=new g("0."+r),s++):p=new g(n+"."+r.slice(1)),d=p,l=a=p=rB(p.minus(1),p.plus(1),c,1),h=rH(p.times(p),c,1),i=3;;){if(a=rH(a.times(h),c,1),rM((u=l.plus(rB(a,new g(i),c,1))).d).slice(0,c)===rM(l.d).slice(0,c)){if(l=l.times(2),0!==s&&(l=l.plus(rK(g,c+2,w).times(s+""))),l=rB(l,new g(f),c,1),null!=t)return g.precision=w,l;if(!rL(l.d,c-10,y,o))return rH(l,g.precision=w,y,r_=!0);g.precision=c+=10,u=a=p=rB(d.minus(1),d.plus(1),c,1),h=rH(p.times(p),c,1),i=o=1}l=u,i+=2}}function r4(e){return String(e.s*e.s/0)}function r3(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);n++);for(i=t.length;48===t.charCodeAt(i-1);--i);if(t=t.slice(n,i)){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";e.d.push(+t),r_&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function r7(e,t,r,n,i){var s,a,o,l,u=e.precision,c=Math.ceil(u/7);for(r_=!1,l=r.times(r),o=new e(n);;){if(a=rB(o.times(l),new e(t++*t++),u,1),o=i?n.plus(a):n.minus(a),n=rB(a.times(l),new e(t++*t++),u,1),void 0!==(a=o.plus(n)).d[c]){for(s=c;a.d[s]===o.d[s]&&s--;);if(-1==s)break}s=o,o=n,n=a,a=s}return r_=!0,a.d.length=c+1,a}function r6(e,t){for(var r=e;--t;)r*=e;return r}function r9(e,t){var r,n=t.s<0,i=rQ(e,e.precision,1),s=i.times(.5);if((t=t.abs()).lte(s))return rv=n?4:1,t;if((r=t.divToInt(i)).isZero())rv=n?3:2;else{if((t=t.minus(r.times(i))).lte(s))return rv=rX(r)?n?2:3:n?4:1,t;rv=rX(r)?n?1:4:n?3:2}return t.minus(i).abs()}function r5(e,t,r,n){var i,s,a,o,l,u,c,d,h,f=e.constructor,p=void 0!==r;if(p?(rU(r,1,1e9),void 0===n?n=f.rounding:rU(n,0,8)):(r=f.precision,n=f.rounding),e.isFinite()){for(a=(c=rW(e)).indexOf("."),p?(i=2,16==t?r=4*r-3:8==t&&(r=3*r-2)):i=t,a>=0&&(c=c.replace(".",""),(h=new f(1)).e=c.length-a,h.d=rG(rW(h),10,i),h.e=h.d.length),s=l=(d=rG(c,10,i)).length;0==d[--l];)d.pop();if(d[0]){if(a<0?s--:((e=new f(e)).d=d,e.e=s,d=(e=rB(e,h,r,n,0,i)).d,s=e.e,u=rb),a=d[r],o=i/2,u=u||void 0!==d[r+1],u=n<4?(void 0!==a||u)&&(0===n||n===(e.s<0?3:2)):a>o||a===o&&(4===n||u||6===n&&1&d[r-1]||n===(e.s<0?8:7)),d.length=r,u)for(;++d[--r]>i-1;)d[r]=0,r||(++s,d.unshift(1));for(l=d.length;!d[l-1];--l);for(a=0,c="";a<l;a++)c+=rE.charAt(d[a]);if(p){if(l>1){if(16==t||8==t){for(a=16==t?4:3,--l;l%a;l++)c+="0";for(l=(d=rG(c,i,t)).length;!d[l-1];--l);for(a=1,c="1.";a<l;a++)c+=rE.charAt(d[a])}else c=c.charAt(0)+"."+c.slice(1)}c=c+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)c="0"+c;c="0."+c}else if(++s>l)for(s-=l;s--;)c+="0";else s<l&&(c=c.slice(0,s)+"."+c.slice(s))}else c=p?"0p+0":"0";c=(16==t?"0x":2==t?"0b":8==t?"0o":"")+c}else c=r4(e);return e.s<0?"-"+c:c}function r8(e,t){if(e.length>t)return e.length=t,!0}function ne(e){return new this(e).abs()}function nt(e){return new this(e).acos()}function nr(e){return new this(e).acosh()}function nn(e,t){return new this(e).plus(t)}function ni(e){return new this(e).asin()}function ns(e){return new this(e).asinh()}function na(e){return new this(e).atan()}function no(e){return new this(e).atanh()}function nl(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,s=n+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(r=t.s<0?rQ(this,n,i):new this(0)).s=e.s:!e.d||t.isZero()?(r=rQ(this,s,1).times(.5)).s=e.s:t.s<0?(this.precision=s,this.rounding=1,r=this.atan(rB(e,t,s,1)),t=rQ(this,s,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(rB(e,t,s,1)):(r=rQ(this,s,1).times(t.s>0?.25:.75)).s=e.s:r=new this(NaN),r}function nu(e){return new this(e).cbrt()}function nc(e){return rH(e=new this(e),e.e+1,2)}function nd(e,t,r){return new this(e).clamp(t,r)}function nh(e){if(!e||"object"!=typeof e)throw Error(rA+"Object expected");var t,r,n,i=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(r=s[t],i&&(this[r]=rS[r]),void 0!==(n=e[r])){if(rO(n)===n&&n>=s[t+1]&&n<=s[t+2])this[r]=n;else throw Error(rN+r+": "+n)}if(r="crypto",i&&(this[r]=rS[r]),void 0!==(n=e[r])){if(!0===n||!1===n||0===n||1===n){if(n){if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(r$)}else this[r]=!1}else throw Error(rN+r+": "+n)}return this}function nf(e){return new this(e).cos()}function np(e){return new this(e).cosh()}function nm(e,t){return new this(e).div(t)}function ng(e){return new this(e).exp()}function ny(e){return rH(e=new this(e),e.e+1,3)}function nw(){var e,t,r=new this(0);for(r_=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return r_=!0,new this(1/0);r=t}return r_=!0,r.sqrt()}function nb(e){return e instanceof nM||e&&e.toStringTag===rR||!1}function nv(e){return new this(e).ln()}function nE(e,t){return new this(e).log(t)}function nx(e){return new this(e).log(2)}function nP(e){return new this(e).log(10)}function nS(){return r0(this,arguments,-1)}function n_(){return r0(this,arguments,1)}function nA(e,t){return new this(e).mod(t)}function nN(e,t){return new this(e).mul(t)}function nT(e,t){return new this(e).pow(t)}function n$(e){var t,r,n,i,s=0,a=new this(1),o=[];if(void 0===e?e=this.precision:rU(e,1,1e9),n=Math.ceil(e/7),this.crypto){if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));s<n;)(i=t[s])>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:o[s++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);s<n;)(i=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((127&t[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,s):(o.push(i%1e7),s+=4);s=n/4}else throw Error(r$)}else for(;s<n;)o[s++]=1e7*Math.random()|0;for(n=o[--s],e%=7,n&&e&&(i=rk(10,7-e),o[s]=(n/i|0)*i);0===o[s];s--)o.pop();if(s<0)r=0,o=[0];else{for(r=-1;0===o[0];r-=7)o.shift();for(n=1,i=o[0];i>=10;i/=10)n++;n<7&&(r-=7-n)}return a.e=r,a.d=o,a}function nR(e){return rH(e=new this(e),e.e+1,this.rounding)}function nO(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function nk(e){return new this(e).sin()}function nD(e){return new this(e).sinh()}function nI(e){return new this(e).sqrt()}function nq(e,t){return new this(e).sub(t)}function nC(){var e=0,t=arguments,r=new this(t[0]);for(r_=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return r_=!0,rH(r,this.precision,this.rounding)}function nF(e){return new this(e).tan()}function nV(e){return new this(e).tanh()}function nj(e){return rH(e=new this(e),e.e+1,1)}rj[Symbol.for("nodejs.util.inspect.custom")]=rj.toString,rj[Symbol.toStringTag]="Decimal";var nM=rj.constructor=function e(t){var r,n,i;function s(e){var t,r,n;if(!(this instanceof s))return new s(e);if(this.constructor=s,nb(e)){this.s=e.s,r_?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(n=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,r=e;r>=10;r/=10)t++;r_?t>s.maxE?(this.e=NaN,this.d=null):t<s.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return r3(this,e.toString())}if("string"===n)return 45===(r=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===r&&(e=e.slice(1)),this.s=1),rC.test(e)?r3(this,e):function(e,t){var r,n,i,s,a,o,l,u,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),rC.test(t))return r3(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(rI.test(t))r=16,t=t.toLowerCase();else if(rD.test(t))r=2;else if(rq.test(t))r=8;else throw Error(rN+t);for((s=t.search(/p/i))>0?(l=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),a=(s=t.indexOf("."))>=0,n=e.constructor,a&&(s=(o=(t=t.replace(".","")).length)-s,i=rZ(n,new n(r),s,2*s)),s=c=(u=rG(t,r,1e7)).length-1;0===u[s];--s)u.pop();return s<0?new n(0*e.s):(e.e=rJ(u,c),e.d=u,r_=!1,a&&(e=rB(e,i,4*o)),l&&(e=e.times(54>Math.abs(l)?rk(2,l):nM.pow(2,l))),r_=!0,e)}(this,e);if("bigint"===n)return e<0?(e=-e,this.s=-1):this.s=1,r3(this,e.toString());throw Error(rN+e)}if(s.prototype=rj,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=nh,s.clone=e,s.isDecimal=nb,s.abs=ne,s.acos=nt,s.acosh=nr,s.add=nn,s.asin=ni,s.asinh=ns,s.atan=na,s.atanh=no,s.atan2=nl,s.cbrt=nu,s.ceil=nc,s.clamp=nd,s.cos=nf,s.cosh=np,s.div=nm,s.exp=ng,s.floor=ny,s.hypot=nw,s.ln=nv,s.log=nE,s.log10=nP,s.log2=nx,s.max=nS,s.min=n_,s.mod=nA,s.mul=nN,s.pow=nT,s.random=n$,s.round=nR,s.sign=nO,s.sin=nk,s.sinh=nD,s.sqrt=nI,s.sub=nq,s.sum=nC,s.tan=nF,s.tanh=nV,s.trunc=nj,void 0===t&&(t={}),t&&!0!==t.defaults)for(i=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return s.config(t),s}(rS);rx=new nM(rx),rP=new nM(rP);var nU=nM;function nL(e){var t;return null===e?e:Array.isArray(e)?e.map(nL):"object"==typeof e?null!==(t=e)&&"object"==typeof t&&"string"==typeof t.$type?function({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:e,byteOffset:r,byteLength:n}=Buffer.from(t,"base64");return new Uint8Array(e,r,n)}case"DateTime":return new Date(t);case"Decimal":return new nU(t);case"Json":return JSON.parse(t);default:rt(t,"Unknown tagged value")}}(e):rc(e,nL):e}var nG=class{get(e){return this._map.get(e)?.value}set(e,t){this._map.set(e,{value:t})}getOrCreate(e,t){let r=this._map.get(e);if(r)return r.value;let n=t();return this.set(e,n),n}constructor(){this._map=new Map}};function nB(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function nH(e){let t;return{get:()=>(t||(t={value:e()}),t.value)}}function nW(e){return{models:nJ(e.models),enums:nJ(e.enums),types:nJ(e.types)}}function nJ(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function nK(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function nQ(e){return"Invalid Date"!==e.toString()}function nY(e){return!!nM.isDecimal(e)||null!==e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.e&&"function"==typeof e.toFixed&&Array.isArray(e.d)}var nz={};function nZ(e){return{name:e.name,values:e.values.map(e=>e.name)}}d(nz,{ModelAction:()=>nX,datamodelEnumToSchemaEnum:()=>nZ});var nX=(e=>(e.findUnique="findUnique",e.findUniqueOrThrow="findUniqueOrThrow",e.findFirst="findFirst",e.findFirstOrThrow="findFirstOrThrow",e.findMany="findMany",e.create="create",e.createMany="createMany",e.createManyAndReturn="createManyAndReturn",e.update="update",e.updateMany="updateMany",e.updateManyAndReturn="updateManyAndReturn",e.upsert="upsert",e.delete="delete",e.deleteMany="deleteMany",e.groupBy="groupBy",e.count="count",e.aggregate="aggregate",e.findRaw="findRaw",e.aggregateRaw="aggregateRaw",e))(nX||{});f(E()),f(r(73024));var n0={keyword:ei,entity:ei,value:e=>H(er(e)),punctuation:er,directive:ei,function:ei,variable:e=>H(er(e)),string:e=>H(ee(e)),boolean:et,number:ei,comment:ea},n1=e=>e,n2={},n4=0,n3={manual:n2.Prism&&n2.Prism.manual,disableWorkerMessageHandler:n2.Prism&&n2.Prism.disableWorkerMessageHandler,util:{encode:function(e){return e instanceof n7?new n7(e.type,n3.util.encode(e.content),e.alias):Array.isArray(e)?e.map(n3.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n4}),e.__id},clone:function e(t,r){let n,i,s=n3.util.type(t);switch(r=r||{},s){case"Object":if(r[i=n3.util.objId(t)])return r[i];for(let s in n={},r[i]=n,t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return r[i=n3.util.objId(t)]?r[i]:(n=[],r[i]=n,t.forEach(function(t,i){n[i]=e(t,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=n3.util.clone(n3.languages[e]);for(let e in t)r[e]=t[e];return r},insertBefore:function(e,t,r,n){let i=(n=n||n3.languages)[e],s={};for(let e in i)if(i.hasOwnProperty(e)){if(e==t)for(let e in r)r.hasOwnProperty(e)&&(s[e]=r[e]);r.hasOwnProperty(e)||(s[e]=i[e])}let a=n[e];return n[e]=s,n3.languages.DFS(n3.languages,function(t,r){r===a&&t!=e&&(this[t]=s)}),s},DFS:function e(t,r,n,i){i=i||{};let s=n3.util.objId;for(let a in t)if(t.hasOwnProperty(a)){r.call(t,a,t[a],n||a);let o=t[a],l=n3.util.type(o);"Object"!==l||i[s(o)]?"Array"!==l||i[s(o)]||(i[s(o)]=!0,e(o,r,a,i)):(i[s(o)]=!0,e(o,r,null,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return n3.hooks.run("before-tokenize",n),n.tokens=n3.tokenize(n.code,n.grammar),n3.hooks.run("after-tokenize",n),n7.stringify(n3.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,s,a){for(let m in r){if(!r.hasOwnProperty(m)||!r[m])continue;if(m==a)return;let g=r[m];g="Array"===n3.util.type(g)?g:[g];for(let a=0;a<g.length;++a){let y=g[a],w=y.inside,b=!!y.lookbehind,v=!!y.greedy,E=0,x=y.alias;if(v&&!y.pattern.global){let e=y.pattern.toString().match(/[imuy]*$/)[0];y.pattern=RegExp(y.pattern.source,e+"g")}y=y.pattern||y;for(let a=n,g=i;a<t.length;g+=t[a].length,++a){let n=t[a];if(t.length>e.length)return;if(n instanceof n7)continue;if(v&&a!=t.length-1){y.lastIndex=g;var o=y.exec(e);if(!o)break;var l=o.index+(b?o[1].length:0),u=o.index+o[0].length,c=a,d=g;for(let e=t.length;c<e&&(d<u||!t[c].type&&!t[c-1].greedy);++c)l>=(d+=t[c].length)&&(++a,g=d);if(t[a]instanceof n7)continue;h=c-a,n=e.slice(g,d),o.index-=g}else{y.lastIndex=0;var o=y.exec(n),h=1}if(!o){if(s)break;continue}b&&(E=o[1]?o[1].length:0);var l=o.index+E,o=o[0].slice(E),u=l+o.length,f=n.slice(0,l),p=n.slice(u);let i=[a,h];f&&(++a,g+=f.length,i.push(f));let P=new n7(m,w?n3.tokenize(o,w):o,x,o,v);if(i.push(P),p&&i.push(p),Array.prototype.splice.apply(t,i),1!=h&&n3.matchGrammar(e,t,r,a,g,!0,m),s)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let e in n)t[e]=n[e];delete t.rest}return n3.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=n3.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=n3.hooks.all[e];if(!(!r||!r.length))for(var n,i=0;n=r[i++];)n(t)}},Token:n7};function n7(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=0|(n||"").length,this.greedy=!!i}n3.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/},n3.languages.javascript=n3.languages.extend("clike",{"class-name":[n3.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/}),n3.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,n3.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:n3.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:n3.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:n3.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:n3.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),n3.languages.markup&&n3.languages.markup.tag.addInlined("script","javascript"),n3.languages.js=n3.languages.javascript,n3.languages.typescript=n3.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/}),n3.languages.ts=n3.languages.typescript,n7.stringify=function(e,t){return"string"==typeof e?e:Array.isArray(e)?e.map(function(e){return n7.stringify(e,t)}).join(""):(n0[e.type]||n1)(e.content)};var n6={red:X,gray:ea,dim:W,bold:H,underline:K,highlightSource:e=>e.highlight()},n9={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function n5(e){let t=e.showColors?n6:n9;return function({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:s},a){var o;let l,u=[""],c=t?" in":":";if(n?(u.push(a.red(`Oops, an unknown error occurred! This is ${a.bold("on us")}, you did nothing wrong.`)),u.push(a.red(`It occurred in the ${a.bold(`\`${e}\``)} invocation${c}`))):u.push(a.red(`Invalid ${a.bold(`\`${e}\``)} invocation${c}`)),t&&u.push(a.underline((l=[(o=t).fileName],o.lineNumber&&l.push(String(o.lineNumber)),o.columnNumber&&l.push(String(o.columnNumber)),l.join(":")))),i){u.push("");let e=[i.toString()];s&&(e.push(s),e.push(a.dim(")"))),u.push(e.join("")),s&&u.push("")}else u.push(""),s&&u.push(s),u.push("");return u.push(r),u.join(`
`)}(function({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},s){return function({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}({message:t,originalMethod:r,isPanic:n,callArguments:i})}(e,0),t)}var n8=f(N());function ie(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function it(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return -10;default:return 0}}var ir=class{constructor(e,t){this.isRequired=!1,this.name=e,this.value=t}makeRequired(){return this.isRequired=!0,this}write(e){let{colors:{green:t}}=e.context;e.addMarginSymbol(t(this.isRequired?"+":"?")),e.write(t(this.name)),this.isRequired||e.write(t("?")),e.write(t(": ")),"string"==typeof this.value?e.write(t(this.value)):e.write(this.value)}};$();var ii=class{constructor(e=0,t){this.lines=[],this.currentLine="",this.currentIndent=0,this.context=t,this.currentIndent=e}write(e){return"string"==typeof e?this.currentLine+=e:e.write(this),this}writeJoined(e,t,r=(e,t)=>t.write(e)){let n=t.length-1;for(let i=0;i<t.length;i++)r(t[i],this),i!==n&&this.write(e);return this}writeLine(e){return this.write(e).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let e=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,e?.(),this}withIndent(e){return this.indent(),e(this),this.unindent(),this}afterNextNewline(e){return this.afterNextNewLineCallback=e,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(e){return this.marginSymbol=e,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let e=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+e.slice(1):e}};T();var is=class{constructor(e){this.value=e}write(e){e.write(this.value)}markAsError(){this.value.markAsError()}},ia=e=>e,io={bold:ia,red:ia,green:ia,dim:ia,enabled:!1},il={bold:H,red:X,green:ee,dim:W,enabled:!0},iu={write(e){e.writeLine(",")}},ic=class{constructor(e){this.isUnderlined=!1,this.color=e=>e,this.contents=e}underline(){return this.isUnderlined=!0,this}setColor(e){return this.color=e,this}write(e){let t=e.getCurrentLineLength();e.write(this.color(this.contents)),this.isUnderlined&&e.afterNextNewline(()=>{e.write(" ".repeat(t)).writeLine(this.color("~".repeat(this.contents.length)))})}},id=class{markAsError(){return this.hasError=!0,this}constructor(){this.hasError=!1}},ih=class extends id{addItem(e){return this.items.push(new is(e)),this}getField(e){return this.items[e]}getPrintWidth(){return 0===this.items.length?2:Math.max(...this.items.map(e=>e.value.getPrintWidth()))+2}write(e){if(0===this.items.length){this.writeEmpty(e);return}this.writeWithItems(e)}writeEmpty(e){let t=new ic("[]");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithItems(e){let{colors:t}=e.context;e.writeLine("[").withIndent(()=>e.writeJoined(iu,this.items).newLine()).write("]"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(t.red("~".repeat(this.getPrintWidth())))})}asObject(){}constructor(...e){super(...e),this.items=[]}},ip=class e extends id{addField(e){this.fields[e.name]=e}addSuggestion(e){this.suggestions.push(e)}getField(e){return this.fields[e]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let s=i;for(let t of n){let r;if(s.value instanceof e?r=s.value.getField(t):s.value instanceof ih&&(r=s.value.getField(Number(t))),!r)return;s=r}return s}getDeepFieldValue(e){return 0===e.length?this:this.getDeepField(e)?.value}hasField(e){return!!this.getField(e)}removeAllFields(){this.fields={}}removeField(e){delete this.fields[e]}getFields(){return this.fields}isEmpty(){return 0===Object.keys(this.fields).length}getFieldValue(e){return this.getField(e)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let t=r.getSubSelectionValue(n);if(!t)return;r=t}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let r of t){let t=n.value.getFieldValue(r);if(!t||!(t instanceof e))return;let i=t.getSelectionParent();if(!i)return;n=i}return n}getSelectionParent(){let e=this.getField("select")?.value.asObject();if(e)return{kind:"select",value:e};let t=this.getField("include")?.value.asObject();if(t)return{kind:"include",value:t}}getSubSelectionValue(e){return this.getSelectionParent()?.value.fields[e].value}getPrintWidth(){let e=Object.values(this.fields);return 0==e.length?2:Math.max(...e.map(e=>e.getPrintWidth()))+2}write(e){let t=Object.values(this.fields);if(0===t.length&&0===this.suggestions.length){this.writeEmpty(e);return}this.writeWithContents(e,t)}asObject(){return this}writeEmpty(e){let t=new ic("{}");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithContents(e,t){e.writeLine("{").withIndent(()=>{e.writeJoined(iu,[...t,...this.suggestions]).newLine()}),e.write("}"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(e.context.colors.red("~".repeat(this.getPrintWidth())))})}constructor(...e){super(...e),this.fields={},this.suggestions=[]}},im=class extends id{constructor(e){super(),this.text=e}getPrintWidth(){return this.text.length}write(e){let t=new ic(this.text);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t)}asObject(){}},ig=class{addField(e,t){return this.fields.push({write(r){let{green:n,dim:i}=r.context.colors;r.write(n(i(`${e}: ${t}`))).addMarginSymbol(n(i("+")))}}),this}write(e){let{colors:{green:t}}=e.context;e.writeLine(t("{")).withIndent(()=>{e.writeJoined(iu,this.fields).newLine()}).write(t("}")).addMarginSymbol(t("+"))}constructor(){this.fields=[]}};function iy(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=function(e,t){let r=1/0,n;for(let i of t){let t=(0,n8.default)(e,i);t>3||t<r&&(r=t,n=i)}return n}(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(ix(e)),n.join(" ")}function iw(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ir(r.name,"true"))}function ib(e,t){let[r,n]=iE(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let s=i.getFieldValue("select")?.asObject(),a=i.getFieldValue("include")?.asObject(),o=i.getFieldValue("omit")?.asObject(),l=s?.getField(n);return s&&l?{parentKind:"select",parent:s,field:l,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"include",field:l,parent:a,fieldName:n}:(l=o?.getField(n),o&&l?{parentKind:"omit",field:l,parent:o,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function iv(e,t){if("object"===t.kind)for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ir(r.name,r.typeNames.join(" | ")))}function iE(e){let t=[...e],r=t.pop();if(!r)throw Error("unexpected empty path");return[t,r]}function ix({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function iP(e,t){if(1===t.length)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var iS=class{constructor(e,t,r,n,i){this.modelName=e,this.name=t,this.typeName=r,this.isList=n,this.isEnum=i}_toGraphQLInputType(){let e=this.isList?"List":"",t=this.isEnum?"Enum":"";return`${e}${t}${this.typeName}FieldRefInput<${this.modelName}>`}};function i_(e){return e instanceof iS}var iA=Symbol(),iN=new WeakMap,iT=class{constructor(e){e===iA?iN.set(this,`Prisma.${this._getName()}`):iN.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return iN.get(this)}},i$=class extends iT{_getNamespace(){return"NullTypes"}},iR=class extends i${#e};iI(iR,"DbNull");var iO=class extends i${#e};iI(iO,"JsonNull");var ik=class extends i${#e};iI(ik,"AnyNull");var iD={classes:{DbNull:iR,JsonNull:iO,AnyNull:ik},instances:{DbNull:new iR(iA),JsonNull:new iO(iA),AnyNull:new ik(iA)}};function iI(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var iq=class{constructor(e,t){this.hasError=!1,this.name=e,this.value=t}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+2}write(e){let t=new ic(this.name);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t).write(": ").write(this.value)}},iC=class{constructor(e){this.errorMessages=[],this.arguments=e}write(e){e.write(this.arguments)}addErrorMessage(e){this.errorMessages.push(e)}renderAllMessages(e){return this.errorMessages.map(t=>t(e)).join(`
`)}};function iF(e){return new iC(iV(e))}function iV(e){let t=new ip;for(let[r,n]of Object.entries(e)){let e=new iq(r,function e(t){if("string"==typeof t)return new im(JSON.stringify(t));if("number"==typeof t||"boolean"==typeof t)return new im(String(t));if("bigint"==typeof t)return new im(`${t}n`);if(null===t)return new im("null");if(void 0===t)return new im("undefined");if(nY(t))return new im(`new Prisma.Decimal("${t.toFixed()}")`);if(t instanceof Uint8Array)return Buffer.isBuffer(t)?new im(`Buffer.alloc(${t.byteLength})`):new im(`new Uint8Array(${t.byteLength})`);if(t instanceof Date){let e=nQ(t)?t.toISOString():"Invalid Date";return new im(`new Date("${e}")`)}return t instanceof iT?new im(`Prisma.${t._getName()}`):i_(t)?new im(`prisma.${nB(t.modelName)}.$fields.${t.name}`):Array.isArray(t)?function(t){let r=new ih;for(let n of t)r.addItem(e(n));return r}(t):"object"==typeof t?iV(t):new im(Object.prototype.toString.call(t))}(n));t.addField(e)}return t}function ij(e,t){let r="pretty"===t?il:io;return{message:e.renderAllMessages(r),args:new ii(0,{colors:r}).write(e).toString()}}function iM({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:s,globalOmit:a}){let o=iF(e);for(let e of t)(function e(t,r,n){var i,s,a,o,l,u,c,d,h,f;switch(t.kind){case"MutuallyExclusiveFields":let p;i=t,s=r,(p=s.arguments.getDeepSubSelectionValue(i.selectionPath)?.asObject())&&(p.getField(i.firstField)?.markAsError(),p.getField(i.secondField)?.markAsError()),s.addErrorMessage(e=>`Please ${e.bold("either")} use ${e.green(`\`${i.firstField}\``)} or ${e.green(`\`${i.secondField}\``)}, but ${e.red("not both")} at the same time.`);break;case"IncludeOnScalar":(function(e,t){let[r,n]=iE(e.selectionPath),i=e.outputType,s=t.arguments.getDeepSelectionParent(r)?.value;if(s&&(s.getField(n)?.markAsError(),i))for(let e of i.fields)e.isRelation&&s.addSuggestion(new ir(e.name,"true"));t.addErrorMessage(e=>{let t=`Invalid scalar field ${e.red(`\`${n}\``)} for ${e.bold("include")} statement`;return i?t+=` on model ${e.bold(i.name)}. ${ix(e)}`:t+=".",t+=`
Note that ${e.bold("include")} statements only accept relation fields.`})})(t,r);break;case"EmptySelection":(function(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let r=n.getField("omit")?.value.asObject();if(r){(function(e,t,r){for(let t of(r.removeAllFields(),e.outputType.fields))r.addSuggestion(new ir(t.name,"false"));t.addErrorMessage(t=>`The ${t.red("omit")} statement includes every field of the model ${t.bold(e.outputType.name)}. At least one field must be included in the result`)})(e,t,r);return}if(n.hasField("select")){var i,s;let r,n,a;i=e,s=t,r=i.outputType,n=s.arguments.getDeepSelectionParent(i.selectionPath)?.value,a=n?.isEmpty()??!1,n&&(n.removeAllFields(),iw(n,r)),s.addErrorMessage(e=>a?`The ${e.red("`select`")} statement for type ${e.bold(r.name)} must not be empty. ${ix(e)}`:`The ${e.red("`select`")} statement for type ${e.bold(r.name)} needs ${e.bold("at least one truthy value")}.`);return}}if(r?.[nB(e.outputType.name)]){(function(e,t){let r=new ig;for(let t of e.outputType.fields)t.isRelation||r.addField(t.name,"false");let n=new ir("omit",r).makeRequired();if(0===e.selectionPath.length)t.arguments.addSuggestion(n);else{let[r,i]=iE(e.selectionPath),s=t.arguments.getDeepSelectionParent(r)?.value.asObject()?.getField(i);if(s){let e=s?.value.asObject()??new ip;e.addSuggestion(n),s.value=e}}t.addErrorMessage(t=>`The global ${t.red("omit")} configuration excludes every field of the model ${t.bold(e.outputType.name)}. At least one field must be included in the result`)})(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)})(t,r,n);break;case"UnknownSelectionField":(function(e,t){let r=ib(e.selectionPath,t);if("unknown"!==r.parentKind){r.field.markAsError();let t=r.parent;switch(r.parentKind){case"select":iw(t,e.outputType);break;case"include":(function(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ir(r.name,"true"))})(t,e.outputType);break;case"omit":(function(e,t){for(let r of t.fields)e.hasField(r.name)||r.isRelation||e.addSuggestion(new ir(r.name,"true"))})(t,e.outputType)}}t.addErrorMessage(t=>{let n=[`Unknown field ${t.red(`\`${r.fieldName}\``)}`];return"unknown"!==r.parentKind&&n.push(`for ${t.bold(r.parentKind)} statement`),n.push(`on model ${t.bold(`\`${e.outputType.name}\``)}.`),n.push(ix(t)),n.join(" ")})})(t,r);break;case"InvalidSelectionValue":let m;a=t,o=r,"unknown"!==(m=ib(a.selectionPath,o)).parentKind&&m.field.value.markAsError(),o.addErrorMessage(e=>`Invalid value for selection field \`${e.red(m.fieldName)}\`: ${a.underlyingError}`);break;case"UnknownArgument":let g,y;l=t,u=r,g=l.argumentPath[0],(y=u.arguments.getDeepSubSelectionValue(l.selectionPath)?.asObject())&&(y.getField(g)?.markAsError(),function(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ir(r.name,r.typeNames.join(" | ")))}(y,l.arguments)),u.addErrorMessage(e=>iy(e,g,l.arguments.map(e=>e.name)));break;case"UnknownInputField":(function(e,t){let[r,n]=iE(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let t=i.getDeepFieldValue(r)?.asObject();t&&iv(t,e.inputType)}t.addErrorMessage(t=>iy(t,n,e.inputType.fields.map(e=>e.name)))})(t,r);break;case"RequiredArgumentMissing":(function(e,t){let r;t.addErrorMessage(e=>r?.value instanceof im&&"null"===r.value.text?`Argument \`${e.green(s)}\` must not be ${e.red("null")}.`:`Argument \`${e.green(s)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,s]=iE(e.argumentPath),a=new ig,o=n.getDeepFieldValue(i)?.asObject();if(o){if((r=o.getField(s))&&o.removeField(s),1===e.inputTypes.length&&"object"===e.inputTypes[0].kind){for(let t of e.inputTypes[0].fields)a.addField(t.name,t.typeNames.join(" | "));o.addSuggestion(new ir(s,a).makeRequired())}else{let t=e.inputTypes.map(function e(t){return"list"===t.kind?`${e(t.elementType)}[]`:t.name}).join(" | ");o.addSuggestion(new ir(s,t).makeRequired())}}})(t,r);break;case"InvalidArgumentType":let w,b;c=t,d=r,w=c.argument.name,(b=d.arguments.getDeepSubSelectionValue(c.selectionPath)?.asObject())&&b.getDeepFieldValue(c.argumentPath)?.markAsError(),d.addErrorMessage(e=>{let t=iP("or",c.argument.typeNames.map(t=>e.green(t)));return`Argument \`${e.bold(w)}\`: Invalid value provided. Expected ${t}, provided ${e.red(c.inferredType)}.`});break;case"InvalidArgumentValue":let v,E;h=t,f=r,v=h.argument.name,(E=f.arguments.getDeepSubSelectionValue(h.selectionPath)?.asObject())&&E.getDeepFieldValue(h.argumentPath)?.markAsError(),f.addErrorMessage(e=>{let t=[`Invalid value for argument \`${e.bold(v)}\``];if(h.underlyingError&&t.push(`: ${h.underlyingError}`),t.push("."),h.argument.typeNames.length>0){let r=iP("or",h.argument.typeNames.map(t=>e.green(t)));t.push(` Expected ${r}.`)}return t.join("")});break;case"ValueTooLarge":(function(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let t=n.getDeepField(e.argumentPath)?.value;t?.markAsError(),t instanceof im&&(i=t.text)}t.addErrorMessage(e=>{let t=["Unable to fit value"];return i&&t.push(e.red(i)),t.push(`into a 64-bit signed integer for field \`${e.bold(r)}\``),t.join(" ")})})(t,r);break;case"SomeFieldsMissing":(function(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let t=n.getDeepFieldValue(e.argumentPath)?.asObject();t&&iv(t,e.inputType)}t.addErrorMessage(t=>{let n=[`Argument \`${t.bold(r)}\` of type ${t.bold(e.inputType.name)} needs`];return 1===e.constraints.minFieldCount?e.constraints.requiredFields?n.push(`${t.green("at least one of")} ${iP("or",e.constraints.requiredFields.map(e=>`\`${t.bold(e)}\``))} arguments.`):n.push(`${t.green("at least one")} argument.`):n.push(`${t.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),n.push(ix(t)),n.join(" ")})})(t,r);break;case"TooManyFieldsGiven":(function(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let t=n.getDeepFieldValue(e.argumentPath)?.asObject();t&&(t.markAsError(),i=Object.keys(t.getFields()))}t.addErrorMessage(t=>{let n=[`Argument \`${t.bold(r)}\` of type ${t.bold(e.inputType.name)} needs`];return 1===e.constraints.minFieldCount&&1==e.constraints.maxFieldCount?n.push(`${t.green("exactly one")} argument,`):1==e.constraints.maxFieldCount?n.push(`${t.green("at most one")} argument,`):n.push(`${t.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),n.push(`but you provided ${iP("and",i.map(e=>t.red(e)))}. Please choose`),1===e.constraints.maxFieldCount?n.push("one."):n.push(`${e.constraints.maxFieldCount}.`),n.join(" ")})})(t,r);break;case"Union":let x;(x=function(e,t){if(0===e.length)return;let r=e[0];for(let n=1;n<e.length;n++)0>t(r,e[n])&&(r=e[n]);return r}(function(e){let t=new Map,r=[];for(let s of e){var n,i;if("InvalidArgumentType"!==s.kind){r.push(s);continue}let e=`${s.selectionPath.join(".")}:${s.argumentPath.join(".")}`,a=t.get(e);a?t.set(e,{...s,argument:{...s.argument,typeNames:(n=a.argument.typeNames,i=s.argument.typeNames,[...new Set(n.concat(i))])}}):t.set(e,s)}return r.push(...t.values()),r}(function e(t){return t.errors.flatMap(t=>"Union"===t.kind?e(t):[t])}(t)),(e,t)=>{let r=ie(e),n=ie(t);return r!==n?r-n:it(e)-it(t)}))?e(x,r,n):r.addErrorMessage(()=>"Unknown error");break;default:throw Error("not implemented: "+t.kind)}})(e,o,a);let{message:l,args:u}=ij(o,r);throw new rw(n5({message:l,callsite:n,originalMethod:i,showColors:"pretty"===r,callArguments:u}),{clientVersion:s})}function iU(e){return e.replace(/^./,e=>e.toLowerCase())}function iL(e,t,r){return r?rc(r,({needs:e,compute:r},n)=>{var i,s,a;let o;return{name:n,needs:e?Object.keys(e).filter(t=>e[t]):[],compute:(i=t,s=n,a=r,(o=i?.[s]?.compute)?e=>a({...e,[s]:o(e)}):a)}}):{}}var iG=class{constructor(e,t){this.computedFieldsCache=new nG,this.modelExtensionsCache=new nG,this.queryCallbacksCache=new nG,this.clientExtensions=nH(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions()),this.batchCallbacks=nH(()=>{let e=this.previous?.getAllBatchQueryCallbacks()??[],t=this.extension.query?.$__internalBatch;return t?e.concat(t):e}),this.extension=e,this.previous=t}getAllComputedFields(e){return this.computedFieldsCache.getOrCreate(e,()=>{var t,r,n;let i,s,a;return t=this.previous?.getAllComputedFields(e),r=this.extension,i=iU(e),r.result&&(r.result.$allModels||r.result[i])?(n={...t,...iL(r.name,t,r.result.$allModels),...iL(r.name,t,r.result[i])},s=new nG,a=(e,t)=>s.getOrCreate(e,()=>t.has(e)?[e]:(t.add(e),n[e]?n[e].needs.flatMap(e=>a(e,t)):[e])),rc(n,e=>({...e,needs:a(e.name,new Set)}))):t})}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(e){return this.modelExtensionsCache.getOrCreate(e,()=>{let t=iU(e);return this.extension.model&&(this.extension.model[t]||this.extension.model.$allModels)?{...this.previous?.getAllModelExtensions(e),...this.extension.model.$allModels,...this.extension.model[t]}:this.previous?.getAllModelExtensions(e)})}getAllQueryCallbacks(e,t){return this.queryCallbacksCache.getOrCreate(`${e}:${t}`,()=>{let r=this.previous?.getAllQueryCallbacks(e,t)??[],n=[],i=this.extension.query;return i&&(i[e]||i.$allModels||i[t]||i.$allOperations)?(void 0!==i[e]&&(void 0!==i[e][t]&&n.push(i[e][t]),void 0!==i[e].$allOperations&&n.push(i[e].$allOperations)),"$none"!==e&&void 0!==i.$allModels&&(void 0!==i.$allModels[t]&&n.push(i.$allModels[t]),void 0!==i.$allModels.$allOperations&&n.push(i.$allModels.$allOperations)),void 0!==i[t]&&n.push(i[t]),void 0!==i.$allOperations&&n.push(i.$allOperations),r.concat(n)):r})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},iB=class e{constructor(e){this.head=e}static empty(){return new e}static single(t){return new e(new iG(t))}isEmpty(){return void 0===this.head}append(t){return new e(new iG(t,this.head))}getAllComputedFields(e){return this.head?.getAllComputedFields(e)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(e){return this.head?.getAllModelExtensions(e)}getAllQueryCallbacks(e,t){return this.head?.getAllQueryCallbacks(e,t)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}},iH=class{constructor(e){this.name=e}};function iW(e){return new iH(e)}var iJ=Symbol(),iK=class{constructor(e){if(e!==iJ)throw Error("Skip instance can not be constructed directly")}ifUndefined(e){return void 0===e?iQ:e}},iQ=new iK(iJ);function iY(e){return e instanceof iK}var iz={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},iZ="explicitly `undefined` values are not allowed";function iX({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=iB.empty(),callsite:s,clientMethod:a,errorFormat:o,clientVersion:l,previewFeatures:u,globalOmit:c}){let d=new i2({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:s,extensions:i,selectionPath:[],argumentPath:[],originalMethod:a,errorFormat:o,clientVersion:l,previewFeatures:u,globalOmit:c});return{modelName:e,action:iz[t],query:function e({select:t,include:r,...n}={},i){var s,a,o,l,u,c,d;let h,f=n.omit;return delete n.omit,{arguments:i0(n,i),selection:(s=t,a=r,o=f,l=i,s?(a?l.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:l.getSelectionPath()}):o&&l.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:l.getSelectionPath()}),function(t,r){let n={},i=r.getComputedFields();for(let[s,a]of Object.entries(function(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let e of n.needs)r[e]=!0;return r}(t,i))){if(iY(a))continue;let t=r.nestSelection(s);i1(a,t);let o=r.findField(s);if(!(i?.[s]&&!o)){if(!1===a||void 0===a||iY(a)){n[s]=!1;continue}if(!0===a){o?.kind==="object"?n[s]=e({},t):n[s]=!0;continue}n[s]=e(a,t)}}return n}(s,l)):(u=l,c=a,d=o,h={},u.modelOrType&&!u.isRawAction()&&(h.$composites=!0,h.$scalars=!0),c&&function(t,r,n){for(let[i,s]of Object.entries(r)){if(iY(s))continue;let r=n.nestSelection(i);if(i1(s,r),!1===s||void 0===s){t[i]=!1;continue}let a=n.findField(i);if(a&&"object"!==a.kind&&n.throwValidationError({kind:"IncludeOnScalar",selectionPath:n.getSelectionPath().concat(i),outputType:n.getOutputTypeDescription()}),a){t[i]=e(!0===s?{}:s,r);continue}if(!0===s){t[i]=!0;continue}t[i]=e(s,r)}}(h,c,u),function(e,t,r){let n=r.getComputedFields();for(let[i,s]of Object.entries(function(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let e of n.needs)delete r[e];return r}({...r.getGlobalOmit(),...t},n))){if(iY(s))continue;i1(s,r.nestSelection(i));let t=r.findField(i);n?.[i]&&!t||(e[i]=!s)}}(h,d,u),h))}}(r,d)}}function i0(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],s=t.nestArgument(n);iY(i)||(void 0!==i?r[n]=function e(t,r){var n,i;if(null===t)return null;if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return t;if("bigint"==typeof t)return{$type:"BigInt",value:String(t)};if(nK(t)){if(nQ(t))return{$type:"DateTime",value:t.toISOString()};r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:r.getSelectionPath(),argumentPath:r.getArgumentPath(),argument:{name:r.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(t instanceof iH)return{$type:"Param",value:t.name};if(i_(t))return{$type:"FieldRef",value:{_ref:t.name,_container:t.modelName}};if(Array.isArray(t))return function(t,r){let n=[];for(let i=0;i<t.length;i++){let s=r.nestArgument(String(i)),a=t[i];if(void 0===a||iY(a)){let e=void 0===a?"undefined":"Prisma.skip";r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:s.getSelectionPath(),argumentPath:s.getArgumentPath(),argument:{name:`${r.getArgumentName()}[${i}]`,typeNames:[]},underlyingError:`Can not use \`${e}\` value within array. Use \`null\` or filter out \`${e}\` values`})}n.push(e(a,s))}return n}(t,r);if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:r,byteLength:n}=t;return{$type:"Bytes",value:Buffer.from(e,r,n).toString("base64")}}if("object"==typeof(n=t)&&null!==n&&!0===n.__prismaRawParameters__)return t.values;if(nY(t))return{$type:"Decimal",value:t.toFixed()};if(t instanceof iT){if(t!==iD.instances[t._getName()])throw Error("Invalid ObjectEnumValue");return{$type:"Enum",value:t._getName()}}return"object"==typeof(i=t)&&null!==i&&"function"==typeof i.toJSON?t.toJSON():"object"==typeof t?i0(t,r):void r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:r.getSelectionPath(),argumentPath:r.getArgumentPath(),argument:{name:r.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(t)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}(i,s):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:s.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:iZ}))}return r}function i1(e,t){void 0===e&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:iZ})}var i2=class e{constructor(e){this.params=e,this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}throwValidationError(e){iM({errors:[e],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(e=>({name:e.name,typeName:"boolean",isRelation:"object"===e.kind}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(e){return this.params.previewFeatures.includes(e)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(e){return this.modelOrType?.fields.find(t=>t.name===e)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[nB(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:rt(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function i4(e){if(!e._hasPreviewFlag("metrics"))throw new rw("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var i3=class{constructor(e){this._client=e}prometheus(e){return i4(this._client),this._client._engine.metrics({format:"prometheus",...e})}json(e){return i4(this._client),this._client._engine.metrics({format:"json",...e})}};function i7(e,t){let r=nH(()=>{var e;return{datamodel:{models:i6((e=t).models),enums:i6(e.enums),types:i6(e.types)}}});Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function i6(e){return Object.entries(e).map(([e,t])=>({name:e,...t}))}var i9=new WeakMap,i5="$$PrismaTypedSql",i8=class{constructor(e,t){i9.set(this,{sql:e,values:t}),Object.defineProperty(this,i5,{value:i5})}get sql(){return i9.get(this).sql}get values(){return i9.get(this).values}};function se(e){return(...t)=>new i8(e,t)}function st(e){return null!=e&&e[i5]===i5}var sr=f(w()),sn=r(16698),si=r(78474),ss=f(r(73024)),sa=f(r(76760)),so=class e{constructor(t,r){if(t.length-1!==r.length)throw 0===t.length?TypeError("Expected at least 1 string"):TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((t,r)=>t+(r instanceof e?r.values.length:1),0);this.values=Array(n),this.strings=Array(n+1),this.strings[0]=t[0];let i=0,s=0;for(;i<r.length;){let n=r[i++],a=t[i];if(n instanceof e){this.strings[s]+=n.strings[0];let e=0;for(;e<n.values.length;)this.values[s++]=n.values[e++],this.strings[s]=n.strings[e];this.strings[s]+=a}else this.values[s++]=n,this.strings[s]=a}}get sql(){let e=this.strings.length,t=1,r=this.strings[0];for(;t<e;)r+=`?${this.strings[t++]}`;return r}get statement(){let e=this.strings.length,t=1,r=this.strings[0];for(;t<e;)r+=`:${t}${this.strings[t++]}`;return r}get text(){let e=this.strings.length,t=1,r=this.strings[0];for(;t<e;)r+=`$${t}${this.strings[t++]}`;return r}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function sl(e,t=",",r="",n=""){if(0===e.length)throw TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new so([r,...Array(e.length-1).fill(t),n],e)}function su(e){return new so([e],[])}var sc=su("");function sd(e,...t){return new so(e,t)}function sh(e){return{getKeys:()=>Object.keys(e),getPropertyValue:t=>e[t]}}function sf(e,t){return{getKeys:()=>[e],getPropertyValue:()=>t()}}function sp(e){let t=new nG;return{getKeys:()=>e.getKeys(),getPropertyValue:r=>t.getOrCreate(r,()=>e.getPropertyValue(r)),getPropertyDescriptor:t=>e.getPropertyDescriptor?.(t)}}var sm={enumerable:!0,configurable:!0,writable:!0};function sg(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>sm,has:(e,r)=>t.has(r),set:(e,r,n)=>t.add(r)&&Reflect.set(e,r,n),ownKeys:()=>[...t]}}var sy=Symbol.for("nodejs.util.inspect.custom");function sw(e,t){let r=function(e){let t=new Map;for(let r of e)for(let e of r.getKeys())t.set(e,r);return t}(t),n=new Set,i=new Proxy(e,{get(e,t){if(n.has(t))return e[t];let i=r.get(t);return i?i.getPropertyValue(t):e[t]},has(e,t){if(n.has(t))return!0;let i=r.get(t);return i?i.has?.(t)??!0:Reflect.has(e,t)},ownKeys:e=>[...new Set([...sb(Reflect.ownKeys(e),r),...sb(Array.from(r.keys()),r),...n])],set:(e,t,i)=>r.get(t)?.getPropertyDescriptor?.(t)?.writable!==!1&&(n.add(t),Reflect.set(e,t,i)),getOwnPropertyDescriptor(e,t){let n=Reflect.getOwnPropertyDescriptor(e,t);if(n&&!n.configurable)return n;let i=r.get(t);return i?i.getPropertyDescriptor?{...sm,...i?.getPropertyDescriptor(t)}:sm:n},defineProperty:(e,t,r)=>(n.add(t),Reflect.defineProperty(e,t,r)),getPrototypeOf:()=>Object.prototype});return i[sy]=function(){let e={...this};return delete e[sy],e},i}function sb(e,t){return e.filter(e=>t.get(e)?.has?.(e)??!0)}function sv(e){return{getKeys:()=>e,has:()=>!1,getPropertyValue(){}}}function sE(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function sx({error:e,user_facing_error:t},r,n){var i,s;let a;return t.error_code?new rm((i=t,s=n,a=i.message,("postgresql"===s||"postgres"===s||"mysql"===s)&&"P2037"===i.error_code&&(a+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),a),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new ry(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}var sP="<unknown>",sS=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,s_=/\((\S*)(?::(\d+))(?::(\d+))\)/,sA=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,sN=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,sT=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,s$=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i,sR=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i,sO=class{getLocation(){return null}},sk=class{constructor(){this._error=Error()}getLocation(){let e=this._error.stack;if(!e)return null;let t=e.split(`
`).reduce(function(e,t){var r,n,i,s,a,o,l=function(e){var t=sS.exec(e);if(!t)return null;var r=t[2]&&0===t[2].indexOf("native"),n=t[2]&&0===t[2].indexOf("eval"),i=s_.exec(t[2]);return n&&null!=i&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||sP,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}(t)||(r=t,(n=sA.exec(r))?{file:n[2],methodName:n[1]||sP,arguments:[],lineNumber:+n[3],column:n[4]?+n[4]:null}:null)||function(e){var t=sN.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=sT.exec(t[3]);return r&&null!=n&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||sP,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}(t)||(i=t,(s=sR.exec(i))?{file:s[2],methodName:s[1]||sP,arguments:[],lineNumber:+s[3],column:s[4]?+s[4]:null}:null)||(a=t,(o=s$.exec(a))?{file:o[3],methodName:o[1]||sP,arguments:[],lineNumber:+o[4],column:o[5]?+o[5]:null}:null);return l&&e.push(l),e},[]).find(e=>{var t;if(!e.file)return!1;let r=(t=e.file,rr.default.sep===rr.default.posix.sep?t:t.split(rr.default.sep).join(rr.default.posix.sep));return"<anonymous>"!==r&&!r.includes("@prisma")&&!r.includes("/packages/client/src/runtime/")&&!r.endsWith("/runtime/binary.js")&&!r.endsWith("/runtime/library.js")&&!r.endsWith("/runtime/edge.js")&&!r.endsWith("/runtime/edge-esm.js")&&!r.startsWith("internal/")&&!e.methodName.includes("new ")&&!e.methodName.includes("getCallSite")&&!e.methodName.includes("Proxy.")&&e.methodName.split(".").length<4});return t&&t.file?{fileName:t.file,lineNumber:t.lineNumber,columnNumber:t.column}:null}};function sD(e){return"minimal"===e?"function"==typeof $EnabledCallSite&&"minimal"!==e?new $EnabledCallSite:new sO:new sk}var sI={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function sq(e={}){return Object.entries(function(e={}){return"boolean"==typeof e._count?{...e,_count:{_all:e._count}}:e}(e)).reduce((e,[t,r])=>(void 0!==sI[t]?e.select[t]={select:r}:e[t]=r,e),{select:{}})}function sC(e={}){return t=>("boolean"==typeof e._count&&(t._count=t._count._all),t)}function sF(e={}){let{select:t,...r}=e;return"object"==typeof t?sq({...r,_count:t}):sq({...r,_count:{_all:!0}})}function sV(e={}){let t=sq(e);if(Array.isArray(t.by))for(let e of t.by)"string"==typeof e&&(t.select[e]=!0);else"string"==typeof t.by&&(t.select[t.by]=!0);return t}var sj=e=>Array.isArray(e)?e:e.split("."),sM=(e,t)=>sj(t).reduce((e,t)=>e&&e[t],e),sU=(e,t,r)=>sj(t).reduceRight((t,r,n,i)=>Object.assign({},sM(e,i.slice(0,n)),{[r]:t}),r),sL=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],sG=["aggregate","count","groupBy"];function sB(e,t){var r,n,i,s;let a,o,l=e._extensions.getAllModelExtensions(t)??{};return sw({},[(r=e,a=iU(n=t),o=Object.keys(nX).concat("count"),{getKeys:()=>o,getPropertyValue(e){var t;let i=t=>i=>{let s=sD(r._errorFormat);return r._createPrismaPromise(o=>{let l={args:i,dataPath:[],action:e,model:n,clientMethod:`${a}.${e}`,jsModelName:a,transaction:o,callsite:s};return r._request({...l,...t})},{action:e,args:i,model:n})};return sL.includes(e)?function e(t,r,n,i,s,a){let o=t._runtimeDataModel.models[r].fields.reduce((e,t)=>({...e,[t.name]:t}),{});return l=>{var u,c;let d=sD(t._errorFormat),h=void 0===i||void 0===s?[]:[...s,"select",i],f=void 0===a?l??{}:sU(a,h,l||!0),p=n({dataPath:h,callsite:d})(f),m=(u=t,c=r,u._runtimeDataModel.models[c].fields.filter(e=>"object"===e.kind).map(e=>e.name));return new Proxy(p,{get:(r,i)=>m.includes(i)?e(t,o[i].type,n,i,h,f):r[i],...sg([...m,...Object.getOwnPropertyNames(p)])})}}(r,n,i):(t=e,sG.includes(t))?"aggregate"===e?e=>i({action:"aggregate",unpacker:sC(e),argsMapper:sq})(e):"count"===e?e=>i({action:"count",unpacker:function(e={}){return"object"==typeof e.select?t=>sC(e)(t)._count:t=>sC(e)(t)._count._all}(e),argsMapper:sF})(e):"groupBy"===e?e=>i({action:"groupBy",unpacker:function(e={}){return t=>("boolean"==typeof e?._count&&t.forEach(e=>{e._count=e._count._all}),t)}(e),argsMapper:sV})(e):void 0:i({})}}),(i=e,s=t,sp(sf("fields",()=>{let e;return new Proxy({},{get(t,r){if(r in t||"symbol"==typeof r)return t[r];let n=e[r];if(n)return new iS(s,r,n.type,n.isList,"enum"===n.kind)},...sg(Object.keys(e=function(e,t){let r={};for(let n of e)r[n[t]]=n;return r}(i._runtimeDataModel.models[s].fields.filter(e=>!e.relationName),"name")))})}))),sh(l),sf("name",()=>t),sf("$name",()=>t),sf("$parent",()=>e._appliedParent)])}var sH=Symbol();function sW(e){var t,r;let n,i,s,a;let o=[(n=[...new Set(Object.getOwnPropertyNames(Object.getPrototypeOf((t=e)._originalClient)))],{getKeys:()=>n,getPropertyValue:e=>t[e]}),(s=(i=Object.keys((r=e)._runtimeDataModel.models)).map(iU),a=[...new Set(i.concat(s))],sp({getKeys:()=>a,getPropertyValue(e){let t=e.replace(/^./,e=>e.toUpperCase());return void 0!==r._runtimeDataModel.models[t]?sB(r,t):void 0!==r._runtimeDataModel.models[e]?sB(r,e):void 0},getPropertyDescriptor(e){if(!s.includes(e))return{enumerable:!1}}})),sf(sH,()=>e),sf("$parent",()=>e._appliedParent)],l=e._extensions.getAllClientExtensions();return l&&o.push(sh(l)),sw(e,o)}function sJ(e){if("function"==typeof e)return e(this);if(e.client?.__AccelerateEngine){let t=e.client.__AccelerateEngine;this._originalClient._engine=new t(this._originalClient._accelerateEngineConfig)}return sW(Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}}))}function sK({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=sK({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let s=e(t,i,r)??t;return r.include&&sQ({includeOrSelect:r.include,result:s,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&sQ({includeOrSelect:r.select,result:s,parentModelName:i,runtimeDataModel:n,visitor:e}),s}function sQ({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[s,a]of Object.entries(e)){if(!a||null==t[s]||iY(a))continue;let e=n.models[r].fields.find(e=>e.name===s);if(!e||"object"!==e.kind||!e.relationName)continue;let o="object"==typeof a?a:{};t[s]=sK({visitor:i,result:t[s],args:o,modelName:e.type,runtimeDataModel:n})}}var sY=["$connect","$disconnect","$on","$transaction","$use","$extends"];function sz(e){if("object"!=typeof e||null==e||e instanceof iT||i_(e))return e;if(nY(e))return new nU(e.toFixed());if(nK(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=sz(e[t]);return r}if("object"==typeof e){let t={};for(let r in e)"__proto__"===r?Object.defineProperty(t,r,{value:sz(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=sz(e[r]);return t}rt(e,"Unknown value")}var sZ=e=>e;function sX(e=sZ,t=sZ){return r=>e(t(r))}var s0=ex("prisma:client"),s1={Vercel:"vercel","Netlify CI":"netlify"},s2=()=>globalThis.process?.release?.name==="node",s4=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,s3=()=>!!globalThis.Deno,s7=()=>"object"==typeof globalThis.Netlify,s6=()=>"object"==typeof globalThis.EdgeRuntime,s9=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers",s5={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function s8(){let e=[[s7,"netlify"],[s6,"edge-light"],[s9,"workerd"],[s3,"deno"],[s4,"bun"],[s2,"node"]].flatMap(e=>e[0]()?[e[1]]:[]).at(0)??"";return{id:e,prettyName:s5[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var ae=f(r(73024)),at=f(r(76760));function ar(e){let{runtimeBinaryTarget:t}=e;return`Add "${t}" to \`binaryTargets\` in the "schema.prisma" file and run \`prisma generate\` after saving it:

${function(e){let{generator:t,generatorBinaryTargets:r,runtimeBinaryTarget:n}=e,i=[...r,{fromEnvVar:null,value:n}];return String(new t1({...t,binaryTargets:i}))}(e)}`}function an(e){let{runtimeBinaryTarget:t}=e;return`Prisma Client could not locate the Query Engine for runtime "${t}".`}function ai(e){let{searchedLocations:t}=e;return`The following locations have been searched:
${[...new Set(t)].map(e=>`  ${e}`).join(`
`)}`}function as(e){return`We would appreciate if you could take the time to share some information with us.
Please help us by answering a few questions: https://pris.ly/${e}`}function aa(e){let{errorStack:t}=e;return t?.match(/\/\.next|\/next@|\/next\//)?`

We detected that you are using Next.js, learn how to fix this: https://pris.ly/d/engine-not-found-nextjs.`:""}var ao=ex("prisma:client:engines:resolveEnginePath"),al=()=>RegExp("runtime[\\\\/]library\\.m?js$");async function au(e,t){let r={binary:process.env.PRISMA_QUERY_ENGINE_BINARY,library:process.env.PRISMA_QUERY_ENGINE_LIBRARY}[e]??t.prismaPath;if(void 0!==r)return r;let{enginePath:n,searchedLocations:i}=await ac(e,t);if(ao("enginePath",n),void 0!==n&&"binary"===e&&function(e){if("win32"===process.platform)return;let t=tz.default.statSync(e),r=73|t.mode;if(t.mode===r){tZ(`Execution permissions of ${e} are fine`);return}let n=r.toString(8).slice(-3);tZ(`Have to call chmodPlusX on ${e}`),tz.default.chmodSync(e,n)}(n),void 0!==n)return t.prismaPath=n;let s=await te(),a=t.generator?.binaryTargets??[],o=a.some(e=>e.native),l=!a.some(e=>e.value===s),u=null===__filename.match(al()),c={searchedLocations:i,generatorBinaryTargets:a,generator:t.generator,runtimeBinaryTarget:s,queryEngineName:ad(e,s),expectedLocation:at.default.relative(process.cwd(),t.dirname),errorStack:Error().stack},d;throw new rp(o&&l?function(e){let{runtimeBinaryTarget:t,generatorBinaryTargets:r}=e,n=r.find(e=>e.native);return`${an(e)}

This happened because Prisma Client was generated for "${n?.value??"unknown"}", but the actual deployment required "${t}".
${ar(e)}

${ai(e)}`}(c):l?function(e){let{runtimeBinaryTarget:t}=e;return`${an(e)}

This happened because \`binaryTargets\` have been pinned, but the actual deployment also required "${t}".
${ar(e)}

${ai(e)}`}(c):u?function(e){let{queryEngineName:t}=e;return`${an(e)}${aa(e)}

This is likely caused by a bundler that has not copied "${t}" next to the resulting bundle.
Ensure that "${t}" has been copied next to the bundle or in "${e.expectedLocation}".

${as("engine-not-found-bundler-investigation")}

${ai(e)}`}(c):function(e){let{queryEngineName:t}=e;return`${an(e)}${aa(e)}

This is likely caused by tooling that has not copied "${t}" to the deployment folder.
Ensure that you ran \`prisma generate\` and that "${t}" has been copied to "${e.expectedLocation}".

${as("engine-not-found-tooling-investigation")}

${ai(e)}`}(c),t.clientVersion)}async function ac(e,t){let r=await te(),n=[],i=[t.dirname,at.default.resolve(__dirname,".."),t.generator?.output?.value??__dirname,at.default.resolve(__dirname,"../../../.prisma/client"),"/tmp/prisma-engines",t.cwd];for(let t of(__filename.includes("resolveEnginePath")&&i.push(tY.default.join(__dirname,"../")),i)){let i=ad(e,r),s=at.default.join(t,i);if(n.push(t),ae.default.existsSync(s))return{enginePath:s,searchedLocations:n}}return{enginePath:void 0,searchedLocations:n}}function ad(e,t){return"library"===e?t.includes("windows")?`query_engine-${t}.dll.node`:t.includes("darwin")?`${eS}-${t}.dylib.node`:`${eS}-${t}.so.node`:`query-engine-${t}${"windows"===t?".exe":""}`}var ah=f(P()),af=f(A());function ap(e){return"DriverAdapterError"===e.name&&"object"==typeof e.cause}function am(e){return{ok:!0,value:e,map:t=>am(t(e)),flatMap:t=>t(e)}}function ag(e){return{ok:!1,error:e,map:()=>ag(e),flatMap:()=>ag(e)}}var ay=ex("driver-adapter-utils"),aw=class{consumeError(e){return this.registeredErrors[e]}registerNewError(e){let t=0;for(;void 0!==this.registeredErrors[t];)t++;return this.registeredErrors[t]={error:e},t}constructor(){this.registeredErrors=[]}},ab=(e,t=new aw)=>{var r,n;let i={adapterName:e.adapterName,errorRegistry:t,queryRaw:aE(t,e.queryRaw.bind(e)),executeRaw:aE(t,e.executeRaw.bind(e)),executeScript:aE(t,e.executeScript.bind(e)),dispose:aE(t,e.dispose.bind(e)),provider:e.provider,startTransaction:async(...r)=>(await aE(t,e.startTransaction.bind(e))(...r)).map(e=>av(t,e))};return e.getConnectionInfo&&(r=t,n=e.getConnectionInfo.bind(e),i.getConnectionInfo=(...e)=>{try{return am(n(...e))}catch(e){if(ay("[error@wrapSync]",e),ap(e))return ag(e.cause);return ag({kind:"GenericJs",id:r.registerNewError(e)})}}),i},av=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:aE(e,t.queryRaw.bind(t)),executeRaw:aE(e,t.executeRaw.bind(t)),commit:aE(e,t.commit.bind(t)),rollback:aE(e,t.rollback.bind(t))});function aE(e,t){return async(...r)=>{try{return am(await t(...r))}catch(t){if(ay("[error@wrapAsync]",t),ap(t))return ag(t.cause);return ag({kind:"GenericJs",id:e.registerNewError(t)})}}}function ax({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,s=Object.keys(e)[0],a=e[s]?.url,o=t[s]?.url;if(void 0===s?i=void 0:o?i=o:a?.value?i=a.value:a?.fromEnvVar&&(i=r[a.fromEnvVar]),a?.fromEnvVar!==void 0&&void 0===i)throw new rp(`error: Environment variable not found: ${a.fromEnvVar}.`,n);if(void 0===i)throw new rp("error: Missing URL environment variable, value, or override.",n);return i}var aP=class extends Error{constructor(e,t){super(e),this.clientVersion=t.clientVersion,this.cause=t.cause}get[Symbol.toStringTag](){return this.name}},aS=class extends aP{constructor(e,t){super(e,t),this.isRetryable=t.isRetryable??!0}};function a_(e,t){return{...e,isRetryable:t}}var aA=class extends aS{constructor(e){super("This request must be retried",a_(e,!0)),this.name="ForcedRetryError",this.code="P5001"}};rd(aA,"ForcedRetryError");var aN=class extends aS{constructor(e,t){super(e,a_(t,!1)),this.name="InvalidDatasourceError",this.code="P6001"}};rd(aN,"InvalidDatasourceError");var aT=class extends aS{constructor(e,t){super(e,a_(t,!1)),this.name="NotImplementedYetError",this.code="P5004"}};rd(aT,"NotImplementedYetError");var a$=class extends aS{constructor(e,t){super(e,t),this.response=t.response;let r=this.response.headers.get("prisma-request-id");if(r){let e=`(The request id was: ${r})`;this.message=this.message+" "+e}}},aR=class extends a${constructor(e){super("Schema needs to be uploaded",a_(e,!0)),this.name="SchemaMissingError",this.code="P5005"}};rd(aR,"SchemaMissingError");var aO="This request could not be understood by the server",ak=class extends a${constructor(e,t,r){super(t||aO,a_(e,!1)),this.name="BadRequestError",this.code="P5000",r&&(this.code=r)}};rd(ak,"BadRequestError");var aD=class extends a${constructor(e,t){super("Engine not started: healthcheck timeout",a_(e,!0)),this.name="HealthcheckTimeoutError",this.code="P5013",this.logs=t}};rd(aD,"HealthcheckTimeoutError");var aI=class extends a${constructor(e,t,r){super(t,a_(e,!0)),this.name="EngineStartupError",this.code="P5014",this.logs=r}};rd(aI,"EngineStartupError");var aq=class extends a${constructor(e){super("Engine version is not supported",a_(e,!1)),this.name="EngineVersionNotSupportedError",this.code="P5012"}};rd(aq,"EngineVersionNotSupportedError");var aC="Request timed out",aF=class extends a${constructor(e,t=aC){super(t,a_(e,!1)),this.name="GatewayTimeoutError",this.code="P5009"}};rd(aF,"GatewayTimeoutError");var aV=class extends a${constructor(e,t="Interactive transaction error"){super(t,a_(e,!1)),this.name="InteractiveTransactionError",this.code="P5015"}};rd(aV,"InteractiveTransactionError");var aj=class extends a${constructor(e,t="Request parameters are invalid"){super(t,a_(e,!1)),this.name="InvalidRequestError",this.code="P5011"}};rd(aj,"InvalidRequestError");var aM="Requested resource does not exist",aU=class extends a${constructor(e,t=aM){super(t,a_(e,!1)),this.name="NotFoundError",this.code="P5003"}};rd(aU,"NotFoundError");var aL="Unknown server error",aG=class extends a${constructor(e,t,r){super(t||aL,a_(e,!0)),this.name="ServerError",this.code="P5006",this.logs=r}};rd(aG,"ServerError");var aB="Unauthorized, check your connection string",aH=class extends a${constructor(e,t=aB){super(t,a_(e,!1)),this.name="UnauthorizedError",this.code="P5007"}};rd(aH,"UnauthorizedError");var aW="Usage exceeded, retry again later",aJ=class extends a${constructor(e,t=aW){super(t,a_(e,!0)),this.name="UsageExceededError",this.code="P5008"}};async function aK(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let e=JSON.parse(t);if("string"==typeof e){if("InternalDataProxyError"===e)return{type:"DataProxyError",body:e};return{type:"UnknownTextError",body:e}}if("object"==typeof e&&null!==e){if("is_panic"in e&&"message"in e&&"error_code"in e)return{type:"QueryEngineError",body:e};if("EngineNotStarted"in e||"InteractiveTransactionMisrouted"in e||"InvalidRequestError"in e){let t=Object.values(e)[0].reason;return"string"!=typeof t||["SchemaMissing","EngineVersionNotSupported"].includes(t)?{type:"DataProxyError",body:e}:{type:"UnknownJsonError",body:e}}}return{type:"UnknownJsonError",body:e}}catch{return""===t?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function aQ(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await aK(e);if("QueryEngineError"===n.type)throw new rm(n.body.message,{code:n.body.error_code,clientVersion:t});if("DataProxyError"===n.type){if("InternalDataProxyError"===n.body)throw new aG(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if("SchemaMissing"===n.body.EngineNotStarted.reason)return new aR(r);if("EngineVersionNotSupported"===n.body.EngineNotStarted.reason)throw new aq(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:e,logs:t}=n.body.EngineNotStarted.reason.EngineStartupError;throw new aI(r,e,t)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:e,error_code:r}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new rp(e,t,r)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:e}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new aD(r,e)}}if("InteractiveTransactionMisrouted"in n.body)throw new aV(r,{IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"}[n.body.InteractiveTransactionMisrouted.reason]);if("InvalidRequestError"in n.body)throw new aj(r,n.body.InvalidRequestError.reason)}if(401===e.status||403===e.status)throw new aH(r,aY(aB,n));if(404===e.status)return new aU(r,aY(aM,n));if(429===e.status)throw new aJ(r,aY(aW,n));if(504===e.status)throw new aF(r,aY(aC,n));if(e.status>=500)throw new aG(r,aY(aL,n));if(e.status>=400)throw new ak(r,aY(aO,n))}function aY(e,t){return"EmptyError"===t.type?e:`${e}: ${JSON.stringify(t)}`}rd(aJ,"UsageExceededError");var az="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function aZ(e){return new Date(1e3*e[0]+e[1]/1e6)}var aX={"@prisma/engines-version":"6.7.0-36.3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"},a0=class extends aS{constructor(e,t){super(`Cannot fetch data from service:
${e}`,a_(t,!0)),this.name="RequestError",this.code="P5010"}};async function a1(e,t,r=e=>e){let{clientVersion:n,...i}=t,s=r(fetch);try{return await s(e,i)}catch(e){throw new a0(e.message??"Unknown error",{clientVersion:n,cause:e})}}rd(a0,"RequestError");var a2=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,a4=ex("prisma:client:dataproxyEngine");async function a3(e,t){let r=aX["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&"0.0.0"!==n&&"in-memory"!==n)return n;let[i,s]=n?.split("-")??[];if(void 0===s&&a2.test(i))return i;if(void 0!==s||"0.0.0"===n||"in-memory"===n){var a;let t;if(e.startsWith("localhost")||e.startsWith("127.0.0.1"))return"0.0.0";let[i]=r.split("-")??[],[s,o,l]=i.split("."),u=(a=`<=${s}.${o}.${l}`,encodeURI(`https://unpkg.com/prisma@${a}/package.json`)),c=await a1(u,{clientVersion:n});if(!c.ok)throw Error(`Failed to fetch stable Prisma version, unpkg.com status ${c.status} ${c.statusText}, response body: ${await c.text()||"<empty body>"}`);let d=await c.text();a4("length of body fetched from unpkg.com",d.length);try{t=JSON.parse(d)}catch(e){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),e}return t.version}throw new aT("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function a7(e,t){let r=await a3(e,t);return a4("version",r),r}var a6,a9=ex("prisma:client:dataproxyEngine"),a5=class{constructor({apiKey:e,tracingHelper:t,logLevel:r,logQueries:n,engineHash:i}){this.apiKey=e,this.tracingHelper=t,this.logLevel=r,this.logQueries=n,this.engineHash=i}build({traceparent:e,interactiveTransaction:t}={}){let r={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(r.traceparent=e??this.tracingHelper.getTraceParent()),t&&(r["X-transaction-id"]=t.id);let n=this.buildCaptureSettings();return n.length>0&&(r["X-capture-telemetry"]=n.join(", ")),r}buildCaptureSettings(){let e=[];return this.tracingHelper.isEnabled()&&e.push("tracing"),this.logLevel&&e.push(this.logLevel),this.logQueries&&e.push("query"),e}},a8=class{constructor(e){this.name="DataProxyEngine",function(e){if(e.generator?.previewFeatures.some(e=>e.toLowerCase().includes("metrics")))throw new rp("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}(e),this.config=e,this.env={...e.env,..."u">typeof process?process.env:{}},this.inlineSchema=function(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,s=n-i,a,o,l,u,c;for(let e=0;e<s;e+=3)a=(0xfc0000&(c=t[e]<<16|t[e+1]<<8|t[e+2]))>>18,o=(258048&c)>>12,l=(4032&c)>>6,u=63&c,r+=az[a]+az[o]+az[l]+az[u];return 1==i?(a=(252&(c=t[s]))>>2,o=(3&c)<<4,r+=az[a]+az[o]+"=="):2==i&&(a=(64512&(c=t[s]<<8|t[s+1]))>>10,o=(1008&c)>>4,l=(15&c)<<2,r+=az[a]+az[o]+az[l]+"="),r}(e.inlineSchema),this.inlineDatasources=e.inlineDatasources,this.inlineSchemaHash=e.inlineSchemaHash,this.clientVersion=e.clientVersion,this.engineHash=e.engineVersion,this.logEmitter=e.logEmitter,this.tracingHelper=e.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){void 0!==this.startPromise&&await this.startPromise,this.startPromise=(async()=>{let[e,t]=this.extractHostAndApiKey();this.host=e,this.headerBuilder=new a5({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await a7(e,this.config),a9("host",this.host)})(),await this.startPromise}async stop(){}propagateResponseExtensions(e){e?.logs?.length&&e.logs.forEach(e=>{switch(e.level){case"debug":case"trace":a9(e);break;case"error":case"warn":case"info":this.logEmitter.emit(e.level,{timestamp:aZ(e.timestamp),message:e.attributes.message??"",target:e.target});break;case"query":this.logEmitter.emit("query",{query:e.attributes.query??"",timestamp:aZ(e.timestamp),duration:e.attributes.duration_ms??0,params:e.attributes.params??"",target:e.target});break;default:e.level}}),e?.traces?.length&&this.tracingHelper.dispatchEngineSpans(e.traces)}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the remote query engine')}async url(e){return await this.start(),`https://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${e}`}async uploadSchema(){return this.tracingHelper.runInChildSpan({name:"schemaUpload",internal:!0},async()=>{let e=await a1(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});e.ok||a9("schema response status",e.status);let t=await aQ(e,this.clientVersion);if(t)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${t.message}`,timestamp:new Date,target:""}),t;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(e,{traceparent:t,interactiveTransaction:r,customDataProxyFetch:n}){return this.requestInternal({body:e,traceparent:t,interactiveTransaction:r,customDataProxyFetch:n})}async requestBatch(e,{traceparent:t,transaction:r,customDataProxyFetch:n}){let i=r?.kind==="itx"?r.options:void 0,s=sE(e,r);return(await this.requestInternal({body:s,customDataProxyFetch:n,interactiveTransaction:i,traceparent:t})).map(e=>(e.extensions&&this.propagateResponseExtensions(e.extensions),"errors"in e?this.convertProtocolErrorsToClientError(e.errors):e))}requestInternal({body:e,traceparent:t,customDataProxyFetch:r,interactiveTransaction:n}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:i})=>{let s=n?`${n.payload.endpoint}/graphql`:await this.url("graphql");i(s);let a=await a1(s,{method:"POST",headers:this.headerBuilder.build({traceparent:t,interactiveTransaction:n}),body:JSON.stringify(e),clientVersion:this.clientVersion},r);a.ok||a9("graphql response status",a.status),await this.handleError(await aQ(a,this.clientVersion));let o=await a.json();if(o.extensions&&this.propagateResponseExtensions(o.extensions),"errors"in o)throw this.convertProtocolErrorsToClientError(o.errors);return"batchResult"in o?o.batchResult:o}})}async transaction(e,t,r){return this.withRetry({actionGerund:`${{start:"starting",commit:"committing",rollback:"rolling back"}[e]} transaction`,callback:async({logHttpCall:n})=>{if("start"===e){let e=JSON.stringify({max_wait:r.maxWait,timeout:r.timeout,isolation_level:r.isolationLevel}),i=await this.url("transaction/start");n(i);let s=await a1(i,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),body:e,clientVersion:this.clientVersion});await this.handleError(await aQ(s,this.clientVersion));let a=await s.json(),{extensions:o}=a;return o&&this.propagateResponseExtensions(o),{id:a.id,payload:{endpoint:a["data-proxy"].endpoint}}}{let i=`${r.payload.endpoint}/${e}`;n(i);let s=await a1(i,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),clientVersion:this.clientVersion});await this.handleError(await aQ(s,this.clientVersion));let{extensions:a}=await s.json();a&&this.propagateResponseExtensions(a);return}}})}extractHostAndApiKey(){let e={clientVersion:this.clientVersion},t=Object.keys(this.inlineDatasources)[0],r=ax({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),n;try{n=new URL(r)}catch{throw new aN(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,e)}let{protocol:i,host:s,searchParams:a}=n;if("prisma:"!==i&&i!==tX)throw new aN(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,e);let o=a.get("api_key");if(null===o||o.length<1)throw new aN(`Error validating datasource \`${t}\`: the URL must contain a valid API key`,e);return[s,o]}metrics(){throw new aT("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(e){for(let t=0;;t++){let r=e=>{this.logEmitter.emit("info",{message:`Calling ${e} (n=${t})`,timestamp:new Date,target:""})};try{return await e.callback({logHttpCall:r})}catch(n){if(!(n instanceof aS)||!n.isRetryable)throw n;if(t>=3)throw n instanceof aA?n.cause:n;this.logEmitter.emit("warn",{message:`Attempt ${t+1}/3 failed for ${e.actionGerund}: ${n.message??"(unknown)"}`,timestamp:new Date,target:""});let r=await function(e){let t=50*Math.pow(2,e),r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(e=>setTimeout(()=>e(n),n))}(t);this.logEmitter.emit("warn",{message:`Retrying after ${r}ms`,timestamp:new Date,target:""})}}}async handleError(e){if(e instanceof aR)throw await this.uploadSchema(),new aA({clientVersion:this.clientVersion,cause:e});if(e)throw e}convertProtocolErrorsToClientError(e){return 1===e.length?sx(e[0],this.config.clientVersion,this.config.activeProvider):new ry(JSON.stringify(e),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw Error("Method not implemented.")}},oe=f(r(48161)),ot=f(r(76760)),or=Symbol("PrismaLibraryEngineCache"),on={async loadLibrary(e){let t=await tt(),r=await au("library",e);try{return e.tracingHelper.runInChildSpan({name:"loadLibrary",internal:!0},()=>(function(e){let t;let r=(void 0===(t=globalThis)[or]&&(t[or]={}),t[or]);if(void 0!==r[e])return r[e];let n=ot.default.toNamespacedPath(e),i={exports:{}},s=0;return"win32"!==process.platform&&(s=oe.default.constants.dlopen.RTLD_LAZY|oe.default.constants.dlopen.RTLD_DEEPBIND),process.dlopen(i,n,s),r[e]=i.exports,i.exports})(r))}catch(d){var n,i;let s,a,o,l,u,c;throw new rp((s=(n={e:d,platformInfo:t,id:r}).e,a=e=>`Prisma cannot find the required \`${e}\` system library in your system`,o=s.message.includes("cannot open shared object file"),l=`Please refer to the documentation about Prisma's system requirements: ${tJ(i="https://pris.ly/d/system-requirements",i,{fallback:K})}`,u=`Unable to require(\`${W(n.id)}\`).`,c=eK({message:s.message,code:s.code}).with({code:"ENOENT"},()=>"File does not exist.").when(({message:e})=>o&&e.includes("libz"),()=>`${a("libz")}. Please install it and try again.`).when(({message:e})=>o&&e.includes("libgcc_s"),()=>`${a("libgcc_s")}. Please install it and try again.`).when(({message:e})=>o&&e.includes("libssl"),()=>{let e=n.platformInfo.libssl?`openssl-${n.platformInfo.libssl}`:"openssl";return`${a("libssl")}. Please install ${e} and try again.`}).when(({message:e})=>e.includes("GLIBC"),()=>`Prisma has detected an incompatible version of the \`glibc\` C standard library installed in your system. This probably means your system may be too old to run Prisma. ${l}`).when(({message:e})=>"linux"===n.platformInfo.platform&&e.includes("symbol not found"),()=>`The Prisma engines are not compatible with your system ${n.platformInfo.originalDistro} on (${n.platformInfo.archFromUname}) which uses the \`${n.platformInfo.binaryTarget}\` binaryTarget by default. ${l}`).otherwise(()=>`The Prisma engines do not seem to be compatible with your system. ${l}`),`${u}
${c}

Details: ${s.message}`),e.clientVersion)}}},oi={async loadLibrary(e){let{clientVersion:t,adapter:r,engineWasm:n}=e;if(void 0===r)throw new rp(`The \`adapter\` option for \`PrismaClient\` is required in this context (${s8().prettyName})`,t);if(void 0===n)throw new rp("WASM engine was unexpectedly `undefined`",t);return void 0===a6&&(a6=(async()=>{let e=await n.getRuntime(),r=await n.getQueryEngineWasmModule();if(null==r)throw new rp("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let i=new WebAssembly.Instance(r,{"./query_engine_bg.js":e}),s=i.exports.__wbindgen_start;return e.__wbg_set_wasm(i.exports),s(),e.QueryEngine})()),{debugPanic:()=>Promise.reject("{}"),dmmf:()=>Promise.resolve("{}"),version:()=>({commit:"unknown",version:"unknown"}),QueryEngine:await a6}}},os=ex("prisma:client:libraryEngine"),oa=["darwin","darwin-arm64","debian-openssl-1.0.x","debian-openssl-1.1.x","debian-openssl-3.0.x","rhel-openssl-1.0.x","rhel-openssl-1.1.x","rhel-openssl-3.0.x","linux-arm64-openssl-1.1.x","linux-arm64-openssl-1.0.x","linux-arm64-openssl-3.0.x","linux-arm-openssl-1.1.x","linux-arm-openssl-1.0.x","linux-arm-openssl-3.0.x","linux-musl","linux-musl-openssl-3.0.x","linux-musl-arm64-openssl-1.1.x","linux-musl-arm64-openssl-3.0.x","linux-nixos","linux-static-x64","linux-static-arm64","windows","freebsd11","freebsd12","freebsd13","freebsd14","freebsd15","openbsd","netbsd","arm","native"],oo=1n,ol=class{constructor(e,t){this.name="LibraryEngine",this.libraryLoader=t??on,void 0!==e.engineWasm&&(this.libraryLoader=t??oi),this.config=e,this.libraryStarted=!1,this.logQueries=e.logQueries??!1,this.logLevel=e.logLevel??"error",this.logEmitter=e.logEmitter,this.datamodel=e.inlineSchema,this.tracingHelper=e.tracingHelper,e.enableDebugLogs&&(this.logLevel="debug");let r=Object.keys(e.overrideDatasources)[0],n=e.overrideDatasources[r]?.url;void 0!==r&&void 0!==n&&(this.datasourceOverrides={[r]:n}),this.libraryInstantiationPromise=this.instantiateLibrary()}wrapEngine(e){return{applyPendingMigrations:e.applyPendingMigrations?.bind(e),commitTransaction:this.withRequestId(e.commitTransaction.bind(e)),connect:this.withRequestId(e.connect.bind(e)),disconnect:this.withRequestId(e.disconnect.bind(e)),metrics:e.metrics?.bind(e),query:this.withRequestId(e.query.bind(e)),rollbackTransaction:this.withRequestId(e.rollbackTransaction.bind(e)),sdlSchema:e.sdlSchema?.bind(e),startTransaction:this.withRequestId(e.startTransaction.bind(e)),trace:e.trace.bind(e)}}withRequestId(e){return async(...t)=>{let r;let n=(r=oo++,oo>0xffffffffffffffffn&&(oo=1n),r).toString();try{return await e(...t,n)}finally{if(this.tracingHelper.isEnabled()){let e=await this.engine?.trace(n);if(e){let t=JSON.parse(e);this.tracingHelper.dispatchEngineSpans(t.spans)}}}}}async applyPendingMigrations(){throw Error("Cannot call this method from this type of engine instance")}async transaction(e,t,r){var n;await this.start();let i=await this.adapterPromise,s=JSON.stringify(t),a;if("start"===e){let e=JSON.stringify({max_wait:r.maxWait,timeout:r.timeout,isolation_level:r.isolationLevel});a=await this.engine?.startTransaction(e,s)}else"commit"===e?a=await this.engine?.commitTransaction(r.id,s):"rollback"===e&&(a=await this.engine?.rollbackTransaction(r.id,s));let o=this.parseEngineResponse(a);if("object"==typeof(n=o)&&null!==n&&void 0!==n.error_code){let e=this.getExternalAdapterError(o,i?.errorRegistry);throw e?e.error:new rm(o.message,{code:o.error_code,clientVersion:this.config.clientVersion,meta:o.meta})}if("string"==typeof o.message)throw new ry(o.message,{clientVersion:this.config.clientVersion});return o}async instantiateLibrary(){if(os("internalSetup"),this.libraryInstantiationPromise)return this.libraryInstantiationPromise;(function(){let e=process.env.PRISMA_QUERY_ENGINE_LIBRARY;if(!(e&&eP.default.existsSync(e))&&"ia32"===process.arch)throw Error('The default query engine type (Node-API, "library") is currently not supported for 32bit Node. Please set `engineType = "binary"` in the "generator" block of your "schema.prisma" file (or use the environment variables "PRISMA_CLIENT_ENGINE_TYPE=binary" and/or "PRISMA_CLI_QUERY_ENGINE_TYPE=binary".)')})(),this.binaryTarget=await this.getCurrentBinaryTarget(),await this.tracingHelper.runInChildSpan("load_engine",()=>this.loadEngine()),this.version()}async getCurrentBinaryTarget(){{if(this.binaryTarget)return this.binaryTarget;let e=await this.tracingHelper.runInChildSpan("detect_platform",()=>te());if(!oa.includes(e))throw new rp(`Unknown ${X("PRISMA_QUERY_ENGINE_LIBRARY")} ${X(H(e))}. Possible binaryTargets: ${ee(oa.join(", "))} or a path to the query engine library.
You may have to run ${ee("prisma generate")} for your changes to take effect.`,this.config.clientVersion);return e}}parseEngineResponse(e){if(!e)throw new ry("Response from the Engine was empty",{clientVersion:this.config.clientVersion});try{return JSON.parse(e)}catch{throw new ry("Unable to JSON.parse response from engine",{clientVersion:this.config.clientVersion})}}async loadEngine(){if(!this.engine){this.QueryEngineConstructor||(this.library=await this.libraryLoader.loadLibrary(this.config),this.QueryEngineConstructor=this.library.QueryEngine);try{let e=new WeakRef(this);this.adapterPromise||(this.adapterPromise=this.config.adapter?.connect()?.then(ab));let t=await this.adapterPromise;t&&os("Using driver adapter: %O",t),this.engine=this.wrapEngine(new this.QueryEngineConstructor({datamodel:this.datamodel,env:process.env,logQueries:this.config.logQueries??!1,ignoreEnvVarErrors:!0,datasourceOverrides:this.datasourceOverrides??{},logLevel:this.logLevel,configDir:this.config.cwd,engineProtocol:"json",enableTracing:this.tracingHelper.isEnabled()},t=>{e.deref()?.logger(t)},t))}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new rp(e.message,this.config.clientVersion,e.error_code)}}}logger(e){let t=this.parseEngineResponse(e);t&&(t.level=t?.level.toLowerCase()??"unknown","query"===t.item_type&&"query"in t?this.logEmitter.emit("query",{timestamp:new Date,query:t.query,params:t.params,duration:Number(t.duration_ms),target:t.module_path}):"level"in t&&"error"===t.level&&"PANIC"===t.message?this.loggerRustPanic=new rg(ou(this,`${t.message}: ${t.reason} in ${t.file}:${t.line}:${t.column}`),this.config.clientVersion):this.logEmitter.emit(t.level,{timestamp:new Date,message:t.message,target:t.module_path}))}parseInitError(e){try{return JSON.parse(e)}catch{}return e}parseRequestError(e){try{return JSON.parse(e)}catch{}return e}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){if(await this.libraryInstantiationPromise,await this.libraryStoppingPromise,this.libraryStartingPromise)return os(`library already starting, this.libraryStarted: ${this.libraryStarted}`),this.libraryStartingPromise;if(this.libraryStarted)return;let e=async()=>{os("library starting");try{let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.connect(JSON.stringify(e)),this.libraryStarted=!0,os("library started")}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new rp(e.message,this.config.clientVersion,e.error_code)}finally{this.libraryStartingPromise=void 0}};return this.libraryStartingPromise=this.tracingHelper.runInChildSpan("connect",e),this.libraryStartingPromise}async stop(){if(await this.libraryInstantiationPromise,await this.libraryStartingPromise,await this.executingQueryPromise,this.libraryStoppingPromise)return os("library is already stopping"),this.libraryStoppingPromise;if(!this.libraryStarted)return;let e=async()=>{await new Promise(e=>setTimeout(e,5)),os("library stopping");let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.disconnect(JSON.stringify(e)),this.libraryStarted=!1,this.libraryStoppingPromise=void 0,await (await this.adapterPromise)?.dispose(),this.adapterPromise=void 0,os("library stopped")};return this.libraryStoppingPromise=this.tracingHelper.runInChildSpan("disconnect",e),this.libraryStoppingPromise}version(){return this.versionInfo=this.library?.version(),this.versionInfo?.version??"unknown"}debugPanic(e){return this.library?.debugPanic(e)}async request(e,{traceparent:t,interactiveTransaction:r}){os(`sending request, this.libraryStarted: ${this.libraryStarted}`);let n=JSON.stringify({traceparent:t}),i=JSON.stringify(e);try{await this.start();let e=await this.adapterPromise;this.executingQueryPromise=this.engine?.query(i,n,r?.id),this.lastQuery=i;let t=this.parseEngineResponse(await this.executingQueryPromise);if(t.errors)throw 1===t.errors.length?this.buildQueryError(t.errors[0],e?.errorRegistry):new ry(JSON.stringify(t.errors),{clientVersion:this.config.clientVersion});if(this.loggerRustPanic)throw this.loggerRustPanic;return{data:t}}catch(t){if(t instanceof rp)throw t;if("GenericFailure"===t.code&&t.message?.startsWith("PANIC:"))throw new rg(ou(this,t.message),this.config.clientVersion);let e=this.parseRequestError(t.message);throw"string"==typeof e?t:new ry(`${e.message}
${e.backtrace}`,{clientVersion:this.config.clientVersion})}}async requestBatch(e,{transaction:t,traceparent:r}){os("requestBatch");let n=sE(e,t);await this.start();let i=await this.adapterPromise;this.lastQuery=JSON.stringify(n),this.executingQueryPromise=this.engine.query(this.lastQuery,JSON.stringify({traceparent:r}),function(e){if(e?.kind==="itx")return e.options.id}(t));let s=await this.executingQueryPromise,a=this.parseEngineResponse(s);if(a.errors)throw 1===a.errors.length?this.buildQueryError(a.errors[0],i?.errorRegistry):new ry(JSON.stringify(a.errors),{clientVersion:this.config.clientVersion});let{batchResult:o,errors:l}=a;if(Array.isArray(o))return o.map(e=>e.errors&&e.errors.length>0?this.loggerRustPanic??this.buildQueryError(e.errors[0],i?.errorRegistry):{data:e});throw l&&1===l.length?Error(l[0].error):Error(JSON.stringify(a))}buildQueryError(e,t){if(e.user_facing_error.is_panic)return new rg(ou(this,e.user_facing_error.message),this.config.clientVersion);let r=this.getExternalAdapterError(e.user_facing_error,t);return r?r.error:sx(e,this.config.clientVersion,this.config.activeProvider)}getExternalAdapterError(e,t){if("P2036"===e.error_code&&t){let r=e.meta?.id;re("number"==typeof r,"Malformed external JS error received from the engine");let n=t.consumeError(r);return re(n,"External error with reported id was not registered"),n}}async metrics(e){await this.start();let t=await this.engine.metrics(JSON.stringify(e));return"prometheus"===e.format?t:this.parseEngineResponse(t)}};function ou(e,t){return function({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:s,query:a}){var o;let l=function(e=7500){let t=ey.map(([e,...t])=>`${e} ${t.map(e=>"string"==typeof e?e:JSON.stringify(e)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}(6e3-(a?.length??0)),u=(0,ah.default)(l).split(`
`).map(e=>e.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`),c=n?`# Description
\`\`\`
${n}
\`\`\``:"",d=function({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,af.default)({user:t,repo:r,template:n,title:e,body:i})}({title:r,body:(0,ah.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${s?.padEnd(19)}|

${c}

## Logs
\`\`\`
${u}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${a&&(o=a)?o.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,e=>`${e[0]}5`):""}
\`\`\`
`)});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${K(d)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}({binaryTarget:e.binaryTarget,title:t,version:e.config.clientVersion,engineVersion:e.versionInfo?.commit,database:e.config.activeProvider,query:e.lastQuery})}function oc({generator:e}){return e?.previewFeatures??[]}var od=e=>({command:e}),oh=e=>e.strings.reduce((e,t,r)=>`${e}@P${r}${t}`);function of(e){try{return op(e,"fast")}catch{return op(e,"slow")}}function op(e,t){return JSON.stringify(e.map(e=>(function e(t,r){var n;if(Array.isArray(t))return t.map(t=>e(t,r));if("bigint"==typeof t)return{prisma__type:"bigint",prisma__value:t.toString()};if(nK(t))return{prisma__type:"date",prisma__value:t.toJSON()};if(nU.isDecimal(t))return{prisma__type:"decimal",prisma__value:t.toJSON()};if(Buffer.isBuffer(t))return{prisma__type:"bytes",prisma__value:t.toString("base64")};if((n=t)instanceof ArrayBuffer||n instanceof SharedArrayBuffer||"object"==typeof n&&null!==n&&("ArrayBuffer"===n[Symbol.toStringTag]||"SharedArrayBuffer"===n[Symbol.toStringTag]))return{prisma__type:"bytes",prisma__value:Buffer.from(t).toString("base64")};if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:r,byteLength:n}=t;return{prisma__type:"bytes",prisma__value:Buffer.from(e,r,n).toString("base64")}}return"object"==typeof t&&"slow"===r?om(t):t})(e,t)))}function om(e){if("object"!=typeof e||null===e)return e;if("function"==typeof e.toJSON)return e.toJSON();if(Array.isArray(e))return e.map(og);let t={};for(let r of Object.keys(e))t[r]=og(e[r]);return t}function og(e){return"bigint"==typeof e?e.toString():om(e)}var oy=/^(\s*alter\s)/i,ow=ex("prisma:client");function ob(e,t,r,n){if(("postgresql"===e||"cockroachdb"===e)&&r.length>0&&oy.exec(t))throw Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var ov=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(st(r))n=r.sql,i={values:of(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[e,...t]=r;n=e,i={values:of(t||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":n=r.sql,i={values:of(r.values),__prismaRawParameters__:!0};break;case"cockroachdb":case"postgresql":case"postgres":n=r.text,i={values:of(r.values),__prismaRawParameters__:!0};break;case"sqlserver":n=oh(r),i={values:of(r.values),__prismaRawParameters__:!0};break;default:throw Error(`The ${t} provider does not support ${e}`)}return i?.values?ow(`prisma.${e}(${n}, ${i.values})`):ow(`prisma.${e}(${n})`),{query:n,parameters:i}},oE={requestArgsToMiddlewareArgs:e=>[e.strings,...e.values],middlewareArgsToRequestArgs(e){let[t,...r]=e;return new so(t,r)}},ox={requestArgsToMiddlewareArgs:e=>[e],middlewareArgsToRequestArgs:e=>e[0]};function oP(e){return function(t,r){let n,i=(r=e)=>{try{return void 0===r||r?.kind==="itx"?n??=oS(t(r)):oS(t(r))}catch(e){return Promise.reject(e)}};return{get spec(){return r},then:(e,t)=>i().then(e,t),catch:e=>i().catch(e),finally:e=>i().finally(e),requestTransaction(e){let t=i(e);return t.requestTransaction?t.requestTransaction(e):t},[Symbol.toStringTag]:"PrismaPromise"}}}function oS(e){return"function"==typeof e.then?e:Promise.resolve(e)}var o_=tK.split(".")[0],oA={isEnabled:()=>!1,getTraceParent:()=>"00-10-10-00",dispatchEngineSpans(){},getActiveContext(){},runInChildSpan:(e,t)=>t()},oN=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(e){return this.getGlobalTracingHelper().getTraceParent(e)}dispatchEngineSpans(e){return this.getGlobalTracingHelper().dispatchEngineSpans(e)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(e,t){return this.getGlobalTracingHelper().runInChildSpan(e,t)}getGlobalTracingHelper(){let e=globalThis[`V${o_}_PRISMA_INSTRUMENTATION`],t=globalThis.PRISMA_INSTRUMENTATION;return e?.helper??t?.helper??oA}},oT=class{use(e){this._middlewares.push(e)}get(e){return this._middlewares[e]}has(e){return!!this._middlewares[e]}length(){return this._middlewares.length}constructor(){this._middlewares=[]}},o$=f(P());function oR(e){return"number"==typeof e.batchRequestIdx}function oO(e){return`(${Object.keys(e).sort().map(t=>{let r=e[t];return"object"==typeof r&&null!==r?`(${t} ${oO(r)})`:t}).join(" ")})`}var ok={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0},oD=class{constructor(e){this.tickActive=!1,this.options=e,this.batches={}}request(e){let t=this.options.batchBy(e);return t?(this.batches[t]||(this.batches[t]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((r,n)=>{this.batches[t].push({request:e,resolve:r,reject:n})})):this.options.singleLoader(e)}dispatchBatches(){for(let e in this.batches){let t=this.batches[e];delete this.batches[e],1===t.length?this.options.singleLoader(t[0].request).then(e=>{e instanceof Error?t[0].reject(e):t[0].resolve(e)}).catch(e=>{t[0].reject(e)}):(t.sort((e,t)=>this.options.batchOrder(e.request,t.request)),this.options.batchLoader(t.map(e=>e.request)).then(e=>{if(e instanceof Error)for(let r=0;r<t.length;r++)t[r].reject(e);else for(let r=0;r<t.length;r++){let n=e[r];n instanceof Error?t[r].reject(n):t[r].resolve(n)}}).catch(e=>{for(let r=0;r<t.length;r++)t[r].reject(e)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function oI(e){let t=[],r=function(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],s={...r};for(let t=0;t<i.length;t++)s[e.columns[t]]=function e(t,r){if(null===r)return r;switch(t){case"bigint":return BigInt(r);case"bytes":{let{buffer:e,byteOffset:t,byteLength:n}=Buffer.from(r,"base64");return new Uint8Array(e,t,n)}case"decimal":return new nU(r);case"datetime":case"date":return new Date(r);case"time":return new Date(`1970-01-01T${r}Z`);case"bigint-array":return r.map(t=>e("bigint",t));case"bytes-array":return r.map(t=>e("bytes",t));case"decimal-array":return r.map(t=>e("decimal",t));case"datetime-array":return r.map(t=>e("datetime",t));case"date-array":return r.map(t=>e("date",t));case"time-array":return r.map(t=>e("time",t));default:return r}}(e.types[t],i[t]);t.push(s)}return t}var oq=ex("prisma:client:request_handler"),oC=class{constructor(e,t){this.logEmitter=t,this.client=e,this.dataloader=new oD({batchLoader:function(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?function e(t,r,n,i){if(n===r.length)return i(t);let s=t.customDataProxyFetch,a=t.requests[0].transaction;return r[n]({args:{queries:t.requests.map(e=>({model:e.modelName,operation:e.action,args:e.args})),transaction:a?{isolationLevel:"batch"===a.kind?a.isolationLevel:void 0}:void 0},__internalParams:t,query(a,o=t){let l=o.customDataProxyFetch;return o.customDataProxyFetch=sX(s,l),e(o,r,n+1,i)}})}(r,n,0,e):e(r)}}(async({requests:e,customDataProxyFetch:t})=>{let{transaction:r,otelParentCtx:n}=e[0],i=e.map(e=>e.protocolQuery),s=this.client._tracingHelper.getTraceParent(n),a=e.some(e=>ok[e.protocolQuery.action]);return(await this.client._engine.requestBatch(i,{traceparent:s,transaction:function(e){if(e){if("batch"===e.kind)return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if("itx"===e.kind)return{kind:"itx",options:oF(e)};rt(e,"Unknown transaction kind")}}(r),containsWrite:a,customDataProxyFetch:t})).map((t,r)=>{if(t instanceof Error)return t;try{return this.mapQueryEngineResult(e[r],t)}catch(e){return e}})}),singleLoader:async e=>{let t=e.transaction?.kind==="itx"?oF(e.transaction):void 0,r=await this.client._engine.request(e.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:t,isWrite:ok[e.protocolQuery.action],customDataProxyFetch:e.customDataProxyFetch});return this.mapQueryEngineResult(e,r)},batchBy:e=>e.transaction?.id?`transaction-${e.transaction.id}`:function(e){if("findUnique"!==e.action&&"findUniqueOrThrow"!==e.action)return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(oO(e.query.arguments)),t.push(oO(e.query.selection)),t.join("")}(e.protocolQuery),batchOrder:(e,t)=>e.transaction?.kind==="batch"&&t.transaction?.kind==="batch"?e.transaction.index-t.transaction.index:0})}async request(e){try{return await this.dataloader.request(e)}catch(a){let{clientMethod:t,callsite:r,transaction:n,args:i,modelName:s}=e;this.handleAndLogRequestError({error:a,clientMethod:t,callsite:r,transaction:n,args:i,modelName:s,globalOmit:e.globalOmit})}}mapQueryEngineResult({dataPath:e,unpacker:t},r){let n=r?.data,i=this.unpack(n,e,t);return process.env.PRISMA_CLIENT_GET_TIME?{data:i}:i}handleAndLogRequestError(e){try{this.handleRequestError(e)}catch(t){throw this.logEmitter&&this.logEmitter.emit("error",{message:t.message,target:e.clientMethod,timestamp:new Date}),t}}handleRequestError({error:e,clientMethod:t,callsite:r,transaction:n,args:i,modelName:s,globalOmit:a}){var o,l,u;if(oq(e),o=e,l=n,oR(o)&&l?.kind==="batch"&&o.batchRequestIdx!==l.index)throw e;e instanceof rm&&("P2009"===(u=e).code||"P2012"===u.code)&&iM({args:i,errors:[function e(t){if("Union"===t.kind)return{kind:"Union",errors:t.errors.map(e)};if(Array.isArray(t.selectionPath)){let[,...e]=t.selectionPath;return{...t,selectionPath:e}}return t}(e.meta)],callsite:r,errorFormat:this.client._errorFormat,originalMethod:t,clientVersion:this.client._clientVersion,globalOmit:a});let c=e.message;if(r&&(c=n5({callsite:r,originalMethod:t,isPanic:e.isPanic,showColors:"pretty"===this.client._errorFormat,message:c})),c=this.sanitizeMessage(c),e.code){let t=s?{modelName:s,...e.meta}:e.meta;throw new rm(c,{code:e.code,clientVersion:this.client._clientVersion,meta:t,batchRequestIdx:e.batchRequestIdx})}if(e.isPanic)throw new rg(c,this.client._clientVersion);if(e instanceof ry)throw new ry(c,{clientVersion:this.client._clientVersion,batchRequestIdx:e.batchRequestIdx});if(e instanceof rp)throw new rp(c,this.client._clientVersion);if(e instanceof rg)throw new rg(c,this.client._clientVersion);throw e.clientVersion=this.client._clientVersion,e}sanitizeMessage(e){return this.client._errorFormat&&"pretty"!==this.client._errorFormat?(0,o$.default)(e):e}unpack(e,t,r){if(!e||(e.data&&(e=e.data),!e))return e;let n=Object.keys(e)[0],i=sM(Object.values(e)[0],t.filter(e=>"select"!==e&&"include"!==e)),s="queryRaw"===n?oI(i):nL(i);return r?r(s):s}get[Symbol.toStringTag](){return"RequestHandler"}};function oF(e){return{id:e.id,payload:e.payload}}var oV=f(N()),oj=class extends Error{constructor(e){super(e+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};rd(oj,"PrismaClientConstructorValidationError");var oM=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],oU=["pretty","colorless","minimal"],oL=["info","query","warn","error"],oG={datasources:(e,{datasourceNames:t})=>{if(e){if("object"!=typeof e||Array.isArray(e))throw new oj(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let e=oB(r,t)||` Available datasources: ${t.join(", ")}`;throw new oj(`Unknown datasource ${r} provided to PrismaClient constructor.${e}`)}if("object"!=typeof n||Array.isArray(n))throw new oj(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&"object"==typeof n)for(let[t,i]of Object.entries(n)){if("url"!==t)throw new oj(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if("string"!=typeof i)throw new oj(`Invalid value ${JSON.stringify(i)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&"client"===tQ(t.generator))throw new oj('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(null!==e){if(void 0===e)throw new oj('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!oc(t).includes("driverAdapters"))throw new oj('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if("binary"===tQ(t.generator))throw new oj('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')}},datasourceUrl:e=>{if("u">typeof e&&"string"!=typeof e)throw new oj(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if("string"!=typeof e)throw new oj(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!oU.includes(e)){let t=oB(e,oU);throw new oj(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(e){if(!Array.isArray(e))throw new oj(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);for(let r of e){t(r);let e={level:t,emit:e=>{let t=["stdout","event"];if(!t.includes(e)){let r=oB(e,t);throw new oj(`Invalid value ${JSON.stringify(e)} for "emit" in logLevel provided to PrismaClient constructor.${r}`)}}};if(r&&"object"==typeof r)for(let[t,n]of Object.entries(r))if(e[t])e[t](n);else throw new oj(`Invalid property ${t} for "log" provided to PrismaClient constructor`)}}function t(e){if("string"==typeof e&&!oL.includes(e)){let t=oB(e,oL);throw new oj(`Invalid log level "${e}" provided to PrismaClient constructor.${t}`)}}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(null!=t&&t<=0)throw new oj(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(null!=r&&r<=0)throw new oj(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if("object"!=typeof e)throw new oj('"omit" option is expected to be an object.');if(null===e)throw new oj('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let e=function(e,t){return oH(t.models,e)??oH(t.types,e)}(n,t.runtimeDataModel);if(!e){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[t,s]of Object.entries(i)){let i=e.fields.find(e=>e.name===t);if(!i){r.push({kind:"UnknownField",modelKey:n,fieldName:t});continue}if(i.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:t});continue}"boolean"!=typeof s&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:t})}}if(r.length>0)throw new oj(function(e,t){let r=iF(e);for(let e of t)switch(e.kind){case"UnknownModel":r.arguments.getField(e.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${e.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${e.modelKey}" does not have a field named "${e.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([e.modelKey,e.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.")}let{message:n,args:i}=ij(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if("object"!=typeof e)throw new oj(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let e=oB(r,t);throw new oj(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${e}`)}}};function oB(e,t){if(0===t.length||"string"!=typeof e)return"";let r=function(e,t){if(0===t.length)return null;let r=t.map(t=>({value:t,distance:(0,oV.default)(e,t)}));r.sort((e,t)=>e.distance<t.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}(e,t);return r?` Did you mean "${r}"?`:""}function oH(e,t){let r=Object.keys(e).find(e=>nB(e)===t);if(r)return e[r]}var oW=ex("prisma:client");"object"==typeof globalThis&&(globalThis.NODE_CLIENT=!0);var oJ={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},oK=Symbol.for("prisma.client.transaction.id"),oQ={id:0,nextId(){return++this.id}};function oY(e){class t{constructor(t){this._originalClient=this,this._middlewares=new oT,this._createPrismaPromise=oP(),this.$metrics=new i3(this),this.$extends=sJ,function({postinstall:e,ciName:t,clientVersion:r}){if(s0("checkPlatformCaching:postinstall",e),s0("checkPlatformCaching:ciName",t),!0===e&&t&&t in s1){let e=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${s1[t]}-build`;throw console.error(e),new rp(e,r)}}(e=t?.__internal?.configOverride?.(e)??e),t&&function(e,t){for(let[r,n]of Object.entries(e)){if(!oM.includes(r)){let e=oB(r,oM);throw new oj(`Unknown property ${r} provided to PrismaClient constructor.${e}`)}oG[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new oj('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}(t,e);let r=new si.EventEmitter().on("error",()=>{});this._extensions=iB.empty(),this._previewFeatures=oc(e),this._clientVersion=e.clientVersion??"6.7.0",this._activeProvider=e.activeProvider,this._globalOmit=t?.omit,this._tracingHelper=new oN;let n=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&sa.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&sa.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},i;if(t?.adapter){i=t.adapter;let r="postgresql"===e.activeProvider?"postgres":e.activeProvider;if(i.provider!==r)throw new rp(`The Driver Adapter \`${i.adapterName}\`, based on \`${i.provider}\`, is not compatible with the provider \`${r}\` specified in the Prisma schema.`,this._clientVersion);if(t.datasources||void 0!==t.datasourceUrl)throw new rp("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let s=!i&&n&&ro(n,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{var a,o;let n=t??{},l=n.__internal??{},u=!0===l.debug;u&&ex.enable("prisma:client");let c=sa.default.resolve(e.dirname,e.relativePath);ss.default.existsSync(c)||(c=e.dirname),oW("dirname",e.dirname),oW("relativePath",e.relativePath),oW("cwd",c);let d=l.engine||{};if(n.errorFormat?this._errorFormat=n.errorFormat:this._errorFormat="minimal",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:c,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:d.allowTriggerPanic,prismaPath:d.binaryPath??void 0,engineEndpoint:d.endpoint,generator:e.generator,showColors:"pretty"===this._errorFormat,logLevel:n.log&&(a=n.log,"string"==typeof a?a:a.reduce((e,t)=>{let r="string"==typeof t?t:t.level;return"query"===r?e:e&&("info"===t||"info"===e)?"info":r},void 0)),logQueries:n.log&&!!("string"==typeof n.log?"query"===n.log:n.log.find(e=>"string"==typeof e?"query"===e:"query"===e.level)),env:s?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:(o=e.datasourceNames,n?n.datasources?n.datasources:n.datasourceUrl?{[o[0]]:{url:n.datasourceUrl}}:{}:{}),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:n.transactionOptions?.maxWait??2e3,timeout:n.transactionOptions?.timeout??5e3,isolationLevel:n.transactionOptions?.isolationLevel},logEmitter:r,isBundled:e.isBundled,adapter:i},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:ax,getBatchRequestPayload:sE,prismaGraphQLToJSError:sx,PrismaClientUnknownRequestError:ry,PrismaClientInitializationError:rp,PrismaClientKnownRequestError:rm,debug:ex("prisma:client:accelerateEngine"),engineVersion:sr.version,clientVersion:e.clientVersion}},oW("clientVersion",e.clientVersion),this._engine=function({copyEngine:e=!0},t){var r;let n;try{n=ax({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let i=!!(n?.startsWith("prisma://")||(r=n,r?.startsWith(`${tX}//`)));e&&i&&rf("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"),tQ(t.generator);let s=i||!e,a=!!t.adapter;if(s&&a){let r;throw new rw((e?n?.startsWith("prisma://")?["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]).join(`
`),{clientVersion:t.clientVersion})}return s?new a8(t):new ol(t)}(e,this._engineConfig),this._requestHandler=new oC(this,r),n.log)for(let e of n.log){let t="string"==typeof e?e:"stdout"===e.emit?e.level:null;t&&this.$on(t,e=>{t2.log(`${t2.tags[t]??""}`,e.message||e.query)})}}catch(e){throw e.clientVersion=this._clientVersion,e}return this._appliedParent=sW(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(e){this._middlewares.use(e)}$on(e,t){return"beforeExit"===e?this._engine.onBeforeExit(t):e&&this._engineConfig.logEmitter.on(e,t),this}$connect(){try{return this._engine.start()}catch(e){throw e.clientVersion=this._clientVersion,e}}async $disconnect(){try{await this._engine.stop()}catch(e){throw e.clientVersion=this._clientVersion,e}finally{ey.length=0}}$executeRawInternal(e,t,r,n){let i=this._activeProvider;return this._request({action:"executeRaw",args:r,transaction:e,clientMethod:t,argsMapper:ov({clientMethod:t,activeProvider:i}),callsite:sD(this._errorFormat),dataPath:[],middlewareArgsMapper:n})}$executeRaw(e,...t){return this._createPrismaPromise(r=>{if(void 0!==e.raw||void 0!==e.sql){let[n,i]=oz(e,t);return ob(this._activeProvider,n.text,n.values,Array.isArray(e)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(r,"$executeRaw",n,i)}throw new rw("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(e,...t){return this._createPrismaPromise(r=>(ob(this._activeProvider,e,t,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(r,"$executeRawUnsafe",[e,...t])))}$runCommandRaw(t){if("mongodb"!==e.activeProvider)throw new rw(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(e=>this._request({args:t,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:od,callsite:sD(this._errorFormat),transaction:e}))}async $queryRawInternal(e,t,r,n){let i=this._activeProvider;return this._request({action:"queryRaw",args:r,transaction:e,clientMethod:t,argsMapper:ov({clientMethod:t,activeProvider:i}),callsite:sD(this._errorFormat),dataPath:[],middlewareArgsMapper:n})}$queryRaw(e,...t){return this._createPrismaPromise(r=>{if(void 0!==e.raw||void 0!==e.sql)return this.$queryRawInternal(r,"$queryRaw",...oz(e,t));throw new rw("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(e){return this._createPrismaPromise(t=>{if(!this._hasPreviewFlag("typedSql"))throw new rw("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(t,"$queryRawTyped",e)})}$queryRawUnsafe(e,...t){return this._createPrismaPromise(r=>this.$queryRawInternal(r,"$queryRawUnsafe",[e,...t]))}_transactionWithArray({promises:e,options:t}){var r;let n=oQ.nextId(),i=function(e,t=()=>{}){let r,n=new Promise(e=>r=e);return{then:i=>(0==--e&&r(t()),i?.(n))}}(e.length);return 0===(r=e.map((e,r)=>{if(e?.[Symbol.toStringTag]!=="PrismaPromise")throw Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let s=t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel;return e.requestTransaction?.({kind:"batch",id:n,index:r,isolationLevel:s,lock:i})??e})).length?Promise.resolve([]):new Promise((e,t)=>{let n=Array(r.length),i=null,s=!1,a=0,o=()=>{s||++a===r.length&&(s=!0,i?t(i):e(n))},l=e=>{s||(s=!0,t(e))};for(let e=0;e<r.length;e++)r[e].then(t=>{n[e]=t,o()},t=>{if(!oR(t)){l(t);return}t.batchRequestIdx===e?l(t):(i||(i=t),o())})})}async _transactionWithCallback({callback:e,options:t}){let r={traceparent:this._tracingHelper.getTraceParent()},n={maxWait:t?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:t?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},i=await this._engine.transaction("start",r,n),s;try{let t={kind:"itx",...i};s=await e(this._createItxClient(t)),await this._engine.transaction("commit",r,i)}catch(e){throw await this._engine.transaction("rollback",r,i).catch(()=>{}),e}return s}_createItxClient(e){return sw(sW(sw(this[sH]?this[sH]:this,[sf("_appliedParent",()=>this._appliedParent._createItxClient(e)),sf("_createPrismaPromise",()=>oP(e)),sf(oK,()=>e.id)])),[sv(sY)])}$transaction(e,t){let r;return r="function"==typeof e?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?()=>{throw Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:()=>this._transactionWithCallback({callback:e,options:t}):()=>this._transactionWithArray({promises:e,options:t}),this._tracingHelper.runInChildSpan({name:"transaction",attributes:{method:"$transaction"}},r)}_request(e){e.otelParentCtx=this._tracingHelper.getActiveContext();let t=e.middlewareArgsMapper??oJ,r={args:t.requestArgsToMiddlewareArgs(e.args),dataPath:e.dataPath,runInTransaction:!!e.transaction,action:e.action,model:e.model},n={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:r.action,model:r.model,name:r.model?`${r.model}.${r.action}`:r.action}}},i=-1,s=async r=>{let a=this._middlewares.get(++i);if(a)return this._tracingHelper.runInChildSpan(n.middleware,e=>a(r,t=>(e?.end(),s(t))));let{runInTransaction:o,args:l,...u}=r,c={...e,...u};l&&(c.args=t.middlewareArgsToRequestArgs(l)),void 0!==e.transaction&&!1===o&&delete c.transaction;let d=await function(e,t){let{jsModelName:r,action:n,clientMethod:i}=t;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",r?n:i);return function e(t,r,n,i=0){return t._createPrismaPromise(s=>{let a=r.customDataProxyFetch;return"transaction"in r&&void 0!==s&&(r.transaction?.kind==="batch"&&r.transaction.lock.then(),r.transaction=s),i===n.length?t._executeRequest(r):n[i]({model:r.model,operation:r.model?r.action:r.clientMethod,args:function(e){var t,r;if(e instanceof so){return new so((t=e).strings,t.values)}if(st(e)){return new i8((r=e).sql,r.values)}if(Array.isArray(e)){let t=[e[0]];for(let r=1;r<e.length;r++)t[r]=sz(e[r]);return t}let n={};for(let t in e)n[t]=sz(e[t]);return n}(r.args??{}),__internalParams:r,query:(s,o=r)=>{let l=o.customDataProxyFetch;return o.customDataProxyFetch=sX(a,l),o.args=s,e(t,o,n,i+1)}})})}(e,t,s)}(this,c);return c.model?function({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:s}){return n.isEmpty()||null==e||"object"!=typeof e||!i.models[t]?e:sK({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(e,t,r)=>{let i=iU(t);return function({result:e,modelName:t,select:r,omit:n,extensions:i}){let s=i.getAllComputedFields(t);if(!s)return e;let a=[],o=[];for(let t of Object.values(s)){if(n){if(n[t.name])continue;let e=t.needs.filter(e=>n[e]);e.length>0&&o.push(sv(e))}else if(r){if(!r[t.name])continue;let e=t.needs.filter(e=>!r[e]);e.length>0&&o.push(sv(e))}(function(e,t){return t.every(t=>Object.prototype.hasOwnProperty.call(e,t))})(e,t.needs)&&a.push(function(e,t){return sp(sf(e.name,()=>e.compute(t)))}(t,sw(e,a)))}return a.length>0||o.length>0?sw(e,[...a,...o]):e}({result:e,modelName:i,select:r.select,omit:r.select?void 0:{...s?.[i],...r.omit},extensions:n})}})}({result:d,modelName:c.model,args:c.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):d};return this._tracingHelper.runInChildSpan(n.operation,()=>new sn.AsyncResource("prisma-client-request").runInAsyncScope(()=>s(r)))}async _executeRequest({args:e,clientMethod:t,dataPath:r,callsite:n,action:i,model:s,argsMapper:a,transaction:o,unpacker:l,otelParentCtx:u,customDataProxyFetch:c}){try{e=a?a(e):e;let d=this._tracingHelper.runInChildSpan({name:"serialize"},()=>iX({modelName:s,runtimeDataModel:this._runtimeDataModel,action:i,args:e,clientMethod:t,callsite:n,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return ex.enabled("prisma:client")&&(oW("Prisma Client call:"),oW(`prisma.${t}(${function(e){if(void 0===e)return"";let t=iF(e);return new ii(0,{colors:io}).write(t).toString()}(e)})`),oW("Generated request:"),oW(JSON.stringify(d,null,2)+`
`)),o?.kind==="batch"&&await o.lock,this._requestHandler.request({protocolQuery:d,modelName:s,action:i,clientMethod:t,dataPath:r,callsite:n,args:e,extensions:this._extensions,transaction:o,unpacker:l,otelParentCtx:u,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:c})}catch(e){throw e.clientVersion=this._clientVersion,e}}_hasPreviewFlag(e){return!!this._engineConfig.previewFeatures?.includes(e)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}}return t}function oz(e,t){var r;return Array.isArray(r=e)&&Array.isArray(r.raw)?[new so(e,t),oE]:[e,ox]}var oZ=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function oX(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!oZ.has(t))throw TypeError(`Invalid enum value: ${String(t)}`)}})}function o0(e){ro(e,{conflictCheck:"warn"})}}};