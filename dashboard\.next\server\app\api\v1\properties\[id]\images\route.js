(()=>{var e={};e.id=5908,e.ids=[5908],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return i},throwWithStaticGenerationBailoutError:function(){return n},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let s=r(80023),a=r(3295);function n(e,t){throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function i(e){throw Object.defineProperty(Error(`Route ${e} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0})}function c(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u,PUT:()=>c});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function c(e,{params:t}){try{let{images:r}=await e.json(),s=t.id;if(!s)return i.NextResponse.json({success:!1,error:"Property ID is required"},{status:400});if(!Array.isArray(r))return i.NextResponse.json({success:!1,error:"Images must be an array"},{status:400});try{let e=await fetch(`${process.env.BACKEND_URL||"http://localhost:5000"}/api/v1/properties/${s}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({images:r})});if(e.ok){let t=await e.json();return i.NextResponse.json({success:!0,data:t.data,message:"Images auto-saved successfully"})}throw Error("Backend API failed")}catch(e){return console.log("Backend not available, using mock response for auto-save"),i.NextResponse.json({success:!0,data:{id:s,images:r,updatedAt:new Date().toISOString()},message:"Images auto-saved successfully (mock)"})}}catch(e){return console.error("Error auto-saving images:",e),i.NextResponse.json({success:!1,error:"Failed to auto-save images",message:e.message},{status:500})}}async function u(e,{params:t}){try{let e=t.id;if(!e)return i.NextResponse.json({success:!1,error:"Property ID is required"},{status:400});try{let t=await fetch(`${process.env.BACKEND_URL||"http://localhost:5000"}/api/v1/properties/${e}`);if(t.ok){let e=await t.json();return i.NextResponse.json({success:!0,data:{images:e.data?.images||[]}})}throw Error("Backend API failed")}catch(e){return console.log("Backend not available, using mock response"),i.NextResponse.json({success:!0,data:{images:["/placeholder.jpg"]}})}}catch(e){return console.error("Error fetching property images:",e),i.NextResponse.json({success:!1,error:"Failed to fetch property images",message:e.message},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/v1/properties/[id]/images/route",pathname:"/api/v1/properties/[id]/images",filename:"route",bundlePath:"app/api/v1/properties/[id]/images/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\api\\v1\\properties\\[id]\\images\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:f}=d;function g(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return s},wellKnownProperties:function(){return n}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function s(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let n=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,2190],()=>r(65642));module.exports=s})();