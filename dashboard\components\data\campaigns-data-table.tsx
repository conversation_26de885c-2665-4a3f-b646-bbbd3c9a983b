"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import {
  MoreHorizontal,
  Eye,
  Loader2
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getCampaigns, type CampaignResponse } from "@/services/campaignService"
import { formatDistanceToNow } from "date-fns"

export function CampaignsDataTable() {
  const [campaigns, setCampaigns] = useState<CampaignResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await getCampaigns()

        // Ensure response is an array
        if (Array.isArray(response)) {
          setCampaigns(response)
        } else {
          console.warn("Campaigns response is not an array:", response)
          setCampaigns([])
        }
      } catch (err) {
        console.error("Error fetching campaigns:", err)
        setError("فشل في تحميل الحملات. يرجى المحاولة مرة أخرى.")
        setCampaigns([])
      } finally {
        setLoading(false)
      }
    }

    fetchCampaigns()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        <p>{error}</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>النوع</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>تاريخ الإنشاء</TableHead>
            <TableHead>موعد الإرسال</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {campaigns.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                لم يتم العثور على حملات
              </TableCell>
            </TableRow>
          ) : (
            campaigns.map((campaign) => (
              <TableRow key={campaign.id}>
                <TableCell className="font-medium">{campaign.name}</TableCell>
                <TableCell>{campaign.type}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      campaign.status.toLowerCase() === "active"
                        ? "default"
                        : campaign.status.toLowerCase() === "completed"
                          ? "secondary"
                          : "outline"
                    }
                  >
                    {campaign.status === "active" ? "نشط" :
                     campaign.status === "completed" ? "مكتمل" :
                     campaign.status === "pending" ? "في الانتظار" :
                     campaign.status === "draft" ? "مسودة" : campaign.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(campaign.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {campaign.scheduledAt
                    ? new Date(campaign.scheduledAt).toLocaleDateString()
                    : "غير مجدول"}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>عرض التفاصيل</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
