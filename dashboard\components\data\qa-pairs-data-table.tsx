"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import {
  MoreHorizontal,
  Edit,
  Trash,
  Loader2
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getQAPairs, type QAPair } from "@/services/qaPairService"

export function QAPairsDataTable() {
  const [qaPairs, setQAPairs] = useState<QAPair[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchQAPairs = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await getQAPairs()

        // Ensure response is an array
        if (Array.isArray(response)) {
          setQAPairs(response)
        } else {
          console.warn("QA pairs response is not an array:", response)
          setQAPairs([])
        }
      } catch (err) {
        console.error("Error fetching QA pairs:", err)
        setError("فشل في تحميل أزواج الأسئلة والأجوبة. يرجى المحاولة مرة أخرى.")
        setQAPairs([])
      } finally {
        setLoading(false)
      }
    }

    fetchQAPairs()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        <p>{error}</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>السؤال</TableHead>
            <TableHead>الجواب</TableHead>
            <TableHead>الفئة</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {qaPairs.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                لم يتم العثور على أزواج أسئلة وأجوبة
              </TableCell>
            </TableRow>
          ) : (
            qaPairs.map((qaPair) => (
              <TableRow key={qaPair.id}>
                <TableCell className="font-medium max-w-[300px] truncate">{qaPair.question}</TableCell>
                <TableCell className="max-w-[400px] truncate">{qaPair.answer}</TableCell>
                <TableCell>
                  <Badge variant="outline">{qaPair.category}</Badge>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>تعديل</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash className="mr-2 h-4 w-4" />
                        <span>حذف</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
