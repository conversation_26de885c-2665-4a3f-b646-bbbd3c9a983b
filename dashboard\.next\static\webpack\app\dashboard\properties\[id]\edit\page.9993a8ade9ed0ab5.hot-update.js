"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/edit/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyCreateForm.tsx":
/*!******************************************************!*\
  !*** ./components/properties/PropertyCreateForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyCreateForm: () => (/* binding */ PropertyCreateForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/uploadthing */ \"(app-pages-browser)/./lib/uploadthing.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyCreateForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyCreateForm(param) {\n    let { onSuccess, onCancel, initialData, isEdit = false, propertyId } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // UploadThing hook for property images\n    const { startUpload, isUploading } = (0,_lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__.useUploadThing)(\"propertyImageUploader\", {\n        onClientUploadComplete: {\n            \"PropertyCreateForm.useUploadThing\": (res)=>{\n                if (res) {\n                    const newImageUrls = res.map({\n                        \"PropertyCreateForm.useUploadThing.newImageUrls\": (file)=>file.url\n                    }[\"PropertyCreateForm.useUploadThing.newImageUrls\"]);\n                    setFormData({\n                        \"PropertyCreateForm.useUploadThing\": (prev)=>({\n                                ...prev,\n                                images: [\n                                    ...prev.images,\n                                    ...newImageUrls\n                                ]\n                            })\n                    }[\"PropertyCreateForm.useUploadThing\"]);\n                    toast({\n                        title: t('images.success'),\n                        description: t('images.success')\n                    });\n                }\n            }\n        }[\"PropertyCreateForm.useUploadThing\"],\n        onUploadError: {\n            \"PropertyCreateForm.useUploadThing\": (error)=>{\n                toast({\n                    title: t('images.error'),\n                    description: t('images.error'),\n                    variant: 'destructive'\n                });\n            }\n        }[\"PropertyCreateForm.useUploadThing\"]\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: (initialData === null || initialData === void 0 ? void 0 : initialData.title) || '',\n        description: (initialData === null || initialData === void 0 ? void 0 : initialData.description) || '',\n        price: (initialData === null || initialData === void 0 ? void 0 : initialData.price) || '',\n        currency: (initialData === null || initialData === void 0 ? void 0 : initialData.currency) || 'SAR',\n        type: (initialData === null || initialData === void 0 ? void 0 : initialData.type) || '',\n        status: (initialData === null || initialData === void 0 ? void 0 : initialData.status) || 'AVAILABLE',\n        bedrooms: (initialData === null || initialData === void 0 ? void 0 : initialData.bedrooms) || '',\n        bathrooms: (initialData === null || initialData === void 0 ? void 0 : initialData.bathrooms) || '',\n        area: (initialData === null || initialData === void 0 ? void 0 : initialData.area) || '',\n        location: (initialData === null || initialData === void 0 ? void 0 : initialData.location) || '',\n        address: (initialData === null || initialData === void 0 ? void 0 : initialData.address) || '',\n        city: (initialData === null || initialData === void 0 ? void 0 : initialData.city) || '',\n        country: (initialData === null || initialData === void 0 ? void 0 : initialData.country) || 'SAUDI',\n        images: (initialData === null || initialData === void 0 ? void 0 : initialData.images) || [],\n        features: (initialData === null || initialData === void 0 ? void 0 : initialData.features) || [],\n        amenities: (initialData === null || initialData === void 0 ? void 0 : initialData.amenities) || [],\n        yearBuilt: (initialData === null || initialData === void 0 ? void 0 : initialData.yearBuilt) || '',\n        parking: (initialData === null || initialData === void 0 ? void 0 : initialData.parking) || '',\n        furnished: (initialData === null || initialData === void 0 ? void 0 : initialData.furnished) || false,\n        petFriendly: (initialData === null || initialData === void 0 ? void 0 : initialData.petFriendly) || false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleInputChange]\": (field, value)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                        ...prev,\n                        [field]: value\n                    })\n            }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            // Clear error when user starts typing\n            if (errors[field]) {\n                setErrors({\n                    \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                            ...prev,\n                            [field]: ''\n                        })\n                }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleInputChange]\"], [\n        errors\n    ]);\n    // Handle image file selection\n    const handleImageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleImageChange]\": (files)=>{\n            if (!files) return;\n            const validFiles = [];\n            Array.from(files).forEach({\n                \"PropertyCreateForm.useCallback[handleImageChange]\": (file)=>{\n                    // Validate file type\n                    if (!file.type.startsWith('image/')) {\n                        toast({\n                            title: t('images.error'),\n                            description: t('images.fileType'),\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    // Validate file size (8MB max for UploadThing)\n                    if (file.size > 8 * 1024 * 1024) {\n                        toast({\n                            title: t('images.error'),\n                            description: t('images.fileSize'),\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    validFiles.push(file);\n                }\n            }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            if (validFiles.length > 0) {\n                setSelectedFiles({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>[\n                            ...prev,\n                            ...validFiles\n                        ]\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n                // Start upload immediately\n                startUpload(validFiles);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleImageChange]\"], [\n        toast,\n        t,\n        startUpload\n    ]);\n    // Remove image\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[removeImage]\": (index)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>({\n                        ...prev,\n                        images: prev.images.filter({\n                            \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                        }[\"PropertyCreateForm.useCallback[removeImage]\"])\n                    })\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n        }\n    }[\"PropertyCreateForm.useCallback[removeImage]\"], []);\n    // Handle drag and drop\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"PropertyCreateForm.useCallback[handleDragOver]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleImageChange(e.dataTransfer.files);\n        }\n    }[\"PropertyCreateForm.useCallback[handleDrop]\"], [\n        handleImageChange\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.title.trim()) newErrors.title = t('validation.required');\n        if (!formData.description.trim()) newErrors.description = t('validation.required');\n        if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive');\n        if (formData.price && formData.price > 100000000000) newErrors.price = 'السعر كبير جداً';\n        if (!formData.type) newErrors.type = t('validation.required');\n        if (!formData.location.trim()) newErrors.location = t('validation.required');\n        if (!formData.address.trim()) newErrors.address = t('validation.required');\n        if (!formData.city.trim()) newErrors.city = t('validation.required');\n        if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive');\n        if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive');\n        if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive');\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: t('properties.error'),\n                description: t('validation.required'),\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: Number(formData.price),\n                currency: formData.currency,\n                type: formData.type,\n                status: formData.status,\n                bedrooms: Number(formData.bedrooms),\n                bathrooms: Number(formData.bathrooms),\n                area: Number(formData.area),\n                location: formData.location,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                images: formData.images,\n                features: formData.features,\n                amenities: formData.amenities,\n                yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,\n                parking: formData.parking ? Number(formData.parking) : undefined,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly\n            };\n            if (isEdit && propertyId) {\n                await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.updateProperty(propertyId, propertyData);\n            } else {\n                await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.createProperty(propertyData);\n            }\n            toast({\n                title: t('properties.success'),\n                description: isEdit ? 'تم تحديث العقار بنجاح' : t('properties.success')\n            });\n            onSuccess();\n        } catch (error) {\n            console.error('Error creating property:', error);\n            toast({\n                title: t('properties.error'),\n                description: t('properties.error'),\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const propertyTypes = [\n        {\n            value: 'APARTMENT',\n            label: t('property.type.apartment')\n        },\n        {\n            value: 'VILLA',\n            label: t('property.type.villa')\n        },\n        {\n            value: 'TOWNHOUSE',\n            label: t('property.type.townhouse')\n        },\n        {\n            value: 'PENTHOUSE',\n            label: t('property.type.penthouse')\n        },\n        {\n            value: 'STUDIO',\n            label: t('property.type.studio')\n        },\n        {\n            value: 'OFFICE',\n            label: t('property.type.office')\n        },\n        {\n            value: 'SHOP',\n            label: t('property.type.shop')\n        },\n        {\n            value: 'WAREHOUSE',\n            label: t('property.type.warehouse')\n        },\n        {\n            value: 'LAND',\n            label: t('property.type.land')\n        },\n        {\n            value: 'BUILDING',\n            label: t('property.type.building')\n        }\n    ];\n    const propertyStatuses = [\n        {\n            value: 'AVAILABLE',\n            label: t('property.status.available')\n        },\n        {\n            value: 'SOLD',\n            label: t('property.status.sold')\n        },\n        {\n            value: 'RENTED',\n            label: t('property.status.rented')\n        },\n        {\n            value: 'PENDING',\n            label: t('property.status.pending')\n        }\n    ];\n    const countries = [\n        {\n            value: 'SAUDI',\n            label: t('country.saudi')\n        },\n        {\n            value: 'UAE',\n            label: t('country.uae')\n        },\n        {\n            value: 'QATAR',\n            label: t('country.qatar')\n        },\n        {\n            value: 'KUWAIT',\n            label: t('country.kuwait')\n        },\n        {\n            value: 'BAHRAIN',\n            label: t('country.bahrain')\n        },\n        {\n            value: 'OMAN',\n            label: t('country.oman')\n        }\n    ];\n    // Saudi cities for quick selection\n    const saudiCities = [\n        'الرياض',\n        'جدة',\n        'مكة المكرمة',\n        'المدينة المنورة',\n        'الدمام',\n        'الخبر',\n        'الظهران',\n        'الطائف',\n        'بريدة',\n        'تبوك',\n        'حائل',\n        'أبها',\n        'ينبع',\n        'الجبيل',\n        'نجران'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"المعلومات الأساسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.title'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.title ? 'error' : ''),\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange('title', e.target.value),\n                                        placeholder: t('property.title.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.price'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                className: \"form-input \".concat(errors.price ? 'error' : '', \" flex-1\"),\n                                                type: \"number\",\n                                                value: formData.price,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value ? Number(e.target.value) : '';\n                                                    // Limit price to reasonable maximum (100 billion)\n                                                    if (typeof value === 'number' && value > 100000000000) {\n                                                        return; // Don't update if price is too high\n                                                    }\n                                                    handleInputChange('price', value);\n                                                },\n                                                placeholder: t('property.price.placeholder'),\n                                                min: \"0\",\n                                                max: \"100000000000\",\n                                                dir: \"rtl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: formData.currency,\n                                                onValueChange: (value)=>handleInputChange('currency', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: \"form-select w-32\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"SAR\",\n                                                                children: t('currency.sar')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AED\",\n                                                                children: t('currency.aed')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"USD\",\n                                                                children: t('currency.usd')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"EUR\",\n                                                                children: t('currency.eur')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"GBP\",\n                                                                children: t('currency.gbp')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.type'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.type,\n                                        onValueChange: (value)=>handleInputChange('type', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select \".concat(errors.type ? 'error' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: t('property.type.select')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: type.value,\n                                                        children: type.label\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.status')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.status,\n                                        onValueChange: (value)=>handleInputChange('status', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: t('property.status.select')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyStatuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: status.value,\n                                                        children: status.label\n                                                    }, status.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"تفاصيل العقار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bedrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bedrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bedrooms,\n                                        onChange: (e)=>handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bedrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bathrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bathrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bathrooms,\n                                        onChange: (e)=>handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bathrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.area'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.area ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.area,\n                                        onChange: (e)=>handleInputChange('area', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.area\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.yearBuilt')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.yearBuilt,\n                                        onChange: (e)=>handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٢٠٢٤\",\n                                        min: \"1900\",\n                                        max: \"2030\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.parking')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.parking,\n                                        onChange: (e)=>handleInputChange('parking', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"معلومات الموقع\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.location'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.location ? 'error' : ''),\n                                        value: formData.location,\n                                        onChange: (e)=>handleInputChange('location', e.target.value),\n                                        placeholder: t('property.location.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.location\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.address'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.address ? 'error' : ''),\n                                        value: formData.address,\n                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                        placeholder: t('property.address.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.city'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.city ? 'error' : ''),\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                        placeholder: t('property.city.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.city\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.country')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.country,\n                                        onValueChange: (value)=>handleInputChange('country', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: country.value,\n                                                        children: country.label\n                                                    }, country.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"الوصف\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                className: \"form-label\",\n                                children: [\n                                    t('property.description'),\n                                    \" *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                className: \"form-textarea \".concat(errors.description ? 'error' : ''),\n                                value: formData.description,\n                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                placeholder: t('property.description.placeholder'),\n                                dir: \"rtl\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-error\",\n                                children: errors.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 34\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: t('property.images')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"image-upload-area \".concat(isUploading ? 'loading' : ''),\n                                onDragOver: handleDragOver,\n                                onDrop: handleDrop,\n                                onClick: ()=>{\n                                    const input = document.createElement('input');\n                                    input.type = 'file';\n                                    input.multiple = true;\n                                    input.accept = 'image/*';\n                                    input.onchange = (e)=>{\n                                        const target = e.target;\n                                        handleImageChange(target.files);\n                                    };\n                                    input.click();\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-medium text-gray-300 mb-2\",\n                                            children: t('images.drag')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: t('images.formats')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this),\n                                        isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"loading-spinner\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: t('images.uploading')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, this),\n                            formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                children: formData.images.map((url, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-800 rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: url,\n                                                    alt: \"\".concat(t('images.preview'), \" \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>removeImage(index),\n                                                className: \"absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                title: t('images.remove'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"خيارات إضافية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"furnished\",\n                                        checked: formData.furnished,\n                                        onChange: (e)=>handleInputChange('furnished', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"furnished\",\n                                        className: \"form-label\",\n                                        children: t('property.furnished')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"petFriendly\",\n                                        checked: formData.petFriendly,\n                                        onChange: (e)=>handleInputChange('petFriendly', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"petFriendly\",\n                                        className: \"form-label\",\n                                        children: t('property.petFriendly')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 599,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 flex-row-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading || isUploading,\n                        className: \"btn-primary\",\n                        children: [\n                            (isLoading || isUploading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 42\n                            }, this),\n                            isLoading || isUploading ? t('properties.loading') : t('properties.save')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        onClick: onCancel,\n                        disabled: isLoading || isUploading,\n                        className: \"btn-secondary\",\n                        children: t('properties.cancel')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 632,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyCreateForm, \"iX5sOBcOi6AYfiHUyEcXAg0msx8=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__.useUploadThing\n    ];\n});\n_c = PropertyCreateForm;\nvar _c;\n$RefreshReg$(_c, \"PropertyCreateForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\n"));

/***/ })

});