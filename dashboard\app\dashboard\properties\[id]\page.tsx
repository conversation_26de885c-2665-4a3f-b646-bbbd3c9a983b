"use client"

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'

import { propertyService } from '@/services/propertyService'
import { PropertyShowComponent } from '@/components/properties/PropertyShowComponent'
import { Button } from '@/components/ui/button'
import { ArrowRight, Edit, Trash2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import '@/styles/arabic-properties.css'

interface Property {
  id: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  price: number
  currency: string
  type: string
  status: string
  bedrooms?: number
  bathrooms?: number
  area?: number
  location: string
  locationAr?: string
  address: string
  addressAr?: string
  city: string
  cityAr?: string
  country: string
  countryAr?: string
  latitude?: number
  longitude?: number
  images: string[]
  features: string[]
  featuresAr: string[]
  amenities: string[]
  amenitiesAr: string[]
  yearBuilt?: number
  parking?: number
  furnished: boolean
  petFriendly: boolean
  utilities?: string
  utilitiesAr?: string
  contactInfo?: string
  agentId?: string
  isActive: boolean
  isFeatured: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    email: string
  }
}

export default function PropertyShowPage() {
  const params = useParams()
  const router = useRouter()

  const { toast } = useToast()

  const [property, setProperty] = useState<Property | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)

  const propertyId = params.id as string

  useEffect(() => {
    if (propertyId) {
      console.log('Component mounted, property ID:', propertyId)
      fetchProperty()
    }
  }, [propertyId])

  const fetchProperty = async () => {
    try {
      setIsLoading(true)
      console.log('Fetching property:', propertyId)

      const data = await propertyService.getPropertyById(propertyId)
      console.log('API data received:', data)

      if (data && data.id) {
        setProperty(data)
        console.log('Property loaded successfully from API')
      } else {
        console.log('No property data received from API')
        setProperty(null)
      }
    } catch (error: any) {
      console.error('Error fetching property:', error)
      const status = error.status || error.response?.status;

      if (status === 404) {
        console.log(`Property ${propertyId} not found in database (404)`);
        toast({
          title: 'العقار غير موجود',
          description: 'لم يتم العثور على العقار المطلوب',
          variant: 'destructive',
        })
      } else if (error.message && error.message.includes('Network Error')) {
        console.log('Network error - backend server not available');
        toast({
          title: 'خطأ في الاتصال',
          description: 'لا يمكن الاتصال بالخادم. يرجى المحاولة مرة أخرى.',
          variant: 'destructive',
        })
      } else {
        console.log('API error:', status || error.message);
        toast({
          title: 'خطأ في تحميل العقار',
          description: 'حدث خطأ أثناء تحميل بيانات العقار',
          variant: 'destructive',
        })
      }
      setProperty(null)
    } finally {
      setIsLoading(false)
    }
  }

  const handleEdit = () => {
    router.push(`/dashboard/properties/${propertyId}/edit`)
  }

  const handleDelete = async () => {
    if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {
      return
    }

    try {
      setIsDeleting(true)
      await propertyService.deleteProperty(propertyId)
      toast({
        title: 'تم حذف العقار',
        description: 'تم حذف العقار بنجاح',
      })
      router.push('/dashboard/properties')
    } catch (error) {
      console.error('Error deleting property:', error)
      toast({
        title: 'خطأ في حذف العقار',
        description: 'حدث خطأ أثناء حذف العقار',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleBack = () => {
    router.push('/dashboard/properties')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-7xl mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="loading-spinner" />
            <span className="mr-3 text-lg">جاري تحميل العقار...</span>
          </div>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-7xl mx-auto p-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-red-400 mb-4">العقار غير موجود</h1>
            <p className="text-gray-400 mb-6">لم يتم العثور على العقار المطلوب</p>
            <Button onClick={handleBack} className="btn-primary">
              <ArrowRight className="h-4 w-4 ml-2" />
              العودة إلى قائمة العقارات
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen property-form-dark rtl arabic-text">
      <div className="max-w-7xl mx-auto p-6">


        {/* Header */}
        <div className="property-header-dark mb-6">
          <div className="flex items-center justify-between">
            <div>
              <Button
                onClick={handleBack}
                variant="ghost"
                className="text-gray-400 hover:text-white mb-4"
              >
                <ArrowRight className="h-4 w-4 ml-2" />
                العودة إلى قائمة العقارات
              </Button>
              <h1 className="arabic-heading text-3xl font-bold text-white">
                {property.title}
              </h1>
              <p className="text-gray-400 mt-2">
                {property.location} • {property.city} • {property.country}
              </p>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleEdit}
                className="btn-secondary"
              >
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
              <Button
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting && <div className="loading-spinner" />}
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </Button>
            </div>
          </div>
        </div>

        {/* Property Details Component */}
        <PropertyShowComponent property={property} />
      </div>
    </div>
  )
}
