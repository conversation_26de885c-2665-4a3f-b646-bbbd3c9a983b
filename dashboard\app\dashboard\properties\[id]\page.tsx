"use client"

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'

import { propertyService } from '@/services/propertyService'
import { PropertyShowComponent } from '@/components/properties/PropertyShowComponent'
import { Button } from '@/components/ui/button'
import { ArrowRight, Edit, Trash2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import '@/styles/arabic-properties.css'

interface Property {
  id: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  price: number
  currency: string
  type: string
  status: string
  bedrooms?: number
  bathrooms?: number
  area?: number
  location: string
  locationAr?: string
  address: string
  addressAr?: string
  city: string
  cityAr?: string
  country: string
  countryAr?: string
  latitude?: number
  longitude?: number
  images: string[]
  features: string[]
  featuresAr: string[]
  amenities: string[]
  amenitiesAr: string[]
  yearBuilt?: number
  parking?: number
  furnished: boolean
  petFriendly: boolean
  utilities?: string
  utilitiesAr?: string
  contactInfo?: string
  agentId?: string
  isActive: boolean
  isFeatured: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    email: string
  }
}

export default function PropertyShowPage() {
  const params = useParams()
  const router = useRouter()

  const { toast } = useToast()

  const [property, setProperty] = useState<Property | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUsingMockData, setIsUsingMockData] = useState(false)

  const propertyId = params.id as string

  // Function to generate different mock properties based on ID
  const getMockProperty = (id: string): Property => {
    const baseProperties = {
      available: {
        id,
        title: 'فيلا فاخرة في دبي مارينا',
        titleAr: 'فيلا فاخرة في دبي مارينا',
        description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',
        descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',
        price: 2500000,
        currency: 'AED',
        type: 'VILLA',
        status: 'AVAILABLE',
        bedrooms: 4,
        bathrooms: 3,
        area: 350,
        location: 'دبي مارينا',
        locationAr: 'دبي مارينا',
        address: '123 ممشى المارينا',
        addressAr: '123 ممشى المارينا',
        city: 'دبي',
        cityAr: 'دبي',
        country: 'الإمارات العربية المتحدة',
        countryAr: 'الإمارات العربية المتحدة',
        latitude: 25.0772,
        longitude: 55.1395,
        images: [
          'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'
        ],
        features: ['مسبح خاص', 'حديقة', 'موقف سيارات', 'أمن 24/7'],
        featuresAr: ['مسبح خاص', 'حديقة', 'موقف سيارات', 'أمن 24/7'],
        amenities: ['صالة رياضية', 'سبا', 'ملعب تنس', 'مارينا خاصة'],
        amenitiesAr: ['صالة رياضية', 'سبا', 'ملعب تنس', 'مارينا خاصة'],
        yearBuilt: 2020,
        parking: 2,
        furnished: true,
        petFriendly: false,
        utilities: 'جميع المرافق متضمنة',
        utilitiesAr: 'جميع المرافق متضمنة',
        contactInfo: '+971 50 123 4567',
        agentId: 'agent1',
        isActive: true,
        isFeatured: true,
        viewCount: 125,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        agent: {
          id: 'agent1',
          name: 'أحمد محمد',
          email: '<EMAIL>'
        }
      },
      sold: {
        id,
        title: 'شقة مباعة في برج خليفة',
        titleAr: 'شقة مباعة في برج خليفة',
        description: 'شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.',
        descriptionAr: 'شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.',
        price: 3200000,
        currency: 'AED',
        type: 'APARTMENT',
        status: 'SOLD',
        bedrooms: 3,
        bathrooms: 2,
        area: 180,
        location: 'وسط المدينة',
        locationAr: 'وسط المدينة',
        address: 'برج خليفة، الطابق 45',
        addressAr: 'برج خليفة، الطابق 45',
        city: 'دبي',
        cityAr: 'دبي',
        country: 'الإمارات العربية المتحدة',
        countryAr: 'الإمارات العربية المتحدة',
        latitude: 25.1972,
        longitude: 55.2744,
        images: [
          'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop'
        ],
        features: ['إطلالة على برج خليفة', 'مصعد عالي السرعة', 'أمن متقدم', 'موقف سيارات'],
        featuresAr: ['إطلالة على برج خليفة', 'مصعد عالي السرعة', 'أمن متقدم', 'موقف سيارات'],
        amenities: ['صالة رياضية', 'مسبح', 'سبا', 'مطاعم'],
        amenitiesAr: ['صالة رياضية', 'مسبح', 'سبا', 'مطاعم'],
        yearBuilt: 2018,
        parking: 1,
        furnished: true,
        petFriendly: false,
        utilities: 'جميع المرافق متضمنة',
        utilitiesAr: 'جميع المرافق متضمنة',
        contactInfo: '+971 50 987 6543',
        agentId: 'agent2',
        isActive: false,
        isFeatured: false,
        viewCount: 89,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        agent: {
          id: 'agent2',
          name: 'فاطمة أحمد',
          email: '<EMAIL>'
        }
      }
    }

    // Return different properties based on ID pattern
    if (id.includes('sold') || id.includes('SOLD')) {
      return baseProperties.sold
    }
    return baseProperties.available
  }

  useEffect(() => {
    if (propertyId) {
      console.log('Component mounted, property ID:', propertyId)
      fetchProperty()
    }
  }, [propertyId])

  const fetchProperty = async () => {
    try {
      setIsLoading(true)
      console.log('Fetching property:', propertyId)

      // Create different mock properties based on ID
      console.log('Using mock data for property:', propertyId)
      const mockProperty: Property = getMockProperty(propertyId)

      console.log('Mock property created:', mockProperty)
      setProperty(mockProperty)
      setIsUsingMockData(true)

      // Try to fetch from API in background (optional)
      try {
        const data = await propertyService.getPropertyById(propertyId)
        console.log('API data received:', data)
        // Only replace mock data if we get valid API data
        if (data && data.id) {
          setProperty(data)
          setIsUsingMockData(false)
        }
      } catch (apiError: any) {
        const status = apiError.status || apiError.response?.status;
        const message = apiError.message || 'Unknown error';

        if (status === 404) {
          console.log(`Property ${propertyId} not found in database (404) - using mock data`);
        } else if (apiError.message && apiError.message.includes('Network Error')) {
          console.log('Network error - backend server not available, using mock data');
          // Show a toast notification for network issues
          toast({
            title: 'وضع عدم الاتصال',
            description: 'لا يمكن الاتصال بالخادم. يتم عرض بيانات تجريبية.',
            variant: 'default',
          })
        } else {
          console.log('API error (using mock data):', status || message);
        }
        // Keep using mock data - this is expected for mock IDs or when property doesn't exist
        setIsUsingMockData(true)
      }

    } catch (error) {
      console.error('Error in fetchProperty:', error)
      // Ensure we always have mock data even if something goes wrong
      if (!property) {
        const mockProperty: Property = getMockProperty(propertyId)
        setProperty(mockProperty)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleEdit = () => {
    router.push(`/dashboard/properties/${propertyId}/edit`)
  }

  const handleDelete = async () => {
    if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {
      return
    }

    try {
      setIsDeleting(true)
      await propertyService.deleteProperty(propertyId)
      toast({
        title: 'تم حذف العقار',
        description: 'تم حذف العقار بنجاح',
      })
      router.push('/dashboard/properties')
    } catch (error) {
      console.error('Error deleting property:', error)
      toast({
        title: 'خطأ في حذف العقار',
        description: 'حدث خطأ أثناء حذف العقار',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleBack = () => {
    router.push('/dashboard/properties')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-7xl mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="loading-spinner" />
            <span className="mr-3 text-lg">جاري تحميل العقار...</span>
          </div>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-7xl mx-auto p-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-red-400 mb-4">العقار غير موجود</h1>
            <p className="text-gray-400 mb-6">لم يتم العثور على العقار المطلوب</p>
            <Button onClick={handleBack} className="btn-primary">
              <ArrowRight className="h-4 w-4 ml-2" />
              العودة إلى قائمة العقارات
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen property-form-dark rtl arabic-text">
      <div className="max-w-7xl mx-auto p-6">
        {/* Mock Data Indicator */}
        {isUsingMockData && (
          <div className="bg-blue-900/50 border border-blue-700 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 text-blue-300">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm">
                وضع العرض التجريبي - يتم عرض بيانات تجريبية (الخادم غير متاح)
              </span>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="property-header-dark mb-6">
          <div className="flex items-center justify-between">
            <div>
              <Button
                onClick={handleBack}
                variant="ghost"
                className="text-gray-400 hover:text-white mb-4"
              >
                <ArrowRight className="h-4 w-4 ml-2" />
                العودة إلى قائمة العقارات
              </Button>
              <h1 className="arabic-heading text-3xl font-bold text-white">
                {property.title}
              </h1>
              <p className="text-gray-400 mt-2">
                {property.location} • {property.city} • {property.country}
              </p>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleEdit}
                className="btn-secondary"
              >
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
              <Button
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting && <div className="loading-spinner" />}
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </Button>
            </div>
          </div>
        </div>

        {/* Property Details Component */}
        <PropertyShowComponent property={property} />
      </div>
    </div>
  )
}
