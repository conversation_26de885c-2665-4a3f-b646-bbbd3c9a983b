"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/properties/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _lib_utils_formatPrice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/formatPrice */ \"(app-pages-browser)/./lib/utils/formatPrice.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PropertiesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage)();\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    // Removed dialog states - now using dedicated pages\n    // Fetch properties and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertiesPage.useEffect\": ()=>{\n            fetchProperties();\n            fetchStats();\n        }\n    }[\"PropertiesPage.useEffect\"], [\n        searchTerm,\n        filterType,\n        filterStatus\n    ]);\n    const fetchProperties = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (filterType && filterType !== 'الكل' && filterType !== 'ALL') params.append('type', filterType);\n            if (filterStatus && filterStatus !== 'الكل' && filterStatus !== 'ALL') params.append('status', filterStatus);\n            params.append('isActive', 'true');\n            const response = await fetch(\"/api/v1/properties?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.data.properties) {\n                    setProperties(data.data.properties);\n                    console.log('API data loaded:', data.data.properties.length, 'properties');\n                } else {\n                    console.log('No properties found in API response');\n                    setProperties([]);\n                }\n            } else {\n                console.error('Failed to fetch properties:', response.status);\n                setProperties([]);\n            }\n        } catch (error) {\n            console.error('Error fetching properties:', error);\n            // Fallback to empty array on error\n            setProperties([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch('/api/v1/properties/stats');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.data) {\n                    setStats(data.data);\n                    console.log('API stats loaded');\n                    return; // Exit early if API stats are successful\n                }\n            }\n            // Always use mock stats as fallback\n            console.log('Using mock stats (API status:', response.status, ')');\n            setStats({\n                total: 3,\n                available: 1,\n                sold: 1,\n                rented: 1,\n                featured: 2,\n                byType: {\n                    APARTMENT: 1,\n                    VILLA: 1,\n                    HOUSE: 1,\n                    TOWNHOUSE: 0,\n                    PENTHOUSE: 0,\n                    STUDIO: 0,\n                    OFFICE: 0,\n                    SHOP: 0,\n                    WAREHOUSE: 0,\n                    LAND: 0,\n                    BUILDING: 0\n                }\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // Fallback to default stats on error\n            setStats({\n                total: 0,\n                available: 0,\n                sold: 0,\n                rented: 0,\n                featured: 0,\n                byType: {\n                    APARTMENT: 0,\n                    VILLA: 0,\n                    TOWNHOUSE: 0,\n                    PENTHOUSE: 0,\n                    STUDIO: 0,\n                    OFFICE: 0,\n                    SHOP: 0,\n                    WAREHOUSE: 0,\n                    LAND: 0,\n                    BUILDING: 0\n                }\n            });\n        }\n    };\n    const handleDeleteProperty = async (id)=>{\n        if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                fetchProperties();\n                fetchStats();\n            }\n        } catch (error) {\n            console.error('Error deleting property:', error);\n        }\n    };\n    const formatPriceLocal = (price, currency)=>{\n        return (0,_lib_utils_formatPrice__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(price, currency, language === 'ar' ? 'ar-AE' : 'en-US');\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'bg-green-600 text-white';\n            case 'SOLD':\n                return 'bg-red-600 text-white';\n            case 'RENTED':\n                return 'bg-blue-600 text-white';\n            case 'PENDING':\n                return 'bg-yellow-600 text-black';\n            case 'RESERVED':\n                return 'bg-orange-600 text-white';\n            default:\n                return 'bg-gray-600 text-white';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'متاح';\n            case 'SOLD':\n                return 'مباع';\n            case 'RENTED':\n                return 'مؤجر';\n            case 'PENDING':\n                return 'قيد المراجعة';\n            case 'RESERVED':\n                return 'محجوز';\n            default:\n                return status;\n        }\n    };\n    const getPropertyTitle = (property)=>{\n        return language === 'ar' && property.titleAr ? property.titleAr : property.title;\n    };\n    const getPropertyLocation = (property)=>{\n        return language === 'ar' && property.locationAr ? property.locationAr : property.location;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: language === 'ar' ? 'العقارات' : 'Properties'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: language === 'ar' ? 'إدارة العقارات والممتلكات' : 'Manage properties and real estate'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push('/dashboard/properties/create'),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            language === 'ar' ? 'إضافة عقار' : 'Add Property'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'إجمالي العقارات' : 'Total Properties'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: stats.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'متاح' : 'Available'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: stats.available\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مباع' : 'Sold'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-red-600\",\n                                    children: stats.sold\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مؤجر' : 'Rented'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: stats.rented\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مميز' : 'Featured'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: stats.featured\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: language === 'ar' ? 'البحث في العقارات...' : 'Search properties...',\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterType,\n                        onValueChange: setFilterType,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'نوع العقار' : 'Property Type'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"الكل\",\n                                        children: \"جميع الأنواع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"APARTMENT\",\n                                        children: \"شقة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"VILLA\",\n                                        children: \"فيلا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"TOWNHOUSE\",\n                                        children: \"تاون هاوس\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"OFFICE\",\n                                        children: \"مكتب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterStatus,\n                        onValueChange: setFilterStatus,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'الحالة' : 'Status'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"الكل\",\n                                        children: \"جميع الحالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"AVAILABLE\",\n                                        children: \"متاح\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"SOLD\",\n                                        children: \"مباع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"RENTED\",\n                                        children: \"مؤجر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'grid' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('grid'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'list' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('list'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4',\n                children: properties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"overflow-hidden hover:shadow-lg transition-shadow\",\n                        children: viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gray-200 dark:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 23\n                                        }, this),\n                                        property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"absolute top-2 left-2 bg-purple-600\",\n                                            children: language === 'ar' ? 'مميز' : 'Featured'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-lg truncate\",\n                                                    children: getPropertyTitle(property)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(property.status),\n                                                    children: getStatusText(property.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                            children: getPropertyLocation(property)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-blue-600\",\n                                                    children: formatPriceLocal(property.price, property.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-sm text-gray-500\",\n                                                    children: [\n                                                        property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'غرف' : 'bed'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 47\n                                                        }, this),\n                                                        property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bathrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'حمام' : 'bath'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 48\n                                                        }, this),\n                                                        property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.area,\n                                                                \"m\\xb2\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id)),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id, \"/edit\")),\n                                                            className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDeleteProperty(property.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                        \" \",\n                                                        property.viewCount\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-lg\",\n                                                        children: getPropertyTitle(property)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: getStatusColor(property.status),\n                                                                children: getStatusText(property.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-purple-600\",\n                                                                children: language === 'ar' ? 'مميز' : 'Featured'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                                children: getPropertyLocation(property)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-blue-600\",\n                                                        children: formatPriceLocal(property.price, property.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bedrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'غرف' : 'bed'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bathrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'حمام' : 'bath'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 50\n                                                            }, this),\n                                                            property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.area,\n                                                                    \"m\\xb2\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                                    \" \",\n                                                                    property.viewCount\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id, \"/edit\")),\n                                                                className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteProperty(property.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 17\n                        }, this)\n                    }, property.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertiesPage, \"fSK+ki2zuOH8XoJ/w9r3SeZEO7o=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage\n    ];\n});\n_c = PropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"PropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/page.tsx\n"));

/***/ })

});