(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4663],{10996:(e,s,t)=>{"use strict";function a(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ar-AE",a=s&&3===s.length?s.toUpperCase():"USD";if("number"!=typeof e||isNaN(e))return"0 "+a;try{return new Intl.NumberFormat(t,{style:"currency",currency:a,minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}catch(s){try{return new Intl.NumberFormat(t,{minimumFractionDigits:0,maximumFractionDigits:0}).format(e)+" "+a}catch(s){return e.toLocaleString()+" "+a}}}function r(e,s){return a(e,s,"ar-AE")}t.d(s,{$g:()=>a,PW:()=>r})},37637:(e,s,t)=>{Promise.resolve().then(t.bind(t,92265))},88145:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var r=t(74466),l=t(53999);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},88482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>c,wL:()=>m});var a=t(95155),r=t(12115),l=t(53999);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});n.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},92265:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>F});var a=t(95155),r=t(12115),l=t(35695),i=t(86132),c=t(78137),n=t(88145),d=t(88482),o=t(97168),m=t(10996),x=t(19420),h=t(42355),g=t(13052),u=t(57340),p=t(36643),b=t(22717),j=t(18979),f=t(57930),N=t(69074),y=t(92657),v=t(51976),w=t(4516),A=t(28883),k=t(54416);function I(e){let{property:s}=e,{t}=(0,i.Y)(),[l,c]=(0,r.useState)(0),[I,C]=(0,r.useState)(!1);if(!s)return(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-red-400 text-xl",children:"لا توجد بيانات للعقار"})});let E=()=>{c(e=>e===s.images.length-1?0:e+1)},D=()=>{c(e=>0===e?s.images.length-1:e-1)},S=e=>{c(e),C(!0)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(d.Zp,{className:"property-card-dark bg-gradient-to-r from-blue-600/20 to-green-600/20 border-2 border-blue-500/30",children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-500/30 rounded-full flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-1",children:"اتصل بنا الآن"}),(0,a.jsx)("p",{className:"text-gray-300",children:"للاستفسار عن هذا العقار"}),s.contactInfo&&(0,a.jsx)("p",{className:"text-green-400 font-semibold text-lg",children:s.contactInfo})]})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[s.contactInfo&&(0,a.jsxs)(o.$,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white px-6",onClick:()=>window.open("tel:".concat(s.contactInfo),"_self"),children:[(0,a.jsx)(x.A,{className:"h-5 w-5 ml-2"}),"اتصال مباشر"]}),(0,a.jsx)(o.$,{size:"lg",variant:"outline",className:"border-blue-500 text-blue-400 hover:bg-blue-500/10 px-6",onClick:()=>window.open("https://wa.me/971501234567","_blank"),children:"واتساب"})]})]})})}),s.images&&s.images.length>0&&(0,a.jsx)(d.Zp,{className:"property-card-dark overflow-hidden",children:(0,a.jsxs)(d.Wu,{className:"p-0",children:[(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("div",{className:"aspect-video bg-gray-800 overflow-hidden relative",children:[(0,a.jsx)("img",{src:s.images[l],alt:s.title,className:"w-full h-full object-cover cursor-pointer transition-transform duration-300 hover:scale-105",onClick:()=>S(l)}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:"انقر للتكبير"})]}),s.images.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:D,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100",children:(0,a.jsx)(h.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:E,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100",children:(0,a.jsx)(g.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm",children:[l+1," / ",s.images.length]}),(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2",children:s.images.map((e,s)=>(0,a.jsx)("button",{onClick:()=>c(s),className:"w-3 h-3 rounded-full transition-all duration-200 ".concat(s===l?"bg-white scale-110":"bg-white/50 hover:bg-white/70")},s))})]}),s.images.length>1&&(0,a.jsx)("div",{className:"p-4 bg-gray-900/50",children:(0,a.jsxs)("div",{className:"grid grid-cols-6 gap-3",children:[s.images.slice(0,6).map((e,t)=>(0,a.jsx)("div",{className:"aspect-square bg-gray-800 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:scale-105 ".concat(t===l?"border-blue-500 shadow-lg shadow-blue-500/25":"border-transparent hover:border-gray-600"),onClick:()=>c(t),children:(0,a.jsx)("img",{src:e,alt:"".concat(s.title," ").concat(t+1),className:"w-full h-full object-cover"})},t)),s.images.length>6&&(0,a.jsxs)("div",{className:"aspect-square bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 text-sm border-2 border-dashed border-gray-600",children:["+",s.images.length-6]})]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(d.Zp,{className:"property-card-dark border-l-4 border-l-blue-500",children:[(0,a.jsx)(d.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(d.ZB,{className:"arabic-heading text-xl flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 text-blue-400"}),"معلومات العقار"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.E,{className:(e=>{switch(e){case"AVAILABLE":return"bg-green-600 text-white";case"SOLD":return"bg-red-600 text-white";case"RENTED":return"bg-blue-600 text-white";case"PENDING":return"bg-yellow-600 text-black";default:return"bg-gray-600 text-white"}})(s.status),children:(e=>{switch(e){case"AVAILABLE":return"متاح";case"SOLD":return"مباع";case"RENTED":return"مؤجر";case"PENDING":return"قيد المراجعة";default:return e}})(s.status)}),s.isFeatured&&(0,a.jsx)(n.E,{className:"bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-medium",children:"⭐ مميز"})]})]})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"نوع العقار"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-white",children:(e=>{switch(e){case"APARTMENT":return"شقة";case"VILLA":return"فيلا";case"TOWNHOUSE":return"تاون هاوس";case"PENTHOUSE":return"بنتهاوس";case"STUDIO":return"استوديو";case"OFFICE":return"مكتب";case"SHOP":return"محل تجاري";case"WAREHOUSE":return"مستودع";case"LAND":return"أرض";case"BUILDING":return"مبنى";default:return e}})(s.type)})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[s.bedrooms&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-4 text-center",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:s.bedrooms}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"غرف نوم"})]}),s.bathrooms&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-4 text-center",children:[(0,a.jsx)(b.A,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:s.bathrooms}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"حمامات"})]}),s.area&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-4 text-center",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:s.area}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"متر مربع"})]}),s.parking&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-4 text-center",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:s.parking}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"مواقف سيارات"})]})]}),s.yearBuilt&&(0,a.jsx)("div",{className:"bg-gray-800/30 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-blue-400"}),(0,a.jsx)("span",{className:"text-gray-300",children:"سنة البناء: "}),(0,a.jsx)("span",{className:"font-semibold text-white",children:s.yearBuilt})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[s.furnished&&(0,a.jsx)(n.E,{variant:"outline",className:"text-green-400 border-green-400 bg-green-400/10 px-3 py-1",children:"✓ مفروش"}),s.petFriendly&&(0,a.jsx)(n.E,{variant:"outline",className:"text-green-400 border-green-400 bg-green-400/10 px-3 py-1",children:"✓ يسمح بالحيوانات الأليفة"}),"SOLD"===s.status&&(0,a.jsx)(n.E,{className:"bg-red-600 text-white px-3 py-1",children:"\uD83C\uDFF7️ مباع"})]})]})]}),(0,a.jsxs)(d.Zp,{className:"property-card-dark",children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"arabic-heading",children:"الوصف"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("p",{className:"text-gray-300 leading-relaxed whitespace-pre-wrap",children:s.description})})]}),s.features&&s.features.length>0&&(0,a.jsxs)(d.Zp,{className:"property-card-dark",children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"arabic-heading",children:"المميزات"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:s.features.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-300",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,a.jsx)("span",{children:e})]},s))})})]}),s.amenities&&s.amenities.length>0&&(0,a.jsxs)(d.Zp,{className:"property-card-dark",children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"arabic-heading",children:"الخدمات"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:s.amenities.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-300",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,a.jsx)("span",{children:e})]},s))})})]}),s.utilities&&(0,a.jsxs)(d.Zp,{className:"property-card-dark",children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"arabic-heading",children:"المرافق المشمولة"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("p",{className:"text-gray-300",children:s.utilities})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(d.Zp,{className:"property-card-dark border-t-4 border-t-green-500",children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:"السعر"}),(0,a.jsx)("div",{className:"text-4xl font-bold text-white mb-2",children:(0,m.PW)(s.price,s.currency)}),"SOLD"===s.status&&(0,a.jsx)("div",{className:"inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium",children:"\uD83C\uDFF7️ تم البيع"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 pt-4 border-t border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-400",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[s.viewCount," مشاهدة"]})]}),s.isFeatured&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-yellow-400",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"مميز"})]})]})]})})}),(0,a.jsxs)(d.Zp,{className:"property-card-dark border-l-4 border-l-orange-500",children:[(0,a.jsx)(d.aR,{className:"pb-3",children:(0,a.jsxs)(d.ZB,{className:"arabic-heading flex items-center gap-2 text-lg",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-orange-400"}),"الموقع"]})}),(0,a.jsxs)(d.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:"العنوان"}),(0,a.jsx)("p",{className:"text-gray-200 font-medium",children:s.address})]}),(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:"المدينة والدولة"}),(0,a.jsxs)("p",{className:"text-gray-200 font-medium",children:[s.city,", ",s.country]})]}),s.location&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:"المنطقة"}),(0,a.jsx)("p",{className:"text-gray-200 font-medium",children:s.location})]})]})]}),(0,a.jsxs)(d.Zp,{className:"property-card-dark border-l-4 border-l-blue-500",children:[(0,a.jsx)(d.aR,{className:"pb-3",children:(0,a.jsxs)(d.ZB,{className:"arabic-heading flex items-center gap-2 text-lg",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-blue-400"}),"معلومات الاتصال"]})}),(0,a.jsxs)(d.Wu,{className:"space-y-4",children:[s.agent&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"الوكيل المسؤول"}),(0,a.jsx)("p",{className:"font-semibold text-white text-lg",children:s.agent.name})]})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-300",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-blue-400"}),(0,a.jsx)("span",{className:"text-sm",children:s.agent.email})]}),s.contactInfo&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-300",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:s.contactInfo})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-2",children:[(0,a.jsxs)(o.$,{className:"bg-green-600 hover:bg-green-700 text-white w-full",onClick:()=>s.contactInfo&&window.open("tel:".concat(s.contactInfo),"_self"),children:[(0,a.jsx)(x.A,{className:"h-4 w-4 ml-2"}),"اتصال مباشر"]}),(0,a.jsxs)(o.$,{variant:"outline",className:"border-blue-500 text-blue-400 hover:bg-blue-500/10 w-full",onClick:()=>{var e;return(null===(e=s.agent)||void 0===e?void 0:e.email)&&window.open("mailto:".concat(s.agent.email),"_self")},children:[(0,a.jsx)(A.A,{className:"h-4 w-4 ml-2"}),"إرسال رسالة"]})]})]}),!s.agent&&s.contactInfo&&(0,a.jsxs)("div",{className:"bg-gray-800/30 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-green-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"للاستفسار"}),(0,a.jsx)("p",{className:"font-semibold text-white text-lg",children:s.contactInfo})]})]}),(0,a.jsxs)(o.$,{className:"bg-green-600 hover:bg-green-700 text-white w-full",onClick:()=>window.open("tel:".concat(s.contactInfo),"_self"),children:[(0,a.jsx)(x.A,{className:"h-4 w-4 ml-2"}),"اتصال الآن"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-500/20",children:[(0,a.jsx)("h4",{className:"text-white font-medium mb-3 text-center",children:"طرق أخرى للتواصل"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(o.$,{size:"sm",variant:"outline",className:"border-green-500 text-green-400 hover:bg-green-500/10",onClick:()=>window.open("https://wa.me/971501234567","_blank"),children:"واتساب"}),(0,a.jsx)(o.$,{size:"sm",variant:"outline",className:"border-blue-500 text-blue-400 hover:bg-blue-500/10",children:"زيارة"})]})]})]})]})]})]}),I&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"relative max-w-4xl max-h-full",children:[(0,a.jsx)("button",{onClick:()=>C(!1),className:"absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 z-10",children:(0,a.jsx)(k.A,{className:"h-6 w-6"})}),(0,a.jsx)("img",{src:s.images[l],alt:s.title,className:"max-w-full max-h-full object-contain"}),s.images.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:D,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3",children:(0,a.jsx)(h.A,{className:"h-6 w-6"})}),(0,a.jsx)("button",{onClick:E,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3",children:(0,a.jsx)(g.A,{className:"h-6 w-6"})})]}),(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 rounded-full px-4 py-2",children:(0,a.jsxs)("span",{className:"text-white text-sm",children:[l+1," من ",s.images.length]})})]})}),s.contactInfo&&(0,a.jsx)("div",{className:"fixed bottom-6 right-6 z-40 md:hidden",children:(0,a.jsx)(o.$,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white rounded-full shadow-2xl shadow-green-600/25 w-16 h-16",onClick:()=>window.open("tel:".concat(s.contactInfo),"_self"),children:(0,a.jsx)(x.A,{className:"h-6 w-6"})})})]})}t(6721);var C=t(92138),E=t(13717),D=t(62525),S=t(53580);function F(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),{t}=(0,i.Y)(),{toast:n}=(0,S.dj)(),[d,m]=(0,r.useState)(null),[x,h]=(0,r.useState)(!0),[g,u]=(0,r.useState)(!1),p=e.id,b=e=>{let s={available:{id:e,title:"فيلا فاخرة في دبي مارينا",titleAr:"فيلا فاخرة في دبي مارينا",description:"فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.",descriptionAr:"فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.",price:25e5,currency:"AED",type:"VILLA",status:"AVAILABLE",bedrooms:4,bathrooms:3,area:350,location:"دبي مارينا",locationAr:"دبي مارينا",address:"123 ممشى المارينا",addressAr:"123 ممشى المارينا",city:"دبي",cityAr:"دبي",country:"الإمارات العربية المتحدة",countryAr:"الإمارات العربية المتحدة",latitude:25.0772,longitude:55.1395,images:["https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop"],features:["مسبح خاص","حديقة","موقف سيارات","أمن 24/7"],featuresAr:["مسبح خاص","حديقة","موقف سيارات","أمن 24/7"],amenities:["صالة رياضية","سبا","ملعب تنس","مارينا خاصة"],amenitiesAr:["صالة رياضية","سبا","ملعب تنس","مارينا خاصة"],yearBuilt:2020,parking:2,furnished:!0,petFriendly:!1,utilities:"جميع المرافق متضمنة",utilitiesAr:"جميع المرافق متضمنة",contactInfo:"+971 50 123 4567",agentId:"agent1",isActive:!0,isFeatured:!0,viewCount:125,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),agent:{id:"agent1",name:"أحمد محمد",email:"<EMAIL>"}},sold:{id:e,title:"شقة مباعة في برج خليفة",titleAr:"شقة مباعة في برج خليفة",description:"شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.",descriptionAr:"شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.",price:32e5,currency:"AED",type:"APARTMENT",status:"SOLD",bedrooms:3,bathrooms:2,area:180,location:"وسط المدينة",locationAr:"وسط المدينة",address:"برج خليفة، الطابق 45",addressAr:"برج خليفة، الطابق 45",city:"دبي",cityAr:"دبي",country:"الإمارات العربية المتحدة",countryAr:"الإمارات العربية المتحدة",latitude:25.1972,longitude:55.2744,images:["https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop"],features:["إطلالة على برج خليفة","مصعد عالي السرعة","أمن متقدم","موقف سيارات"],featuresAr:["إطلالة على برج خليفة","مصعد عالي السرعة","أمن متقدم","موقف سيارات"],amenities:["صالة رياضية","مسبح","سبا","مطاعم"],amenitiesAr:["صالة رياضية","مسبح","سبا","مطاعم"],yearBuilt:2018,parking:1,furnished:!0,petFriendly:!1,utilities:"جميع المرافق متضمنة",utilitiesAr:"جميع المرافق متضمنة",contactInfo:"+971 50 987 6543",agentId:"agent2",isActive:!1,isFeatured:!1,viewCount:89,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),agent:{id:"agent2",name:"فاطمة أحمد",email:"<EMAIL>"}}};return e.includes("sold")||e.includes("SOLD")?s.sold:s.available};(0,r.useEffect)(()=>{p&&(console.log("Component mounted, property ID:",p),j())},[p]);let j=async()=>{try{h(!0),console.log("Fetching property:",p),console.log("Using mock data for property:",p);let s=b(p);console.log("Mock property created:",s),m(s);try{let e=await c.Jf.getPropertyById(p);console.log("API data received:",e),e&&e.id&&m(e)}catch(a){var e;let s=a.status||(null===(e=a.response)||void 0===e?void 0:e.status),t=a.message||"Unknown error";404===s?console.log("Property ".concat(p," not found in database (404) - using mock data")):console.log("API error (using mock data):",s||t)}}catch(e){console.error("Error in fetchProperty:",e)}finally{h(!1)}},f=async()=>{if(confirm("هل أنت متأكد من حذف هذا العقار؟"))try{u(!0),await c.Jf.deleteProperty(p),n({title:"تم حذف العقار",description:"تم حذف العقار بنجاح"}),s.push("/dashboard/properties")}catch(e){console.error("Error deleting property:",e),n({title:"خطأ في حذف العقار",description:"حدث خطأ أثناء حذف العقار",variant:"destructive"})}finally{u(!1)}},N=()=>{s.push("/dashboard/properties")};return x?(0,a.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[(0,a.jsx)("div",{className:"loading-spinner"}),(0,a.jsx)("span",{className:"mr-3 text-lg",children:"جاري تحميل العقار..."})]})})}):d?(0,a.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:[(0,a.jsx)("div",{className:"property-header-dark mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(o.$,{onClick:N,variant:"ghost",className:"text-gray-400 hover:text-white mb-4",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 ml-2"}),"العودة إلى قائمة العقارات"]}),(0,a.jsx)("h1",{className:"arabic-heading text-3xl font-bold text-white",children:d.title}),(0,a.jsxs)("p",{className:"text-gray-400 mt-2",children:[d.location," • ",d.city," • ",d.country]})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)(o.$,{onClick:()=>{s.push("/dashboard/properties/".concat(p,"/edit"))},className:"btn-secondary",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 ml-2"}),"تعديل"]}),(0,a.jsxs)(o.$,{onClick:f,disabled:g,className:"bg-red-600 hover:bg-red-700 text-white",children:[g&&(0,a.jsx)("div",{className:"loading-spinner"}),(0,a.jsx)(D.A,{className:"h-4 w-4 ml-2"}),"حذف"]})]})]})}),(0,a.jsx)(I,{property:d})]})}):(0,a.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"العقار غير موجود"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"لم يتم العثور على العقار المطلوب"}),(0,a.jsxs)(o.$,{onClick:N,className:"btn-primary",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 ml-2"}),"العودة إلى قائمة العقارات"]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1368,4277,3464,3552,446,8441,1684,7358],()=>s(37637)),_N_E=e.O()}]);