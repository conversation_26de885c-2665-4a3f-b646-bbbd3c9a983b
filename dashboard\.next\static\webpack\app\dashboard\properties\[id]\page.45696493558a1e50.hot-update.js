"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx":
/*!************************************************!*\
  !*** ./app/dashboard/properties/[id]/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertyShowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/properties/PropertyShowComponent */ \"(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertyShowPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const propertyId = params.id;\n    // Function to generate different mock properties based on ID\n    const getMockProperty = (id)=>{\n        const baseProperties = {\n            available: {\n                id,\n                title: 'فيلا فاخرة في دبي مارينا',\n                titleAr: 'فيلا فاخرة في دبي مارينا',\n                description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',\n                descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',\n                price: 2500000,\n                currency: 'AED',\n                type: 'VILLA',\n                status: 'AVAILABLE',\n                bedrooms: 4,\n                bathrooms: 3,\n                area: 350,\n                location: 'دبي مارينا',\n                locationAr: 'دبي مارينا',\n                address: '123 ممشى المارينا',\n                addressAr: '123 ممشى المارينا',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.0772,\n                longitude: 55.1395,\n                images: [\n                    'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                featuresAr: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                yearBuilt: 2020,\n                parking: 2,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 123 4567',\n                agentId: 'agent1',\n                isActive: true,\n                isFeatured: true,\n                viewCount: 125,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent1',\n                    name: 'أحمد محمد',\n                    email: '<EMAIL>'\n                }\n            },\n            sold: {\n                id,\n                title: 'شقة مباعة في برج خليفة',\n                titleAr: 'شقة مباعة في برج خليفة',\n                description: 'شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.',\n                descriptionAr: 'شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.',\n                price: 3200000,\n                currency: 'AED',\n                type: 'APARTMENT',\n                status: 'SOLD',\n                bedrooms: 3,\n                bathrooms: 2,\n                area: 180,\n                location: 'وسط المدينة',\n                locationAr: 'وسط المدينة',\n                address: 'برج خليفة، الطابق 45',\n                addressAr: 'برج خليفة، الطابق 45',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.1972,\n                longitude: 55.2744,\n                images: [\n                    'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'إطلالة على برج خليفة',\n                    'مصعد عالي السرعة',\n                    'أمن متقدم',\n                    'موقف سيارات'\n                ],\n                featuresAr: [\n                    'إطلالة على برج خليفة',\n                    'مصعد عالي السرعة',\n                    'أمن متقدم',\n                    'موقف سيارات'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'مسبح',\n                    'سبا',\n                    'مطاعم'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'مسبح',\n                    'سبا',\n                    'مطاعم'\n                ],\n                yearBuilt: 2018,\n                parking: 1,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 987 6543',\n                agentId: 'agent2',\n                isActive: false,\n                isFeatured: false,\n                viewCount: 89,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent2',\n                    name: 'فاطمة أحمد',\n                    email: '<EMAIL>'\n                }\n            }\n        };\n        // Return different properties based on ID pattern\n        if (id.includes('sold') || id.includes('SOLD')) {\n            return baseProperties.sold;\n        }\n        return baseProperties.available;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyShowPage.useEffect\": ()=>{\n            if (propertyId) {\n                console.log('Component mounted, property ID:', propertyId);\n                fetchProperty();\n            }\n        }\n    }[\"PropertyShowPage.useEffect\"], [\n        propertyId\n    ]);\n    const fetchProperty = async ()=>{\n        try {\n            setIsLoading(true);\n            console.log('Fetching property:', propertyId);\n            // Create different mock properties based on ID\n            console.log('Using mock data for property:', propertyId);\n            const mockProperty = getMockProperty(propertyId);\n            console.log('Mock property created:', mockProperty);\n            setProperty(mockProperty);\n            // Try to fetch from API in background (optional)\n            try {\n                const data = await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.getPropertyById(propertyId);\n                console.log('API data received:', data);\n                // Only replace mock data if we get valid API data\n                if (data && data.id) {\n                    setProperty(data);\n                }\n            } catch (apiError) {\n                var _apiError_response;\n                const status = apiError.status || ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : _apiError_response.status);\n                const message = apiError.message || 'Unknown error';\n                if (status === 404) {\n                    console.log(\"Property \".concat(propertyId, \" not found in database (404) - using mock data\"));\n                } else if (apiError.message && apiError.message.includes('Network Error')) {\n                    console.log('Network error - backend server not available, using mock data');\n                } else {\n                    console.log('API error (using mock data):', status || message);\n                }\n            // Keep using mock data - this is expected for mock IDs or when property doesn't exist\n            }\n        } catch (error) {\n            console.error('Error in fetchProperty:', error);\n            // Ensure we always have mock data even if something goes wrong\n            if (!property) {\n                const mockProperty = getMockProperty(propertyId);\n                setProperty(mockProperty);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/properties/\".concat(propertyId, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {\n            return;\n        }\n        try {\n            setIsDeleting(true);\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.deleteProperty(propertyId);\n            toast({\n                title: 'تم حذف العقار',\n                description: 'تم حذف العقار بنجاح'\n            });\n            router.push('/dashboard/properties');\n        } catch (error) {\n            console.error('Error deleting property:', error);\n            toast({\n                title: 'خطأ في حذف العقار',\n                description: 'حدث خطأ أثناء حذف العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/dashboard/properties');\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[400px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-3 text-lg\",\n                            children: \"جاري تحميل العقار...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 278,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-400 mb-4\",\n                            children: \"العقار غير موجود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"لم يتم العثور على العقار المطلوب\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: handleBack,\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة إلى قائمة العقارات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleBack,\n                                        variant: \"ghost\",\n                                        className: \"text-gray-400 hover:text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة إلى قائمة العقارات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"arabic-heading text-3xl font-bold text-white\",\n                                        children: property.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mt-2\",\n                                        children: [\n                                            property.location,\n                                            \" • \",\n                                            property.city,\n                                            \" • \",\n                                            property.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleEdit,\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تعديل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                        children: [\n                                            isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__.PropertyShowComponent, {\n                    property: property\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 308,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowPage, \"iRGGcDe76PMT/k9AlYn3/VeU8tI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PropertyShowPage;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx\n"));

/***/ })

});