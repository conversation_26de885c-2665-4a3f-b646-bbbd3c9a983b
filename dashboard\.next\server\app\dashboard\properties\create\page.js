(()=>{var e={};e.id=6108,e.ids=[6108],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},52980:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>l,tree:()=>n});var s=t(65239),o=t(48088),i=t(88170),a=t.n(i),p=t(30893),d={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>p[e]);t.d(r,d);let n={children:["",{children:["dashboard",{children:["properties",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56095)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\create\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/properties/create/page",pathname:"/dashboard/properties/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56095:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\create\\page.tsx","default")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65600:(e,r,t)=>{Promise.resolve().then(t.bind(t,56095))},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},79557:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),o=t(16189),i=t(59828),a=t(10452);function p(){let e=(0,o.useRouter)(),{t:r}=(0,a.Y)();return(0,s.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,s.jsxs)("div",{className:"property-header-dark",children:[(0,s.jsx)("h1",{className:"arabic-heading",children:r("properties.create")}),(0,s.jsx)("p",{className:"text-slate-400",children:r("properties.subtitle")})]}),(0,s.jsx)(i.o,{onSuccess:()=>{e.push("/dashboard/properties")},onCancel:()=>{e.push("/dashboard/properties")}})]})})}t(83279)},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},94976:(e,r,t)=>{Promise.resolve().then(t.bind(t,79557))},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,8157,2190,3903,5153,1467,1060,4097,3298,4088,9464,381,8318],()=>t(52980));module.exports=s})();