{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/properties(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/properties/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/campaigns(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/campaigns/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/users(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/users/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(\\.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(\\.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-out(\\.json)?[\\/#\\?]?$", "originalSource": "/sign-out"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "sDIGMDKRMXvVT-Fw-KTon", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "26zddxOokX6zbWrQMJkqoSTZS6YNqhv9deADI8LBdMY=", "__NEXT_PREVIEW_MODE_ID": "4aaa75087b9cf04a2ef4710bd1230d97", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cc5e6131ae5c8db72fbb3439223da42cbe950ab27076e404a47d63e77c8f784b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b46b7d5bb27bbf53f592332e6edbbc633f137e7f3a6fe8bfa6dd2ca9b30ed533"}}}, "functions": {}, "sortedMiddleware": ["/"]}