"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx":
/*!************************************************!*\
  !*** ./app/dashboard/properties/[id]/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertyShowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/properties/PropertyShowComponent */ \"(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction PropertyShowPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUsingMockData, setIsUsingMockData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const propertyId = params.id;\n    // Function to generate different mock properties based on ID\n    const getMockProperty = (id)=>{\n        const baseProperties = {\n            available: {\n                id,\n                title: 'فيلا فاخرة في دبي مارينا',\n                titleAr: 'فيلا فاخرة في دبي مارينا',\n                description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',\n                descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',\n                price: 2500000,\n                currency: 'AED',\n                type: 'VILLA',\n                status: 'AVAILABLE',\n                bedrooms: 4,\n                bathrooms: 3,\n                area: 350,\n                location: 'دبي مارينا',\n                locationAr: 'دبي مارينا',\n                address: '123 ممشى المارينا',\n                addressAr: '123 ممشى المارينا',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.0772,\n                longitude: 55.1395,\n                images: [\n                    'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                featuresAr: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                yearBuilt: 2020,\n                parking: 2,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 123 4567',\n                agentId: 'agent1',\n                isActive: true,\n                isFeatured: true,\n                viewCount: 125,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent1',\n                    name: 'أحمد محمد',\n                    email: '<EMAIL>'\n                }\n            },\n            sold: {\n                id,\n                title: 'شقة مباعة في برج خليفة',\n                titleAr: 'شقة مباعة في برج خليفة',\n                description: 'شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.',\n                descriptionAr: 'شقة فاخرة من 3 غرف نوم في برج خليفة مع إطلالة رائعة على المدينة. تم بيع هذه الشقة مؤخراً بسعر ممتاز.',\n                price: 3200000,\n                currency: 'AED',\n                type: 'APARTMENT',\n                status: 'SOLD',\n                bedrooms: 3,\n                bathrooms: 2,\n                area: 180,\n                location: 'وسط المدينة',\n                locationAr: 'وسط المدينة',\n                address: 'برج خليفة، الطابق 45',\n                addressAr: 'برج خليفة، الطابق 45',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.1972,\n                longitude: 55.2744,\n                images: [\n                    'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'إطلالة على برج خليفة',\n                    'مصعد عالي السرعة',\n                    'أمن متقدم',\n                    'موقف سيارات'\n                ],\n                featuresAr: [\n                    'إطلالة على برج خليفة',\n                    'مصعد عالي السرعة',\n                    'أمن متقدم',\n                    'موقف سيارات'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'مسبح',\n                    'سبا',\n                    'مطاعم'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'مسبح',\n                    'سبا',\n                    'مطاعم'\n                ],\n                yearBuilt: 2018,\n                parking: 1,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 987 6543',\n                agentId: 'agent2',\n                isActive: false,\n                isFeatured: false,\n                viewCount: 89,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent2',\n                    name: 'فاطمة أحمد',\n                    email: '<EMAIL>'\n                }\n            }\n        };\n        // Return different properties based on ID pattern\n        if (id.includes('sold') || id.includes('SOLD')) {\n            return baseProperties.sold;\n        }\n        return baseProperties.available;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyShowPage.useEffect\": ()=>{\n            if (propertyId) {\n                console.log('Component mounted, property ID:', propertyId);\n                fetchProperty();\n            }\n        }\n    }[\"PropertyShowPage.useEffect\"], [\n        propertyId\n    ]);\n    const fetchProperty = async ()=>{\n        try {\n            setIsLoading(true);\n            console.log('Fetching property:', propertyId);\n            // Create different mock properties based on ID\n            console.log('Using mock data for property:', propertyId);\n            const mockProperty = getMockProperty(propertyId);\n            console.log('Mock property created:', mockProperty);\n            setProperty(mockProperty);\n            // Try to fetch from API in background (optional)\n            try {\n                const data = await _services_propertyService__WEBPACK_IMPORTED_MODULE_3__.propertyService.getPropertyById(propertyId);\n                console.log('API data received:', data);\n                // Only replace mock data if we get valid API data\n                if (data && data.id) {\n                    setProperty(data);\n                }\n            } catch (apiError) {\n                var _apiError_response;\n                const status = apiError.status || ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : _apiError_response.status);\n                const message = apiError.message || 'Unknown error';\n                if (status === 404) {\n                    console.log(\"Property \".concat(propertyId, \" not found in database (404) - using mock data\"));\n                } else if (apiError.message && apiError.message.includes('Network Error')) {\n                    console.log('Network error - backend server not available, using mock data');\n                } else {\n                    console.log('API error (using mock data):', status || message);\n                }\n            // Keep using mock data - this is expected for mock IDs or when property doesn't exist\n            }\n        } catch (error) {\n            console.error('Error in fetchProperty:', error);\n            // Ensure we always have mock data even if something goes wrong\n            if (!property) {\n                const mockProperty = getMockProperty(propertyId);\n                setProperty(mockProperty);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/properties/\".concat(propertyId, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {\n            return;\n        }\n        try {\n            setIsDeleting(true);\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_3__.propertyService.deleteProperty(propertyId);\n            toast({\n                title: 'تم حذف العقار',\n                description: 'تم حذف العقار بنجاح'\n            });\n            router.push('/dashboard/properties');\n        } catch (error) {\n            console.error('Error deleting property:', error);\n            toast({\n                title: 'خطأ في حذف العقار',\n                description: 'حدث خطأ أثناء حذف العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/dashboard/properties');\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[400px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-3 text-lg\",\n                            children: \"جاري تحميل العقار...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 279,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-400 mb-4\",\n                            children: \"العقار غير موجود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"لم يتم العثور على العقار المطلوب\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: handleBack,\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة إلى قائمة العقارات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 292,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleBack,\n                                        variant: \"ghost\",\n                                        className: \"text-gray-400 hover:text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة إلى قائمة العقارات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"arabic-heading text-3xl font-bold text-white\",\n                                        children: property.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mt-2\",\n                                        children: [\n                                            property.location,\n                                            \" • \",\n                                            property.city,\n                                            \" • \",\n                                            property.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleEdit,\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تعديل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                        children: [\n                                            isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_4__.PropertyShowComponent, {\n                    property: property\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 309,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 308,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowPage, \"XT7sA6P2nvGcAMSqms9mmIO9DKM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = PropertyShowPage;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx\n"));

/***/ })

});