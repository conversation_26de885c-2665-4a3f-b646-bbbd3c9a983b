exports.id=381,exports.ids=[381],exports.modules={1659:(e,r,s)=>{Promise.resolve().then(s.bind(s,82427)),Promise.resolve().then(s.bind(s,18075))},12484:(e,r,s)=>{"use strict";s.d(r,{ES:()=>a,eo:()=>t});let t=["ar"],a={ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"الذكاء الاصطناعي","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","properties.title":"العقارات","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.add":"إضافة عقار","properties.create":"إنشاء عقار جديد","properties.edit":"تعديل العقار","properties.delete":"حذف العقار","properties.view":"عرض العقار","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.currency":"العملة","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.features":"المميزات","property.amenities":"الخدمات","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","validation.required":"هذا الحقل مطلوب","validation.email":"يرجى إدخال بريد إلكتروني صحيح","validation.minLength":"يجب أن يكون الحد الأدنى {{min}} أحرف","validation.maxLength":"يجب أن يكون الحد الأقصى {{max}} أحرف","validation.number":"يرجى إدخال رقم صحيح","validation.positive":"يجب أن يكون الرقم أكبر من الصفر","images.upload":"رفع الصور","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت"}}}},14033:(e,r,s)=>{"use strict";s.d(r,{Sidebar:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\sidebar.tsx","Sidebar")},18075:(e,r,s)=>{"use strict";s.d(r,{Topbar:()=>N});var t=s(60687),a=s(72840),o=s(11437),i=s(21134),n=s(363),l=s(97051),d=s(58869),c=s(40083),p=s(59821),m=s(24934),u=s(55629),h=s(10218),f=s(68082),x=s(12484),y=s(70373),b=s(16189),g=s(70333);function N({user:e}){let{setTheme:r}=(0,h.D)(),{i18n:s,t:N}=(0,f.B)(),v=(0,b.useRouter)(),j=e=>{s.changeLanguage(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},w=e?e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`:e.email.split("@")[0]:"User",A=e?e.firstName&&e.lastName?`${e.firstName[0]}${e.lastName[0]}`.toUpperCase():e.email[0].toUpperCase():"U",E=async()=>{try{if((await fetch("/api/auth/logout",{method:"POST"})).ok)(0,g.oR)({title:N("auth.logoutSuccess"),description:N("auth.redirectingToLogin")}),v.push("/sign-in");else throw Error("Logout failed")}catch(e){console.error("Logout error:",e),(0,g.oR)({variant:"destructive",title:N("auth.logoutFailed"),description:e instanceof Error?e.message:String(e)})}};return(0,t.jsxs)("div",{className:"h-16 border-b bg-card flex items-center px-6 justify-between",children:[(0,t.jsx)("div",{className:"flex-1",children:e&&(0,t.jsx)("div",{className:"hidden md:flex items-center gap-2",children:(0,t.jsxs)(p.E,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[(0,t.jsx)(a.A,{className:"h-3.5 w-3.5"}),(0,t.jsx)("span",{className:"capitalize",children:e.role.name})]})})}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[e&&(0,t.jsxs)("div",{className:"hidden md:block text-sm mr-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Welcome,"})," ",(0,t.jsx)("span",{className:"font-medium",children:w})]}),(0,t.jsxs)(u.rI,{children:[(0,t.jsx)(u.ty,{asChild:!0,children:(0,t.jsx)(m.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(o.A,{className:"h-5 w-5"})})}),(0,t.jsx)(u.SQ,{align:"end",children:x.eo.map(e=>(0,t.jsx)(u._2,{onClick:()=>j(e),className:"cursor-pointer",children:"en"===e?"English":"العربية"},e))})]}),(0,t.jsxs)(u.rI,{children:[(0,t.jsx)(u.ty,{asChild:!0,children:(0,t.jsxs)(m.$,{variant:"ghost",size:"icon",children:[(0,t.jsx)(i.A,{className:"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,t.jsx)(n.A,{className:"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,t.jsxs)(u.SQ,{align:"end",children:[(0,t.jsx)(u._2,{onClick:()=>r("light"),children:"Light"}),(0,t.jsx)(u._2,{onClick:()=>r("dark"),children:"Dark"}),(0,t.jsx)(u._2,{onClick:()=>r("system"),children:"System"})]})]}),(0,t.jsx)(m.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(l.A,{className:"h-5 w-5"})}),(0,t.jsxs)(u.rI,{children:[(0,t.jsx)(u.ty,{asChild:!0,children:(0,t.jsx)(m.$,{variant:"ghost",className:"relative h-9 w-9 rounded-full",children:(0,t.jsxs)(y.eu,{children:[(0,t.jsx)(y.BK,{src:e?.profileImage||"",alt:w}),(0,t.jsx)(y.q5,{children:A})]})})}),(0,t.jsxs)(u.SQ,{align:"end",children:[(0,t.jsxs)(u._2,{className:"cursor-default",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:w})]}),(0,t.jsxs)(u._2,{onClick:E,className:"cursor-pointer text-destructive",children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:N("auth.logout")})]})]})]})]})]})}},37621:(e,r,s)=>{"use strict";s.d(r,{Topbar:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Topbar() from the server but Topbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\topbar.tsx","Topbar")},41098:(e,r,s)=>{"use strict";s.d(r,{HW:()=>o,qc:()=>n});var t=s(56814);async function a(){return await (0,t.j2)()}async function o(){let e=await a();return e?.user?.email?e.user:null}async function i(e){let r=await o();return!!r&&r.role===e}async function n(){return await i("ADMIN")}},55629:(e,r,s)=>{"use strict";s.d(r,{SQ:()=>m,_2:()=>u,mB:()=>h,rI:()=>c,ty:()=>p});var t=s(60687),a=s(43210),o=s(26312),i=s(14952),n=s(13964),l=s(65822),d=s(96241);let c=o.bL,p=o.l9;o.YJ,o.ZL,o.Pb,o.z6,a.forwardRef(({className:e,inset:r,children:s,...a},n)=>(0,t.jsxs)(o.ZP,{ref:n,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",e),...a,children:[s,(0,t.jsx)(i.A,{className:"ml-auto"})]})).displayName=o.ZP.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(o.G5,{ref:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",e),...r})).displayName=o.G5.displayName;let m=a.forwardRef(({className:e,sideOffset:r=4,...s},a)=>(0,t.jsx)(o.ZL,{children:(0,t.jsx)(o.UC,{ref:a,sideOffset:r,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",e),...s})}));m.displayName=o.UC.displayName;let u=a.forwardRef(({className:e,inset:r,...s},a)=>(0,t.jsx)(o.q7,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",e),...s}));u.displayName=o.q7.displayName,a.forwardRef(({className:e,children:r,checked:s,...a},i)=>(0,t.jsxs)(o.H_,{ref:i,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...a,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(o.VF,{children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})}),r]})).displayName=o.H_.displayName,a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(o.hN,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(o.VF,{children:(0,t.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),r]})).displayName=o.hN.displayName,a.forwardRef(({className:e,inset:r,...s},a)=>(0,t.jsx)(o.JU,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",e),...s})).displayName=o.JU.displayName;let h=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(o.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...r}));h.displayName=o.wv.displayName},56814:(e,r,s)=>{"use strict";s.d(r,{Jv:()=>m,Y9:()=>c,j2:()=>p});var t=s(19443),a=s(16467),o=s(10189),i=s(56056),n=s(73560),l=s(79464);let d={adapter:(0,a.y)(l.A),providers:[(0,o.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){try{if(!e?.email||!e?.password)return null;console.log("Looking for user with email:",e.email);let r=await l.A.user.findUnique({where:{email:e.email}});if(console.log("User found:",r?"yes":"no"),!r)return console.log("User not found:",e.email),null;let t=s(5486);console.log("Comparing password for user:",e.email);try{let s=await t.compare(e.password,r.password);if(console.log("Password match:",s?"yes":"no"),!s)return console.log("Password doesn't match for user:",e.email),null;return console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:`${r.firstName||""} ${r.lastName||""}`.trim()||r.email,image:r.profileImage||void 0,role:r.role||"USER"}}catch(e){return console.error("Error comparing passwords:",e),null}}catch(e){return console.error("Error in authorize function:",e),null}}}),(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(e.user&&(e.user.role=r.role,e.user.id=r.id),e),signIn:async e=>!0},pages:{signIn:"/sign-in",signOut:"/sign-out",error:"/sign-in",newUser:"/sign-up",verifyRequest:"/sign-in"},session:{strategy:"jwt",maxAge:2592e3},cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}},secret:process.env.NEXTAUTH_SECRET},{handlers:c,auth:p,signIn:m,signOut:u}=(0,t.Ay)(d)},59821:(e,r,s)=>{"use strict";s.d(r,{E:()=>n});var t=s(60687);s(43210);var a=s(24224),o=s(96241);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:r,...s}){return(0,t.jsx)("div",{className:(0,o.cn)(i({variant:r}),e),...s})}},68082:(e,r,s)=>{"use strict";s.d(r,{B:()=>i});var t=s(46755),a=s(16457),o=s(12484);function i(){return(0,a.Bd)()}t.Ay.use(a.r9).init({lng:"en",fallbackLng:"en",resources:o.ES,interpolation:{escapeValue:!1}})},70333:(e,r,s)=>{"use strict";s.d(r,{dj:()=>m,oR:()=>p});var t=s(43210);let a=0,o=new Map,i=e=>{if(o.has(e))return;let r=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,r)},n=(e,r)=>{switch(r.type){case"ADD_TOAST":return{...e,toasts:[r.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===r.toast.id?{...e,...r.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=r;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===r.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==r.toastId)}}},l=[],d={toasts:[]};function c(e){d=n(d,e),l.forEach(e=>{e(d)})}function p({...e}){let r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...e,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,r]=t.useState(d);return t.useEffect(()=>(l.push(r),()=>{let e=l.indexOf(r);e>-1&&l.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},70373:(e,r,s)=>{"use strict";s.d(r,{BK:()=>l,eu:()=>n,q5:()=>d});var t=s(60687),a=s(43210),o=s(92951),i=s(96241);let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(o.bL,{ref:s,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...r}));n.displayName=o.bL.displayName;let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(o._V,{ref:s,className:(0,i.cn)("aspect-square h-full w-full",e),...r}));l.displayName=o._V.displayName;let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(o.H4,{ref:s,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...r}));d.displayName=o.H4.displayName},82427:(e,r,s)=>{"use strict";s.d(r,{Sidebar:()=>T});var t=s(60687),a=s(96241),o=s(24934),i=s(16189),n=s(85814),l=s.n(n),d=s(53411),c=s(58869),p=s(41312),m=s(58887),u=s(20798),h=s(10022),f=s(40228),x=s(83753),y=s(72840),b=s(56843),g=s(32192),N=s(84027),v=s(12941),j=s(11860),w=s(43210),A=s(68082);let E=[{label:"sidebar.analytics",icon:d.A,href:"/dashboard/analytics",roles:["ADMIN","AGENT"]},{label:"sidebar.user",icon:c.A,href:"/dashboard/user",roles:["USER","CLIENT","AGENT","ADMIN"]},{label:"sidebar.clients",icon:p.A,href:"/dashboard/clients",roles:["ADMIN","AGENT"]},{label:"sidebar.messaging",icon:m.A,href:"/dashboard/messaging",roles:["ADMIN","AGENT","CLIENT","USER"]},{label:"sidebar.marketing",icon:u.A,href:"/dashboard/marketing",roles:["ADMIN","AGENT"],children:[{label:"sidebar.campaigns",icon:u.A,href:"/dashboard/campaigns",roles:["ADMIN","AGENT"]},{label:"sidebar.templates",icon:h.A,href:"/dashboard/marketing/templates",roles:["ADMIN","AGENT"]}]},{label:"sidebar.appointments",icon:f.A,href:"/dashboard/appointments",roles:["ADMIN","AGENT","CLIENT","USER"]},{label:"sidebar.ai-chatbot",icon:x.A,href:"/dashboard/ai-chatbot",roles:["ADMIN","AGENT"]},{label:"sidebar.database",icon:y.A,href:"/dashboard/data",roles:["ADMIN"]},{label:"sidebar.users",icon:b.A,href:"/dashboard/users",roles:["ADMIN"]},{label:"sidebar.properties",icon:g.A,href:"/dashboard/properties",roles:["ADMIN","AGENT","CLIENT","USER"]},{label:"sidebar.settings",icon:N.A,href:"/dashboard/settings",roles:["ADMIN","AGENT","CLIENT","USER"]},{label:"sidebar.profile",icon:c.A,href:"/dashboard/profile",roles:["ADMIN","AGENT","CLIENT","USER"]}];function T({userRole:e="USER"}){let r=(0,i.usePathname)(),[s,n]=(0,w.useState)(!1),{t:d}=(0,A.B)(),c=E.filter(r=>r.roles.includes(e));return(0,t.jsxs)("div",{className:(0,a.cn)("relative h-full border-r bg-card transition-all duration-300",s?"w-16":"w-64"),children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b",children:[!s&&(0,t.jsx)(l(),{href:"/dashboard/analytics",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-primary",children:"Real Estate AI"})}),(0,t.jsx)(o.$,{variant:"ghost",size:"icon",className:"ml-auto",onClick:()=>n(!s),children:s?(0,t.jsx)(v.A,{className:"h-5 w-5"}):(0,t.jsx)(j.A,{className:"h-5 w-5"})})]}),!s&&(0,t.jsx)("div",{className:"px-4 py-2 border-b",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"capitalize",children:[e," Role"]})]})}),(0,t.jsx)("div",{className:"space-y-1 py-4",children:c.map(e=>e.children?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:(0,a.cn)("flex items-center px-4 py-2 text-sm font-medium text-muted-foreground",s&&"justify-center px-0"),children:[(0,t.jsx)(e.icon,{className:(0,a.cn)("h-5 w-5",s?"mr-0":"mr-3")}),!s&&(0,t.jsx)("span",{children:d(e.label)})]}),!s&&e.children.map(e=>(0,t.jsxs)(l(),{href:e.href,className:(0,a.cn)("flex items-center pl-8 pr-4 py-2 text-sm font-medium transition-colors",r===e.href||r.startsWith(`${e.href}/`)?"bg-primary/10 text-primary":"text-muted-foreground hover:bg-primary/5 hover:text-primary"),children:[(0,t.jsx)(e.icon,{className:"h-4 w-4 mr-3"}),(0,t.jsx)("span",{children:d(e.label)})]},e.href)),s&&e.children.map(e=>(0,t.jsx)(l(),{href:e.href,className:(0,a.cn)("flex items-center justify-center px-0 py-2 text-sm font-medium transition-colors",r===e.href||r.startsWith(`${e.href}/`)?"bg-primary/10 text-primary":"text-muted-foreground hover:bg-primary/5 hover:text-primary"),children:(0,t.jsx)(e.icon,{className:"h-4 w-4"})},e.href))]},e.href):(0,t.jsxs)(l(),{href:e.href,className:(0,a.cn)("flex items-center px-4 py-3 text-sm font-medium transition-colors",r===e.href||r.startsWith(`${e.href}/`)?"bg-primary/10 text-primary":"text-muted-foreground hover:bg-primary/5 hover:text-primary",s&&"justify-center px-0"),children:[(0,t.jsx)(e.icon,{className:(0,a.cn)("h-5 w-5",s?"mr-0":"mr-3")}),!s&&(0,t.jsx)("span",{children:d(e.label)})]},e.href))})]})}},83249:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(37413),a=s(14033),o=s(37621),i=s(39916),n=s(41098);async function l({children:e}){let r=await (0,n.HW)();return r||(0,i.redirect)("/sign-in?redirect_url=/dashboard/analytics"),r.role&&["ADMIN","USER","AGENT","CLIENT"].includes(r.role)||(0,i.redirect)(`/access-denied?url=${encodeURIComponent("/dashboard/analytics")}`),(0,t.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,t.jsx)(a.Sidebar,{userRole:r.role}),(0,t.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden",children:[(0,t.jsx)(o.Topbar,{user:r}),(0,t.jsx)("main",{className:"flex-1 overflow-auto p-6",children:e})]})]})}},87587:(e,r,s)=>{Promise.resolve().then(s.bind(s,14033)),Promise.resolve().then(s.bind(s,37621))}};