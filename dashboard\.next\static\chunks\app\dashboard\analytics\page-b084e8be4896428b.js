(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{14503:(e,t,s)=>{"use strict";s.d(t,{dj:()=>f,oR:()=>u});var a=s(12115);let r=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function u(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=a.useState(c);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},15925:(e,t,s)=>{"use strict";s.d(t,{StatsCards:()=>b});var a=s(95155),r=s(88482),n=s(97168),o=s(51154),i=s(17580),l=s(81497),c=s(13062),d=s(12115),u=s(31886);async function f(){try{return console.log("Fetching analytics stats from /stats"),await u.A.get("/stats")}catch(e){throw console.error("Error fetching analytics stats:",e),e}}async function m(){try{return console.log("Fetching messages by day from /stats/messages-by-day"),await u.A.get("/stats/messages-by-day")}catch(e){throw console.error("Error fetching messages by day:",e),e}}async function h(){try{return console.log("Fetching clients by day from /stats/clients-by-day"),await u.A.get("/stats/clients-by-day")}catch(e){throw console.error("Error fetching clients by day:",e),e}}async function g(){try{return console.log("Fetching messages by type from /stats/messages-by-type"),await u.A.get("/stats/messages-by-type")}catch(e){throw console.error("Error fetching messages by type:",e),e}}async function p(){try{return console.log("Fetching all analytics data from /stats/all"),await u.A.get("/stats/all")}catch(e){throw console.error("Error fetching all analytics data:",e),e}}let y={totalClients:0,totalMessages:0,activeClients:0,activeCampaigns:0,timestamp:new Date().toISOString()},x={labels:["Day 1","Day 2","Day 3","Day 4","Day 5","Day 6","Day 7"],datasets:[{label:"Count",data:[0,0,0,0,0,0,0],borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.2)"}]};var v=s(14503);function b(){let{stats:e,loading:t,error:s,refreshData:u}=function(){let[e,t]=(0,d.useState)(null),[s,a]=(0,d.useState)(null),[r,n]=(0,d.useState)(null),[o,i]=(0,d.useState)(null),[l,c]=(0,d.useState)(!0),[u,v]=(0,d.useState)(null),b=(0,d.useCallback)(async()=>{try{c(!0),v(null),console.log("Attempting to fetch all analytics data...");try{let e=await p();console.log("Successfully fetched all analytics data:",e),t(e.stats),a(e.messagesByDay),n(e.clientsByDay),i(e.messagesByType)}catch(e){console.warn("Failed to fetch all analytics data, falling back to individual requests:",e.message);try{console.log("Fetching analytics stats individually...");let e=await f();console.log("Successfully fetched analytics stats:",e),t(e)}catch(e){console.error("Error fetching analytics stats:",e.message),console.log("Using fallback stats data"),t(y)}try{console.log("Fetching messages by day individually...");let e=await m();console.log("Successfully fetched messages by day:",e),a(e)}catch(e){console.error("Error fetching messages by day:",e.message),console.log("Using fallback messages by day data"),a(x)}try{console.log("Fetching clients by day individually...");let e=await h();console.log("Successfully fetched clients by day:",e),n(e)}catch(e){console.error("Error fetching clients by day:",e.message),console.log("Using fallback clients by day data"),n(x)}try{console.log("Fetching messages by type individually...");let e=await g();console.log("Successfully fetched messages by type:",e),i(e)}catch(e){console.error("Error fetching messages by type:",e.message),console.log("Using fallback messages by type data"),i({labels:["Bot","Human"],datasets:[{label:"Messages",data:[0,0]}]})}}}catch(e){console.error("Error fetching analytics data:",e.message),v(e.message||"Failed to load analytics data"),console.log("Using all fallback data due to error"),t(y),a(x),n(x),i({labels:["Bot","Human"],datasets:[{label:"Messages",data:[0,0]}]})}finally{c(!1)}},[]);return(0,d.useEffect)(()=>{b()},[b]),{stats:e,messagesByDay:s,clientsByDay:r,messagesByType:o,loading:l,error:u,refreshData:b}}(),{toast:b}=(0,v.dj)();return((0,d.useEffect)(()=>{s&&b({title:"Error loading statistics",description:s,variant:"destructive"})},[s,b]),t)?(0,a.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Dashboard Statistics"}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{u(),b({title:"Refreshing data",description:"Fetching the latest statistics..."})},disabled:t,children:[t?(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2 animate-spin"}):null,"Refresh Data"]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Clients"}),(0,a.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.totalClients.toLocaleString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total registered clients"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Messages"}),(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.totalMessages.toLocaleString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total messages sent"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Active Clients"}),(0,a.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.activeClients.toLocaleString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active in last 30 days"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Active Campaigns"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.activeCampaigns.toLocaleString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently active campaigns"})]})]})]}),e.timestamp&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground text-right",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):(0,a.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:(0,a.jsx)("p",{children:"Unable to load statistics at this time."})})}},31886:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(23464);class r{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(t){if(t.response){let s=t.response.status;if(console.log("API Client: Request to ".concat(e," failed with status ").concat(s)),404===s){let t=Error("Resource not found: ".concat(e));throw t.status=404,t.isNotFound=!0,t}}throw t}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let s=await this.client.delete(e,t);if(204===s.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return s.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let a={...s,headers:{...null==s?void 0:s.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let n=new r},34964:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>i,TabsContent:()=>d,TabsList:()=>l,TabsTrigger:()=>c});var a=s(95155),r=s(12115),n=s(60704),o=s(53999);let i=n.bL,l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.B8,{ref:t,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});l.displayName=n.B8.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.l9,{ref:t,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});c.displayName=n.l9.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});d.displayName=n.UC.displayName},37595:(e,t,s)=>{"use strict";s.d(t,{ClientsChart:()=>d});var a=s(95155),r=s(58700),n=s(57046),o=s(96025),i=s(16238),l=s(83394);let c=[{month:"Jan",new:45,returning:75},{month:"Feb",new:52,returning:88},{month:"Mar",new:61,returning:109},{month:"Apr",new:67,returning:133},{month:"May",new:72,returning:148},{month:"Jun",new:80,returning:170},{month:"Jul",new:91,returning:189},{month:"Aug",new:103,returning:207},{month:"Sep",new:110,returning:230},{month:"Oct",new:116,returning:244},{month:"Nov",new:125,returning:255},{month:"Dec",new:132,returning:278}];function d(){return(0,a.jsx)(r.at,{className:"h-full w-full",config:{},children:(0,a.jsxs)(n.X,{data:c,children:[(0,a.jsx)(o.W,{dataKey:"month"}),(0,a.jsx)(i.h,{}),(0,a.jsx)(r.II,{cursor:!1,content:(0,a.jsx)(r.Nt,{})}),(0,a.jsx)(l.y,{dataKey:"new",fill:"hsl(var(--primary))",radius:4,barSize:20}),(0,a.jsx)(l.y,{dataKey:"returning",fill:"hsl(var(--primary) / 0.3)",radius:4,barSize:20})]})})}},41966:(e,t,s)=>{"use strict";s.d(t,{OverviewChart:()=>d});var a=s(95155),r=s(58700),n=s(57046),o=s(96025),i=s(16238),l=s(21374);let c=[{date:"Jan",clients:120,messages:2400,properties:45},{date:"Feb",clients:140,messages:2800,properties:52},{date:"Mar",clients:170,messages:3200,properties:61},{date:"Apr",clients:200,messages:4e3,properties:70},{date:"May",clients:220,messages:4500,properties:78},{date:"Jun",clients:250,messages:5200,properties:85},{date:"Jul",clients:280,messages:5800,properties:92},{date:"Aug",clients:310,messages:6400,properties:98},{date:"Sep",clients:340,messages:7e3,properties:105},{date:"Oct",clients:360,messages:7500,properties:112},{date:"Nov",clients:380,messages:8e3,properties:118},{date:"Dec",clients:410,messages:8500,properties:125}];function d(){return(0,a.jsx)(r.at,{className:"h-full w-full",config:{},children:(0,a.jsxs)(n.X,{data:c,children:[(0,a.jsx)(o.W,{dataKey:"date"}),(0,a.jsx)(i.h,{}),(0,a.jsx)(r.II,{content:(0,a.jsx)(r.Nt,{})}),(0,a.jsx)(l.N,{dataKey:"clients",stroke:"hsl(var(--primary))",strokeWidth:2,activeDot:{r:6}}),(0,a.jsx)(l.N,{dataKey:"messages",stroke:"hsl(var(--destructive))",strokeWidth:2,activeDot:{r:6}}),(0,a.jsx)(l.N,{dataKey:"properties",stroke:"hsl(var(--muted-foreground))",strokeWidth:2,activeDot:{r:6}})]})})}},49443:(e,t,s)=>{Promise.resolve().then(s.bind(s,60166)),Promise.resolve().then(s.bind(s,37595)),Promise.resolve().then(s.bind(s,41966)),Promise.resolve().then(s.bind(s,88624)),Promise.resolve().then(s.bind(s,15925)),Promise.resolve().then(s.bind(s,34964))},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(52596),r=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},58700:(e,t,s)=>{"use strict";s.d(t,{Fq:()=>D,Gk:()=>O,II:()=>E,Nt:()=>S,WX:()=>_,at:()=>C,h8:()=>I,t1:()=>R});var a=s(95155),r=s(12115),n=s(83540),o=s(94517),i=s(24026),l=s(57046),c=s(93504),d=s(3401),u=s(8782),f=s(99445),m=s(96025),h=s(16238),g=s(94754),p=s(21374),y=s(83394),x=s(34e3),v=s(62341),b=s(63807),w=s(53999);let j={light:"",dark:".dark"},N=r.createContext(null);function k(){let e=r.useContext(N);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let C=r.forwardRef((e,t)=>{let{id:s,className:o,children:i,config:l,...c}=e,d=r.useId(),u="chart-".concat(s||d.replace(/:/g,""));return(0,a.jsx)(N.Provider,{value:{config:l||{}},children:(0,a.jsxs)("div",{"data-chart":u,ref:t,className:(0,w.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",o),...c,children:[(0,a.jsx)(A,{id:u,config:l||{}}),(0,a.jsx)(n.u,{children:i})]})})});C.displayName="Chart";let A=e=>{let{id:t,config:s}=e;if(!s||"object"!=typeof s)return null;let r=Object.entries(s).filter(e=>{let[,t]=e;return t&&(t.theme||t.color)});return r.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(j).map(e=>{let[s,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(r.map(e=>{let[t,a]=e,r=a.theme&&a.theme[s]||a.color;return r?"  --color-".concat(t,": ").concat(r,";"):null}).filter(Boolean).join("\n"),"\n}\n")}).join("\n")}}):null},E=o.m,S=r.forwardRef((e,t)=>{let{active:s,payload:n,className:o,indicator:i="dot",hideLabel:l=!1,hideIndicator:c=!1,label:d,labelFormatter:u,labelClassName:f,formatter:m,color:h,nameKey:g,labelKey:p}=e,{config:y}=k(),x=r.useMemo(()=>{var e;if(l||!(null==n?void 0:n.length))return null;let[t]=n,s="".concat(p||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),r=T(y,t,s),o=p||"string"!=typeof d?null==r?void 0:r.label:(null===(e=y[d])||void 0===e?void 0:e.label)||d;return u?(0,a.jsx)("div",{className:(0,w.cn)("font-medium",f),children:u(o,n)}):o?(0,a.jsx)("div",{className:(0,w.cn)("font-medium",f),children:o}):null},[d,u,n,l,f,y,p]);if(!s||!(null==n?void 0:n.length))return null;let v=1===n.length&&"dot"!==i;return(0,a.jsxs)("div",{ref:t,className:(0,w.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",o),children:[v?null:x,(0,a.jsx)("div",{className:"grid gap-1.5",children:n.map((e,t)=>{var s;let r="".concat(g||e.name||e.dataKey||"value"),n=T(y,e,r),o=h||(null===(s=e.payload)||void 0===s?void 0:s.fill)||e.color;return(0,a.jsx)("div",{className:(0,w.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===i&&"items-center"),children:m&&(null==e?void 0:e.value)!==void 0&&e.name?m(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==n?void 0:n.icon)?(0,a.jsx)(n.icon,{}):!c&&(0,a.jsx)("div",{className:(0,w.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===i,"w-1":"line"===i,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===i,"my-0.5":v&&"dashed"===i}),style:{"--color-bg":o,"--color-border":o}}),(0,a.jsxs)("div",{className:(0,w.cn)("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[v?x:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==n?void 0:n.label)||e.name})]}),void 0!==e.value&&(0,a.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey||t)})})]})});function T(e,t,s){if(!e||"object"!=typeof e||"object"!=typeof t||null===t)return;let a=t&&"payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,r=s;return t&&s in t&&"string"==typeof t[s]?r=t[s]:a&&s in a&&"string"==typeof a[s]&&(r=a[s]),r in e?e[r]:e[s]}S.displayName="ChartTooltip",i.s,r.forwardRef((e,t)=>{let{className:s,hideIcon:r=!1,payload:n,verticalAlign:o="bottom",nameKey:i}=e,{config:l}=k();return(null==n?void 0:n.length)?(0,a.jsx)("div",{ref:t,className:(0,w.cn)("flex items-center justify-center gap-4","top"===o?"pb-3":"pt-3",s),children:n.map((e,t)=>{let s="".concat(i||e.dataKey||"value"),n=T(l,e,s);return(0,a.jsxs)("div",{className:(0,w.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==n?void 0:n.icon)&&!r?(0,a.jsx)(n.icon,{}):(0,a.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),(null==n?void 0:n.label)||e.value]},e.value||t)})}):null}).displayName="ChartLegend";let R=l.X;c.b,d.E,u.r,f.Q;let _=m.W,I=h.h;g.d,p.N,y.y;let D=x.F,O=v.G;b.X,i.s},60166:(e,t,s)=>{"use strict";s.d(t,{CampaignsChart:()=>o});var a=s(95155),r=s(58700);let n=[{name:"WhatsApp Template",value:45},{name:"Connect With Us",value:30},{name:"Property Showcase",value:15},{name:"Market Updates",value:10}];function o(){return(0,a.jsx)(r.at,{className:"h-full w-full",data:n,children:(0,a.jsxs)(r.t1,{children:[(0,a.jsx)(r.II,{children:(0,a.jsx)(r.Nt,{})}),(0,a.jsx)(r.Fq,{dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:150,fill:"hsl(var(--primary))",label:!0})]})})}},88482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>u});var a=s(95155),r=s(12115),n=s(53999);let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});o.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});l.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...r})});u.displayName="CardFooter"},88624:(e,t,s)=>{"use strict";s.d(t,{PropertiesChart:()=>o});var a=s(95155),r=s(58700);let n=[{month:"Jan",views:1200,inquiries:240,sales:12},{month:"Feb",views:1400,inquiries:280,sales:15},{month:"Mar",views:1600,inquiries:320,sales:18},{month:"Apr",views:2e3,inquiries:400,sales:22},{month:"May",views:2200,inquiries:440,sales:25},{month:"Jun",views:2600,inquiries:520,sales:30},{month:"Jul",views:2900,inquiries:580,sales:32},{month:"Aug",views:3200,inquiries:640,sales:36},{month:"Sep",views:3500,inquiries:700,sales:40},{month:"Oct",views:3800,inquiries:760,sales:42},{month:"Nov",views:4e3,inquiries:800,sales:45},{month:"Dec",views:4200,inquiries:840,sales:48}];function o(){return(0,a.jsx)(r.at,{className:"h-full w-full",data:n,children:(0,a.jsxs)(r.t1,{children:[(0,a.jsx)(r.WX,{dataKey:"month"}),(0,a.jsx)(r.h8,{}),(0,a.jsx)(r.II,{children:(0,a.jsx)(r.Nt,{})}),(0,a.jsx)(r.Gk,{type:"monotone",dataKey:"views",stroke:"hsl(var(--primary))",fill:"hsl(var(--primary) / 0.2)"}),(0,a.jsx)(r.Gk,{type:"monotone",dataKey:"inquiries",stroke:"hsl(var(--primary) / 0.7)",fill:"hsl(var(--primary) / 0.1)"})]})})}},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>l});var a=s(95155),r=s(12115),n=s(99708),o=s(74466),i=s(53999);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:o,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(l({variant:r,size:o,className:s})),ref:t,...d})});c.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6071,3464,6708,8441,1684,7358],()=>t(49443)),_N_E=e.O()}]);