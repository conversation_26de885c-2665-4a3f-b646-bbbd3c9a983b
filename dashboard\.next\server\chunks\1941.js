"use strict";exports.id=1941,exports.ids=[1941],exports.modules={8819:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},15616:(e,t,s)=>{s.d(t,{T:()=>n});var a=s(60687),r=s(43210),o=s(96241);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));n.displayName="Textarea"},39390:(e,t,s)=>{s.d(t,{J:()=>d});var a=s(60687),r=s(43210),o=s(78148),n=s(24224),i=s(96241);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.b,{ref:s,className:(0,i.cn)(l(),e),...t}));d.displayName=o.b.displayName},55192:(e,t,s)=>{s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>f});var a=s(60687),r=s(43210),o=s(96241);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let f=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t}));f.displayName="CardFooter"},59556:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(51060);class r{constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>(e.response?(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:`Request failed with status code ${e.response.status}`}),404===e.response.status&&(console.log("Resource not found:",e.config?.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}),e.responseData=e.response.data):e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"}),Promise.reject(e)))}async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log(`API Client: In offline mode, skipping GET request to ${e}`),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(t){if(t.response){let s=t.response.status;if(console.log(`API Client: Request to ${e} failed with status ${s}`),404===s){let t=Error(`Resource not found: ${e}`);throw t.status=404,t.isNotFound=!0,t}}throw t}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log(`Making DELETE request to: ${e}`);let s=await this.client.delete(e,t);if(204===s.status)return console.log(`DELETE request to ${e} successful with 204 status`),null;return s.data}catch(t){throw console.error(`DELETE request to ${e} failed:`,t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let a={...s,headers:{...s?.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log(`API Client: Setting offline mode to ${e}`),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}}let o=new r},63974:(e,t,s)=>{s.d(t,{bq:()=>u,eb:()=>g,gC:()=>h,l6:()=>c,yv:()=>f});var a=s(60687),r=s(43210),o=s(22670),n=s(78272),i=s(3589),l=s(13964),d=s(96241);let c=o.bL;o.YJ;let f=o.WT,u=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(o.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(o.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=o.l9.displayName;let p=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=o.PP.displayName;let m=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));m.displayName=o.wn.displayName;let h=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(o.ZL,{children:(0,a.jsxs)(o.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(p,{}),(0,a.jsx)(o.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(m,{})]})}));h.displayName=o.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=o.JU.displayName;let g=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(o.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(o.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(o.p4,{children:t})]}));g.displayName=o.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=o.wv.displayName},68988:(e,t,s)=>{s.d(t,{p:()=>n});var a=s(60687),r=s(43210),o=s(96241);let n=r.forwardRef(({className:e,type:t,...s},r)=>(0,a.jsx)("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...s}));n.displayName="Input"},78148:(e,t,s)=>{s.d(t,{b:()=>i});var a=s(43210),r=s(14163),o=s(60687),n=a.forwardRef((e,t)=>(0,o.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n}};