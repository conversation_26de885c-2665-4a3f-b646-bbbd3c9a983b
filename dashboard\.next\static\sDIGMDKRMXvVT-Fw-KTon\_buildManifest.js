self.__BUILD_MANIFEST=function(e,r,t,s,_){return{__rewrites:{afterFiles:[{has:t,source:"/api/v1/:path*",destination:t}],beforeFiles:[],fallback:[]},__routerFilterStatic:{numItems:34,errorRate:1e-4,numBits:652,numHashes:14,bitArray:[1,1,e,e,e,e,e,0,0,r,e,r,r,e,r,r,e,r,e,r,e,e,e,r,e,r,e,e,r,r,e,r,r,r,r,r,r,r,r,e,r,r,r,e,e,e,e,e,r,e,r,r,r,r,e,r,r,e,r,r,r,e,e,e,r,r,e,e,r,r,e,e,e,e,e,e,e,e,r,r,r,r,e,r,e,e,r,e,e,e,r,r,r,e,r,e,e,e,e,e,r,r,e,r,e,r,r,r,e,e,e,e,r,e,e,e,r,r,r,r,r,r,e,r,r,e,r,r,r,r,r,r,e,r,e,r,r,r,e,r,e,e,e,r,e,e,r,r,e,e,e,r,r,r,e,e,r,r,e,r,e,e,e,r,e,e,e,r,r,r,r,e,r,r,e,e,e,e,r,e,r,e,e,r,e,r,e,r,r,r,r,e,r,r,r,e,r,r,e,e,e,e,r,e,e,r,r,r,r,r,e,e,r,e,r,e,r,e,e,e,r,e,e,r,e,r,r,r,e,e,r,r,e,r,e,r,r,e,r,e,e,e,e,e,r,r,r,e,r,r,r,e,e,e,r,r,e,e,e,r,r,e,e,r,e,r,e,e,e,r,e,e,r,e,e,e,e,e,r,r,r,e,e,e,e,r,e,e,r,r,r,e,e,r,e,e,r,e,r,r,e,e,r,e,e,e,r,e,e,r,r,e,e,r,r,r,e,r,e,r,r,r,e,e,e,r,r,e,e,e,r,e,r,e,e,e,e,e,e,e,r,e,e,e,r,r,r,r,r,r,e,e,e,e,r,r,r,r,r,r,e,r,r,r,e,r,e,r,r,e,e,e,e,r,e,e,r,e,e,r,e,e,r,e,e,e,e,e,e,e,e,r,e,e,e,e,r,e,e,r,r,e,r,r,r,r,e,e,e,e,e,e,e,r,e,e,r,e,r,r,e,r,e,r,r,e,e,r,e,e,r,e,r,e,r,e,r,r,r,r,r,e,e,e,e,r,r,e,e,e,e,e,e,r,e,r,e,e,e,e,r,e,e,r,e,r,r,e,e,e,r,e,e,e,e,e,e,r,e,r,r,r,r,e,r,e,r,e,e,e,r,r,e,e,e,r,e,r,r,r,r,e,r,r,e,r,e,e,e,r,r,r,e,r,r,e,r,r,e,r,r,r,r,r,r,r,e,r,r,e,e,e,e,e,r,r,r,r,e,e,r,r,e,e,e,e,r,r,e,r,r,e,r,e,e,r,r,e,r,r,e,e,r,e,e,r,e,r,e,r,r,e,e,e,r,e,r,e,r,e,e,r,e,e,e,r,r,e,e,r,e,e,r,e,e,r,e,r,e,e,r,e,r,r,e,r,e,e,e,e,r,r,r,e,r,e,e,e,r,e,e,r,r,e,e,r,e,r,e,r,e,e,r,e,r,r,e,e,r,r,r,e,r,r,r,e,r,e,e,e,e,r]},__routerFilterDynamic:{numItems:4,errorRate:1e-4,numBits:77,numHashes:14,bitArray:[r,e,e,e,r,e,e,r,e,r,r,r,r,r,r,e,r,e,r,r,e,e,r,e,e,e,r,e,e,e,r,e,r,r,e,e,r,e,e,r,r,r,r,e,r,r,r,r,r,r,e,e,r,r,r,r,r,r,e,e,e,e,e,r,r,e,r,e,e,r,r,e,e,e,e,e,r]},"/_error":["static/chunks/pages/_error-f94192b14105bd76.js"],sortedPages:["/_app","/_error"]}}(1,0,void 0,1e-4,14),self.__BUILD_MANIFEST_CB&&self.__BUILD_MANIFEST_CB();