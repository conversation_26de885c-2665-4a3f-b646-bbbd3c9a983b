"use client"

import { useTranslation } from "@/lib/i18n/client"
import { But<PERSON> } from "@/components/ui/button"
import { languages } from "@/lib/i18n/settings"
import { Globe } from "lucide-react"

export function LanguageSwitcher() {
  const { i18n } = useTranslation()

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
    document.documentElement.dir = lng === "ar" ? "rtl" : "ltr"
  }

  const toggleLanguage = () => {
    const currentLng = i18n.language
    const newLng = currentLng === "ar" ? "en" : "ar" // Default to Arabic
    changeLanguage(newLng)
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleLanguage}
      title={i18n.language === "ar" ? "Switch to English" : "Switch to Arabic"}
    >
      <Globe className="h-4 w-4" />
    </Button>
  )
}
