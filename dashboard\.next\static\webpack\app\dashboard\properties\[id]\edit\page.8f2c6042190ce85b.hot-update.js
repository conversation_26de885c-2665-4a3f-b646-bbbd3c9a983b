"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/edit/page",{

/***/ "(app-pages-browser)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(app-pages-browser)/./lib/cookieCleanup.ts\");\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ var _s = $RefreshSig$();\n\n\n/**\n * Arabic-only language hook for Properties system\n * Supports Arabic language with RTL text direction\n */ function useSimpleLanguage() {\n    _s();\n    const [language] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar'); // Arabic only\n    // Initialize Arabic interface with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Clean up cookies first\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.cleanupCookies)();\n            // Set Arabic language preference\n            localStorage.setItem('properties-language', 'ar');\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🏠 Arabic Properties system initialized');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Set Arabic document properties\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Update document properties for Arabic\n            document.documentElement.lang = 'ar';\n            document.documentElement.dir = 'rtl';\n            // Update CSS classes for Arabic\n            document.documentElement.className = 'rtl arabic-interface';\n            // Set Arabic fonts\n            document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🌐 Arabic language interface active');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Translation function for Arabic with direct mapping\n    const translations = {\n        'properties.create': 'إنشاء عقار جديد',\n        'properties.subtitle': 'إدارة وإضافة العقارات الجديدة',\n        'properties.save': 'حفظ العقار',\n        'properties.cancel': 'إلغاء',\n        'properties.loading': 'جاري التحميل...',\n        'properties.success': 'تم حفظ العقار بنجاح',\n        'properties.error': 'حدث خطأ أثناء حفظ العقار',\n        'property.title': 'عنوان العقار',\n        'property.title.placeholder': 'أدخل عنوان العقار',\n        'property.description': 'وصف العقار',\n        'property.description.placeholder': 'أدخل وصف مفصل للعقار',\n        'property.price': 'السعر',\n        'property.price.placeholder': 'أدخل سعر العقار',\n        'property.type': 'نوع العقار',\n        'property.type.select': 'اختر نوع العقار',\n        'property.status': 'حالة العقار',\n        'property.status.select': 'اختر حالة العقار',\n        'property.bedrooms': 'عدد غرف النوم',\n        'property.bathrooms': 'عدد دورات المياه',\n        'property.area': 'المساحة',\n        'property.location': 'الموقع',\n        'property.location.placeholder': 'أدخل موقع العقار',\n        'property.address': 'العنوان',\n        'property.address.placeholder': 'أدخل العنوان التفصيلي',\n        'property.city': 'المدينة',\n        'property.city.placeholder': 'أدخل اسم المدينة',\n        'property.country': 'الدولة',\n        'property.images': 'صور العقار',\n        'property.yearBuilt': 'سنة البناء',\n        'property.parking': 'مواقف السيارات',\n        'property.furnished': 'مفروش',\n        'property.petFriendly': 'يسمح بالحيوانات الأليفة',\n        'property.type.apartment': 'شقة',\n        'property.type.villa': 'فيلا',\n        'property.type.townhouse': 'تاون هاوس',\n        'property.type.penthouse': 'بنتهاوس',\n        'property.type.studio': 'استوديو',\n        'property.type.office': 'مكتب',\n        'property.type.shop': 'محل تجاري',\n        'property.type.warehouse': 'مستودع',\n        'property.type.land': 'أرض',\n        'property.type.building': 'مبنى',\n        'property.status.available': 'متاح',\n        'property.status.sold': 'مباع',\n        'property.status.rented': 'مؤجر',\n        'property.status.pending': 'قيد المراجعة',\n        'country.uae': 'الإمارات العربية المتحدة',\n        'country.saudi': 'المملكة العربية السعودية',\n        'country.qatar': 'قطر',\n        'country.kuwait': 'الكويت',\n        'country.bahrain': 'البحرين',\n        'country.oman': 'عمان',\n        // Currencies\n        'currency.sar': 'ريال سعودي',\n        'currency.aed': 'درهم إماراتي',\n        'currency.usd': 'دولار أمريكي',\n        'currency.eur': 'يورو',\n        'currency.gbp': 'جنيه إسترليني',\n        'images.drag': 'اسحب الصور هنا أو انقر للاختيار',\n        'images.formats': 'صور حتى ٨ ميجابايت',\n        'images.uploading': 'جاري رفع الصور...',\n        'images.success': 'تم رفع الصور بنجاح',\n        'images.error': 'خطأ في رفع الصور',\n        'images.remove': 'حذف الصورة',\n        'images.preview': 'معاينة الصورة',\n        'images.fileType': 'يرجى اختيار ملفات صور فقط',\n        'images.fileSize': 'حجم الصورة يجب أن يكون أقل من ٨ ميجابايت',\n        'validation.required': 'هذا الحقل مطلوب',\n        'validation.positive': 'يجب أن يكون الرقم أكبر من الصفر'\n    };\n    const t = (key)=>{\n        const translation = translations[key];\n        if (translation) {\n            return translation;\n        }\n        // If no translation found, return the key without the prefix for debugging\n        console.warn(\"Missing translation for: \".concat(key));\n        return key;\n    };\n    return {\n        language,\n        setLanguage: ()=>{},\n        isRTL: true,\n        isArabic: true,\n        isEnglish: false,\n        t\n    };\n}\n_s(useSimpleLanguage, \"vJYXkmcagJrDfhu6Qs2lIfg6PMg=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useSimpleLanguage.tsx\n"));

/***/ })

});