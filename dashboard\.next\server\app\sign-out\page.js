(()=>{var e={};e.id=5683,e.ids=[5683],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12484:(e,r,t)=>{"use strict";t.d(r,{ES:()=>s,eo:()=>o});let o=["ar"],s={ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"الذكاء الاصطناعي","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","properties.title":"العقارات","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.add":"إضافة عقار","properties.create":"إنشاء عقار جديد","properties.edit":"تعديل العقار","properties.delete":"حذف العقار","properties.view":"عرض العقار","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.currency":"العملة","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.features":"المميزات","property.amenities":"الخدمات","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","validation.required":"هذا الحقل مطلوب","validation.email":"يرجى إدخال بريد إلكتروني صحيح","validation.minLength":"يجب أن يكون الحد الأدنى {{min}} أحرف","validation.maxLength":"يجب أن يكون الحد الأقصى {{max}} أحرف","validation.number":"يرجى إدخال رقم صحيح","validation.positive":"يجب أن يكون الرقم أكبر من الصفر","images.upload":"رفع الصور","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت"}}}},16200:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>d});var o=t(65239),s=t(48088),p=t(88170),a=t.n(p),i=t(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let d={children:["",{children:["sign-out",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65852)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-out\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-out\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/sign-out/page",pathname:"/sign-out",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27063:(e,r,t)=>{Promise.resolve().then(t.bind(t,65852))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34302:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var o=t(60687);t(43210);var s=t(16189);t(99208);var p=t(68082);function a(){let{t:e}=(0,p.B)();return(0,s.useRouter)(),(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:e("auth.signingOut")}),(0,o.jsx)("p",{className:"text-muted-foreground mb-8",children:e("auth.redirectingToSignIn")}),(0,o.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65852:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-out\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-out\\page.tsx","default")},68082:(e,r,t)=>{"use strict";t.d(r,{B:()=>a});var o=t(46755),s=t(16457),p=t(12484);function a(){return(0,s.Bd)()}o.Ay.use(s.r9).init({lng:"en",fallbackLng:"en",resources:p.ES,interpolation:{escapeValue:!1}})},80207:(e,r,t)=>{Promise.resolve().then(t.bind(t,34302))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[7719,8157,3903,4088],()=>t(16200));module.exports=o})();