"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(app-pages-browser)/./lib/cookieCleanup.ts\");\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ var _s = $RefreshSig$();\n\n\n/**\n * Arabic-only language hook for Properties system\n * Supports Arabic language with RTL text direction\n */ function useSimpleLanguage() {\n    _s();\n    const [language] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar'); // Arabic only\n    // Initialize Arabic interface with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Clean up cookies first\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.cleanupCookies)();\n            // Set Arabic language preference\n            localStorage.setItem('properties-language', 'ar');\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🏠 Arabic Properties system initialized');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Set Arabic document properties\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Update document properties for Arabic\n            document.documentElement.lang = 'ar';\n            document.documentElement.dir = 'rtl';\n            // Update CSS classes for Arabic\n            document.documentElement.className = 'rtl arabic-interface';\n            // Set Arabic fonts\n            document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🌐 Arabic language interface active');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Translation function for Arabic with direct mapping\n    const translations = {\n        'properties.create': 'إنشاء عقار جديد',\n        'properties.subtitle': 'إدارة وإضافة العقارات الجديدة',\n        'properties.save': 'حفظ العقار',\n        'properties.cancel': 'إلغاء',\n        'properties.loading': 'جاري التحميل...',\n        'properties.success': 'تم حفظ العقار بنجاح',\n        'properties.error': 'حدث خطأ أثناء حفظ العقار',\n        'property.title': 'عنوان العقار',\n        'property.title.placeholder': 'أدخل عنوان العقار',\n        'property.description': 'وصف العقار',\n        'property.description.placeholder': 'أدخل وصف مفصل للعقار',\n        'property.price': 'السعر',\n        'property.price.placeholder': 'أدخل سعر العقار',\n        'property.type': 'نوع العقار',\n        'property.type.select': 'اختر نوع العقار',\n        'property.status': 'حالة العقار',\n        'property.status.select': 'اختر حالة العقار',\n        'property.bedrooms': 'عدد غرف النوم',\n        'property.bathrooms': 'عدد دورات المياه',\n        'property.area': 'المساحة',\n        'property.location': 'الموقع',\n        'property.location.placeholder': 'أدخل موقع العقار',\n        'property.address': 'العنوان',\n        'property.address.placeholder': 'أدخل العنوان التفصيلي',\n        'property.city': 'المدينة',\n        'property.city.placeholder': 'أدخل اسم المدينة',\n        'property.country': 'الدولة',\n        'property.images': 'صور العقار',\n        'property.yearBuilt': 'سنة البناء',\n        'property.parking': 'مواقف السيارات',\n        'property.furnished': 'مفروش',\n        'property.petFriendly': 'يسمح بالحيوانات الأليفة',\n        'property.type.apartment': 'شقة',\n        'property.type.villa': 'فيلا',\n        'property.type.townhouse': 'تاون هاوس',\n        'property.type.penthouse': 'بنتهاوس',\n        'property.type.studio': 'استوديو',\n        'property.type.office': 'مكتب',\n        'property.type.shop': 'محل تجاري',\n        'property.type.warehouse': 'مستودع',\n        'property.type.land': 'أرض',\n        'property.type.building': 'مبنى',\n        'property.status.available': 'متاح',\n        'property.status.sold': 'مباع',\n        'property.status.rented': 'مؤجر',\n        'property.status.pending': 'قيد المراجعة',\n        'country.uae': 'الإمارات العربية المتحدة',\n        'country.saudi': 'المملكة العربية السعودية',\n        'country.qatar': 'قطر',\n        'country.kuwait': 'الكويت',\n        'country.bahrain': 'البحرين',\n        'country.oman': 'عمان',\n        // Currencies\n        'currency.sar': 'ريال سعودي',\n        'currency.aed': 'درهم إماراتي',\n        'currency.usd': 'دولار أمريكي',\n        'currency.eur': 'يورو',\n        'currency.gbp': 'جنيه إسترليني',\n        'images.drag': 'اسحب الصور هنا أو انقر للاختيار',\n        'images.formats': 'صور حتى ٨ ميجابايت',\n        'images.uploading': 'جاري رفع الصور...',\n        'images.success': 'تم رفع الصور بنجاح',\n        'images.error': 'خطأ في رفع الصور',\n        'images.remove': 'حذف الصورة',\n        'images.preview': 'معاينة الصورة',\n        'images.fileType': 'يرجى اختيار ملفات صور فقط',\n        'images.fileSize': 'حجم الصورة يجب أن يكون أقل من ٨ ميجابايت',\n        'validation.required': 'هذا الحقل مطلوب',\n        'validation.positive': 'يجب أن يكون الرقم أكبر من الصفر',\n        // Dashboard general\n        'dashboard.title': 'لوحة التحكم',\n        'dashboard.welcome': 'أهلاً وسهلاً',\n        'dashboard.loading': 'جاري التحميل...',\n        'dashboard.error': 'حدث خطأ',\n        'dashboard.success': 'تم بنجاح',\n        'dashboard.save': 'حفظ',\n        'dashboard.cancel': 'إلغاء',\n        'dashboard.delete': 'حذف',\n        'dashboard.edit': 'تعديل',\n        'dashboard.view': 'عرض',\n        'dashboard.create': 'إنشاء',\n        'dashboard.search': 'بحث',\n        'dashboard.filter': 'تصفية',\n        'dashboard.export': 'تصدير',\n        'dashboard.import': 'استيراد',\n        'dashboard.refresh': 'تحديث',\n        'dashboard.settings': 'الإعدادات',\n        'dashboard.profile': 'الملف الشخصي',\n        'dashboard.logout': 'تسجيل الخروج',\n        // Navigation\n        'nav.dashboard': 'الرئيسية',\n        'nav.analytics': 'التحليلات',\n        'nav.properties': 'العقارات',\n        'nav.clients': 'العملاء',\n        'nav.appointments': 'المواعيد',\n        'nav.marketing': 'التسويق',\n        'nav.campaigns': 'الحملات',\n        'nav.templates': 'القوالب',\n        'nav.messaging': 'المراسلة',\n        'nav.ai-chatbot': 'الذكاء الاصطناعي',\n        'nav.users': 'المستخدمين',\n        'nav.settings': 'الإعدادات',\n        // Common actions\n        'action.add': 'إضافة',\n        'action.remove': 'إزالة',\n        'action.update': 'تحديث',\n        'action.confirm': 'تأكيد',\n        'action.close': 'إغلاق',\n        'action.back': 'رجوع',\n        'action.next': 'التالي',\n        'action.previous': 'السابق',\n        'action.submit': 'إرسال',\n        'action.reset': 'إعادة تعيين',\n        // Status messages\n        'status.loading': 'جاري التحميل...',\n        'status.saving': 'جاري الحفظ...',\n        'status.deleting': 'جاري الحذف...',\n        'status.updating': 'جاري التحديث...',\n        'status.success': 'تم بنجاح',\n        'status.error': 'حدث خطأ',\n        'status.warning': 'تحذير',\n        'status.info': 'معلومات',\n        // User roles\n        'role.admin': 'مدير',\n        'role.agent': 'وكيل',\n        'role.client': 'عميل',\n        'role.user': 'مستخدم',\n        // Theme\n        'theme.light': 'فاتح',\n        'theme.dark': 'داكن',\n        'theme.system': 'النظام'\n    };\n    const t = (key)=>{\n        const translation = translations[key];\n        if (translation) {\n            return translation;\n        }\n        // If no translation found, return the key without the prefix for debugging\n        console.warn(\"Missing translation for: \".concat(key));\n        return key;\n    };\n    return {\n        language,\n        setLanguage: ()=>{},\n        isRTL: true,\n        isArabic: true,\n        isEnglish: false,\n        t\n    };\n}\n_s(useSimpleLanguage, \"vJYXkmcagJrDfhu6Qs2lIfg6PMg=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useSimpleLanguage.tsx\n"));

/***/ })

});