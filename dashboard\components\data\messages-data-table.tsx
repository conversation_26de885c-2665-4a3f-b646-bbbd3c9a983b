"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Loader2 } from "lucide-react"
import { getRecentActivity, type Message } from "@/services/messageService"
import { formatDistanceToNow } from "date-fns"

export function MessagesDataTable() {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await getRecentActivity()

        // Ensure response has the expected structure
        if (response && response.recentMessages && Array.isArray(response.recentMessages)) {
          setMessages(response.recentMessages)
        } else {
          console.warn("Messages response has unexpected structure:", response)
          setMessages([])
        }
      } catch (err) {
        console.error("Error fetching messages:", err)
        setError("فشل في تحميل الرسائل. يرجى المحاولة مرة أخرى.")
        setMessages([])
      } finally {
        setLoading(false)
      }
    }

    fetchMessages()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        <p>{error}</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الرسالة</TableHead>
            <TableHead>العميل</TableHead>
            <TableHead>النوع</TableHead>
            <TableHead>الوقت</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {messages.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                لم يتم العثور على رسائل
              </TableCell>
            </TableRow>
          ) : (
            messages.map((message) => (
              <TableRow key={message.id}>
                <TableCell className="max-w-[400px] truncate">{message.text}</TableCell>
                <TableCell>{message.client?.name || 'غير معروف'}</TableCell>
                <TableCell>
                  <Badge variant={message.isBot ? "secondary" : "default"}>
                    {message.isBot ? "بوت" : "مستخدم"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
