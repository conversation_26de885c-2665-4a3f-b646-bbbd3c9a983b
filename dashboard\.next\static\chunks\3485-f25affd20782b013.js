"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3485],{82714:(e,r,a)=>{a.d(r,{J:()=>n});var s=a(95155),l=a(12115),t=a(40968),i=a(74466),o=a(53999);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=l.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,s.jsx)(t.b,{ref:r,className:(0,o.cn)(d(),a),...l})});n.displayName=t.b.displayName},83485:(e,r,a)=>{a.d(r,{o:()=>g});var s=a(95155),l=a(12115),t=a(86132),i=a(97168),o=a(89852),d=a(82714),n=a(99474),c=a(95784),p=a(53580),m=a(78137),u=a(29869),h=a(54416),y=a(6e3),v=a(42972);let{useUploadThing:f,uploadFiles:x}=(0,y.g)({url:"".concat("http://localhost:5000","/api/v1/uploadthing")});function g(e){let{onSuccess:r,onCancel:a,initialData:y,isEdit:v=!1,propertyId:x}=e,{t:g}=(0,t.Y)(),{toast:b}=(0,p.dj)(),{startUpload:N,isUploading:j}=f("propertyImageUploader",{onClientUploadComplete:e=>{if(e){let r=e.map(e=>e.url);C(e=>({...e,images:[...e.images,...r]})),b({title:g("images.success"),description:g("images.success")})}},onUploadError:e=>{b({title:g("images.error"),description:g("images.error"),variant:"destructive"})}}),[w,C]=(0,l.useState)({title:(null==y?void 0:y.title)||"",description:(null==y?void 0:y.description)||"",price:(null==y?void 0:y.price)||"",currency:(null==y?void 0:y.currency)||"USD",type:(null==y?void 0:y.type)||"",status:(null==y?void 0:y.status)||"AVAILABLE",bedrooms:(null==y?void 0:y.bedrooms)||"",bathrooms:(null==y?void 0:y.bathrooms)||"",area:(null==y?void 0:y.area)||"",location:(null==y?void 0:y.location)||"",address:(null==y?void 0:y.address)||"",city:(null==y?void 0:y.city)||"",country:(null==y?void 0:y.country)||"UAE",images:(null==y?void 0:y.images)||[],features:(null==y?void 0:y.features)||[],amenities:(null==y?void 0:y.amenities)||[],yearBuilt:(null==y?void 0:y.yearBuilt)||"",parking:(null==y?void 0:y.parking)||"",furnished:(null==y?void 0:y.furnished)||!1,petFriendly:(null==y?void 0:y.petFriendly)||!1}),[k,A]=(0,l.useState)({}),[J,E]=(0,l.useState)([]),[U,q]=(0,l.useState)(!1),F=(0,l.useCallback)((e,r)=>{C(a=>({...a,[e]:r})),k[e]&&A(r=>({...r,[e]:""}))},[k]),R=(0,l.useCallback)(e=>{if(!e)return;let r=[];Array.from(e).forEach(e=>{if(!e.type.startsWith("image/")){b({title:g("images.error"),description:g("images.fileType"),variant:"destructive"});return}if(e.size>8388608){b({title:g("images.error"),description:g("images.fileSize"),variant:"destructive"});return}r.push(e)}),r.length>0&&(E(e=>[...e,...r]),N(r))},[b,g,N]),I=(0,l.useCallback)(e=>{C(r=>({...r,images:r.images.filter((r,a)=>a!==e)}))},[]),S=(0,l.useCallback)(e=>{e.preventDefault(),e.stopPropagation()},[]),T=(0,l.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),R(e.dataTransfer.files)},[R]),D=()=>{let e={};return w.title.trim()||(e.title=g("validation.required")),w.description.trim()||(e.description=g("validation.required")),(!w.price||w.price<=0)&&(e.price=g("validation.positive")),w.price&&w.price>1e11&&(e.price="السعر كبير جداً"),w.type||(e.type=g("validation.required")),w.location.trim()||(e.location=g("validation.required")),w.address.trim()||(e.address=g("validation.required")),w.city.trim()||(e.city=g("validation.required")),(!w.bedrooms||w.bedrooms<0)&&(e.bedrooms=g("validation.positive")),(!w.bathrooms||w.bathrooms<0)&&(e.bathrooms=g("validation.positive")),(!w.area||w.area<=0)&&(e.area=g("validation.positive")),A(e),0===Object.keys(e).length},B=async e=>{if(e.preventDefault(),!D()){b({title:g("properties.error"),description:g("validation.required"),variant:"destructive"});return}q(!0);try{let e={title:w.title,description:w.description,price:Number(w.price),currency:w.currency,type:w.type,status:w.status,bedrooms:Number(w.bedrooms),bathrooms:Number(w.bathrooms),area:Number(w.area),location:w.location,address:w.address,city:w.city,country:w.country,images:w.images,features:w.features,amenities:w.amenities,yearBuilt:w.yearBuilt?Number(w.yearBuilt):void 0,parking:w.parking?Number(w.parking):void 0,furnished:w.furnished,petFriendly:w.petFriendly};v&&x?await m.Jf.updateProperty(x,e):await m.Jf.createProperty(e),b({title:g("properties.success"),description:v?"تم تحديث العقار بنجاح":g("properties.success")}),r()}catch(e){console.error("Error creating property:",e),b({title:g("properties.error"),description:g("properties.error"),variant:"destructive"})}finally{q(!1)}},L=[{value:"APARTMENT",label:g("property.type.apartment")},{value:"VILLA",label:g("property.type.villa")},{value:"TOWNHOUSE",label:g("property.type.townhouse")},{value:"PENTHOUSE",label:g("property.type.penthouse")},{value:"STUDIO",label:g("property.type.studio")},{value:"OFFICE",label:g("property.type.office")},{value:"SHOP",label:g("property.type.shop")},{value:"WAREHOUSE",label:g("property.type.warehouse")},{value:"LAND",label:g("property.type.land")},{value:"BUILDING",label:g("property.type.building")}],P=[{value:"AVAILABLE",label:g("property.status.available")},{value:"SOLD",label:g("property.status.sold")},{value:"RENTED",label:g("property.status.rented")},{value:"PENDING",label:g("property.status.pending")}],O=[{value:"UAE",label:g("country.uae")},{value:"SAUDI",label:g("country.saudi")},{value:"QATAR",label:g("country.qatar")},{value:"KUWAIT",label:g("country.kuwait")},{value:"BAHRAIN",label:g("country.bahrain")},{value:"OMAN",label:g("country.oman")}];return(0,s.jsxs)("form",{onSubmit:B,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"arabic-heading",children:"المعلومات الأساسية"}),(0,s.jsxs)("div",{className:"property-grid property-grid-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.title")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.title?"error":""),value:w.title,onChange:e=>F("title",e.target.value),placeholder:g("property.title.placeholder"),dir:"rtl"}),k.title&&(0,s.jsx)("div",{className:"form-error",children:k.title})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.price")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.price?"error":""),type:"number",value:w.price,onChange:e=>{let r=e.target.value?Number(e.target.value):"";("number"!=typeof r||!(r>1e11))&&F("price",r)},placeholder:g("property.price.placeholder"),min:"0",max:"100000000000",dir:"rtl"}),k.price&&(0,s.jsx)("div",{className:"form-error",children:k.price})]})]}),(0,s.jsxs)("div",{className:"property-grid property-grid-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.type")," *"]}),(0,s.jsxs)(c.l6,{value:w.type,onValueChange:e=>F("type",e),children:[(0,s.jsx)(c.bq,{className:"form-select ".concat(k.type?"error":""),children:(0,s.jsx)(c.yv,{placeholder:g("property.type.select")})}),(0,s.jsx)(c.gC,{children:L.map(e=>(0,s.jsx)(c.eb,{value:e.value,children:e.label},e.value))})]}),k.type&&(0,s.jsx)("div",{className:"form-error",children:k.type})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"form-label",children:g("property.status")}),(0,s.jsxs)(c.l6,{value:w.status,onValueChange:e=>F("status",e),children:[(0,s.jsx)(c.bq,{className:"form-select",children:(0,s.jsx)(c.yv,{placeholder:g("property.status.select")})}),(0,s.jsx)(c.gC,{children:P.map(e=>(0,s.jsx)(c.eb,{value:e.value,children:e.label},e.value))})]})]})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"arabic-heading",children:"تفاصيل العقار"}),(0,s.jsxs)("div",{className:"property-grid property-grid-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.bedrooms")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.bedrooms?"error":""),type:"number",value:w.bedrooms,onChange:e=>F("bedrooms",e.target.value?Number(e.target.value):""),placeholder:"٠",min:"0",dir:"rtl"}),k.bedrooms&&(0,s.jsx)("div",{className:"form-error",children:k.bedrooms})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.bathrooms")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.bathrooms?"error":""),type:"number",value:w.bathrooms,onChange:e=>F("bathrooms",e.target.value?Number(e.target.value):""),placeholder:"٠",min:"0",dir:"rtl"}),k.bathrooms&&(0,s.jsx)("div",{className:"form-error",children:k.bathrooms})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.area")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.area?"error":""),type:"number",value:w.area,onChange:e=>F("area",e.target.value?Number(e.target.value):""),placeholder:"٠",min:"0",dir:"rtl"}),k.area&&(0,s.jsx)("div",{className:"form-error",children:k.area})]})]}),(0,s.jsxs)("div",{className:"property-grid property-grid-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"form-label",children:g("property.yearBuilt")}),(0,s.jsx)(o.p,{className:"form-input",type:"number",value:w.yearBuilt,onChange:e=>F("yearBuilt",e.target.value?Number(e.target.value):""),placeholder:"٢٠٢٤",min:"1900",max:"2030",dir:"rtl"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"form-label",children:g("property.parking")}),(0,s.jsx)(o.p,{className:"form-input",type:"number",value:w.parking,onChange:e=>F("parking",e.target.value?Number(e.target.value):""),placeholder:"٠",min:"0",dir:"rtl"})]})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"arabic-heading",children:"معلومات الموقع"}),(0,s.jsxs)("div",{className:"property-grid property-grid-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.location")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.location?"error":""),value:w.location,onChange:e=>F("location",e.target.value),placeholder:g("property.location.placeholder"),dir:"rtl"}),k.location&&(0,s.jsx)("div",{className:"form-error",children:k.location})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.address")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.address?"error":""),value:w.address,onChange:e=>F("address",e.target.value),placeholder:g("property.address.placeholder"),dir:"rtl"}),k.address&&(0,s.jsx)("div",{className:"form-error",children:k.address})]})]}),(0,s.jsxs)("div",{className:"property-grid property-grid-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.city")," *"]}),(0,s.jsx)(o.p,{className:"form-input ".concat(k.city?"error":""),value:w.city,onChange:e=>F("city",e.target.value),placeholder:g("property.city.placeholder"),dir:"rtl"}),k.city&&(0,s.jsx)("div",{className:"form-error",children:k.city})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"form-label",children:g("property.country")}),(0,s.jsxs)(c.l6,{value:w.country,onValueChange:e=>F("country",e),children:[(0,s.jsx)(c.bq,{className:"form-select",children:(0,s.jsx)(c.yv,{})}),(0,s.jsx)(c.gC,{children:O.map(e=>(0,s.jsx)(c.eb,{value:e.value,children:e.label},e.value))})]})]})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"arabic-heading",children:"الوصف"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(d.J,{className:"form-label",children:[g("property.description")," *"]}),(0,s.jsx)(n.T,{className:"form-textarea ".concat(k.description?"error":""),value:w.description,onChange:e=>F("description",e.target.value),placeholder:g("property.description.placeholder"),dir:"rtl",rows:4}),k.description&&(0,s.jsx)("div",{className:"form-error",children:k.description})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"arabic-heading",children:g("property.images")}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"image-upload-area ".concat(j?"loading":""),onDragOver:S,onDrop:T,onClick:()=>{let e=document.createElement("input");e.type="file",e.multiple=!0,e.accept="image/*",e.onchange=e=>{R(e.target.files)},e.click()},children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-300 mb-2",children:g("images.drag")}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:g("images.formats")}),j&&(0,s.jsxs)("div",{className:"mt-4 flex items-center",children:[(0,s.jsx)("div",{className:"loading-spinner"}),(0,s.jsx)("span",{className:"mr-2",children:g("images.uploading")})]})]})}),w.images.length>0&&(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:w.images.map((e,r)=>(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"aspect-square bg-gray-800 rounded-lg overflow-hidden",children:(0,s.jsx)("img",{src:e,alt:"".concat(g("images.preview")," ").concat(r+1),className:"w-full h-full object-cover"})}),(0,s.jsx)("button",{type:"button",onClick:()=>I(r),className:"absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",title:g("images.remove"),children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]},r))})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"arabic-heading",children:"خيارات إضافية"}),(0,s.jsxs)("div",{className:"property-grid property-grid-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("input",{type:"checkbox",id:"furnished",checked:w.furnished,onChange:e=>F("furnished",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"}),(0,s.jsx)(d.J,{htmlFor:"furnished",className:"form-label",children:g("property.furnished")})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("input",{type:"checkbox",id:"petFriendly",checked:w.petFriendly,onChange:e=>F("petFriendly",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"}),(0,s.jsx)(d.J,{htmlFor:"petFriendly",className:"form-label",children:g("property.petFriendly")})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-4 flex-row-reverse",children:[(0,s.jsxs)(i.$,{type:"submit",disabled:U||j,className:"btn-primary",children:[(U||j)&&(0,s.jsx)("div",{className:"loading-spinner"}),U||j?g("properties.loading"):g("properties.save")]}),(0,s.jsx)(i.$,{type:"button",onClick:a,disabled:U||j,className:"btn-secondary",children:g("properties.cancel")})]})]})}(0,v.Jt)({url:"".concat("http://localhost:5000","/api/v1/uploadthing")}),(0,v.Wi)({url:"".concat("http://localhost:5000","/api/v1/uploadthing")})},89852:(e,r,a)=>{a.d(r,{p:()=>i});var s=a(95155),l=a(12115),t=a(53999);let i=l.forwardRef((e,r)=>{let{className:a,type:l,...i}=e;return(0,s.jsx)("input",{type:l,className:(0,t.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:r,...i})});i.displayName="Input"},95784:(e,r,a)=>{a.d(r,{bq:()=>m,eb:()=>v,gC:()=>y,l6:()=>c,yv:()=>p});var s=a(95155),l=a(12115),t=a(31992),i=a(66474),o=a(47863),d=a(5196),n=a(53999);let c=t.bL;t.YJ;let p=t.WT,m=l.forwardRef((e,r)=>{let{className:a,children:l,...o}=e;return(0,s.jsxs)(t.l9,{ref:r,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...o,children:[l,(0,s.jsx)(t.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=t.l9.displayName;let u=l.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,s.jsx)(t.PP,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})});u.displayName=t.PP.displayName;let h=l.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,s.jsx)(t.wn,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=t.wn.displayName;let y=l.forwardRef((e,r)=>{let{className:a,children:l,position:i="popper",...o}=e;return(0,s.jsx)(t.ZL,{children:(0,s.jsxs)(t.UC,{ref:r,className:(0,n.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...o,children:[(0,s.jsx)(u,{}),(0,s.jsx)(t.LM,{className:(0,n.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(h,{})]})})});y.displayName=t.UC.displayName,l.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,s.jsx)(t.JU,{ref:r,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=t.JU.displayName;let v=l.forwardRef((e,r)=>{let{className:a,children:l,...i}=e;return(0,s.jsxs)(t.q7,{ref:r,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(t.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(t.p4,{children:l})]})});v.displayName=t.q7.displayName,l.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,s.jsx)(t.wv,{ref:r,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=t.wv.displayName},99474:(e,r,a)=>{a.d(r,{T:()=>i});var s=a(95155),l=a(12115),t=a(53999);let i=l.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,s.jsx)("textarea",{className:(0,t.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:r,...l})});i.displayName="Textarea"}}]);