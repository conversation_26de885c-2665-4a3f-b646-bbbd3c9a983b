"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ $e),\n/* harmony export */   toast: () => (/* binding */ ue),\n/* harmony export */   useSonner: () => (/* binding */ Oe)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar jt = (n)=>{\n    switch(n){\n        case \"success\":\n            return ee;\n        case \"info\":\n            return ae;\n        case \"warning\":\n            return oe;\n        case \"error\":\n            return se;\n        default:\n            return null;\n    }\n}, te = Array(12).fill(0), Yt = ({ visible: n, className: e })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            e\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": n\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, te.map((t, a)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${a}`\n        })))), ee = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), oe = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), ae = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), se = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n})), Ot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nvar Ft = ()=>{\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Ft.useEffect\": ()=>{\n            let t = {\n                \"Ft.useEffect.t\": ()=>{\n                    e(document.hidden);\n                }\n            }[\"Ft.useEffect.t\"];\n            return document.addEventListener(\"visibilitychange\", t), ({\n                \"Ft.useEffect\": ()=>window.removeEventListener(\"visibilitychange\", t)\n            })[\"Ft.useEffect\"];\n        }\n    }[\"Ft.useEffect\"], []), n;\n};\n\nvar bt = 1, yt = class {\n    constructor(){\n        this.subscribe = (e)=>(this.subscribers.push(e), ()=>{\n                let t = this.subscribers.indexOf(e);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (e)=>{\n            this.subscribers.forEach((t)=>t(e));\n        };\n        this.addToast = (e)=>{\n            this.publish(e), this.toasts = [\n                ...this.toasts,\n                e\n            ];\n        };\n        this.create = (e)=>{\n            var S;\n            let { message: t, ...a } = e, u = typeof (e == null ? void 0 : e.id) == \"number\" || ((S = e.id) == null ? void 0 : S.length) > 0 ? e.id : bt++, f = this.toasts.find((g)=>g.id === u), w = e.dismissible === void 0 ? !0 : e.dismissible;\n            return this.dismissedToasts.has(u) && this.dismissedToasts.delete(u), f ? this.toasts = this.toasts.map((g)=>g.id === u ? (this.publish({\n                    ...g,\n                    ...e,\n                    id: u,\n                    title: t\n                }), {\n                    ...g,\n                    ...e,\n                    id: u,\n                    dismissible: w,\n                    title: t\n                }) : g) : this.addToast({\n                title: t,\n                ...a,\n                dismissible: w,\n                id: u\n            }), u;\n        };\n        this.dismiss = (e)=>(this.dismissedToasts.add(e), e || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((a)=>a({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: e,\n                    dismiss: !0\n                })), e);\n        this.message = (e, t)=>this.create({\n                ...t,\n                message: e\n            });\n        this.error = (e, t)=>this.create({\n                ...t,\n                message: e,\n                type: \"error\"\n            });\n        this.success = (e, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: e\n            });\n        this.info = (e, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: e\n            });\n        this.warning = (e, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: e\n            });\n        this.loading = (e, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: e\n            });\n        this.promise = (e, t)=>{\n            if (!t) return;\n            let a;\n            t.loading !== void 0 && (a = this.create({\n                ...t,\n                promise: e,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let u = e instanceof Promise ? e : e(), f = a !== void 0, w, S = u.then(async (i)=>{\n                if (w = [\n                    \"resolve\",\n                    i\n                ], /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(i)) f = !1, this.create({\n                    id: a,\n                    type: \"default\",\n                    message: i\n                });\n                else if (ie(i) && !i.ok) {\n                    f = !1;\n                    let T = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${i.status}`) : t.error, F = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${i.status}`) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: T,\n                        description: F\n                    });\n                } else if (t.success !== void 0) {\n                    f = !1;\n                    let T = typeof t.success == \"function\" ? await t.success(i) : t.success, F = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"success\",\n                        message: T,\n                        description: F\n                    });\n                }\n            }).catch(async (i)=>{\n                if (w = [\n                    \"reject\",\n                    i\n                ], t.error !== void 0) {\n                    f = !1;\n                    let D = typeof t.error == \"function\" ? await t.error(i) : t.error, T = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: D,\n                        description: T\n                    });\n                }\n            }).finally(()=>{\n                var i;\n                f && (this.dismiss(a), a = void 0), (i = t.finally) == null || i.call(t);\n            }), g = ()=>new Promise((i, D)=>S.then(()=>w[0] === \"reject\" ? D(w[1]) : i(w[1])).catch(D));\n            return typeof a != \"string\" && typeof a != \"number\" ? {\n                unwrap: g\n            } : Object.assign(a, {\n                unwrap: g\n            });\n        };\n        this.custom = (e, t)=>{\n            let a = (t == null ? void 0 : t.id) || bt++;\n            return this.create({\n                jsx: e(a),\n                id: a,\n                ...t\n            }), a;\n        };\n        this.getActiveToasts = ()=>this.toasts.filter((e)=>!this.dismissedToasts.has(e.id));\n        this.subscribers = [], this.toasts = [], this.dismissedToasts = new Set;\n    }\n}, v = new yt, ne = (n, e)=>{\n    let t = (e == null ? void 0 : e.id) || bt++;\n    return v.addToast({\n        title: n,\n        ...e,\n        id: t\n    }), t;\n}, ie = (n)=>n && typeof n == \"object\" && \"ok\" in n && typeof n.ok == \"boolean\" && \"status\" in n && typeof n.status == \"number\", le = ne, ce = ()=>v.toasts, de = ()=>v.getActiveToasts(), ue = Object.assign(le, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: ce,\n    getToasts: de\n});\nfunction wt(n, { insertAt: e } = {}) {\n    if (!n || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], a = document.createElement(\"style\");\n    a.type = \"text/css\", e === \"top\" && t.firstChild ? t.insertBefore(a, t.firstChild) : t.appendChild(a), a.styleSheet ? a.styleSheet.cssText = n : a.appendChild(document.createTextNode(n));\n}\nwt(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\"true\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\"true\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\"right\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\"left\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction tt(n) {\n    return n.label !== void 0;\n}\nvar pe = 3, me = \"32px\", ge = \"16px\", Wt = 4e3, he = 356, be = 14, ye = 20, we = 200;\nfunction M(...n) {\n    return n.filter(Boolean).join(\" \");\n}\nfunction xe(n) {\n    let [e, t] = n.split(\"-\"), a = [];\n    return e && a.push(e), t && a.push(t), a;\n}\nvar ve = (n)=>{\n    var Dt, Pt, Nt, Bt, Ct, kt, It, Mt, Ht, At, Lt;\n    let { invert: e, toast: t, unstyled: a, interacting: u, setHeights: f, visibleToasts: w, heights: S, index: g, toasts: i, expanded: D, removeToast: T, defaultRichColors: F, closeButton: et, style: ut, cancelButtonStyle: ft, actionButtonStyle: l, className: ot = \"\", descriptionClassName: at = \"\", duration: X, position: st, gap: pt, loadingIcon: rt, expandByDefault: B, classNames: s, icons: P, closeButtonAriaLabel: nt = \"Close toast\", pauseWhenPageIsHidden: it } = n, [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [A, mt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [L, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [ct, d] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [h, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [R, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [p, _] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), O = react__WEBPACK_IMPORTED_MODULE_0__.useRef(t.duration || X || Wt), G = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), k = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), Vt = g === 0, Ut = g + 1 <= w, N = t.type, V = t.dismissible !== !1, Kt = t.className || \"\", Xt = t.descriptionClassName || \"\", dt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[dt]\": ()=>S.findIndex({\n                \"ve.useMemo[dt]\": (r)=>r.toastId === t.id\n            }[\"ve.useMemo[dt]\"]) || 0\n    }[\"ve.useMemo[dt]\"], [\n        S,\n        t.id\n    ]), Jt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[Jt]\": ()=>{\n            var r;\n            return (r = t.closeButton) != null ? r : et;\n        }\n    }[\"ve.useMemo[Jt]\"], [\n        t.closeButton,\n        et\n    ]), Tt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[Tt]\": ()=>t.duration || X || Wt\n    }[\"ve.useMemo[Tt]\"], [\n        t.duration,\n        X\n    ]), gt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), U = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), St = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), K = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [Gt, Qt] = st.split(\"-\"), Rt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[Rt]\": ()=>S.reduce({\n                \"ve.useMemo[Rt]\": (r, m, c)=>c >= dt ? r : r + m.height\n            }[\"ve.useMemo[Rt]\"], 0)\n    }[\"ve.useMemo[Rt]\"], [\n        S,\n        dt\n    ]), Et = Ft(), qt = t.invert || e, ht = N === \"loading\";\n    U.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo\": ()=>dt * pt + Rt\n    }[\"ve.useMemo\"], [\n        dt,\n        Rt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            O.current = Tt;\n        }\n    }[\"ve.useEffect\"], [\n        Tt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            H(!0);\n        }\n    }[\"ve.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            let r = k.current;\n            if (r) {\n                let m = r.getBoundingClientRect().height;\n                return _(m), f({\n                    \"ve.useEffect\": (c)=>[\n                            {\n                                toastId: t.id,\n                                height: m,\n                                position: t.position\n                            },\n                            ...c\n                        ]\n                }[\"ve.useEffect\"]), ({\n                    \"ve.useEffect\": ()=>f({\n                            \"ve.useEffect\": (c)=>c.filter({\n                                    \"ve.useEffect\": (b)=>b.toastId !== t.id\n                                }[\"ve.useEffect\"])\n                        }[\"ve.useEffect\"])\n                })[\"ve.useEffect\"];\n            }\n        }\n    }[\"ve.useEffect\"], [\n        f,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"ve.useLayoutEffect\": ()=>{\n            if (!W) return;\n            let r = k.current, m = r.style.height;\n            r.style.height = \"auto\";\n            let c = r.getBoundingClientRect().height;\n            r.style.height = m, _(c), f({\n                \"ve.useLayoutEffect\": (b)=>b.find({\n                        \"ve.useLayoutEffect\": (x)=>x.toastId === t.id\n                    }[\"ve.useLayoutEffect\"]) ? b.map({\n                        \"ve.useLayoutEffect\": (x)=>x.toastId === t.id ? {\n                                ...x,\n                                height: c\n                            } : x\n                    }[\"ve.useLayoutEffect\"]) : [\n                        {\n                            toastId: t.id,\n                            height: c,\n                            position: t.position\n                        },\n                        ...b\n                    ]\n            }[\"ve.useLayoutEffect\"]);\n        }\n    }[\"ve.useLayoutEffect\"], [\n        W,\n        t.title,\n        t.description,\n        f,\n        t.id\n    ]);\n    let $ = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ve.useCallback[$]\": ()=>{\n            mt(!0), j(U.current), f({\n                \"ve.useCallback[$]\": (r)=>r.filter({\n                        \"ve.useCallback[$]\": (m)=>m.toastId !== t.id\n                    }[\"ve.useCallback[$]\"])\n            }[\"ve.useCallback[$]\"]), setTimeout({\n                \"ve.useCallback[$]\": ()=>{\n                    T(t);\n                }\n            }[\"ve.useCallback[$]\"], we);\n        }\n    }[\"ve.useCallback[$]\"], [\n        t,\n        T,\n        f,\n        U\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            if (t.promise && N === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n            let r;\n            return D || u || it && Et ? ({\n                \"ve.useEffect\": ()=>{\n                    if (St.current < gt.current) {\n                        let b = new Date().getTime() - gt.current;\n                        O.current = O.current - b;\n                    }\n                    St.current = new Date().getTime();\n                }\n            })[\"ve.useEffect\"]() : ({\n                \"ve.useEffect\": ()=>{\n                    O.current !== 1 / 0 && (gt.current = new Date().getTime(), r = setTimeout({\n                        \"ve.useEffect\": ()=>{\n                            var b;\n                            (b = t.onAutoClose) == null || b.call(t, t), $();\n                        }\n                    }[\"ve.useEffect\"], O.current));\n                }\n            })[\"ve.useEffect\"](), ({\n                \"ve.useEffect\": ()=>clearTimeout(r)\n            })[\"ve.useEffect\"];\n        }\n    }[\"ve.useEffect\"], [\n        D,\n        u,\n        t,\n        N,\n        it,\n        Et,\n        $\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            t.delete && $();\n        }\n    }[\"ve.useEffect\"], [\n        $,\n        t.delete\n    ]);\n    function Zt() {\n        var r, m, c;\n        return P != null && P.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (r = t == null ? void 0 : t.classNames) == null ? void 0 : r.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, P.loading) : rt ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (m = t == null ? void 0 : t.classNames) == null ? void 0 : m.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, rt) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Yt, {\n            className: M(s == null ? void 0 : s.loader, (c = t == null ? void 0 : t.classNames) == null ? void 0 : c.loader),\n            visible: N === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: k,\n        className: M(ot, Kt, s == null ? void 0 : s.toast, (Dt = t == null ? void 0 : t.classNames) == null ? void 0 : Dt.toast, s == null ? void 0 : s.default, s == null ? void 0 : s[N], (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt[N]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (Nt = t.richColors) != null ? Nt : F,\n        \"data-styled\": !(t.jsx || t.unstyled || a),\n        \"data-mounted\": W,\n        \"data-promise\": !!t.promise,\n        \"data-swiped\": h,\n        \"data-removed\": A,\n        \"data-visible\": Ut,\n        \"data-y-position\": Gt,\n        \"data-x-position\": Qt,\n        \"data-index\": g,\n        \"data-front\": Vt,\n        \"data-swiping\": L,\n        \"data-dismissible\": V,\n        \"data-type\": N,\n        \"data-invert\": qt,\n        \"data-swipe-out\": ct,\n        \"data-swipe-direction\": lt,\n        \"data-expanded\": !!(D || B && W),\n        style: {\n            \"--index\": g,\n            \"--toasts-before\": g,\n            \"--z-index\": i.length - g,\n            \"--offset\": `${A ? R : U.current}px`,\n            \"--initial-height\": B ? \"auto\" : `${p}px`,\n            ...ut,\n            ...t.style\n        },\n        onDragEnd: ()=>{\n            z(!1), C(null), K.current = null;\n        },\n        onPointerDown: (r)=>{\n            ht || !V || (G.current = new Date, j(U.current), r.target.setPointerCapture(r.pointerId), r.target.tagName !== \"BUTTON\" && (z(!0), K.current = {\n                x: r.clientX,\n                y: r.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var x, Q, q, Z;\n            if (ct || !V) return;\n            K.current = null;\n            let r = Number(((x = k.current) == null ? void 0 : x.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\", \"\")) || 0), m = Number(((Q = k.current) == null ? void 0 : Q.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\", \"\")) || 0), c = new Date().getTime() - ((q = G.current) == null ? void 0 : q.getTime()), b = Y === \"x\" ? r : m, I = Math.abs(b) / c;\n            if (Math.abs(b) >= ye || I > .11) {\n                j(U.current), (Z = t.onDismiss) == null || Z.call(t, t), J(Y === \"x\" ? r > 0 ? \"right\" : \"left\" : m > 0 ? \"down\" : \"up\"), $(), d(!0), y(!1);\n                return;\n            }\n            z(!1), C(null);\n        },\n        onPointerMove: (r)=>{\n            var Q, q, Z, zt;\n            if (!K.current || !V || ((Q = window.getSelection()) == null ? void 0 : Q.toString().length) > 0) return;\n            let c = r.clientY - K.current.y, b = r.clientX - K.current.x, I = (q = n.swipeDirections) != null ? q : xe(st);\n            !Y && (Math.abs(b) > 1 || Math.abs(c) > 1) && C(Math.abs(b) > Math.abs(c) ? \"x\" : \"y\");\n            let x = {\n                x: 0,\n                y: 0\n            };\n            Y === \"y\" ? (I.includes(\"top\") || I.includes(\"bottom\")) && (I.includes(\"top\") && c < 0 || I.includes(\"bottom\") && c > 0) && (x.y = c) : Y === \"x\" && (I.includes(\"left\") || I.includes(\"right\")) && (I.includes(\"left\") && b < 0 || I.includes(\"right\") && b > 0) && (x.x = b), (Math.abs(x.x) > 0 || Math.abs(x.y) > 0) && y(!0), (Z = k.current) == null || Z.style.setProperty(\"--swipe-amount-x\", `${x.x}px`), (zt = k.current) == null || zt.style.setProperty(\"--swipe-amount-y\", `${x.y}px`);\n        }\n    }, Jt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": nt,\n        \"data-disabled\": ht,\n        \"data-close-button\": !0,\n        onClick: ht || !V ? ()=>{} : ()=>{\n            var r;\n            $(), (r = t.onDismiss) == null || r.call(t, t);\n        },\n        className: M(s == null ? void 0 : s.closeButton, (Bt = t == null ? void 0 : t.classNames) == null ? void 0 : Bt.closeButton)\n    }, (Ct = P == null ? void 0 : P.close) != null ? Ct : Ot) : null, t.jsx || /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.title) ? t.jsx ? t.jsx : typeof t.title == \"function\" ? t.title() : t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, N || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: M(s == null ? void 0 : s.icon, (kt = t == null ? void 0 : t.classNames) == null ? void 0 : kt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Zt() : null, t.type !== \"loading\" ? t.icon || (P == null ? void 0 : P[N]) || jt(N) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: M(s == null ? void 0 : s.content, (It = t == null ? void 0 : t.classNames) == null ? void 0 : It.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: M(s == null ? void 0 : s.title, (Mt = t == null ? void 0 : t.classNames) == null ? void 0 : Mt.title)\n    }, typeof t.title == \"function\" ? t.title() : t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: M(at, Xt, s == null ? void 0 : s.description, (Ht = t == null ? void 0 : t.classNames) == null ? void 0 : Ht.description)\n    }, typeof t.description == \"function\" ? t.description() : t.description) : null), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.cancel) ? t.cancel : t.cancel && tt(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || ft,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.cancel) && V && ((c = (m = t.cancel).onClick) == null || c.call(m, r), $());\n        },\n        className: M(s == null ? void 0 : s.cancelButton, (At = t == null ? void 0 : t.classNames) == null ? void 0 : At.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.action) ? t.action : t.action && tt(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || l,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.action) && ((c = (m = t.action).onClick) == null || c.call(m, r), !r.defaultPrevented && $());\n        },\n        className: M(s == null ? void 0 : s.actionButton, (Lt = t == null ? void 0 : t.classNames) == null ? void 0 : Lt.actionButton)\n    }, t.action.label) : null));\n};\nfunction _t() {\n    if (true) return \"ltr\";\n    let n = document.documentElement.getAttribute(\"dir\");\n    return n === \"auto\" || !n ? window.getComputedStyle(document.documentElement).direction : n;\n}\nfunction Te(n, e) {\n    let t = {};\n    return [\n        n,\n        e\n    ].forEach((a, u)=>{\n        let f = u === 1, w = f ? \"--mobile-offset\" : \"--offset\", S = f ? ge : me;\n        function g(i) {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((D)=>{\n                t[`${w}-${D}`] = typeof i == \"number\" ? `${i}px` : i;\n            });\n        }\n        typeof a == \"number\" || typeof a == \"string\" ? g(a) : typeof a == \"object\" ? [\n            \"top\",\n            \"right\",\n            \"bottom\",\n            \"left\"\n        ].forEach((i)=>{\n            a[i] === void 0 ? t[`${w}-${i}`] = S : t[`${w}-${i}`] = typeof a[i] == \"number\" ? `${a[i]}px` : a[i];\n        }) : g(S);\n    }), t;\n}\nfunction Oe() {\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Oe.useEffect\": ()=>v.subscribe({\n                \"Oe.useEffect\": (t)=>{\n                    if (t.dismiss) {\n                        setTimeout({\n                            \"Oe.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"Oe.useEffect\": ()=>{\n                                        e({\n                                            \"Oe.useEffect\": (a)=>a.filter({\n                                                    \"Oe.useEffect\": (u)=>u.id !== t.id\n                                                }[\"Oe.useEffect\"])\n                                        }[\"Oe.useEffect\"]);\n                                    }\n                                }[\"Oe.useEffect\"]);\n                            }\n                        }[\"Oe.useEffect\"]);\n                        return;\n                    }\n                    setTimeout({\n                        \"Oe.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Oe.useEffect\": ()=>{\n                                    e({\n                                        \"Oe.useEffect\": (a)=>{\n                                            let u = a.findIndex({\n                                                \"Oe.useEffect.u\": (f)=>f.id === t.id\n                                            }[\"Oe.useEffect.u\"]);\n                                            return u !== -1 ? [\n                                                ...a.slice(0, u),\n                                                {\n                                                    ...a[u],\n                                                    ...t\n                                                },\n                                                ...a.slice(u + 1)\n                                            ] : [\n                                                t,\n                                                ...a\n                                            ];\n                                        }\n                                    }[\"Oe.useEffect\"]);\n                                }\n                            }[\"Oe.useEffect\"]);\n                        }\n                    }[\"Oe.useEffect\"]);\n                }\n            }[\"Oe.useEffect\"])\n    }[\"Oe.useEffect\"], []), {\n        toasts: n\n    };\n}\nvar $e = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(e, t) {\n    let { invert: a, position: u = \"bottom-right\", hotkey: f = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: w, closeButton: S, className: g, offset: i, mobileOffset: D, theme: T = \"light\", richColors: F, duration: et, style: ut, visibleToasts: ft = pe, toastOptions: l, dir: ot = _t(), gap: at = be, loadingIcon: X, icons: st, containerAriaLabel: pt = \"Notifications\", pauseWhenPageIsHidden: rt } = e, [B, s] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), P = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"$e.useMemo[P]\": ()=>Array.from(new Set([\n                u\n            ].concat(B.filter({\n                \"$e.useMemo[P]\": (d)=>d.position\n            }[\"$e.useMemo[P]\"]).map({\n                \"$e.useMemo[P]\": (d)=>d.position\n            }[\"$e.useMemo[P]\"]))))\n    }[\"$e.useMemo[P]\"], [\n        B,\n        u\n    ]), [nt, it] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(T !== \"system\" ? T :  false ? 0 : \"light\"), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), mt = f.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), L = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), z = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), ct = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"$e.useCallback[ct]\": (d)=>{\n            s({\n                \"$e.useCallback[ct]\": (h)=>{\n                    var y;\n                    return (y = h.find({\n                        \"$e.useCallback[ct]\": (R)=>R.id === d.id\n                    }[\"$e.useCallback[ct]\"])) != null && y.delete || v.dismiss(d.id), h.filter({\n                        \"$e.useCallback[ct]\": ({ id: R })=>R !== d.id\n                    }[\"$e.useCallback[ct]\"]);\n                }\n            }[\"$e.useCallback[ct]\"]);\n        }\n    }[\"$e.useCallback[ct]\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>v.subscribe({\n                \"$e.useEffect\": (d)=>{\n                    if (d.dismiss) {\n                        s({\n                            \"$e.useEffect\": (h)=>h.map({\n                                    \"$e.useEffect\": (y)=>y.id === d.id ? {\n                                            ...y,\n                                            delete: !0\n                                        } : y\n                                }[\"$e.useEffect\"])\n                        }[\"$e.useEffect\"]);\n                        return;\n                    }\n                    setTimeout({\n                        \"$e.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"$e.useEffect\": ()=>{\n                                    s({\n                                        \"$e.useEffect\": (h)=>{\n                                            let y = h.findIndex({\n                                                \"$e.useEffect.y\": (R)=>R.id === d.id\n                                            }[\"$e.useEffect.y\"]);\n                                            return y !== -1 ? [\n                                                ...h.slice(0, y),\n                                                {\n                                                    ...h[y],\n                                                    ...d\n                                                },\n                                                ...h.slice(y + 1)\n                                            ] : [\n                                                d,\n                                                ...h\n                                            ];\n                                        }\n                                    }[\"$e.useEffect\"]);\n                                }\n                            }[\"$e.useEffect\"]);\n                        }\n                    }[\"$e.useEffect\"]);\n                }\n            }[\"$e.useEffect\"])\n    }[\"$e.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            if (T !== \"system\") {\n                H(T);\n                return;\n            }\n            if (T === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? H(\"dark\") : H(\"light\")), \"undefined\" == \"undefined\") return;\n            let d = window.matchMedia(\"(prefers-color-scheme: dark)\");\n            try {\n                d.addEventListener(\"change\", {\n                    \"$e.useEffect\": ({ matches: h })=>{\n                        H(h ? \"dark\" : \"light\");\n                    }\n                }[\"$e.useEffect\"]);\n            } catch (h) {\n                d.addListener({\n                    \"$e.useEffect\": ({ matches: y })=>{\n                        try {\n                            H(y ? \"dark\" : \"light\");\n                        } catch (R) {\n                            console.error(R);\n                        }\n                    }\n                }[\"$e.useEffect\"]);\n            }\n        }\n    }[\"$e.useEffect\"], [\n        T\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            B.length <= 1 && C(!1);\n        }\n    }[\"$e.useEffect\"], [\n        B\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            let d = {\n                \"$e.useEffect.d\": (h)=>{\n                    var R, j;\n                    f.every({\n                        \"$e.useEffect.d\": (p)=>h[p] || h.code === p\n                    }[\"$e.useEffect.d\"]) && (C(!0), (R = A.current) == null || R.focus()), h.code === \"Escape\" && (document.activeElement === A.current || (j = A.current) != null && j.contains(document.activeElement)) && C(!1);\n                }\n            }[\"$e.useEffect.d\"];\n            return document.addEventListener(\"keydown\", d), ({\n                \"$e.useEffect\": ()=>document.removeEventListener(\"keydown\", d)\n            })[\"$e.useEffect\"];\n        }\n    }[\"$e.useEffect\"], [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            if (A.current) return ({\n                \"$e.useEffect\": ()=>{\n                    L.current && (L.current.focus({\n                        preventScroll: !0\n                    }), L.current = null, z.current = !1);\n                }\n            })[\"$e.useEffect\"];\n        }\n    }[\"$e.useEffect\"], [\n        A.current\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: t,\n        \"aria-label\": `${pt} ${mt}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: !0\n    }, P.map((d, h)=>{\n        var j;\n        let [y, R] = d.split(\"-\");\n        return B.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: d,\n            dir: ot === \"auto\" ? _t() : ot,\n            tabIndex: -1,\n            ref: A,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": W,\n            \"data-y-position\": y,\n            \"data-lifted\": Y && B.length > 1 && !w,\n            \"data-x-position\": R,\n            style: {\n                \"--front-toast-height\": `${((j = nt[0]) == null ? void 0 : j.height) || 0}px`,\n                \"--width\": `${he}px`,\n                \"--gap\": `${at}px`,\n                ...ut,\n                ...Te(i, D)\n            },\n            onBlur: (p)=>{\n                z.current && !p.currentTarget.contains(p.relatedTarget) && (z.current = !1, L.current && (L.current.focus({\n                    preventScroll: !0\n                }), L.current = null));\n            },\n            onFocus: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || z.current || (z.current = !0, L.current = p.relatedTarget);\n            },\n            onMouseEnter: ()=>C(!0),\n            onMouseMove: ()=>C(!0),\n            onMouseLeave: ()=>{\n                lt || C(!1);\n            },\n            onDragEnd: ()=>C(!1),\n            onPointerDown: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || J(!0);\n            },\n            onPointerUp: ()=>J(!1)\n        }, B.filter((p)=>!p.position && h === 0 || p.position === d).map((p, _)=>{\n            var O, G;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ve, {\n                key: p.id,\n                icons: st,\n                index: _,\n                toast: p,\n                defaultRichColors: F,\n                duration: (O = l == null ? void 0 : l.duration) != null ? O : et,\n                className: l == null ? void 0 : l.className,\n                descriptionClassName: l == null ? void 0 : l.descriptionClassName,\n                invert: a,\n                visibleToasts: ft,\n                closeButton: (G = l == null ? void 0 : l.closeButton) != null ? G : S,\n                interacting: lt,\n                position: d,\n                style: l == null ? void 0 : l.style,\n                unstyled: l == null ? void 0 : l.unstyled,\n                classNames: l == null ? void 0 : l.classNames,\n                cancelButtonStyle: l == null ? void 0 : l.cancelButtonStyle,\n                actionButtonStyle: l == null ? void 0 : l.actionButtonStyle,\n                removeToast: ct,\n                toasts: B.filter((k)=>k.position == p.position),\n                heights: nt.filter((k)=>k.position == p.position),\n                setHeights: it,\n                expandByDefault: w,\n                gap: at,\n                loadingIcon: X,\n                expanded: Y,\n                pauseWhenPageIsHidden: rt,\n                swipeDirections: e.swipeDirections\n            });\n        })) : null;\n    }));\n});\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;