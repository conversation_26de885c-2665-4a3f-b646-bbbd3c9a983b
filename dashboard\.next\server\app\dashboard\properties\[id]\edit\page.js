(()=>{var e={};e.id=3580,e.ids=[3580],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25817:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),i=t(43210),o=t(16189),a=t(10452);t(14529);var d=t(59828),n=t(24934),p=t(70334),c=t(71702);function u(){let e=(0,o.useParams)(),r=(0,o.useRouter)(),{t}=(0,a.Y)(),{toast:u}=(0,c.dj)(),[l,x]=(0,i.useState)(null),[h,m]=(0,i.useState)(!0),b=e.id,y=()=>{r.push(`/dashboard/properties/${b}`)};if(h)return(0,s.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[(0,s.jsx)("div",{className:"loading-spinner"}),(0,s.jsx)("span",{className:"mr-3 text-lg",children:"جاري تحميل العقار..."})]})})});if(!l)return(0,s.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"العقار غير موجود"}),(0,s.jsx)("p",{className:"text-gray-400 mb-6",children:"لم يتم العثور على العقار المطلوب"}),(0,s.jsxs)(n.$,{onClick:y,className:"btn-primary",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 ml-2"}),"العودة إلى قائمة العقارات"]})]})})});let f={title:l.title,description:l.description,price:l.price,currency:l.currency,type:l.type,status:l.status,bedrooms:l.bedrooms||0,bathrooms:l.bathrooms||0,area:l.area||0,location:l.location,address:l.address,city:l.city,country:l.country,images:l.images,features:l.features,amenities:l.amenities,yearBuilt:l.yearBuilt,parking:l.parking,furnished:l.furnished,petFriendly:l.petFriendly,utilities:l.utilities,contactInfo:l.contactInfo,agentId:l.agentId,isActive:l.isActive,isFeatured:l.isFeatured};return(0,s.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,s.jsxs)("div",{className:"property-header-dark mb-6",children:[(0,s.jsxs)(n.$,{onClick:y,variant:"ghost",className:"text-gray-400 hover:text-white mb-4",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 ml-2"}),"العودة إلى العقار"]}),(0,s.jsx)("h1",{className:"arabic-heading text-3xl font-bold text-white",children:"تعديل العقار"}),(0,s.jsxs)("p",{className:"text-gray-400 mt-2",children:["تحديث معلومات العقار: ",l.title]})]}),(0,s.jsx)(d.o,{initialData:f,isEdit:!0,propertyId:b,onSuccess:()=>{u({title:"تم تحديث العقار",description:"تم تحديث العقار بنجاح"}),r.push(`/dashboard/properties/${b}`)},onCancel:()=>{r.push(`/dashboard/properties/${b}`)}})]})})}t(83279)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30977:(e,r,t)=>{Promise.resolve().then(t.bind(t,25817))},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73055:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\[id]\\edit\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94529:(e,r,t)=>{Promise.resolve().then(t.bind(t,73055))},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96778:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>l,tree:()=>p});var s=t(65239),i=t(48088),o=t(88170),a=t.n(o),d=t(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(r,n);let p={children:["",{children:["dashboard",{children:["properties",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73055)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\[id]\\edit\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/properties/[id]/edit/page",pathname:"/dashboard/properties/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,8157,2190,3903,5153,1467,1060,4097,3298,4088,9464,381,8318],()=>t(96778));module.exports=s})();