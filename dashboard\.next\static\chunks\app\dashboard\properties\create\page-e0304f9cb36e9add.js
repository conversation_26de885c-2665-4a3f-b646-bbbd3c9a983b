(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6108],{40619:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var a=r(95155),t=r(35695),c=r(83485),i=r(86132);function l(){let e=(0,t.useRouter)(),{t:s}=(0,i.Y)();return(0,a.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,a.jsxs)("div",{className:"property-header-dark",children:[(0,a.jsx)("h1",{className:"arabic-heading",children:s("properties.create")}),(0,a.jsx)("p",{className:"text-slate-400",children:s("properties.subtitle")})]}),(0,a.jsx)(c.o,{onSuccess:()=>{e.push("/dashboard/properties")},onCancel:()=>{e.push("/dashboard/properties")}})]})})}r(6721)},91160:(e,s,r)=>{Promise.resolve().then(r.bind(r,40619))}},e=>{var s=s=>e(e.s=s);e.O(0,[1368,4277,6071,9509,3464,9855,6738,446,3485,8441,1684,7358],()=>s(91160)),_N_E=e.O()}]);