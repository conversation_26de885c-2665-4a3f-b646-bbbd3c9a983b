/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./styles/arabic-properties.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* Arabic Properties Styles - Dark Mode Focused */

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Arabic Typography */
.arabic-text {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

.arabic-heading {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 600;
  line-height: 1.4;
}

/* Dark Mode Property Form */
.property-form-dark {
  background: #0f172a;
  color: #f1f5f9;
}

.property-form-dark .form-section {
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.property-form-dark .form-section h3 {
  color: #f1f5f9;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 2px solid #475569;
  padding-bottom: 8px;
}

/* Input Fields - Dark Mode */
.property-form-dark .form-input {
  background: #0f172a;
  border: 2px solid #334155;
  color: #f1f5f9;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.property-form-dark .form-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.property-form-dark .form-input::placeholder {
  color: #64748b;
}

/* RTL Input Adjustments */
.rtl .form-input {
  text-align: right;
  padding-right: 16px;
  padding-left: 16px;
}

/* Labels - Dark Mode */
.property-form-dark .form-label {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  display: block;
}

.rtl .form-label {
  text-align: right;
}

/* Select Fields - Dark Mode */
.property-form-dark .form-select {
  background: #0f172a;
  border: 2px solid #334155;
  color: #f1f5f9;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.property-form-dark .form-select:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Textarea - Dark Mode */
.property-form-dark .form-textarea {
  background: #0f172a;
  border: 2px solid #334155;
  color: #f1f5f9;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  resize: vertical;
  min-height: 120px;
  transition: all 0.2s ease;
}

.property-form-dark .form-textarea:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.rtl .form-textarea {
  text-align: right;
}

/* Buttons - Dark Mode */
.property-form-dark .btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.property-form-dark .btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.property-form-dark .btn-secondary {
  background: #374151;
  color: #f1f5f9;
  border: 2px solid #4b5563;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.property-form-dark .btn-secondary:hover {
  background: #4b5563;
  border-color: #6b7280;
}

/* Grid Layout - RTL Support */
.property-grid {
  display: grid;
  gap: 16px;
}

.property-grid-2 {
  grid-template-columns: 1fr 1fr;
}

.property-grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

@media (max-width: 768px) {
  .property-grid-2,
  .property-grid-3 {
    grid-template-columns: 1fr;
  }
}

/* Image Upload - Dark Mode */
.property-form-dark .image-upload-area {
  background: #0f172a;
  border: 2px dashed #334155;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.property-form-dark .image-upload-area:hover {
  border-color: #3b82f6;
  background: #1e293b;
}

.property-form-dark .image-upload-area.dragover {
  border-color: #3b82f6;
  background: #1e293b;
  transform: scale(1.02);
}

/* Loading States */
.property-form-dark .loading {
  opacity: 0.6;
  pointer-events: none;
}

.property-form-dark .loading-spinner {
  border: 2px solid #334155;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

.rtl .loading-spinner {
  margin-right: 0;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.property-form-dark .form-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.property-form-dark .form-input.error {
  border-color: #ef4444;
}

.property-form-dark .form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success States */
.property-form-dark .form-success {
  color: #10b981;
  font-size: 12px;
  margin-top: 4px;
}

.property-form-dark .form-input.success {
  border-color: #10b981;
}

/* Header Styles */
.property-header-dark {
  background: #1e293b;
  border-bottom: 1px solid #334155;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 12px;
}

.property-header-dark h1 {
  color: #f1f5f9;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.property-header-dark p {
  color: #94a3b8;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .property-form-dark .form-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .property-header-dark {
    padding: 16px;
    margin-bottom: 16px;
  }

  .property-header-dark h1 {
    font-size: 24px;
  }
}

/* Animation for smooth transitions */
.property-form-dark * {
  transition: all 0.2s ease;
}

/* Focus visible for accessibility */
.property-form-dark .form-input:focus-visible,
.property-form-dark .form-select:focus-visible,
.property-form-dark .form-textarea:focus-visible,
.property-form-dark .btn-primary:focus-visible,
.property-form-dark .btn-secondary:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Property Card Styles - Dark Mode */
.property-card-dark {
  background: #1e293b;
  border: 1px solid #334155;
  color: #f1f5f9;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.property-card-dark:hover {
  border-color: #475569;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.property-card-dark .card-header {
  border-bottom: 1px solid #334155;
}

.property-card-dark .card-content {
  color: #e2e8f0;
}

.property-card-dark .card-title {
  color: #f1f5f9;
}

