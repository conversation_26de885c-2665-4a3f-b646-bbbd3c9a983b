"use strict";exports.id=5153,exports.ids=[5153],exports.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10189:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},16467:(e,t,r)=>{r.d(t,{y:()=>a});var n=r(96330);function a(e){return{createUser:({id:t,...r})=>e.user.create(i(r)),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){let r=await e.account.findUnique({where:{provider_providerAccountId:t},include:{user:!0}});return r?.user??null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},...i(r)}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...a}=r;return{user:n,session:a}},createSession:t=>e.session.create(i(t)),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},...i(t)}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create(i(t));return"id"in r&&r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return"id"in r&&r.id&&delete r.id,r}catch(e){if(e instanceof n.Prisma.PrismaClientKnownRequestError&&"P2025"===e.code)return null;throw e}},getAccount:async(t,r)=>e.account.findFirst({where:{providerAccountId:t,provider:r}}),createAuthenticator:async t=>e.authenticator.create(i(t)),getAuthenticator:async t=>e.authenticator.findUnique({where:{credentialID:t}}),listAuthenticatorsByUserId:async t=>e.authenticator.findMany({where:{userId:t}}),updateAuthenticatorCounter:async(t,r)=>e.authenticator.update({where:{credentialID:t},data:{counter:r}})}}function i(e){let t={};for(let r in e)void 0!==e[r]&&(t[r]=e[r]);return{data:t}}},19443:(e,t,r)=>{let n,a,i,o,s;r.d(t,{Ay:()=>od});var c={};r.r(c),r.d(c,{q:()=>tB,l:()=>tF});var l=function(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r},u=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function d(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class p{constructor(e,t,r){if(rg.add(this),rb.set(this,{}),r_.set(this,void 0),rv.set(this,void 0),l(this,rv,r,"f"),l(this,r_,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(u(this,rb,"f")[e]=r)}get value(){return Object.keys(u(this,rb,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>u(this,rb,"f")[e]).join("")}chunk(e,t){let r=u(this,rg,"m",rA).call(this);for(let n of u(this,rg,"m",rk).call(this,{name:u(this,r_,"f").name,value:e,options:{...u(this,r_,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(u(this,rg,"m",rA).call(this))}}rb=new WeakMap,r_=new WeakMap,rv=new WeakMap,rg=new WeakSet,rk=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return u(this,rb,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,a=e.value.substr(3936*n,3936);r.push({...e,name:t,value:a}),u(this,rb,"f")[t]=a}return u(this,rv,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rA=function(){let e={};for(let t in u(this,rb,"f"))delete u(this,rb,"f")?.[t],e[t]={name:t,value:"",options:{...u(this,r_,"f").options,maxAge:0}};return e};class h extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class f extends h{}f.kind="signIn";class y extends h{}y.type="AdapterError";class m extends h{}m.type="AccessDenied";class w extends h{}w.type="CallbackRouteError";class g extends h{}g.type="ErrorPageLoop";class b extends h{}b.type="EventError";class _ extends h{}_.type="InvalidCallbackUrl";class v extends f{constructor(){super(...arguments),this.code="credentials"}}v.type="CredentialsSignin";class k extends h{}k.type="InvalidEndpoints";class A extends h{}A.type="InvalidCheck";class E extends h{}E.type="JWTSessionError";class S extends h{}S.type="MissingAdapter";class x extends h{}x.type="MissingAdapterMethods";class R extends h{}R.type="MissingAuthorize";class T extends h{}T.type="MissingSecret";class P extends f{}P.type="OAuthAccountNotLinked";class C extends f{}C.type="OAuthCallbackError";class O extends h{}O.type="OAuthProfileParseError";class U extends h{}U.type="SessionTokenError";class $ extends f{}$.type="OAuthSignInError";class j extends f{}j.type="EmailSignInError";class H extends h{}H.type="SignOutError";class I extends h{}I.type="UnknownAction";class D extends h{}D.type="UnsupportedStrategy";class W extends h{}W.type="InvalidProvider";class K extends h{}K.type="UntrustedHost";class L extends h{}L.type="Verification";class M extends f{}M.type="MissingCSRF";let N=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class J extends h{}J.type="DuplicateConditionalUI";class B extends h{}B.type="MissingWebAuthnAutocomplete";class q extends h{}q.type="WebAuthnVerificationError";class z extends f{}z.type="AccountNotLinked";class F extends h{}F.type="ExperimentalFeatureNotEnabled";let V=!1;function G(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let X=!1,Y=!1,Z=!1,Q=["createVerificationToken","useVerificationToken","getUserByEmail"],ee=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],et=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var er=r(55511);let en=(e,t,r,n,a)=>{let i=parseInt(e.substr(3),10)>>3||20,o=(0,er.createHmac)(e,r.byteLength?r:new Uint8Array(i)).update(t).digest(),s=Math.ceil(a/i),c=new Uint8Array(i*s+n.byteLength+1),l=0,u=0;for(let t=1;t<=s;t++)c.set(n,u),c[u+n.byteLength]=t,c.set((0,er.createHmac)(e,o).update(c.subarray(l,u+n.byteLength+1)).digest(),u),l=u,u+=i;return c.slice(0,a)};"function"!=typeof er.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{er.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let ea=async(e,t,r,a,i)=>(n||en)(e,t,r,a,i);function ei(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function eo(e,t,r,n,a){return ea(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=ei(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),ei(r,"salt"),function(e){let t=ei(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(a,e))}let es=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},ec=new TextEncoder,el=new TextDecoder;function eu(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function ed(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function ep(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return ed(r,t,0),ed(r,e%0x100000000,4),r}function eh(e){let t=new Uint8Array(4);return ed(t,e),t}function ef(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:el.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=el.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function ey(e){let t=e;return("string"==typeof t&&(t=ec.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class em extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ew extends em{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eg extends em{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eb extends em{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class e_ extends em{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ev extends em{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class ek extends em{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eA extends em{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eE extends em{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eS extends em{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function ex(e){if(!eR(e))throw Error("CryptoKey instance expected")}function eR(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function eT(e){return e?.[Symbol.toStringTag]==="KeyObject"}let eP=e=>eR(e)||eT(e),eC=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function eO(e){return eC(e)&&"string"==typeof e.kty}function eU(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let e$=(e,...t)=>eU("Key must be ",e,...t);function ej(e,t,...r){return eU(`Key for the ${e} algorithm must be `,t,...r)}async function eH(e){if(eT(e)){if("secret"!==e.type)return e.export({format:"jwk"});e=e.export()}if(e instanceof Uint8Array)return{kty:"oct",k:ey(e)};if(!eR(e))throw TypeError(e$(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...i}=await crypto.subtle.exportKey("jwk",e);return i}async function eI(e){return eH(e)}let eD=(e,t)=>{if("string"!=typeof e||!e)throw new eE(`${t} missing or invalid`)};async function eW(e,t){let r,n;if(eO(e))r=e;else if(eP(e))r=await eI(e);else throw TypeError(e$(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":eD(r.crv,'"crv" (Curve) Parameter'),eD(r.x,'"x" (X Coordinate) Parameter'),eD(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":eD(r.crv,'"crv" (Subtype of Key Pair) Parameter'),eD(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":eD(r.e,'"e" (Exponent) Parameter'),eD(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":eD(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new e_('"kty" (Key Type) Parameter missing or unsupported')}let a=ec.encode(JSON.stringify(n));return ey(await es(t,a))}let eK=Symbol();function eL(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new e_(`Unsupported JWE Algorithm: ${e}`)}}let eM=e=>crypto.getRandomValues(new Uint8Array(eL(e)>>3)),eN=(e,t)=>{if(t.length<<3!==eL(e))throw new ek("Invalid Initialization Vector length")},eJ=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new ek(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eB(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eq(e,t){return e.name===t}function ez(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eq(e.algorithm,"AES-GCM"))throw eB("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eB(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eq(e.algorithm,"AES-KW"))throw eB("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eB(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eB("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eq(e.algorithm,"PBKDF2"))throw eB("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eq(e.algorithm,"RSA-OAEP"))throw eB("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eB(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}async function eF(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(e$(r,"Uint8Array"));let i=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(i>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=eu(a,n,c,ep(a.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,i>>3)),iv:n}}async function eV(e,t,r,n,a){let i;r instanceof Uint8Array?i=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(ez(r,e,"encrypt"),i=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},i,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let eG=async(e,t,r,n,a)=>{if(!eR(r)&&!(r instanceof Uint8Array))throw TypeError(e$(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?eN(e,n):n=eM(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eJ(r,parseInt(e.slice(-3),10)),eF(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eJ(r,parseInt(e.slice(1,4),10)),eV(e,t,r,n,a);default:throw new e_("Unsupported JWE Content Encryption Algorithm")}};function eX(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function eY(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(ez(e,t,r),e)}async function eZ(e,t,r){let n=await eY(t,e,"wrapKey");eX(n,e);let a=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",a,n,"AES-KW"))}async function eQ(e,t,r){let n=await eY(t,e,"unwrapKey");eX(n,e);let a=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",a))}function e0(e){return eu(eh(e.length),e)}async function e1(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(eh(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await es("sha256",n),32*t)}return a.slice(0,t>>3)}async function e2(e,t,r,n,a=new Uint8Array(0),i=new Uint8Array(0)){let o;ez(e,"ECDH"),ez(t,"ECDH","deriveBits");let s=eu(e0(ec.encode(r)),e0(a),e0(i),eh(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e1(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e5(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e8=(e,t)=>eu(ec.encode(e),new Uint8Array([0]),t);async function e3(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new ek("PBES2 Salt Input must be 8 or more octets");let a=e8(t,e),i=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(ez(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,i))}async function e6(e,t,r,n=2048,a=crypto.getRandomValues(new Uint8Array(16))){let i=await e3(a,e,n,t);return{encryptedKey:await eZ(e.slice(-6),i,r),p2c:n,p2s:ey(a)}}async function e4(e,t,r,n,a){let i=await e3(a,e,n,t);return eQ(e.slice(-6),i,r)}let e9=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},e7=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new e_(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function te(e,t,r){return ez(t,e,"encrypt"),e9(e,t),new Uint8Array(await crypto.subtle.encrypt(e7(e),t,r))}async function tt(e,t,r){return ez(t,e,"decrypt"),e9(e,t),new Uint8Array(await crypto.subtle.decrypt(e7(e),t,r))}let tr=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new e_('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new e_('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new e_('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new e_('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},tn=async(e,t,r,n=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let o=await tr({...t,alg:r});return n&&Object.freeze(e),i?i[r]=o:a.set(e,{[r]:o}),o},ta=(e,t)=>{let r;let n=(a||=new WeakMap).get(e);if(n?.[t])return n[t];let i="public"===e.type,o=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:a.set(e,{[t]:r}),r},ti=async(e,t)=>{if(e instanceof Uint8Array||eR(e))return e;if(eT(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return ta(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return tn(e,r,t)}if(eO(e))return e.k?ef(e.k):tn(e,e,t,!0);throw Error("unreachable")};function to(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new e_(`Unsupported JWE Algorithm: ${e}`)}}let ts=e=>crypto.getRandomValues(new Uint8Array(to(e)>>3));async function tc(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),a=new Uint8Array(await crypto.subtle.sign(r,n,e)),i=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=a[s]^i[s];return 0===o}async function tl(e,t,r,n,a,i){let o,s;if(!(t instanceof Uint8Array))throw TypeError(e$(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=eu(i,n,r,ep(i.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{o=await tc(a,p)}catch{}if(!o)throw new ev;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new ev;return s}async function tu(e,t,r,n,a,i){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(ez(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},o,eu(r,a)))}catch{throw new ev}}let td=async(e,t,r,n,a,i)=>{if(!eR(t)&&!(t instanceof Uint8Array))throw TypeError(e$(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new ek("JWE Initialization Vector missing");if(!a)throw new ek("JWE Authentication Tag missing");switch(eN(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eJ(t,parseInt(e.slice(-3),10)),tl(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eJ(t,parseInt(e.slice(1,4),10)),tu(e,t,r,n,a,i);default:throw new e_("Unsupported JWE Content Encryption Algorithm")}};async function tp(e,t,r,n){let a=e.slice(0,7),i=await eG(a,r,t,n,new Uint8Array(0));return{encryptedKey:i.ciphertext,iv:ey(i.iv),tag:ey(i.tag)}}async function th(e,t,r,n,a){return td(e.slice(0,7),t,r,n,a,new Uint8Array(0))}let tf=async(e,t,r,n,a={})=>{let i,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(ex(r),!e5(r))throw new e_("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=a;c=a.epk?await ti(a.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:h,kty:f}=await eI(c),y=await e2(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?to(t):parseInt(e.slice(-5,-2),10),l,u);if(o={epk:{x:d,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),l&&(o.apu=ey(l)),u&&(o.apv=ey(u)),"ECDH-ES"===e){s=y;break}s=n||ts(t);let m=e.slice(-6);i=await eZ(m,y,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||ts(t),ex(r),i=await te(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||ts(t);let{p2c:c,p2s:l}=a;({encryptedKey:i,...o}=await e6(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||ts(t),i=await eZ(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||ts(t);let{iv:c}=a;({encryptedKey:i,...o}=await tp(e,r,s,c));break}default:throw new e_('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}},ty=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tm=(e,t,r,n,a)=>{let i;if(void 0!==a.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new e_(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tw=e=>e?.[Symbol.toStringTag],tg=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},tb=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(eO(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tg(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eP(t))throw TypeError(ej(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tw(t)} instances for symmetric algorithms must be of type "secret"`)}},t_=(e,t,r)=>{if(eO(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tg(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tg(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eP(t))throw TypeError(ej(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tw(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tw(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tw(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tw(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tw(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tv=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tb(e,t,r):t_(e,t,r)};class tk{#e;#t;#r;#n;#a;#i;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#a=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,a,i,o;if(!this.#t&&!this.#n&&!this.#r)throw new ek("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!ty(this.#t,this.#n,this.#r))throw new ek("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(tm(ek,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new e_('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new ek('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new ek('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#i&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);tv("dir"===c?l:c,e,"encrypt");{let a;let i=await ti(e,c);({cek:n,encryptedKey:r,parameters:a}=await tf(c,l,i,this.#i,this.#s)),a&&(t&&eK in t?this.#n?this.#n={...this.#n,...a}:this.setUnprotectedHeader(a):this.#t?this.#t={...this.#t,...a}:this.setProtectedHeader(a))}i=this.#t?ec.encode(ey(JSON.stringify(this.#t))):ec.encode(""),this.#a?(o=ey(this.#a),a=eu(i,ec.encode("."),ec.encode(o))):a=i;let{ciphertext:u,tag:d,iv:p}=await eG(l,this.#e,n,this.#o,a),h={ciphertext:ey(u)};return p&&(h.iv=ey(p)),d&&(h.tag=ey(d)),r&&(h.encrypted_key=ey(r)),o&&(h.aad=o),this.#t&&(h.protected=el.decode(i)),this.#r&&(h.unprotected=this.#r),this.#n&&(h.header=this.#n),h}}class tA{#c;constructor(e){this.#c=new tk(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tE=e=>Math.floor(e.getTime()/1e3),tS=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tx=e=>{let t;let r=tS.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tR(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tT=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tP=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tC{#l;constructor(e){if(!eC(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return ec.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=tR("setNotBefore",e):e instanceof Date?this.#l.nbf=tR("setNotBefore",tE(e)):this.#l.nbf=tE(new Date)+tx(e)}set exp(e){"number"==typeof e?this.#l.exp=tR("setExpirationTime",e):e instanceof Date?this.#l.exp=tR("setExpirationTime",tE(e)):this.#l.exp=tE(new Date)+tx(e)}set iat(e){void 0===e?this.#l.iat=tE(new Date):e instanceof Date?this.#l.iat=tR("setIssuedAt",tE(e)):"string"==typeof e?this.#l.iat=tR("setIssuedAt",tE(new Date)+tx(e)):this.#l.iat=tR("setIssuedAt",e)}}class tO{#i;#o;#s;#t;#u;#d;#p;#h;constructor(e={}){this.#h=new tC(e)}setIssuer(e){return this.#h.iss=e,this}setSubject(e){return this.#h.sub=e,this}setAudience(e){return this.#h.aud=e,this}setJti(e){return this.#h.jti=e,this}setNotBefore(e){return this.#h.nbf=e,this}setExpirationTime(e){return this.#h.exp=e,this}setIssuedAt(e){return this.#h.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#p=!0,this}async encrypt(e,t){let r=new tA(this.#h.data());return this.#t&&(this.#u||this.#d||this.#p)&&(this.#t={...this.#t,iss:this.#u?this.#h.iss:void 0,sub:this.#d?this.#h.sub:void 0,aud:this.#p?this.#h.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#i&&r.setContentEncryptionKey(this.#i),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tU(e,t,r){let n;if(!eC(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return ef(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new e_('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return tr({...e,alg:t,ext:n});default:throw new e_('Unsupported "kty" (Key Type) Parameter value')}}let t$=async(e,t,r,n,a)=>{switch(e){case"dir":if(void 0!==r)throw new ek("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new ek("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!eC(n.epk))throw new ek('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(ex(t),!e5(t))throw new e_("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tU(n.epk,e);if(ex(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new ek('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=ef(n.apu)}catch{throw new ek("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new ek('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=ef(n.apv)}catch{throw new ek("Failed to base64url decode the apv")}}let s=await e2(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?to(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new ek("JWE Encrypted Key missing");return eQ(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new ek("JWE Encrypted Key missing");return ex(t),tt(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new ek("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new ek('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=a?.maxPBES2Count||1e4;if(n.p2c>o)throw new ek('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new ek('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=ef(n.p2s)}catch{throw new ek("Failed to base64url decode the p2s")}return e4(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new ek("JWE Encrypted Key missing");return eQ(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new ek("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new ek('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new ek('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=ef(n.iv)}catch{throw new ek("Failed to base64url decode the iv")}try{i=ef(n.tag)}catch{throw new ek("Failed to base64url decode the tag")}return th(e,t,r,a,i)}default:throw new e_('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tj=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tH(e,t,r){let n,a,i,o,s,c,l;if(!eC(e))throw new ek("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new ek("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new ek("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new ek("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new ek("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new ek("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new ek("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new ek("JWE AAD incorrect type");if(void 0!==e.header&&!eC(e.header))throw new ek("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eC(e.unprotected))throw new ek("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=ef(e.protected);n=JSON.parse(el.decode(t))}catch{throw new ek("JWE Protected Header is invalid")}if(!ty(n,e.header,e.unprotected))throw new ek("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(tm(ek,new Map,r?.crit,n,u),void 0!==u.zip)throw new e_('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new ek("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new ek("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&tj("keyManagementAlgorithms",r.keyManagementAlgorithms),f=r&&tj("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new eb('"alg" (Algorithm) Header Parameter value not allowed');if(f&&!f.has(p))throw new eb('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{a=ef(e.encrypted_key)}catch{throw new ek("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(n,e),y=!0),tv("dir"===d?p:d,t,"decrypt");let m=await ti(t,d);try{i=await t$(d,m,a,u,r)}catch(e){if(e instanceof TypeError||e instanceof ek||e instanceof e_)throw e;i=ts(p)}if(void 0!==e.iv)try{o=ef(e.iv)}catch{throw new ek("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=ef(e.tag)}catch{throw new ek("Failed to base64url decode the tag")}let w=ec.encode(e.protected??"");c=void 0!==e.aad?eu(w,ec.encode("."),ec.encode(e.aad)):w;try{l=ef(e.ciphertext)}catch{throw new ek("Failed to base64url decode the ciphertext")}let g={plaintext:await td(p,i,l,o,s,c)};if(void 0!==e.protected&&(g.protectedHeader=n),void 0!==e.aad)try{g.additionalAuthenticatedData=ef(e.aad)}catch{throw new ek("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(g.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(g.unprotectedHeader=e.header),y)?{...g,key:m}:g}async function tI(e,t,r){if(e instanceof Uint8Array&&(e=el.decode(e)),"string"!=typeof e)throw new ek("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new ek("Invalid Compact JWE");let l=await tH({ciphertext:o,iv:i||void 0,protected:n,tag:s||void 0,encrypted_key:a||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function tD(e,t,r){let n=await tI(e,t,r),a=function(e,t,r={}){let n,a;try{n=JSON.parse(el.decode(t))}catch{}if(!eC(n))throw new eA("JWT Claims Set must be a top-level JSON object");let{typ:i}=r;if(i&&("string"!=typeof e.typ||tT(e.typ)!==tT(i)))throw new ew('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ew(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ew('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ew('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!tP(n.aud,"string"==typeof l?[l]:l))throw new ew('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=tx(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=tE(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new ew('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ew('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>h+a)throw new ew('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ew('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=h-a)throw new eg('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=h-n.iat;if(e-a>("number"==typeof u?u:tx(u)))throw new eg('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-a)throw new ew('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new ew('replicated "iss" claim header parameter mismatch',a,"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new ew('replicated "sub" claim header parameter mismatch',a,"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new ew('replicated "aud" claim header parameter mismatch',a,"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}let tW=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tK=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tL=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tM=/^[\u0020-\u003A\u003D-\u007E]*$/,tN=Object.prototype.toString,tJ=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tB(e,t){let r=new tJ,n=e.length;if(n<2)return r;let a=t?.decode||tV,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let o=e.indexOf(";",i),s=-1===o?n:o;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let c=tq(e,i,t),l=tz(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=tq(e,t+1,s),i=tz(e,s,n),o=a(e.slice(n,i));r[u]=o}i=s+1}while(i<n);return r}function tq(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tz(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tF(e,t,r){let n=r?.encode||encodeURIComponent;if(!tW.test(e))throw TypeError(`argument name is invalid: ${e}`);let a=n(t);if(!tK.test(a))throw TypeError(`argument val is invalid: ${t}`);let i=e+"="+a;if(!r)return i;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);i+="; Max-Age="+r.maxAge}if(r.domain){if(!tL.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);i+="; Domain="+r.domain}if(r.path){if(!tM.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);i+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tN.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);i+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.partitioned&&(i+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return i}function tV(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tG}=c,tX=()=>Date.now()/1e3|0,tY="A256CBC-HS512";async function tZ(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a}=e,i=Array.isArray(r)?r:[r],o=await t0(tY,i[0],a),s=await eW({kty:"oct",k:ey(o)},`sha${o.byteLength<<3}`);return await new tO(t).setProtectedHeader({alg:"dir",enc:tY,kid:s}).setIssuedAt().setExpirationTime(tX()+n).setJti(crypto.randomUUID()).encrypt(o)}async function tQ(e){let{token:t,secret:r,salt:n}=e,a=Array.isArray(r)?r:[r];if(!t)return null;let{payload:i}=await tD(t,async({kid:e,enc:t})=>{for(let r of a){let a=await t0(t,r,n);if(void 0===e||e===await eW({kty:"oct",k:ey(a)},`sha${a.byteLength<<3}`))return a}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tY,"A256GCM"]});return i}async function t0(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await eo("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t1({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:a}=e,i=n.origin;return t?i=await a.redirect({url:t,baseUrl:n.origin}):r&&(i=await a.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}let t2="\x1b[31m",t5="\x1b[0m",t8={error(e){let t=e instanceof h?e.type:e.name;if(console.error(`${t2}[auth][error]${t5} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t2}[auth][cause]${t5}:`,t.stack),r&&console.error(`${t2}[auth][details]${t5}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t5}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t5} ${e}`,JSON.stringify(t,null,2))}};function t3(e){let t={...t8};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t6=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:t4,l:t9}=c;async function t7(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function re(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new I("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:a}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new I(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new I(`Cannot parse action at ${e}`);let[a,i]=n;if(!t6.includes(a)||i&&!["signin","callback","webauthn-options"].includes(a))throw new I(`Cannot parse action at ${e}`);return{action:a,providerId:i}}(r.pathname,t.basePath);return{url:r,action:n,providerId:a,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await t7(e):void 0,cookies:t4(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t3(t);r.error(n),r.debug("request",e)}}function rt(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:a}=e,i=t9(r,n,a);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function rr(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function rn(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function ra({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[a,i]=t.split("|");if(i===await rr(`${a}${e.secret}`))return{csrfTokenVerified:r&&a===n,csrfToken:a}}let a=rn(32),i=await rr(`${a}${e.secret}`);return{cookie:`${a}|${i}`,csrfToken:a}}function ri(e,t){if(!t)throw new M(`CSRF token was missing during an action ${e}`)}function ro(e){return null!==e&&"object"==typeof e}function rs(e,...t){if(!t.length)return e;let r=t.shift();if(ro(e)&&ro(r))for(let t in r)ro(r[t])?(ro(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rs(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rs(e,...t)}let rc=Symbol("skip-csrf-check"),rl=Symbol("return-type-raw"),ru=Symbol("custom-fetch"),rd=Symbol("conform-internal"),rp=e=>rf({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rh=e=>rf({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rf(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function ry(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rm={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rw({authOptions:e,providerId:t,action:r,url:n,cookies:a,callbackUrl:i,csrfToken:o,csrfDisabled:s,isPost:c}){var l,u;let p=t3(e),{providers:h,provider:f}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),a=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:a,...i}=t,o=a?.id??i.id,s=rs(i,a,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=a?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ry(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ry(e.token,e.issuer),n=ry(e.userinfo,e.issuer),a=e.checks??["pkce"];return e.redirectProxyUrl&&(a.includes("state")||a.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:a,userinfo:n,profile:e.profile??rp,account:e.account??rh}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[ru]??(e[ru]=a?.[ru]),e}return s}),i=a.find(({id:e})=>e===t);if(t&&!i){let e=a.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:a,provider:i}}({url:n,providerId:t,config:e}),m=!1;if((f?.type==="oauth"||f?.type==="oidc")&&f.redirectProxyUrl)try{m=new URL(f.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${f.redirectProxyUrl}`)}let w={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:f,cookies:rs(d(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:h,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:tZ,decode:tQ,...e.jwt},events:(l=e.events??{},u=p,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new b(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let a=e[n];return await a(...r)}catch(r){let e=new y(r);throw t.error(e),e}},r),{})}(e.adapter,p),callbacks:{...rm,...e.callbacks},logger:p,callbackUrl:n.origin,isOnRedirectProxy:m,experimental:{...e.experimental}},g=[];if(s)w.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await ra({options:w,cookieValue:a?.[w.cookies.csrfToken.name],isPost:c,bodyValue:o});w.csrfToken=e,w.csrfTokenVerified=r,t&&g.push({name:w.cookies.csrfToken.name,value:t,options:w.cookies.csrfToken.options})}let{callbackUrl:_,callbackUrlCookie:v}=await t1({options:w,cookieValue:a?.[w.cookies.callbackUrl.name],paramValue:i});return w.callbackUrl=_,v&&g.push({name:w.cookies.callbackUrl.name,value:v,options:w.cookies.callbackUrl.options}),{options:w,cookies:g}}var rg,rb,r_,rv,rk,rA,rE,rS,rx,rR,rT,rP,rC,rO,rU,r$,rj={},rH=[],rI=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rD=Array.isArray;function rW(e,t){for(var r in t)e[r]=t[r];return e}function rK(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rL(e,t,r){var n,a,i,o={};for(i in t)"key"==i?n=t[i]:"ref"==i?a=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?rE.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===o[i]&&(o[i]=e.defaultProps[i]);return rM(e,o,n,a,null)}function rM(e,t,r,n,a){var i={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++rx:a,__i:-1,__u:0};return null==a&&null!=rS.vnode&&rS.vnode(i),i}function rN(e){return e.children}function rJ(e,t){this.props=e,this.context=t}function rB(e,t){if(null==t)return e.__?rB(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rB(e):null}function rq(e){(!e.__d&&(e.__d=!0)&&rR.push(e)&&!rz.__r++||rT!==rS.debounceRendering)&&((rT=rS.debounceRendering)||rP)(rz)}function rz(){var e,t,r,n,a,i,o,s;for(rR.sort(rC);e=rR.shift();)e.__d&&(t=rR.length,n=void 0,i=(a=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rW({},a)).__v=a.__v+1,rS.vnode&&rS.vnode(n),rY(r.__P,n,a,r.__n,r.__P.namespaceURI,32&a.__u?[i]:null,o,null==i?rB(a):i,!!(32&a.__u),s),n.__v=a.__v,n.__.__k[n.__i]=n,rZ(o,n,s),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rR.length>t&&rR.sort(rC));rz.__r=0}function rF(e,t,r,n,a,i,o,s,c,l,u){var d,p,h,f,y,m=n&&n.__k||rH,w=t.length;for(r.__d=c,function(e,t,r){var n,a,i,o,s,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(a=t[n])&&"boolean"!=typeof a&&"function"!=typeof a?(o=n+d,(a=e.__k[n]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?rM(null,a,null,null,null):rD(a)?rM(rN,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?rM(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,i=null,-1!==(s=a.__i=function(e,t,r,n){var a=e.key,i=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&a==c.key&&i===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return s;s++}}return -1}(a,r,o,u))&&(u--,(i=r[s])&&(i.__u|=131072)),null==i||null===i.__v?(-1==s&&d--,"function"!=typeof a.type&&(a.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,a.__u|=65536))):a=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(i=r[n])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=rB(i)),function e(t,r,n){var a,i;if(rS.unmount&&rS.unmount(t),(a=t.ref)&&(a.current&&a.current!==t.__e||rQ(a,null,r)),null!=(a=t.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(e){rS.__e(e,r)}a.base=a.__P=null}if(a=t.__k)for(i=0;i<a.length;i++)a[i]&&e(a[i],r,n||"function"!=typeof t.type);n||rK(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(i,i))}(r,t,m),c=r.__d,d=0;d<w;d++)null!=(h=r.__k[d])&&(p=-1===h.__i?rj:m[h.__i]||rj,h.__i=d,rY(e,h,p,a,i,o,s,c,l,u),f=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&rQ(p.ref,null,h),u.push(h.ref,h.__c||f,h)),null==y&&null!=f&&(y=f),65536&h.__u||p.__k===h.__k?c=function e(t,r,n){var a,i;if("function"==typeof t.type){for(a=t.__k,i=0;a&&i<a.length;i++)a[i]&&(a[i].__=t,r=e(a[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rB(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(h,c,e):"function"==typeof h.type&&void 0!==h.__d?c=h.__d:f&&(c=f.nextSibling),h.__d=void 0,h.__u&=-196609);r.__d=c,r.__e=y}function rV(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rI.test(t)?r:r+"px"}function rG(e,t,r,n,a){var i;e:if("style"===t){if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rV(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rV(e.style,t,r[t])}}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=rO,e.addEventListener(t,i?r$:rU,i)):e.removeEventListener(t,i?r$:rU,i);else{if("http://www.w3.org/2000/svg"==a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rX(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rO++;else if(t.t<r.u)return;return r(rS.event?rS.event(t):t)}}}function rY(e,t,r,n,a,i,o,s,c,l){var u,d,p,h,f,y,m,w,g,b,_,v,k,A,E,S,x=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),i=[s=t.__e=r.__e]),(u=rS.__b)&&u(t);e:if("function"==typeof x)try{if(w=t.props,g="prototype"in x&&x.prototype.render,b=(u=x.contextType)&&n[u.__c],_=u?b?b.props.value:u.__:n,r.__c?m=(d=t.__c=r.__c).__=d.__E:(g?t.__c=d=new x(w,_):(t.__c=d=new rJ(w,_),d.constructor=x,d.render=r0),b&&b.sub(d),d.props=w,d.state||(d.state={}),d.context=_,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),g&&null==d.__s&&(d.__s=d.state),g&&null!=x.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rW({},d.__s)),rW(d.__s,x.getDerivedStateFromProps(w,d.__s))),h=d.props,f=d.state,d.__v=t,p)g&&null==x.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),g&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(g&&null==x.getDerivedStateFromProps&&w!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(w,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(w,d.__s,_)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=w,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(w,d.__s,_),g&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(h,f,y)})}if(d.context=_,d.props=w,d.__P=e,d.__e=!1,k=rS.__r,A=0,g){for(d.state=d.__s,d.__d=!1,k&&k(t),u=d.render(d.props,d.state,d.context),E=0;E<d._sb.length;E++)d.__h.push(d._sb[E]);d._sb=[]}else do d.__d=!1,k&&k(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++A<25);d.state=d.__s,null!=d.getChildContext&&(n=rW(rW({},n),d.getChildContext())),g&&!p&&null!=d.getSnapshotBeforeUpdate&&(y=d.getSnapshotBeforeUpdate(h,f)),rF(e,rD(S=null!=u&&u.type===rN&&null==u.key?u.props.children:u)?S:[S],t,r,n,a,i,o,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),m&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=i){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;i[i.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rS.__e(e,t,r)}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,a,i,o,s,c){var l,u,d,p,h,f,y,m=r.props,w=t.props,g=t.type;if("svg"===g?a="http://www.w3.org/2000/svg":"math"===g?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=i){for(l=0;l<i.length;l++)if((h=i[l])&&"setAttribute"in h==!!g&&(g?h.localName===g:3===h.nodeType)){e=h,i[l]=null;break}}if(null==e){if(null===g)return document.createTextNode(w);e=document.createElementNS(a,g,w.is&&w),s&&(rS.__m&&rS.__m(t,i),s=!1),i=null}if(null===g)m===w||s&&e.data===w||(e.data=w);else{if(i=i&&rE.call(e.childNodes),m=r.props||rj,!s&&null!=i)for(m={},l=0;l<e.attributes.length;l++)m[(h=e.attributes[l]).name]=h.value;for(l in m)if(h=m[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=h;else if(!(l in w)){if("value"==l&&"defaultValue"in w||"checked"==l&&"defaultChecked"in w)continue;rG(e,l,null,h,a)}for(l in w)h=w[l],"children"==l?p=h:"dangerouslySetInnerHTML"==l?u=h:"value"==l?f=h:"checked"==l?y=h:s&&"function"!=typeof h||m[l]===h||rG(e,l,h,m[l],a);if(u)s||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),rF(e,rD(p)?p:[p],t,r,n,"foreignObject"===g?"http://www.w3.org/1999/xhtml":a,i,o,i?i[0]:r.__k&&rB(r,0),s,c),null!=i)for(l=i.length;l--;)rK(i[l]);s||(l="value","progress"===g&&null==f?e.removeAttribute("value"):void 0===f||f===e[l]&&("progress"!==g||f)&&("option"!==g||f===m[l])||rG(e,l,f,m[l],a),l="checked",void 0!==y&&y!==e[l]&&rG(e,l,y,m[l],a))}return e}(r.__e,t,r,n,a,i,o,c,l);(u=rS.diffed)&&u(t)}function rZ(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)rQ(r[n],r[++n],r[++n]);rS.__c&&rS.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rS.__e(e,t.__v)}})}function rQ(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rS.__e(e,r)}}function r0(e,t,r){return this.constructor(e,r)}function r1(e,t){var r,n,a,i,o;r=e,rS.__&&rS.__(r,t),a=(n="function"==typeof r1)?null:r1&&r1.__k||t.__k,i=[],o=[],rY(t,r=(!n&&r1||t).__k=rL(rN,null,[r]),a||rj,rj,t.namespaceURI,!n&&r1?[r1]:a?null:t.firstChild?rE.call(t.childNodes):null,i,!n&&r1?r1:a?a.__e:t.firstChild,n,o),rZ(i,r,o)}rE=rH.slice,rS={__e:function(e,t,r,n){for(var a,i,o;t=t.__;)if((a=t.__c)&&!a.__)try{if((i=a.constructor)&&null!=i.getDerivedStateFromError&&(a.setState(i.getDerivedStateFromError(e)),o=a.__d),null!=a.componentDidCatch&&(a.componentDidCatch(e,n||{}),o=a.__d),o)return a.__E=a}catch(t){e=t}throw e}},rx=0,rJ.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rW({},this.state),"function"==typeof e&&(e=e(rW({},r),this.props)),e&&rW(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rq(this))},rJ.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rq(this))},rJ.prototype.render=rN,rR=[],rP="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rC=function(e,t){return e.__v.__b-t.__v.__b},rz.__r=0,rO=0,rU=rX(!1),r$=rX(!0);var r2=/[\s\n\\/='"\0<>]/,r5=/^(xlink|xmlns|xml)([A-Z])/,r8=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r3=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,r6=new Set(["draggable","spellcheck"]),r4=/["&<]/;function r9(e){if(0===e.length||!1===r4.test(e))return e;for(var t=0,r=0,n="",a="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:a="&quot;";break;case 38:a="&amp;";break;case 60:a="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=a,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var r7={},ne=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),nt=/[A-Z]/g;function nr(){this.__d=!0}var nn,na,ni,no,ns=null,nc={},nl=[],nu=Array.isArray,nd=Object.assign;function np(e,t){var r,n=e.type,a=!0;return e.__c?(a=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=nc),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=nd({},r.state,n.getDerivedStateFromProps(r.props,r.state)):a&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!a&&r.componentWillUpdate&&r.componentWillUpdate(),ni&&ni(e),r.render(r.props,r.state,t)}var nh=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),nf=/["&<]/,ny=0;function nm(e,t,r,n,a,i){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ny,__i:-1,__u:0,__source:a,__self:i};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rS.vnode&&rS.vnode(l),l}async function nw(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),i().forEach(e=>{n.searchParams.append(e.name,e.value)});let a=await fetch(n);if(!a.ok){console.error("Failed to fetch options",a);return}return a.json()}function a(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(a().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=a();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e){console.error("Failed to fetch option for autofill authentication");return}try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=a();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t){console.error("Failed to fetch options for form submission");return}if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let ng={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},nb=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function n_({html:e,title:t,status:r,cookies:n,theme:a,headTags:i}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${nb}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${a?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rS.__s;rS.__s=!0,nn=rS.__b,na=rS.diffed,ni=rS.__r,no=rS.unmount;var a=rL(rN,null);a.__k=[e];try{var i=function e(t,r,n,a,i,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?r9(t):t+"";if(nu(t)){var l,u="";i.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var h,f=e(p,r,n,a,i,o,s);"string"==typeof f?u+=f:(l||(l=[]),u&&l.push(u),u="",nu(f)?(h=l).push.apply(h,f):l.push(f))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=i,nn&&nn(t);var y=t.type,m=t.props;if("function"==typeof y){var w,g,b,_=r;if(y===rN){if("tpl"in m){for(var v="",k=0;k<m.tpl.length;k++)if(v+=m.tpl[k],m.exprs&&k<m.exprs.length){var A=m.exprs[k];if(null==A)continue;"object"==typeof A&&(void 0===A.constructor||nu(A))?v+=e(A,r,n,a,t,o,s):v+=A}return v}if("UNSTABLE_comment"in m)return"\x3c!--"+r9(m.UNSTABLE_comment)+"--\x3e";g=m.children}else{if(null!=(w=y.contextType)){var E=r[w.__c];_=E?E.props.value:w.__}var S=y.prototype&&"function"==typeof y.prototype.render;if(S)g=np(t,_),b=t.__c;else{t.__c=b={__v:t,context:_,props:t.props,setState:nr,forceUpdate:nr,__d:!0,__h:[]};for(var x=0;b.__d&&x++<25;)b.__d=!1,ni&&ni(t),g=y.call(b,m,_);b.__d=!0}if(null!=b.getChildContext&&(r=nd({},r,b.getChildContext())),S&&rS.errorBoundaries&&(y.getDerivedStateFromError||b.componentDidCatch)){g=null!=g&&g.type===rN&&null==g.key&&null==g.props.tpl?g.props.children:g;try{return e(g,r,n,a,t,o,s)}catch(i){return y.getDerivedStateFromError&&(b.__s=y.getDerivedStateFromError(i)),b.componentDidCatch&&b.componentDidCatch(i,nc),b.__d?(g=np(t,r),null!=(b=t.__c).getChildContext&&(r=nd({},r,b.getChildContext())),e(g=null!=g&&g.type===rN&&null==g.key&&null==g.props.tpl?g.props.children:g,r,n,a,t,o,s)):""}finally{na&&na(t),t.__=null,no&&no(t)}}}g=null!=g&&g.type===rN&&null==g.key&&null==g.props.tpl?g.props.children:g;try{var R=e(g,r,n,a,t,o,s);return na&&na(t),t.__=null,rS.unmount&&rS.unmount(t),R}catch(i){if(!o&&s&&s.onError){var T=s.onError(i,t,function(i){return e(i,r,n,a,t,o,s)});if(void 0!==T)return T;var P=rS.__e;return P&&P(i,t),""}if(!o||!i||"function"!=typeof i.then)throw i;return i.then(function i(){try{return e(g,r,n,a,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(g,r,n,a,t,o,s)},i)}})}}var C,O="<"+y,U="";for(var $ in m){var j=m[$];if("function"!=typeof j||"class"===$||"className"===$){switch($){case"children":C=j;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in m)continue;$="for";break;case"className":if("class"in m)continue;$="class";break;case"defaultChecked":$="checked";break;case"defaultSelected":$="selected";break;case"defaultValue":case"value":switch($="value",y){case"textarea":C=j;continue;case"select":a=j;continue;case"option":a!=j||"selected"in m||(O+=" selected")}break;case"dangerouslySetInnerHTML":U=j&&j.__html;continue;case"style":"object"==typeof j&&(j=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var a="-"==r[0]?r:r7[r]||(r7[r]=r.replace(nt,"-$&").toLowerCase()),i=";";"number"!=typeof n||a.startsWith("--")||ne.has(a)||(i="px;"),t=t+a+":"+n+i}}return t||void 0}(j));break;case"acceptCharset":$="accept-charset";break;case"httpEquiv":$="http-equiv";break;default:if(r5.test($))$=$.replace(r5,"$1:$2").toLowerCase();else{if(r2.test($))continue;("-"===$[4]||r6.has($))&&null!=j?j+="":n?r3.test($)&&($="panose1"===$?"panose-1":$.replace(/([A-Z])/g,"-$1").toLowerCase()):r8.test($)&&($=$.toLowerCase())}}null!=j&&!1!==j&&(O=!0===j||""===j?O+" "+$:O+" "+$+'="'+("string"==typeof j?r9(j):j+"")+'"')}}if(r2.test(y))throw Error(y+" is not a valid HTML tag name in "+O+">");if(U||("string"==typeof C?U=r9(C):null!=C&&!1!==C&&!0!==C&&(U=e(C,r,"svg"===y||"foreignObject"!==y&&n,a,t,o,s))),na&&na(t),t.__=null,no&&no(t),!U&&nh.has(y))return O+"/>";var H="</"+y+">",I=O+">";return nu(U)?[I].concat(U,[H]):"string"!=typeof U?[I,U,H]:I+U+H}(e,nc,!1,void 0,a,!1,void 0);return nu(i)?i.join(""):i}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rS.__c&&rS.__c(e,nl),rS.__s=n,nl.length=0}}(e)}</div></body></html>`}}function nv(e){let{url:t,theme:r,query:n,cookies:a,pages:i,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:a,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:a,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new I("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:a}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return n_({cookies:a,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:a,email:i,error:o}=e;"undefined"!=typeof document&&a?.brandColor&&document.documentElement.style.setProperty("--brand-color",a.brandColor),"undefined"!=typeof document&&a?.buttonText&&document.documentElement.style.setProperty("--button-text-color",a.buttonText);let s=o&&(ng[o]??ng.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return nm("div",{className:"signin",children:[a?.brandColor&&nm("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${a.brandColor}}`}}),a?.buttonText&&nm("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),nm("div",{className:"card",children:[s&&nm("div",{className:"error",children:nm("p",{children:s})}),a?.logo&&nm("img",{src:a.logo,alt:"Logo",className:"logo"}),r.map((e,a)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return nm("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?nm("form",{action:e.signinUrl,method:"POST",children:[nm("input",{type:"hidden",name:"csrfToken",value:t}),n&&nm("input",{type:"hidden",name:"callbackUrl",value:n}),nm("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[nm("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&nm("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a>0&&"email"!==r[a-1].type&&"credentials"!==r[a-1].type&&"webauthn"!==r[a-1].type&&nm("hr",{}),"email"===e.type&&nm("form",{action:e.signinUrl,method:"POST",children:[nm("input",{type:"hidden",name:"csrfToken",value:t}),nm("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),nm("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),nm("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&nm("form",{action:e.callbackUrl,method:"POST",children:[nm("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>nm("div",{children:[nm("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),nm("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),nm("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&nm("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[nm("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>nm("div",{children:[nm("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),nm("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),nm("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a+1<r.length&&nm("hr",{})]},e.id)})]}),c&&nm(rN,{children:nm("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nw})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:a}:n_({cookies:a,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return nm("div",{className:"signout",children:[n?.brandColor&&nm("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&nm("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),nm("div",{className:"card",children:[n?.logo&&nm("img",{src:n.logo,alt:"Logo",className:"logo"}),nm("h1",{children:"Signout"}),nm("p",{children:"Are you sure you want to sign out?"}),nm("form",{action:t?.toString(),method:"POST",children:[nm("input",{type:"hidden",name:"csrfToken",value:r}),nm("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:`${i.verifyRequest}${t?.search??""}`,cookies:a}:n_({cookies:a,theme:r,html:function(e){let{url:t,theme:r}=e;return nm("div",{className:"verify-request",children:[r.brandColor&&nm("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),nm("div",{className:"card",children:[r.logo&&nm("img",{src:r.logo,alt:"Logo",className:"logo"}),nm("h1",{children:"Check your email"}),nm("p",{children:"A sign in link has been sent to your email address."}),nm("p",{children:nm("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:a}:n_({cookies:a,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,a=`${t}/signin`,i={default:{status:200,heading:"Error",message:nm("p",{children:nm("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:nm("div",{children:[nm("p",{children:"There is a problem with the server configuration."}),nm("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:nm("div",{children:[nm("p",{children:"You do not have permission to sign in."}),nm("p",{children:nm("a",{className:"button",href:a,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:nm("div",{children:[nm("p",{children:"The sign in link is no longer valid."}),nm("p",{children:"It may have been used already or it may have expired."})]}),signin:nm("a",{className:"button",href:a,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=i[r]??i.default;return{status:o,html:nm("div",{className:"error",children:[n?.brandColor&&nm("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),nm("div",{className:"card",children:[n?.logo&&nm("img",{src:n?.logo,alt:"Logo",className:"logo"}),nm("h1",{children:s}),nm("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nk(e,t=Date.now()){return new Date(t+1e3*e)}async function nA(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:a,jwt:i,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!a)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:h,getUserByEmail:f,linkAccount:y,createSession:m,getSessionAndUser:w,deleteSession:g}=a,b=null,_=null,v=!1,k="jwt"===s;if(e){if(k)try{let t=n.cookies.sessionToken.name;(b=await i.decode({...i,token:e,salt:t}))&&"sub"in b&&b.sub&&(_=await p(b.sub))}catch{}else{let t=await w(e);t&&(b=t.session,_=t.user)}}if("email"===l.type){let r=await f(t.email);return r?(_?.id!==r.id&&!k&&e&&await g(e),_=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:_})):(_=await u({...t,emailVerified:new Date}),await o.createUser?.({user:_}),v=!0),{session:b=k?{}:await m({sessionToken:c(),userId:_.id,expires:nk(n.session.maxAge)}),user:_,isNewUser:v}}if("webauthn"===l.type){let e=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(_){if(e.id===_.id){let e={...l,userId:_.id};return{session:b,user:_,isNewUser:v,account:e}}throw new z("The account is already associated with another user",{provider:l.provider})}b=k?{}:await m({sessionToken:c(),userId:e.id,expires:nk(n.session.maxAge)});let t={...l,userId:e.id};return{session:b,user:e,isNewUser:v,account:t}}{if(_){await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t});let e={...l,userId:_.id};return{session:b,user:_,isNewUser:v,account:e}}if(t.email?await f(t.email):null)throw new z("Another account already exists with the same e-mail address",{provider:l.provider});_=await u({...t}),await o.createUser?.({user:_}),await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t}),b=k?{}:await m({sessionToken:c(),userId:_.id,expires:nk(n.session.maxAge)});let e={...l,userId:_.id};return{session:b,user:_,isNewUser:!0,account:e}}}let A=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(A){if(_){if(A.id===_.id)return{session:b,user:_,isNewUser:v};throw new P("The account is already associated with another user",{provider:l.provider})}return{session:b=k?{}:await m({sessionToken:c(),userId:A.id,expires:nk(n.session.maxAge)}),user:A,isNewUser:v}}{let{provider:e}=n,{type:r,provider:a,providerAccountId:i,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:i,provider:a,type:r,userId:s}),_)return await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t}),{session:b,user:_,isNewUser:v};let p=t.email?await f(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)_=p,v=!1;else throw new P("Another account already exists with the same e-mail address",{provider:l.provider})}else _=await u({...t,emailVerified:null}),v=!0;return await o.createUser?.({user:_}),await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t}),{session:b=k?{}:await m({sessionToken:c(),userId:_.id,expires:nk(n.session.maxAge)}),user:_,isNewUser:v}}}function nE(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(i="oauth4webapi/v3.5.1");let nS="ERR_INVALID_ARG_VALUE",nx="ERR_INVALID_ARG_TYPE";function nR(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nT=Symbol(),nP=Symbol(),nC=Symbol(),nO=Symbol(),nU=Symbol(),n$=Symbol(),nj=Symbol(),nH=new TextEncoder,nI=new TextDecoder;function nD(e){return"string"==typeof e?nH.encode(e):nI.decode(e)}function nW(e){return"string"==typeof e?function(e){try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nR("The input to be decoded is not correctly encoded.",nS,e)}}(e):function(e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(e)}class nK extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aV,Error.captureStackTrace?.(this,this.constructor)}}class nL extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nM(e,t,r){return new nL(e,{code:t,cause:r})}function nN(e,t){if(!(e instanceof CryptoKey))throw nR(`${t} must be a CryptoKey`,nx)}function nJ(e,t){if(nN(e,t),"private"!==e.type)throw nR(`${t} must be a private CryptoKey`,nS)}function nB(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nq(e){nE(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(i&&!t.has("user-agent")&&t.set("user-agent",i),t.has("authorization"))throw nR('"options.headers" must not include the "authorization" header name',nS);return t}function nz(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nR('"options.signal" must return or be an instance of AbortSignal',nx);return e}function nF(e){return e.includes("//")?e.replace("//","/"):e}async function nV(e,t,r,n){if(!(e instanceof URL))throw nR(`"${t}" must be an instance of URL`,nx);aa(e,n?.[nT]!==!0);let a=r(new URL(e.href)),i=nq(n?.headers);return i.set("accept","application/json"),(n?.[nO]||fetch)(a.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:n?.signal?nz(n.signal):void 0})}async function nG(e,t){return nV(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nF(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=nF(`${n}/${r.pathname}`);break;default:throw nR('"options.algorithm" must be "oidc" (default), or "oauth2"',nS)}return e},t)}function nX(e,t,r,n,a){try{if("number"!=typeof e||!Number.isFinite(e))throw nR(`${r} must be a number`,nx,a);if(e>0)return;if(t){if(0!==e)throw nR(`${r} must be a non-negative number`,nS,a);return}throw nR(`${r} must be a positive number`,nS,a)}catch(e){if(n)throw nM(e.message,n,a);throw e}}function nY(e,t,r,n){try{if("string"!=typeof e)throw nR(`${t} must be a string`,nx,n);if(0===e.length)throw nR(`${t} must not be empty`,nS,n)}catch(e){if(r)throw nM(e.message,r,n);throw e}}async function nZ(e,t){if(!(e instanceof URL)&&e!==iE)throw nR('"expectedIssuerIdentifier" must be an instance of URL',nx);if(!nE(t,Response))throw nR('"response" must be an instance of Response',nx);if(200!==t.status)throw nM('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',a1,t);it(t);let r=await ik(t);if(nY(r.issuer,'"response" body "issuer" property',aZ,{body:r}),e!==iE&&new URL(r.issuer).href!==e.href)throw nM('"response" body "issuer" property does not match the expected value',a6,{expected:e.href,body:r,attribute:"issuer"});return r}function nQ(e){(function(e,t){if(aE(e)!==t)throw n0(e,t)})(e,"application/json")}function n0(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nM(r,a0,e)}function n1(){return nW(crypto.getRandomValues(new Uint8Array(32)))}async function n2(e){return nY(e,"codeVerifier"),nW(await crypto.subtle.digest("SHA-256",nD(e)))}function n5(e){switch(e.algorithm.name){case"RSA-PSS":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nK("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"RSASSA-PKCS1-v1_5":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nK("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"ECDSA":return function(e){switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nK("unsupported EcKeyAlgorithm namedCurve",{cause:e})}}(e);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nK("unsupported CryptoKey algorithm name",{cause:e})}}function n8(e){let t=e?.[nP];return"number"==typeof t&&Number.isFinite(t)?t:0}function n3(e){let t=e?.[nC];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n6(){return Math.floor(Date.now()/1e3)}function n4(e){if("object"!=typeof e||null===e)throw nR('"as" must be an object',nx);nY(e.issuer,'"as.issuer"')}function n9(e){if("object"!=typeof e||null===e)throw nR('"client" must be an object',nx);nY(e.client_id,'"client.client_id"')}function n7(e,t){let r=n6()+n8(t);return{jti:n1(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function ae(e,t,r){if(!r.usages.includes("sign"))throw nR('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nS);let n=`${nW(nD(JSON.stringify(e)))}.${nW(nD(JSON.stringify(t)))}`,a=nW(await crypto.subtle.sign(is(r),r,nD(n)));return`${n}.${a}`}async function at(e){let{kty:t,e:r,n,x:a,y:i,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:a,y:i,crv:s};return o.set(e,c),c}async function ar(e){return(o||=new WeakMap).get(e)||at(e)}let an=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function aa(e,t){if(t&&"https:"!==e.protocol)throw nM("only requests to HTTPS are allowed",a2,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nM("only HTTP and HTTPS requests are allowed",a5,e)}function ai(e,t,r,n){let a;if("string"!=typeof e||!(a=an(e)))throw nM(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?a9:a7,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return aa(a,n),a}function ao(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?ai(e.mtls_endpoint_aliases[t],t,r,n):ai(e[t],t,r,n)}class as extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aF,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class ac extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aG,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class al extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=az,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let au="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",ad=RegExp("^[,\\s]*("+au+")\\s(.*)"),ap=RegExp("^[,\\s]*("+au+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ah=RegExp("^[,\\s]*"+("("+au+")\\s*=\\s*(")+au+")[,\\s]*(.*)"),af=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function ay(e){if(e.status>399&&e.status<500){it(e),nQ(e);try{let t=await e.clone().json();if(nB(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function am(e,t,r){if(e.status!==t){let t;if(t=await ay(e))throw await e.body?.cancel(),new as("server responded with an error in the response body",{cause:t,response:e});throw nM(`"response" is not a conform ${r} response (unexpected HTTP status code)`,a1,e)}}function aw(e){if(!aD.has(e))throw nR('"options.DPoP" is not a valid DPoPHandle',nS)}async function ag(e,t,r,n,a,i){if(nY(e,'"accessToken"'),!(r instanceof URL))throw nR('"url" must be an instance of URL',nx);aa(r,i?.[nT]!==!0),n=nq(n),i?.DPoP&&(aw(i.DPoP),await i.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (i?.[nO]||fetch)(r.href,{body:a,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:i?.signal?nz(i.signal):void 0});return i?.DPoP?.cacheNonce(o),o}async function ab(e,t,r,n){n4(e),n9(t);let a=ao(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nT]!==!0),i=nq(n?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),ag(r,"GET",a,i,null,{...n,[nP]:n8(t)})}function a_(e,t,r,n){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n6()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function av(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}async function ak(e,t,r){var n;let a,i,o;let{alg:c,kid:l}=r;if(function(e){if(!ii(e.alg))throw new nK('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!s?.has(e)&&!("object"!=typeof(n=t?.[nj])||null===n||!("uat"in n)||"number"!=typeof n.uat||n6()-n.uat>=300)&&"jwks"in n&&nB(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nB)&&a_(e,t?.[nj].jwks,t?.[nj].uat),s?.has(e)){if({jwks:a,age:i}=s.get(e),i>=300)return av(e,t?.[nj]),ak(e,t,r)}else a=await ir(e,t).then(ia),i=0,a_(e,a,n6(),t?.[nj]);switch(c.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new nK("unsupported JWS algorithm",{cause:{alg:c}})}let u=a.keys.filter(e=>{if(e.kty!==o||void 0!==l&&l!==e.kid||void 0!==e.alg&&c!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===c&&"P-256"!==e.crv:case"ES384"===c&&"P-384"!==e.crv:case"ES512"===c&&"P-521"!==e.crv:case"Ed25519"===c&&"Ed25519"!==e.crv:case"EdDSA"===c&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=u;if(!p){if(i>=60)return av(e,t?.[nj]),ak(e,t,r);throw nM("error when selecting a JWT verification key, no applicable keys found",a4,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw nM('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',a4,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return i_(c,d)}let aA=Symbol();function aE(e){return e.headers.get("content-type")?.split(";")[0]}async function aS(e,t,r,n,a){let i;if(n4(e),n9(t),!nE(n,Response))throw nR('"response" must be an instance of Response',nx);if(aU(n),200!==n.status)throw nM('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',a1,n);if(it(n),"application/jwt"===aE(n)){let{claims:r,jwt:o}=await il(await n.text(),iy.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n8(t),n3(t),a?.[n$]).then(a$.bind(void 0,t.client_id)).then(aH.bind(void 0,e));aP.set(n,o),i=r}else{if(t.userinfo_signed_response_alg)throw nM("JWT UserInfo Response expected",aX,n);i=await ik(n)}if(nY(i.sub,'"response" body "sub" property',aZ,{body:i}),r===aA);else if(nY(r,'"expectedSubject"'),i.sub!==r)throw nM('unexpected "response" body "sub" property value',a6,{expected:r,body:i,attribute:"sub"});return i}async function ax(e,t,r,n,a,i,o){return await r(e,t,a,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[nO]||fetch)(n.href,{body:a,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:o?.signal?nz(o.signal):void 0})}async function aR(e,t,r,n,a,i){let o=ao(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[nT]!==!0);a.set("grant_type",n);let s=nq(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(aw(i.DPoP),await i.DPoP.addProof(o,s,"POST"));let c=await ax(e,t,r,o,a,s,i);return i?.DPoP?.cacheNonce(c),c}let aT=new WeakMap,aP=new WeakMap;function aC(e){if(!e.id_token)return;let t=aT.get(e);if(!t)throw nR('"ref" was already garbage collected or did not resolve from the proper sources',nS);return t}async function aO(e,t,r,n,a){if(n4(e),n9(t),!nE(r,Response))throw nR('"response" must be an instance of Response',nx);aU(r),await am(r,200,"Token Endpoint"),it(r);let i=await ik(r);if(nY(i.access_token,'"response" body "access_token" property',aZ,{body:i}),nY(i.token_type,'"response" body "token_type" property',aZ,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new nK("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;nX(e,!1,'"response" body "expires_in" property',aZ,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&nY(i.refresh_token,'"response" body "refresh_token" property',aZ,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw nM('"response" body "scope" property must be a string',aZ,{body:i});if(void 0!==i.id_token){nY(i.id_token,'"response" body "id_token" property',aZ,{body:i});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nX(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await il(i.id_token,iy.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n8(t),n3(t),a?.[n$]).then(aL.bind(void 0,o)).then(aI.bind(void 0,e)).then(aj.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nM('ID Token "aud" (audience) claim includes additional untrusted audiences',a3,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nM('unexpected ID Token "azp" (authorized party) claim value',a3,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nX(s.auth_time,!1,'ID Token "auth_time" (authentication time)',aZ,{claims:s}),aP.set(r,c),aT.set(i,s)}return i}function aU(e){let t;if(t=function(e){if(!nE(e,Response))throw nR('"response" must be an instance of Response',nx);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(ad),a=t?.["1"].toLowerCase();if(n=t?.["2"],!a)return;let i={};for(;n;){let r,a;if(t=n.match(ap)){if([,r,a,n]=t,a.includes("\\"))try{a=JSON.parse(`"${a}"`)}catch{}i[r.toLowerCase()]=a;continue}if(t=n.match(ah)){[,r,a,n]=t,i[r.toLowerCase()]=a;continue}if(t=n.match(af)){if(Object.keys(i).length)break;[,e,n]=t;break}return}let o={scheme:a,parameters:i};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new al("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function a$(e,t){return void 0!==t.claims.aud?aj(e,t):t}function aj(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nM('unexpected JWT "aud" (audience) claim value',a3,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nM('unexpected JWT "aud" (audience) claim value',a3,{expected:e,claims:t.claims,claim:"aud"});return t}function aH(e,t){return void 0!==t.claims.iss?aI(e,t):t}function aI(e,t){let r=e[iS]?.(t)??e.issuer;if(t.claims.iss!==r)throw nM('unexpected JWT "iss" (issuer) claim value',a3,{expected:r,claims:t.claims,claim:"iss"});return t}let aD=new WeakSet;async function aW(e,t,r,n,a,i,o){if(n4(e),n9(t),!aD.has(n))throw nR('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nS);nY(a,'"redirectUri"');let s=im(n,"code");if(!s)throw nM('no authorization code in "callbackParameters"',aZ);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",a),c.set("code",s),i!==iA&&(nY(i,'"codeVerifier"'),c.set("code_verifier",i)),aR(e,t,r,"authorization_code",c,o)}let aK={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function aL(e,t){for(let r of e)if(void 0===t.claims[r])throw nM(`JWT "${r}" (${aK[r]}) claim missing`,aZ,{claims:t.claims});return t}let aM=Symbol(),aN=Symbol();async function aJ(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?aB(e,t,r,n.expectedNonce,n.maxAge,{[n$]:n[n$]}):aq(e,t,r,n)}async function aB(e,t,r,n,a,i){let o=[];switch(n){case void 0:n=aM;break;case aM:break;default:nY(n,'"expectedNonce" argument'),o.push("nonce")}switch(a??=t.default_max_age){case void 0:a=aN;break;case aN:break;default:nX(a,!1,'"maxAge" argument'),o.push("auth_time")}let s=await aO(e,t,r,o,i);nY(s.id_token,'"response" body "id_token" property',aZ,{body:s});let c=aC(s);if(a!==aN){let e=n6()+n8(t),r=n3(t);if(c.auth_time+a<e-r)throw nM("too much time has elapsed since the last End-User authentication",a8,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===aM){if(void 0!==c.nonce)throw nM('unexpected ID Token "nonce" claim value',a3,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nM('unexpected ID Token "nonce" claim value',a3,{expected:n,claims:c,claim:"nonce"});return s}async function aq(e,t,r,n){let a=await aO(e,t,r,void 0,n),i=aC(a);if(i){if(void 0!==t.default_max_age){nX(t.default_max_age,!1,'"client.default_max_age"');let e=n6()+n8(t),r=n3(t);if(i.auth_time+t.default_max_age<e-r)throw nM("too much time has elapsed since the last End-User authentication",a8,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw nM('unexpected ID Token "nonce" claim value',a3,{expected:void 0,claims:i,claim:"nonce"})}return a}let az="OAUTH_WWW_AUTHENTICATE_CHALLENGE",aF="OAUTH_RESPONSE_BODY_ERROR",aV="OAUTH_UNSUPPORTED_OPERATION",aG="OAUTH_AUTHORIZATION_RESPONSE_ERROR",aX="OAUTH_JWT_USERINFO_EXPECTED",aY="OAUTH_PARSE_ERROR",aZ="OAUTH_INVALID_RESPONSE",aQ="OAUTH_INVALID_REQUEST",a0="OAUTH_RESPONSE_IS_NOT_JSON",a1="OAUTH_RESPONSE_IS_NOT_CONFORM",a2="OAUTH_HTTP_REQUEST_FORBIDDEN",a5="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",a8="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",a3="OAUTH_JWT_CLAIM_COMPARISON_FAILED",a6="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",a4="OAUTH_KEY_SELECTION_FAILED",a9="OAUTH_MISSING_SERVER_METADATA",a7="OAUTH_INVALID_SERVER_METADATA";function ie(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nM('unexpected JWT "typ" header parameter value',aZ,{header:t.header});return t}function it(e){if(e.bodyUsed)throw nR('"response" body has been used already',nS)}async function ir(e,t){n4(e);let r=ao(e,"jwks_uri",!1,t?.[nT]!==!0),n=nq(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nO]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nz(t.signal):void 0})}async function ia(e){if(!nE(e,Response))throw nR('"response" must be an instance of Response',nx);if(200!==e.status)throw nM('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',a1,e);it(e);let t=await ik(e,e=>(function(e,...t){if(!t.includes(aE(e)))throw n0(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nM('"response" body "keys" property must be an array',aZ,{body:t});if(!Array.prototype.every.call(t.keys,nB))throw nM('"response" body "keys" property members must be JWK formatted objects',aZ,{body:t});return t}function ii(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function io(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nK(`unsupported ${t.name} modulusLength`,{cause:e})}function is(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nK("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(io(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nK("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return io(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nK("unsupported CryptoKey algorithm name",{cause:e})}async function ic(e,t,r,n){let a=nD(`${e}.${t}`),i=is(r);if(!await crypto.subtle.verify(i,r,n,a))throw nM("JWT signature verification failed",aZ,{key:r,data:a,signature:n,algorithm:i})}async function il(e,t,r,n,a){let i,o,{0:s,1:c,length:l}=e.split(".");if(5===l){if(void 0!==a)e=await a(e),{0:s,1:c,length:l}=e.split(".");else throw new nK("JWE decryption is not configured",{cause:e})}if(3!==l)throw nM("Invalid JWT",aZ,e);try{i=JSON.parse(nD(nW(s)))}catch(e){throw nM("failed to parse JWT Header body as base64url encoded JSON",aY,e)}if(!nB(i))throw nM("JWT Header must be a top level object",aZ,e);if(t(i),void 0!==i.crit)throw new nK('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{o=JSON.parse(nD(nW(c)))}catch(e){throw nM("failed to parse JWT Payload body as base64url encoded JSON",aY,e)}if(!nB(o))throw nM("JWT Payload must be a top level object",aZ,e);let u=n6()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nM('unexpected JWT "exp" (expiration time) claim type',aZ,{claims:o});if(o.exp<=u-n)throw nM('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',a8,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nM('unexpected JWT "iat" (issued at) claim type',aZ,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nM('unexpected JWT "iss" (issuer) claim type',aZ,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nM('unexpected JWT "nbf" (not before) claim type',aZ,{claims:o});if(o.nbf>u+n)throw nM('unexpected JWT "nbf" (not before) claim value',a8,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nM('unexpected JWT "aud" (audience) claim type',aZ,{claims:o});return{header:i,claims:o,jwt:e}}async function iu(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nK(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let a=await crypto.subtle.digest(n,nD(e));return nW(a.slice(0,a.byteLength/2))}async function id(e,t,r,n){return t===await iu(e,r,n)}async function ip(e){if(e.bodyUsed)throw nR("form_post Request instances must contain a readable body",nS,{cause:e});return e.text()}async function ih(e){if("POST"!==e.method)throw nR("form_post responses are expected to use the POST method",nS,{cause:e});if("application/x-www-form-urlencoded"!==aE(e))throw nR("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nS,{cause:e});return ip(e)}function iy(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nM('unexpected JWT "alg" header parameter',aZ,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nM('unexpected JWT "alg" header parameter',aZ,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nM('unexpected JWT "alg" header parameter',aZ,{header:n,expected:r,reason:"default value"});return}throw nM('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function im(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nM(`"${t}" parameter must be provided only once`,aZ);return r}let iw=Symbol(),ig=Symbol();function ib(e,t,r,n){var a;if(n4(e),n9(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nR('"parameters" must be an instance of URLSearchParams, or URL',nx);if(im(r,"response"))throw nM('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',aZ,{parameters:r});let i=im(r,"iss"),o=im(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw nM('response parameter "iss" (issuer) missing',aZ,{parameters:r});if(i&&i!==e.issuer)throw nM('unexpected "iss" (issuer) response parameter value',aZ,{expected:e.issuer,parameters:r});switch(n){case void 0:case ig:if(void 0!==o)throw nM('unexpected "state" response parameter encountered',aZ,{expected:void 0,parameters:r});break;case iw:break;default:if(nY(n,'"expectedState" argument'),o!==n)throw nM(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',aZ,{expected:n,parameters:r})}if(im(r,"error"))throw new ac("authorization response from the server is an error",{cause:r});let s=im(r,"id_token"),c=im(r,"token");if(void 0!==s||void 0!==c)throw new nK("implicit and hybrid flows are not supported");return a=new URLSearchParams(r),aD.add(a),a}async function i_(e,t){let{ext:r,key_ops:n,use:a,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nK("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function iv(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function ik(e,t=nQ){let r;try{r=await e.json()}catch(r){throw t(e),nM('failed to parse "response" body as JSON',aY,r)}if(!nB(r))throw nM('"response" body must be a top level object',aZ,{body:r});return r}let iA=Symbol(),iE=Symbol(),iS=Symbol();async function ix(e,t,r){let{cookies:n,logger:a}=r,i=n[e],o=new Date;o.setTime(o.getTime()+9e5),a.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:o});let s=await tZ({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:o};return{name:i.name,value:s,options:c}}async function iR(e,t,r){try{let{logger:n,cookies:a,jwt:i}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new A(`${e} cookie was missing`);let o=await tQ({...i,token:t,salt:a[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new A(`${e} value could not be parsed`,{cause:t})}}function iT(e,t,r){let{logger:n,cookies:a}=t,i=a[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...a[e].options,maxAge:0}})}function iP(e,t){return async function(r,n,a){let{provider:i,logger:o}=a;if(!i?.checks?.includes(e))return;let s=r?.[a.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await iR(t,s,a);return iT(t,a,n),c}}let iC={async create(e){let t=n1(),r=await n2(t);return{cookie:await ix("pkceCodeVerifier",t,e),value:r}},use:iP("pkce","pkceCodeVerifier")},iO="encodedState",iU={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new A("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n1()},a=await tZ({secret:e.jwt.secret,token:n,salt:iO,maxAge:900});return{cookie:await ix("state",a,e),value:a}},use:iP("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await tQ({secret:t.jwt.secret,token:e,salt:iO});if(r)return r;throw Error("Invalid state")}catch(e){throw new A("State could not be decoded",{cause:e})}}},i$={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n1();return{cookie:await ix("nonce",t,e),value:t}},use:iP("nonce","nonce")},ij="encodedWebauthnChallenge",iH={create:async(e,t,r)=>({cookie:await ix("webauthnChallenge",await tZ({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:ij,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],a=await iR("webauthnChallenge",n,e),i=await tQ({secret:e.jwt.secret,token:a,salt:ij});if(iT("webauthnChallenge",e,r),!i)throw new A("WebAuthn challenge was missing");return i}};function iI(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function iD(e,t,r){let n,a,i;let{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await nG(e,{[nT]:!0,[nO]:s[ru]});if(!(n=await nZ(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":a=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=iI(e),n=iI(t),a=btoa(`${r}:${n}`);return`Basic ${a}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nY(d=s.clientSecret,'"clientSecret"'),a=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":a=function(e,t){let r;nY(e,'"clientSecret"');let n=void 0;return async(t,a,i,o)=>{r||=await crypto.subtle.importKey("raw",nD(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=n7(t,a);n?.(s,c);let l=`${nW(nD(JSON.stringify(s)))}.${nW(nD(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,nD(l));i.set("client_id",a.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${l}.${nW(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":a=function(e,t){var r;let{key:n,kid:a}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&nY(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nJ(n,'"clientPrivateKey.key"'),async(e,r,i,o)=>{let s={alg:n5(n),kid:a},c=n7(e,r);t?.[nU]?.(s,c),i.set("client_id",r.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await ae(s,c,n))}}(s.token.clientPrivateKey,{[nU](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":a=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],h=await iU.use(t,p,r);try{i=ib(n,u,new URLSearchParams(e),s.checks.includes("state")?h:iw)}catch(e){if(e instanceof ac){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new C("OAuth Provider returned an error",t)}throw e}let f=await iC.use(t,p,r),y=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(y=s.redirectProxyUrl);let m=await aW(n,u,a,i,y,f??"decoy",{[nT]:!0,[nO]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[ru]??fetch)(...e))});s.token?.conform&&(m=await s.token.conform(m.clone())??m);let w={},g="oidc"===s.type;if(s[rd])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await m.clone().json();if(e.error){let t={providerId:s.id,...e};throw new C(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eA("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new eA("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new eA("Invalid JWT");if(!n)throw new eA("JWTs must contain a payload");try{t=ef(n)}catch{throw new eA("Failed to base64url decode the payload")}try{r=JSON.parse(el.decode(t))}catch{throw new eA("Failed to parse the decoded payload as JSON")}if(!eC(r))throw new eA("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),a=await nG(r,{[nO]:s[ru]});n=await nZ(r,a)}}}let b=await aJ(n,u,m,{expectedNonce:await i$.use(t,p,r),requireIdToken:g});if(g){let t=aC(b);if(w=t,s[rd]&&"apple"===s.id)try{w.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await ab(n,u,b.access_token,{[nO]:s[ru],[nT]:!0});w=await aS(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:b,provider:s});e instanceof Object&&(w=e)}else if(l?.url){let e=await ab(n,u,b.access_token,{[nO]:s[ru],[nT]:!0});w=await e.json()}else throw TypeError("No userinfo endpoint configured");return b.expires_in&&(b.expires_at=Math.floor(Date.now()/1e3)+Number(b.expires_in)),{...await iW(w,s,b,o),profile:w,cookies:p}}async function iW(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new O(r,{provider:t.id}))}}async function iK(e,t,r,n){let a=await iB(e,t,r),{cookie:i}=await iH.create(e,a.challenge,r);return{status:200,cookies:[...n??[],i],body:{action:"register",options:a},headers:{"Content-Type":"application/json"}}}async function iL(e,t,r,n){let a=await iJ(e,t,r),{cookie:i}=await iH.create(e,a.challenge);return{status:200,cookies:[...n??[],i],body:{action:"authenticate",options:a},headers:{"Content-Type":"application/json"}}}async function iM(e,t,r){let n;let{adapter:a,provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new h("Invalid WebAuthn Authentication response");let s=iF(iz(o.id)),c=await a.getAuthenticator(s);if(!c)throw new h(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await iH.use(e,t.cookies,r);try{var u;let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:iV(u.transports),credentialID:iz(u.credentialID),credentialPublicKey:iz(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new q(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new q("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await a.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new y(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let f=await a.getAccount(c.providerAccountId,i.id);if(!f)throw new h(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let m=await a.getUser(f.userId);if(!m)throw new h(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:m}}async function iN(e,t,r){var n;let a;let{provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new h("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await iH.use(e,t.cookies,r);if(!c)throw new h("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);a=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new q(e)}if(!a.verified||!a.registrationInfo)throw new q("WebAuthn registration response could not be verified");let l={providerAccountId:iF(a.registrationInfo.credentialID),provider:e.provider.id,type:i.type},u={providerAccountId:l.providerAccountId,counter:a.registrationInfo.counter,credentialID:iF(a.registrationInfo.credentialID),credentialPublicKey:iF(a.registrationInfo.credentialPublicKey),credentialBackedUp:a.registrationInfo.credentialBackedUp,credentialDeviceType:a.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function iJ(e,t,r){let{provider:n,adapter:a}=e,i=r&&r.id?await a.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:i?.map(e=>({id:iz(e.credentialID),type:"public-key",transports:iV(e.transports)}))})}async function iB(e,t,r){let{provider:n,adapter:a}=e,i=r.id?await a.listAuthenticatorsByUserId(r.id):null,o=rn(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:iz(e.credentialID),type:"public-key",transports:iV(e.transports)}))})}function iq(e){let{provider:t,adapter:r}=e;if(!r)throw new S("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new W("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function iz(e){return new Uint8Array(Buffer.from(e,"base64"))}function iF(e){return Buffer.from(e).toString("base64")}function iV(e){return e?e.split(","):void 0}async function iG(e,t,r,n){if(!t.provider)throw new W("Callback route called without provider");let{query:a,body:i,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:f,events:y,callbacks:m,session:{strategy:g,maxAge:b},logger:_}=t,k="jwt"===g;try{if("oauth"===c.type||"oidc"===c.type){let o;let s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:a;if(t.isOnRedirectProxy&&s?.state){let e=await iU.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return _.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let h=await iD(s,e.cookies,t);h.cookies.length&&n.push(...h.cookies),_.debug("authorization result",h);let{user:w,account:g,profile:v}=h;if(!w||!g||!v)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:g.providerAccountId,provider:c.id})}let A=await iX({user:o??w,account:g,profile:v},t);if(A)return{redirect:A,cookies:n};let{user:E,session:S,isNewUser:x}=await nA(r.value,w,g,t);if(k){let e={name:E.name,email:E.email,picture:E.image,sub:E.id?.toString()},a=await m.jwt({token:e,user:E,account:g,profile:v,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await y.signIn?.({user:E,account:g,profile:v,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=a?.token,i=a?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:i,token:await rr(`${e}${o}`)}),u=!!s,h=u&&s.expires.valueOf()<Date.now();if(!u||h||i&&s.identifier!==i)throw new L({hasInvite:u,expired:h});let{identifier:w}=s,g=await l.getUserByEmail(w)??{id:crypto.randomUUID(),email:w,emailVerified:null},_={providerAccountId:g.email,userId:g.id,type:"email",provider:c.id},v=await iX({user:g,account:_},t);if(v)return{redirect:v,cookies:n};let{user:A,session:E,isNewUser:S}=await nA(r.value,g,_,t);if(k){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},a=await m.jwt({token:e,user:A,account:_,isNewUser:S,trigger:S?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:E.sessionToken,options:{...t.cookies.sessionToken.options,expires:E.expires}});if(await y.signIn?.({user:A,account:_,isNewUser:S}),S&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=i??{};Object.entries(a??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(i)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new v;let p={providerAccountId:l.id,type:"credentials",provider:c.id},h=await iX({user:l,account:p,credentials:e},t);if(h)return{redirect:h,cookies:n};let w={name:l.name,email:l.email,picture:l.image,sub:l.id},g=await m.jwt({token:w,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===g)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:g,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*b);let o=r.chunk(a,{expires:i});n.push(...o)}return await y.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let a,i,o;let s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new h("Invalid action parameter");let c=iq(t);switch(s){case"authenticate":{let t=await iM(c,e,n);a=t.user,i=t.account;break}case"register":{let r=await iN(t,e,n);a=r.user,i=r.account,o=r.authenticator}}await iX({user:a,account:i},t);let{user:l,isNewUser:u,session:w,account:g}=await nA(r.value,a,i,t);if(!g)throw new h("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),k){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},a=await m.jwt({token:e,user:l,account:g,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:w.sessionToken,options:{...t.cookies.sessionToken.options,expires:w.expires}});if(await y.signIn?.({user:l,account:g,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new W(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof h)throw t;let e=new w(t,{provider:c.id});throw _.debug("callback route error details",{method:o,query:a,body:i}),e}}async function iX(e,t){let r;let{signIn:n,redirect:a}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof h)throw e;throw new m(e)}if(!r)throw new m("AccessDenied");if("string"==typeof r)return await a({url:r,baseUrl:t.url.origin})}async function iY(e,t,r,n,a){let{adapter:i,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},h=t.value;if(!h)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,i=await o.decode({...o,token:h,salt:r});if(!i)throw Error("Invalid JWT");let l=await c.jwt({token:i,...n&&{trigger:"update"},session:a}),u=nk(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let a=await o.encode({...o,token:l,salt:r}),i=t.chunk(a,{expires:u});p.cookies?.push(...i),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new E(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=i,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(h),u=null),u){let{user:t,session:r}=u,i=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*i,f=nk(d);o<=Date.now()&&await l({sessionToken:h,expires:f});let y=await c.session({session:{...r,user:t},user:t,newSession:a,...n?{trigger:"update"}:{}});p.body=y,p.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:f}}),await s.session?.({session:y})}else h&&p.cookies?.push(...t.clean())}catch(e){l.error(new U(e))}return p}async function iZ(e,t){let r,n;let{logger:a,provider:i}=t,o=i.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(i.issuer),t=await nG(e,{[nO]:i[ru],[nT]:!0}),r=await nZ(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,n=i.callbackUrl,a.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await iU.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),i.checks?.includes("pkce")){if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await iC.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}}let p=await i$.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==i.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),a.debug("authorization url is ready",{url:o,cookies:u,provider:i}),{redirect:o.toString(),cookies:u}}async function iQ(e,t){let r;let{body:n}=e,{provider:a,callbacks:i,adapter:o}=t,s=(a.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:a.id};try{r=await i.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new m(e)}if(!r)throw new m("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,h=await a.generateVerificationToken?.()??rn(32),f=new Date(Date.now()+(a.maxAge??86400)*1e3),y=a.secret??t.secret,w=new URL(t.basePath,t.url.origin),g=a.sendVerificationRequest({identifier:s,token:h,expires:f,url:`${w}/callback/${a.id}?${new URLSearchParams({callbackUrl:d,token:h,email:s})}`,provider:a,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),b=o.createVerificationToken?.({identifier:s,token:await rr(`${h}${y}`),expires:f});return await Promise.all([g,b]),{redirect:`${w}/verify-request?${new URLSearchParams({provider:a.id,type:a.type})}`}}async function i0(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:a}=await iZ(e.query,r);return a&&t.push(...a),{redirect:n,cookies:t}}case"email":return{...await iQ(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function i1(e,t,r){let{jwt:n,events:a,callbackUrl:i,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await a.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await a.signOut?.({session:e})}}catch(e){o.error(new H(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function i2(e,t){let{adapter:r,jwt:n,session:{strategy:a}}=e,i=t.value;if(!i)return null;if("jwt"===a){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function i5(e,t,r,n){let a=iq(t),{provider:i}=a,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await i2(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:a=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===a)return"register";break;case void 0:if(!t){if(!n||a)return"authenticate";return"register"}}return null}(o,!!s,c)){case"authenticate":return iL(a,e,l,n);case"register":if("string"==typeof l?.email)return iK(a,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function i8(e,t){let{action:r,providerId:n,error:a,method:i}=e,o=t.skipCSRFCheck===rc,{options:s,cookies:c}=await rw({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:o}),l=new p(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=nv({...s,query:e.query,cookies:c});switch(r){case"callback":return await iG(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(a);case"providers":return t.providers(s.providers);case"session":return await iY(s,l,c);case"signin":return t.signin(n,a);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await i5(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&ri(r,t),await iG(e,s,l,c);case"session":return ri(r,t),await iY(s,l,c,!0,e.body?.data);case"signin":return ri(r,t),await i0(e,c,s);case"signout":return ri(r,t),await i1(c,l,s)}}throw new I(`Cannot handle action: ${r}`)}function i3(e,t,r,n,a){let i;let o=a?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)i=new URL(s),o&&"/"!==o&&"/"!==i.pathname&&(i.pathname!==o&&t3(a).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",a=n.endsWith(":")?n:n+":";i=new URL(`${a}//${e}`)}let c=i.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function i6(e,t){let r=t3(t),n=await re(e,t);if(!n)return Response.json("Bad request.",{status:400});let a=function(e,t){let{url:r}=e,n=[];if(!V&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new K(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new T("Please define a `secret`");let a=e.query?.callbackUrl;if(a&&!G(a,r.origin))return new _(`Invalid callback URL. Received: ${a}`);let{callbackUrl:i}=d(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(o&&!G(o,r.origin))return new _(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e;let{authorization:r,token:n,userinfo:a}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof a||a?.url||(e="userinfo"):e="token":e="authorization",e)return new k(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)X=!0;else if("email"===t.type)Y=!0;else if("webauthn"===t.type){var c;if(Z=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new h(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new J("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new B(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(X){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new D("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new R("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,p=[];if(Y||u?.strategy==="database"||!u?.strategy&&l){if(Y){if(!l)return new S("Email login requires an adapter");p.push(...Q)}else{if(!l)return new S("Database session requires an adapter");p.push(...ee)}}if(Z){if(!t.experimental?.enableWebAuthn)return new F("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new S("WebAuthn requires an adapter");p.push(...et)}if(l){let e=p.filter(e=>!(e in l));if(e.length)return new x(`Required adapter methods were missing: ${e.join(", ")}`)}return V||(V=!0),n}(n,t);if(Array.isArray(a))a.forEach(r.warn);else if(a){if(r.error(a),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new g(`The error page ${e?.error} should not require authentication`)),rt(nv({theme:i}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rl;try{let e=await i8(n,t);if(o)return e;let r=rt(e),a=r.headers.get("Location");if(!i||!a)return r;return Response.json({url:a},{headers:r.headers})}catch(d){r.error(d);let a=d instanceof h;if(a&&o&&!i)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof h&&N.has(d.type)?d.type:"Configuration"});d instanceof v&&s.set("code",d.code);let c=a&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(i)return Response.json({url:u});return Response.redirect(u)}}var i4=r(32190);function i9(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:a}=e.nextUrl;return new i4.NextRequest(n.replace(a,r),e)}function i7(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t3(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),a=e[`AUTH_${n}_ID`],i=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:a,clientSecret:i,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=a),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var oe=r(99933),ot=r(86280);async function or(e,t){return i6(new Request(i3("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function on(e){return"function"==typeof e}function oa(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,ot.b)(),n=await e(void 0);return t?.(n),or(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],a=r[1],i=await e(n);return t?.(i),oi([n,a],i)}if(on(r[0])){let n=r[0];return async(...r)=>{let a=await e(r[0]);return t?.(a),oi(r,a,n)}}let n="req"in r[0]?r[0].req:r[0],a="res"in r[0]?r[0].res:r[1],i=await e(n);return t?.(i),or(new Headers(n.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,ot.b)()).then(t=>or(t,e).then(e=>e.json()));if(t[0]instanceof Request)return oi([t[0],t[1]],e);if(on(t[0])){let r=t[0];return async(...t)=>oi(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return or(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function oi(e,t,r){let n=i9(e[0]),a=await or(n.headers,t),i=await a.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:i}));let s=i4.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),a=Object.values(r.pages??{});return(oo.has(n)||a.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=i,s=await r(n,e[1])??i4.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=i4.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of a.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(73913);let oo=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var os=r(39916);async function oc(e,t={},r,n){let a=new Headers(await (0,ot.b)()),{redirect:i=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??a.get("Referer")??"/",l=i3("signin",a.get("x-forwarded-proto"),a,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),i&&(0,os.redirect)(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,a=r?.id??n.id;if(a===e){d={id:a,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,os.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),a.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:a,body:new URLSearchParams({...s,callbackUrl:c})}),h=await i6(p,{...n,raw:rl,skipCSRFCheck:rc}),f=await (0,oe.U)();for(let e of h?.cookies??[])f.set(e.name,e.value,e.options);let y=(h instanceof Response?h.headers.get("Location"):h.redirect)??u;return i?(0,os.redirect)(y):y}async function ol(e,t){let r=new Headers(await (0,ot.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=i3("signout",r.get("x-forwarded-proto"),r,process.env,t),a=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(n,{method:"POST",headers:r,body:a}),o=await i6(i,{...t,raw:rl,skipCSRFCheck:rc}),s=await (0,oe.U)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,os.redirect)(o.redirect):o}async function ou(e,t){let r=new Headers(await (0,ot.b)());r.set("Content-Type","application/json");let n=new Request(i3("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),a=await i6(n,{...t,raw:rl,skipCSRFCheck:rc}),i=await (0,oe.U)();for(let e of a?.cookies??[])i.set(e.name,e.value,e.options);return a.body}function od(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return i7(r),i6(i9(t),r)};return{handlers:{GET:t,POST:t},auth:oa(e,e=>i7(e)),signIn:async(t,r,n)=>{let a=await e(void 0);return i7(a),oc(t,r,n,a)},signOut:async t=>{let r=await e(void 0);return i7(r),ol(t,r)},unstable_update:async t=>{let r=await e(void 0);return i7(r),ou(t,r)}}}i7(e);let t=t=>i6(i9(t),e);return{handlers:{GET:t,POST:t},auth:oa(e),signIn:(t,r,n)=>oc(t,r,n,e),signOut:t=>ol(t,e),unstable_update:t=>ou(t,e)}}},39916:(e,t,r)=>{var n=r(97576);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},48976:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56056:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}}},62765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),a=r(52637),i=r(51846),o=r(31162),s=r(84971),c=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73560:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){let t=e?.enterprise?.baseUrl??"https://github.com",r=e?.enterprise?.baseUrl?`${e?.enterprise?.baseUrl}/api/v3`:"https://api.github.com";return{id:"github",name:"GitHub",type:"oauth",authorization:{url:`${t}/login/oauth/authorize`,params:{scope:"read:user user:email"}},token:`${t}/login/oauth/access_token`,userinfo:{url:`${r}/user`,async request({tokens:e,provider:t}){let n=await fetch(t.userinfo?.url,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}}).then(async e=>await e.json());if(!n.email){let t=await fetch(`${r}/user/emails`,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}});if(t.ok){let e=await t.json();n.email=(e.find(e=>e.primary)??e[0]).email}}return n}},profile:e=>({id:e.id.toString(),name:e.name??e.login,email:e.email,image:e.avatar_url}),style:{bg:"#24292f",text:"#fff"},options:e}}},73913:(e,t,r)=>{let n=r(63033),a=r(29294),i=r(84971),o=r(76926),s=r(80023),c=r(98479),l=new WeakMap;function u(e){let t=new d(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class d{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){h("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){h("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function h(e){let t=a.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},86280:(e,t,r)=>{Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(92584),a=r(29294),i=r(63033),o=r(84971),s=r(80023),c=r(68388),l=r(76926),u=(r(44523),r(8719));function d(){let e=a.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return h(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t){if("prerender"===t.type)return function(e,t){let r=p.get(t);if(r)return r;let n=(0,c.makeHangingPromise)(t.renderSignal,"`headers()`");return p.set(t,n),Object.defineProperties(n,{append:{value:function(){let r=`\`headers().append(${f(arguments[0])}, ...)\``,n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},delete:{value:function(){let r=`\`headers().delete(${f(arguments[0])})\``,n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},get:{value:function(){let r=`\`headers().get(${f(arguments[0])})\``,n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},has:{value:function(){let r=`\`headers().has(${f(arguments[0])})\``,n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},set:{value:function(){let r=`\`headers().set(${f(arguments[0])}, ...)\``,n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},getSetCookie:{value:function(){let r="`headers().getSetCookie()`",n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},forEach:{value:function(){let r="`headers().forEach(...)`",n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},keys:{value:function(){let r="`headers().keys()`",n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},values:{value:function(){let r="`headers().values()`",n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},entries:{value:function(){let r="`headers().entries()`",n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},[Symbol.iterator]:{value:function(){let r="`headers()[Symbol.iterator]()`",n=m(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}}}),n}(e.route,t);"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t)}(0,o.trackDynamicDataInDynamicRender)(e,t)}return h((0,i.getExpectedRequestStore)("headers").headers)}let p=new WeakMap;function h(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function f(e){return"string"==typeof e?`'${e}'`:"..."}let y=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},86897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return l},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(52836),a=r(49026),i=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i?void 0:null==(r=i.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function l(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function u(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92584:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return a}});let n=r(43763);class a extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new a}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,a)},set(t,r,a,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,a,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,s??r,a,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},94069:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return p},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return f},getModifiedCookieValues:function(){return u},responseCookiesToRequestCookies:function(){return m},wrapWithMutableAccessCheck:function(){return h}});let n=r(23158),a=r(43763),i=r(29294),o=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let a=new n.ResponseCookies(e),i=a.getAll();for(let e of r)a.set(e);for(let e of i)a.set(e);return!0}class p{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case l:return o;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{c()}};default:return a.ReflectAdapter.get(e,t,r)}}});return u}}function h(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return a.ReflectAdapter.get(e,r,n)}}});return t}function f(e){return"action"===e.phase}function y(e){if(!f((0,o.getExpectedRequestStore)(e)))throw new s}function m(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},97576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(86897),a=r(49026),i=r(62765),o=r(48976),s=r(70899),c=r(163);class l extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class u extends URLSearchParams{append(){throw new l}delete(){throw new l}set(){throw new l}sort(){throw new l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99933:(e,t,r)=>{Object.defineProperty(t,"U",{enumerable:!0,get:function(){return p}});let n=r(94069),a=r(23158),i=r(29294),o=r(63033),s=r(84971),c=r(80023),l=r(68388),u=r(76926),d=(r(44523),r(8719));function p(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return f(n.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type)return function(e,t){let r=h.get(t);if(r)return r;let n=(0,l.makeHangingPromise)(t.renderSignal,"`cookies()`");return h.set(t,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let r="`cookies()[Symbol.iterator]()`",n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},size:{get(){let r="`cookies().size`",n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},get:{value:function(){let r;r=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},getAll:{value:function(){let r;r=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},has:{value:function(){let r;r=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},set:{value:function(){let r;if(0==arguments.length)r="`cookies().set()`";else{let e=arguments[0];r=e?`\`cookies().set(${y(e)}, ...)\``:"`cookies().set(...)`"}let n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},delete:{value:function(){let r;r=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},clear:{value:function(){let r="`cookies().clear()`",n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},toString:{value:function(){let r="`cookies().toString()`",n=w(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}}}),n}(t.route,r);"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r)}(0,s.trackDynamicDataInDynamicRender)(t,r)}let u=(0,o.getExpectedRequestStore)(e);return f((0,n.areCookiesMutableInCurrentPhase)(u)?u.userspaceMutableCookies:u.cookies)}let h=new WeakMap;function f(e){let t=h.get(e);if(t)return t;let r=Promise.resolve(e);return h.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):g.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(w);function w(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function g(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}}};