"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/megaphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-alert.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_i18n_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/i18n/client */ \"(app-pages-browser)/./lib/i18n/client.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Define routes with role-based access control\nconst routes = [\n    {\n        label: \"sidebar.analytics\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/dashboard/analytics\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ]\n    },\n    {\n        label: \"sidebar.user\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: \"/dashboard/user\",\n        roles: [\n            \"USER\",\n            \"CLIENT\",\n            \"AGENT\",\n            \"ADMIN\"\n        ]\n    },\n    {\n        label: \"sidebar.clients\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: \"/dashboard/clients\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ]\n    },\n    {\n        label: \"sidebar.messaging\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: \"/dashboard/messaging\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    // Marketing section\n    {\n        label: \"sidebar.marketing\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        href: \"/dashboard/marketing\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ],\n        children: [\n            {\n                label: \"sidebar.campaigns\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                href: \"/dashboard/campaigns\",\n                roles: [\n                    \"ADMIN\",\n                    \"AGENT\"\n                ]\n            },\n            {\n                label: \"sidebar.templates\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                href: \"/dashboard/marketing/templates\",\n                roles: [\n                    \"ADMIN\",\n                    \"AGENT\"\n                ]\n            }\n        ]\n    },\n    {\n        label: \"sidebar.appointments\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        href: \"/dashboard/appointments\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    {\n        label: \"sidebar.ai-chatbot\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        href: \"/dashboard/ai-chatbot\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ]\n    },\n    {\n        label: \"sidebar.database\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        href: \"/dashboard/data\",\n        roles: [\n            \"ADMIN\"\n        ]\n    },\n    {\n        label: \"sidebar.users\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        href: \"/dashboard/users\",\n        roles: [\n            \"ADMIN\"\n        ]\n    },\n    {\n        label: \"sidebar.properties\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        href: \"/dashboard/properties\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    {\n        label: \"sidebar.settings\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        href: \"/dashboard/settings\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    {\n        label: \"sidebar.profile\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: \"/dashboard/profile\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    }\n];\nfunction Sidebar(param) {\n    let { userRole = \"USER\" } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const { t } = (0,_lib_i18n_client__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    // Filter routes based on user role\n    const filteredRoutes = routes.filter((route)=>route.roles.includes(userRole));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative h-full border-r bg-card transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-between px-4 border-b\",\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/dashboard/analytics\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-primary\",\n                            children: \"الذكاء الاصطناعي العقاري\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"ml-auto\",\n                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 57\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"capitalize\",\n                            children: userRole === 'ADMIN' ? 'مدير' : userRole === 'AGENT' ? 'وكيل' : userRole === 'CLIENT' ? 'عميل' : userRole === 'USER' ? 'مستخدم' : userRole\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 py-4\",\n                children: filteredRoutes.map((route)=>{\n                    // Check if the route has children\n                    if (route.children) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center px-4 py-2 text-sm font-medium text-muted-foreground\", isCollapsed && \"justify-center px-0\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(route.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-5 w-5\", isCollapsed ? \"mr-0\" : \"mr-3\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t(route.label)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && route.children.map((childRoute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: childRoute.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center pl-8 pr-4 py-2 text-sm font-medium transition-colors\", pathname === childRoute.href || pathname.startsWith(\"\".concat(childRoute.href, \"/\")) ? \"bg-primary/10 text-primary\" : \"text-muted-foreground hover:bg-primary/5 hover:text-primary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(childRoute.icon, {\n                                                className: \"h-4 w-4 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t(childRoute.label)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, childRoute.href, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 19\n                                    }, this)),\n                                isCollapsed && route.children.map((childRoute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: childRoute.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center justify-center px-0 py-2 text-sm font-medium transition-colors\", pathname === childRoute.href || pathname.startsWith(\"\".concat(childRoute.href, \"/\")) ? \"bg-primary/10 text-primary\" : \"text-muted-foreground hover:bg-primary/5 hover:text-primary\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(childRoute.icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, childRoute.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, route.href, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 15\n                        }, this);\n                    }\n                    // Regular route without children\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: route.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center px-4 py-3 text-sm font-medium transition-colors\", pathname === route.href || pathname.startsWith(\"\".concat(route.href, \"/\")) ? \"bg-primary/10 text-primary\" : \"text-muted-foreground hover:bg-primary/5 hover:text-primary\", isCollapsed && \"justify-center px-0\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(route.icon, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-5 w-5\", isCollapsed ? \"mr-0\" : \"mr-3\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t(route.label)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 32\n                            }, this)\n                        ]\n                    }, route.href, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"ITYO8WGB9Zwa9Eu/QN0nB2DrOmM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _lib_i18n_client__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});