"use client"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { usePathname } from "next/navigation"
import Link from "next/link"
import {
  BarChart3,
  Users,
  MessageSquare,
  Megaphone,
  UserCog,
  Home,
  Settings,
  User,
  Menu,
  X,
  Calendar,
  Bot,
  ShieldAlert,
  FileText,
} from "lucide-react"
import { useState } from "react"
import { useTranslation } from "@/lib/i18n/client"

// Define routes with role-based access control
const routes = [
  {
    label: "sidebar.analytics",
    icon: BarChart3,
    href: "/dashboard/analytics",
    roles: ["ADMIN", "AGENT"], // Only admins and agents can see analytics
  },
  {
    label: "sidebar.user",
    icon: User,
    href: "/dashboard/user",
    roles: ["USER", "CLIENT", "AGENT", "ADMIN"], // All roles can access user dashboard
  },
  {
    label: "sidebar.clients",
    icon: Users,
    href: "/dashboard/clients",
    roles: ["ADMIN", "AGENT"], // Only admins and agents can see clients
  },
  {
    label: "sidebar.messaging",
    icon: MessageSquare,
    href: "/dashboard/messaging",
    roles: ["ADMIN", "AGENT", "CLIENT", "USER"], // All roles can access messaging
  },
  // Marketing section
  {
    label: "sidebar.marketing",
    icon: Megaphone,
    href: "/dashboard/marketing",
    roles: ["ADMIN", "AGENT"],
    children: [
      {
        label: "sidebar.campaigns",
        icon: Megaphone,
        href: "/dashboard/campaigns",
        roles: ["ADMIN", "AGENT"], // Only admins and agents can see campaigns
      },
      {
        label: "sidebar.templates",
        icon: FileText,
        href: "/dashboard/marketing/templates",
        roles: ["ADMIN", "AGENT"], // Only admins and agents can manage templates
      },
    ],
  },
  {
    label: "sidebar.appointments",
    icon: Calendar,
    href: "/dashboard/appointments",
    roles: ["ADMIN", "AGENT", "CLIENT", "USER"], // All roles can access appointments
  },
  {
    label: "sidebar.ai-chatbot",
    icon: Bot,
    href: "/dashboard/ai-chatbot",
    roles: ["ADMIN", "AGENT"], // Only admins and agents can access AI chatbot
  },
  {
    label: "sidebar.database",
    icon: ShieldAlert,
    href: "/dashboard/data",
    roles: ["ADMIN"], // Only admins can access the database dashboard
  },
  {
    label: "sidebar.users",
    icon: UserCog,
    href: "/dashboard/users",
    roles: ["ADMIN"], // Only admins can manage users
  },
  {
    label: "sidebar.properties",
    icon: Home,
    href: "/dashboard/properties",
    roles: ["ADMIN", "AGENT", "CLIENT", "USER"], // All roles can see properties
  },
  {
    label: "sidebar.settings",
    icon: Settings,
    href: "/dashboard/settings",
    roles: ["ADMIN", "AGENT", "CLIENT", "USER"], // All roles can access settings
  },
  {
    label: "sidebar.profile",
    icon: User,
    href: "/dashboard/profile",
    roles: ["ADMIN", "AGENT", "CLIENT", "USER"], // All roles can access their profile
  },
]

interface SidebarProps {
  userRole?: string;
}

export function Sidebar({ userRole = "USER" }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const { t } = useTranslation()

  // Filter routes based on user role
  const filteredRoutes = routes.filter(route =>
    route.roles.includes(userRole)
  )

  return (
    <div className={cn("relative h-full border-r bg-card transition-all duration-300", isCollapsed ? "w-16" : "w-64")}>
      <div className="flex h-16 items-center justify-between px-4 border-b">
        {!isCollapsed && (
          <Link href="/dashboard/analytics">
            <h1 className="text-xl font-bold text-primary">الذكاء الاصطناعي العقاري</h1>
          </Link>
        )}
        <Button variant="ghost" size="icon" className="ml-auto" onClick={() => setIsCollapsed(!isCollapsed)}>
          {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
        </Button>
      </div>

      {/* Role indicator */}
      {!isCollapsed && (
        <div className="px-4 py-2 border-b">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <ShieldAlert className="h-4 w-4" />
            <span className="capitalize">
              {userRole === 'ADMIN' ? 'مدير' :
               userRole === 'AGENT' ? 'وكيل' :
               userRole === 'CLIENT' ? 'عميل' :
               userRole === 'USER' ? 'مستخدم' : userRole}
            </span>
          </div>
        </div>
      )}

      <div className="space-y-1 py-4">
        {filteredRoutes.map((route) => {
          // Check if the route has children
          if (route.children) {
            return (
              <div key={route.href} className="space-y-1">
                {/* Parent route as a section header */}
                <div
                  className={cn(
                    "flex items-center px-4 py-2 text-sm font-medium text-muted-foreground",
                    isCollapsed && "justify-center px-0",
                  )}
                >
                  <route.icon className={cn("h-5 w-5", isCollapsed ? "mr-0" : "mr-3")} />
                  {!isCollapsed && <span>{t(route.label)}</span>}
                </div>

                {/* Child routes */}
                {!isCollapsed && route.children.map((childRoute) => (
                  <Link
                    key={childRoute.href}
                    href={childRoute.href}
                    className={cn(
                      "flex items-center pl-8 pr-4 py-2 text-sm font-medium transition-colors",
                      pathname === childRoute.href || pathname.startsWith(`${childRoute.href}/`)
                        ? "bg-primary/10 text-primary"
                        : "text-muted-foreground hover:bg-primary/5 hover:text-primary",
                    )}
                  >
                    <childRoute.icon className="h-4 w-4 mr-3" />
                    <span>{t(childRoute.label)}</span>
                  </Link>
                ))}

                {/* When collapsed, show child routes as separate items */}
                {isCollapsed && route.children.map((childRoute) => (
                  <Link
                    key={childRoute.href}
                    href={childRoute.href}
                    className={cn(
                      "flex items-center justify-center px-0 py-2 text-sm font-medium transition-colors",
                      pathname === childRoute.href || pathname.startsWith(`${childRoute.href}/`)
                        ? "bg-primary/10 text-primary"
                        : "text-muted-foreground hover:bg-primary/5 hover:text-primary",
                    )}
                  >
                    <childRoute.icon className="h-4 w-4" />
                  </Link>
                ))}
              </div>
            );
          }

          // Regular route without children
          return (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "flex items-center px-4 py-3 text-sm font-medium transition-colors",
                pathname === route.href || pathname.startsWith(`${route.href}/`)
                  ? "bg-primary/10 text-primary"
                  : "text-muted-foreground hover:bg-primary/5 hover:text-primary",
                isCollapsed && "justify-center px-0",
              )}
            >
              <route.icon className={cn("h-5 w-5", isCollapsed ? "mr-0" : "mr-3")} />
              {!isCollapsed && <span>{t(route.label)}</span>}
            </Link>
          );
        })}
      </div>
    </div>
  )
}
