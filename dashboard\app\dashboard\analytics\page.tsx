import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { OverviewChart } from "@/components/analytics/overview-chart"
import { StatsCards } from "@/components/analytics/stats-cards"
import { ClientsChart } from "@/components/analytics/clients-chart"
import { CampaignsChart } from "@/components/analytics/campaigns-chart"
import { PropertiesChart } from "@/components/analytics/properties-chart"
import { getCurrentUser, isAdmin } from "@/lib/auth"
import { redirect } from "next/navigation"

export default async function AnalyticsPage() {
  // Get the current user
  const user = await getCurrentUser()

  if (!user) {
    redirect("/sign-in?redirect_url=/dashboard/analytics")
  }

  // Check if the user is an admin
  const hasAdminRole = await isAdmin()

  if (!hasAdminRole) {
    // Redirect to access denied page
    redirect(`/access-denied?url=${encodeURIComponent('/dashboard/analytics')}`)
  }
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">لوحة التحليلات</h1>
      </div>

      <StatsCards />

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="clients">العملاء</TabsTrigger>
          <TabsTrigger value="campaigns">الحملات</TabsTrigger>
          <TabsTrigger value="properties">العقارات</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>نظرة عامة على الأداء</CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <OverviewChart />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>اكتساب العملاء</CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ClientsChart />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>أداء الحملات</CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <CampaignsChart />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="properties" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>تفاعل العقارات</CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <PropertiesChart />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
