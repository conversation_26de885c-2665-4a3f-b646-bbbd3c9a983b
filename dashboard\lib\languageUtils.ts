/**
 * Language utilities for handling multilingual content
 */

/**
 * Supported language codes
 */
export type LanguageCode = 'en' | 'ar';

/**
 * Check if a string contains Arabic characters
 * @param text The text to check
 * @returns True if the text contains Arabic characters
 */
export const containsArabicCharacters = (text?: string): boolean => {
  if (!text) return false;
  return /[\u0600-\u06FF]/.test(text);
};

/**
 * Detect the language of a template based on its properties
 * @param template The template object with id, name, and content
 * @returns The detected language code
 */
export const detectTemplateLanguage = (template: {
  id?: string;
  name?: string;
  content?: string;
  language?: string;
}): LanguageCode => {
  // If language is explicitly set to Arabic, use it
  if (template.language === 'ar') {
    return 'ar';
  }

  // Check ID for Arabic indicator
  if (template.id && template.id.includes('_ar')) {
    return 'ar';
  }

  // Check name and content for Arabic characters
  if (
    (template.name && containsArabicCharacters(template.name)) ||
    (template.content && containsArabicCharacters(template.content))
  ) {
    return 'ar';
  }

  // Default to Arabic
  return 'ar';
};

/**
 * Validate a language code
 * @param language The language code to validate
 * @returns A valid language code (defaults to 'en')
 */
export const validateLanguageCode = (language?: string): LanguageCode => {
  return (language === 'ar' || language === 'en') ? language as LanguageCode : 'en';
};

/**
 * Set the document direction based on language
 * @param language The language code
 */
export const setDocumentDirection = (language: LanguageCode): void => {
  if (typeof document === 'undefined') return;

  if (language === 'ar') {
    document.documentElement.dir = 'rtl';
    document.documentElement.classList.add('rtl-layout');
  } else {
    document.documentElement.dir = 'ltr';
    document.documentElement.classList.remove('rtl-layout');
  }
};

/**
 * Get text direction based on language
 * @param language The language code
 * @returns The text direction ('rtl' or 'ltr')
 */
export const getTextDirection = (language: LanguageCode): 'rtl' | 'ltr' => {
  return language === 'ar' ? 'rtl' : 'ltr';
};
