"use strict";exports.id=1467,exports.ids=[1467],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(43210);n(60687);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},4363:(e,t,n)=>{n.d(t,{Cl:()=>r,Tt:()=>o,YH:()=>a,fX:()=>u,sH:()=>i,zs:()=>l});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function i(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{u(r.next(e))}catch(e){i(e)}}function l(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,l)}u((r=r.apply(e,t||[])).next())})}function a(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(u){return function(l){if(n)throw TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&l[0]?r.return:l[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,l[1])).done)return o;switch(r=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===l[0]||2===l[0])){i=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){i.label=l[1];break}if(6===l[0]&&i.label<o[1]){i.label=o[1],o=l;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(l);break}o[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,u])}}}Object.create;function l(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function u(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11437:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11490:(e,t,n)=>{n.d(t,{A:()=>W});var r,o=n(4363),i=n(43210),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,s=new WeakMap;function d(e){return e}var f=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=d),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,o.Cl)({async:!0,ssr:!1},e),a}(),p=function(){},h=i.forwardRef(function(e,t){var n,r,a,l,d=i.useRef(null),h=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),m=h[0],v=h[1],g=e.forwardProps,y=e.children,w=e.className,x=e.removeScrollBar,b=e.enabled,k=e.shards,C=e.sideCar,A=e.noIsolation,E=e.inert,M=e.allowPinchZoom,R=e.as,S=e.gapMode,j=(0,o.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[d,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(a=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,l=a.facade,c(function(){var e=s.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}s.set(l,n)},[n]),l),L=(0,o.Cl)((0,o.Cl)({},j),m);return i.createElement(i.Fragment,null,b&&i.createElement(C,{sideCar:f,removeScrollBar:x,shards:k,noIsolation:A,inert:E,setCallbacks:v,allowPinchZoom:!!M,lockRef:d,gapMode:S}),g?i.cloneElement(i.Children.only(y),(0,o.Cl)((0,o.Cl)({},L),{ref:T})):i.createElement(void 0===R?"div":R,(0,o.Cl)({},L,{className:w,ref:T}),y))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:l,zeroRight:a};var m=function(e){var t=e.sideCar,n=(0,o.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,(0,o.Cl)({},n))};m.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=v();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},b=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=b(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=y(),A="data-scroll-locked",E=function(e,t,n,r){var o=e.left,i=e.top,u=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},R=function(){i.useEffect(function(){return document.body.setAttribute(A,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},S=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;R();var a=i.useMemo(function(){return k(o)},[o]);return i.createElement(C,{styles:E(a,!t,o,n?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){j=!1}var L=!!j&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=N(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},N=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{var h=N(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&O(e,u)&&(f+=v,p+=m),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},B=0,z=[];let G=(f.useMedium(function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),a=i.useState(B++)[0],l=i.useState(y)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,o.fX)([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,i=F(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return I(p,t,e,"h"===p?l:c,!0)},[]),s=i.useCallback(function(e){if(z.length&&z[z.length-1]===l){var n="deltaY"in e?_(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=i.useCallback(function(e){n.current=F(e),r.current=void 0},[]),p=i.useCallback(function(t){d(t.type,_(t),t.target,c(t,e.lockRef.current))},[]),h=i.useCallback(function(t){d(t.type,F(t),t.target,c(t,e.lockRef.current))},[]);i.useEffect(function(){return z.push(l),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",s,L),document.addEventListener("touchmove",s,L),document.addEventListener("touchstart",f,L),function(){z=z.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,L),document.removeEventListener("touchmove",s,L),document.removeEventListener("touchstart",f,L)}},[]);var m=e.removeScrollBar,v=e.inert;return i.createElement(i.Fragment,null,v?i.createElement(l,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,m?i.createElement(S,{gapMode:e.gapMode}):null)}),m);var K=i.forwardRef(function(e,t){return i.createElement(h,(0,o.Cl)({},e,{ref:t,sideCar:G}))});K.classNames=h.classNames;let W=K},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},20798:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21134:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},26312:(e,t,n)=>{n.d(t,{H_:()=>e8,UC:()=>e2,YJ:()=>e6,q7:()=>e4,VF:()=>e9,JU:()=>e3,ZL:()=>e1,z6:()=>e5,hN:()=>e7,bL:()=>eQ,wv:()=>te,Pb:()=>tt,G5:()=>tr,ZP:()=>tn,l9:()=>e0});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(65551),u=n(14163),c=n(9510),s=n(43),d=n(31355),f=n(1359),p=n(32547),h=n(96963),m=n(55509),v=n(25028),g=n(46059),y=n(72942),w=n(8730),x=n(13495),b=n(63376),k=n(11490),C=n(60687),A=["Enter"," "],E=["ArrowUp","PageDown","End"],M=["ArrowDown","PageUp","Home",...E],R={ltr:[...A,"ArrowRight"],rtl:[...A,"ArrowLeft"]},S={ltr:["ArrowLeft"],rtl:["ArrowRight"]},j="Menu",[T,L,P]=(0,c.N)(j),[D,O]=(0,a.A)(j,[P,m.Bk,y.RG]),N=(0,m.Bk)(),I=(0,y.RG)(),[F,_]=D(j),[H,B]=D(j),z=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=N(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,x.c)(a),h=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(m.bL,{...u,children:(0,C.jsx)(F,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,C.jsx)(H,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};z.displayName=j;var G=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,C.jsx)(m.Mz,{...o,...r,ref:t})});G.displayName="MenuAnchor";var K="MenuPortal",[W,V]=D(K,{forceMount:void 0}),q=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=_(K,t);return(0,C.jsx)(W,{scope:t,forceMount:n,children:(0,C.jsx)(g.C,{present:n||i.open,children:(0,C.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};q.displayName=K;var X="MenuContent",[U,Y]=D(X),$=r.forwardRef((e,t)=>{let n=V(X,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=_(X,e.__scopeMenu),a=B(X,e.__scopeMenu);return(0,C.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:r||i.open,children:(0,C.jsx)(T.Slot,{scope:e.__scopeMenu,children:a.modal?(0,C.jsx)(Z,{...o,ref:t}):(0,C.jsx)(J,{...o,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=_(X,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,b.Eq)(e)},[]),(0,C.jsx)(Q,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=_(X,e.__scopeMenu);return(0,C.jsx)(Q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:b,onDismiss:A,disableOutsideScroll:R,...S}=e,j=_(X,n),T=B(X,n),P=N(n),D=I(n),O=L(n),[F,H]=r.useState(null),z=r.useRef(null),G=(0,i.s)(t,z,j.onContentChange),K=r.useRef(0),W=r.useRef(""),V=r.useRef(0),q=r.useRef(null),Y=r.useRef("right"),$=r.useRef(0),Z=R?k.A:r.Fragment,J=R?{as:w.DX,allowPinchZoom:!0}:void 0,Q=e=>{let t=W.current+e,n=O().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;(function e(t){W.current=t,window.clearTimeout(K.current),""!==t&&(K.current=window.setTimeout(()=>e(""),1e3))})(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(K.current),[]),(0,f.Oh)();let ee=r.useCallback(e=>Y.current===q.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,u=t[i].x,c=t[i].y;l>r!=c>r&&n<(u-a)*(r-l)/(c-l)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,q.current?.area),[]);return(0,C.jsx)(U,{scope:n,searchRef:W,onItemEnter:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:r.useCallback(e=>{ee(e)||(z.current?.focus(),H(null))},[ee]),onTriggerLeave:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:V,onPointerGraceIntentChange:r.useCallback(e=>{q.current=e},[]),children:(0,C.jsx)(Z,{...J,children:(0,C.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),z.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:b,onDismiss:A,children:(0,C.jsx)(y.bL,{asChild:!0,...D,dir:T.dir,orientation:"vertical",loop:a,currentTabStopId:F,onCurrentTabStopIdChange:H,onEntryFocus:(0,o.m)(h,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eR(j.open),"data-radix-menu-content":"",dir:T.dir,...P,...S,ref:G,style:{outline:"none",...S.style},onKeyDown:(0,o.m)(S.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Q(e.key));let o=z.current;if(e.target!==o||!M.includes(e.key))return;e.preventDefault();let i=O().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(K.current),W.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let t=e.target,n=$.current!==e.clientX;e.currentTarget.contains(t)&&n&&(Y.current=e.clientX>$.current?"right":"left",$.current=e.clientX)}))})})})})})})});$.displayName=X;var ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{role:"group",...r,ref:t})});ee.displayName="MenuGroup";var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{...r,ref:t})});et.displayName="MenuLabel";var en="MenuItem",er="menu.itemSelect",eo=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=B(en,e.__scopeMenu),d=Y(en,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,C.jsx)(ei,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(er,{bubbles:!0,cancelable:!0});e.addEventListener(er,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&A.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=Y(en,n),d=I(n),f=r.useRef(null),p=(0,i.s)(t,f),[h,m]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[c.children]),(0,C.jsx)(T.ItemSlot,{scope:n,disabled:a,textValue:l??v,children:(0,C.jsx)(y.q7,{asChild:!0,...d,focusable:!a,children:(0,C.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,C.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eS(n)?"mixed":n,...i,ref:t,"data-state":ej(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!eS(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,ec]=D(el,{value:void 0,onValueChange:()=>{}}),es=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,x.c)(r);return(0,C.jsx)(eu,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,C.jsx)(ee,{...o,ref:t})})});es.displayName=el;var ed="MenuRadioItem",ef=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=ec(ed,e.__scopeMenu),a=n===i.value;return(0,C.jsx)(eh,{scope:e.__scopeMenu,checked:a,children:(0,C.jsx)(eo,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":ej(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=ed;var ep="MenuItemIndicator",[eh,em]=D(ep,{checked:!1}),ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=em(ep,n);return(0,C.jsx)(g.C,{present:r||eS(i.checked)||!0===i.checked,children:(0,C.jsx)(u.sG.span,{...o,ref:t,"data-state":ej(i.checked)})})});ev.displayName=ep;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,C.jsx)(m.i3,{...o,...r,ref:t})});ey.displayName="MenuArrow";var ew="MenuSub",[ex,eb]=D(ew),ek=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=_(ew,t),l=N(t),[u,c]=r.useState(null),[s,d]=r.useState(null),f=(0,x.c)(i);return r.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,C.jsx)(m.bL,{...l,children:(0,C.jsx)(F,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,C.jsx)(ex,{scope:t,contentId:(0,h.B)(),triggerId:(0,h.B)(),trigger:u,onTriggerChange:c,children:n})})})};ek.displayName=ew;var eC="MenuSubTrigger",eA=r.forwardRef((e,t)=>{let n=_(eC,e.__scopeMenu),a=B(eC,e.__scopeMenu),l=eb(eC,e.__scopeMenu),u=Y(eC,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,C.jsx)(G,{asChild:!0,...f,children:(0,C.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eR(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&R[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eA.displayName=eC;var eE="MenuSubContent",eM=r.forwardRef((e,t)=>{let n=V(X,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=_(X,e.__scopeMenu),c=B(X,e.__scopeMenu),s=eb(eE,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,C.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:a||u.open,children:(0,C.jsx)(T.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(Q,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=S[c.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eR(e){return e?"open":"closed"}function eS(e){return"indeterminate"===e}function ej(e){return eS(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}eM.displayName=eE;var eL="DropdownMenu",[eP,eD]=(0,a.A)(eL,[O]),eO=O(),[eN,eI]=eP(eL),eF=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eO(t),d=r.useRef(null),[f=!1,p]=(0,l.i)({prop:i,defaultProp:a,onChange:u});return(0,C.jsx)(eN,{scope:t,triggerId:(0,h.B)(),triggerRef:d,contentId:(0,h.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,C.jsx)(z,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eF.displayName=eL;var e_="DropdownMenuTrigger",eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eI(e_,n),c=eO(n);return(0,C.jsx)(G,{asChild:!0,...c,children:(0,C.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eH.displayName=e_;var eB=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eO(t);return(0,C.jsx)(q,{...r,...n})};eB.displayName="DropdownMenuPortal";var ez="DropdownMenuContent",eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eI(ez,n),l=eO(n),u=r.useRef(!1);return(0,C.jsx)($,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=ez;var eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ee,{...o,...r,ref:t})});eK.displayName="DropdownMenuGroup";var eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(et,{...o,...r,ref:t})});eW.displayName="DropdownMenuLabel";var eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eo,{...o,...r,ref:t})});eV.displayName="DropdownMenuItem";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ea,{...o,...r,ref:t})});eq.displayName="DropdownMenuCheckboxItem";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(es,{...o,...r,ref:t})});eX.displayName="DropdownMenuRadioGroup";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ef,{...o,...r,ref:t})});eU.displayName="DropdownMenuRadioItem";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ev,{...o,...r,ref:t})});eY.displayName="DropdownMenuItemIndicator";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eg,{...o,...r,ref:t})});e$.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eA,{...o,...r,ref:t})});eZ.displayName="DropdownMenuSubTrigger";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eM,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var eQ=eF,e0=eH,e1=eB,e2=eG,e6=eK,e3=eW,e4=eV,e8=eq,e5=eX,e7=eU,e9=eY,te=e$,tt=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=eO(t),[u=!1,c]=(0,l.i)({prop:r,defaultProp:i,onChange:o});return(0,C.jsx)(ek,{...a,open:u,onOpenChange:c,children:n})},tn=eZ,tr=eJ},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(v),k=(0,a.c)(g),C=r.useRef(null),A=(0,o.s)(t,e=>x(e)),E=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(E.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:h(C.current,{select:!0})},t=function(e){if(E.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,E.paused]),r.useEffect(()=>{if(w){m.add(E);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,k),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(c,k),m.remove(E)},0)}}},[w,b,k,E]);let M=r.useCallback(e=>{if(!n&&!d||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,E.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:A,onKeyDown:M})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},40228:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},53411:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55509:(e,t,n)=>{n.d(t,{Mz:()=>e$,i3:()=>eJ,UC:()=>eZ,bL:()=>eY,Bk:()=>eD});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function k(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=m(g(t)),u=v(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=k(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=k(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=x(h),v=l[p?"floating"===d?"reference":"floating":d],g=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),k=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},C=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-C.top+m.top)/k.y,bottom:(C.bottom-g.bottom+m.bottom)/k.y,left:(g.left-C.left+m.left)/k.x,right:(C.right-g.right+m.right)/k.x}}function E(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function M(e){return o.some(t=>e[t]>=0)}async function R(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),u="y"===g(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),u?{x:v*s,y:m*c}:{x:m*c,y:v*s}}function S(){return"undefined"!=typeof window}function j(e){return P(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(P(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function P(e){return!!S()&&(e instanceof Node||e instanceof T(e).Node)}function D(e){return!!S()&&(e instanceof Element||e instanceof T(e).Element)}function O(e){return!!S()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function N(e){return!!S()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=H(),n=D(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function H(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(j(e))}function z(e){return T(e).getComputedStyle(e)}function G(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function K(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||L(e);return N(t)?t.host:t}function W(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=K(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:O(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=T(o);if(i){let e=V(a);return t.concat(a,a.visualViewport||[],I(o)?o:[],e&&n?W(e):[])}return t.concat(o,W(o,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=O(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function X(e){return D(e)?e:e.contextElement}function U(e){let t=X(e);if(!O(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=q(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let Y=c(0);function $(e){let t=T(e);return H()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=X(e),l=c(1);t&&(r?D(r)&&(l=U(r)):l=U(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===T(a))&&o)?$(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=T(a),t=r&&D(r)?T(r):r,n=e,o=V(n);for(;o&&r&&t!==n;){let e=U(o),t=o.getBoundingClientRect(),r=z(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=V(n=T(o))}}return b({width:f,height:p,x:s,y:d})}function J(e,t){let n=G(e).scrollLeft;return t?t.left+n:Z(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=L(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=H();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=G(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===z(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(L(e));else if(D(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=O(e)?U(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=$(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!O(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=T(e);if(F(e))return n;if(!O(e)){let t=K(e);for(;t&&!B(t);){if(D(t)&&!et(t))return t;t=K(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(j(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!_(r)?n:r||function(e){let t=K(e);for(;O(t)&&!B(t);){if(_(t))return t;if(F(t))break;t=K(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=O(t),o=L(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i){if(("body"!==j(t)||I(o))&&(l=G(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o))}i&&!r&&o&&(u.x=J(o));let s=!o||r||i?c(0):Q(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=L(r),l=!!t&&F(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=O(r);if((f||!f&&!i)&&(("body"!==j(r)||I(a))&&(u=G(r)),O(r))){let e=Z(r);s=U(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):Q(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=W(e,[],!1).filter(e=>D(e)&&"body"!==j(e)),o=null,i="fixed"===z(e).position,a=i?K(e):e;for(;D(a)&&!B(a);){let t=z(a),n=_(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(a)&&!n&&function e(t,n){let r=K(t);return!(r===n||!D(r)||B(r))&&("fixed"===z(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=K(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:U,isElement:D,isRTL:function(e){return"rtl"===z(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=x(p),w={x:n,y:r},b=m(g(o)),k=v(b),C=await u.getDimensions(d),A="y"===b,E=A?"clientHeight":"clientWidth",M=l.reference[k]+l.reference[b]-w[b]-l.floating[k],R=w[b]-l.reference[b],S=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),j=S?S[E]:0;j&&await (null==u.isElement?void 0:u.isElement(S))||(j=c.floating[E]||l.floating[k]);let T=j/2-C[k]/2-1,L=i(y[A?"top":"left"],T),P=i(y[A?"bottom":"right"],T),D=j-C[k]-P,O=j/2-C[k]/2+(M/2-R/2),N=a(L,i(O,D)),I=!s.arrow&&null!=h(o)&&O!==N&&l.reference[k]/2-(O<L?L:P)-C[k]/2<0,F=I?O<L?O-L:O-D:0;return{[b]:w[b]+F,data:{[b]:N,centerOffset:O-N-F,...I&&{alignmentOffset:F}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var ec=n(51215),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await R(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},h=await A(t,s),v=g(p(o)),y=m(v),w=d[y],x=d[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=a(n,i(x,r))}let b=c.fn({...t,[y]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:l,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=g(o),h=m(d),v=s[h],y=s[d],w=f(l,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var b,k;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(k=a.offset)?void 0:k[d])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a,l;let{placement:u,middlewareData:c,rects:s,initialPlacement:d,platform:x,elements:b}=t,{mainAxis:k=!0,crossAxis:C=!0,fallbackPlacements:E,fallbackStrategy:M="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:S=!0,...j}=f(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let T=p(u),L=g(d),P=p(d)===d,D=await (null==x.isRTL?void 0:x.isRTL(b.floating)),O=E||(P||!S?[w(d)]:function(e){let t=w(e);return[y(e),t,y(t)]}(d)),N="none"!==R;!E&&N&&O.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(d,S,R,D));let I=[d,...O],F=await A(t,j),_=[],H=(null==(r=c.flip)?void 0:r.overflows)||[];if(k&&_.push(F[T]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(u,s,D);_.push(F[e[0]],F[e[1]])}if(H=[...H,{placement:u,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=I[e];if(t){let n="alignment"===C&&L!==g(t),r=(null==(a=H[0])?void 0:a.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:H},reset:{placement:t}}}let n=null==(i=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(M){case"bestFit":{let e=null==(l=H.filter(e=>{if(N){let t=g(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=d}if(u!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:u,rects:c,platform:s,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),y=await A(t,v),w=p(u),x=h(u),b="y"===g(u),{width:k,height:C}=c.floating;"top"===w||"bottom"===w?(o=w,l=x===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===x?"top":"bottom");let E=C-y.top-y.bottom,M=k-y.left-y.right,R=i(C-y[o],E),S=i(k-y[l],M),j=!t.middlewareData.shift,T=R,L=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=M),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(T=E),j&&!x){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);b?L=k-2*(0!==e||0!==t?e+t:a(y.left,y.right)):T=C-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...t,availableWidth:L,availableHeight:T});let P=await s.getDimensions(d.floating);return k!==P.width||C!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=E(await A(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:M(e)}}}case"escaped":{let e=E(await A(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:M(e)}}}default:return{}}}}}(e),options:[e,t]}),ek=(e,t)=>({...em(e),options:[e,t]});var eC=n(14163),eA=n(60687),eE=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eA.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eE.displayName="Arrow";var eM=n(98599),eR=n(11273),eS=n(13495),ej=n(66156),eT=n(18853),eL="Popper",[eP,eD]=(0,eR.A)(eL),[eO,eN]=eP(eL),eI=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eA.jsx)(eO,{scope:t,anchor:o,onAnchorChange:i,children:n})};eI.displayName=eL;var eF="PopperAnchor",e_=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eN(eF,n),l=r.useRef(null),u=(0,eM.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eA.jsx)(eC.sG.div,{...i,ref:u})});e_.displayName=eF;var eH="PopperContent",[eB,ez]=eP(eH),eG=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,x=eN(eH,n),[b,k]=r.useState(null),C=(0,eM.s)(t,e=>k(e)),[A,E]=r.useState(null),M=(0,eT.X)(A),R=M?.width??0,S=M?.height??0,j="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},T=Array.isArray(p)?p:[p],P=T.length>0,D={padding:j,boundary:T.filter(eq),altBoundary:P},{refs:O,floatingStyles:N,placement:I,isPositioned:F,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),x=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=a||m,k=l||g,C=r.useRef(null),A=r.useRef(null),E=r.useRef(d),M=null!=c,R=eh(c),S=eh(i),j=eh(s),T=r.useCallback(()=>{if(!C.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),eu(C.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};L.current&&!ed(E.current,t)&&(E.current=t,ec.flushSync(()=>{f(t)}))})},[p,t,n,S,j]);es(()=>{!1===s&&E.current.isPositioned&&(E.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let L=r.useRef(!1);es(()=>(L.current=!0,()=>{L.current=!1}),[]),es(()=>{if(b&&(C.current=b),k&&(A.current=k),b&&k){if(R.current)return R.current(b,k,T);T()}},[b,k,T,R,M]);let P=r.useMemo(()=>({reference:C,floating:A,setReference:w,setFloating:x}),[w,x]),D=r.useMemo(()=>({reference:b,floating:k}),[b,k]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),r=ep(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:T,refs:P,elements:D,floatingStyles:O}),[d,T,P,D,O])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=X(e),h=l||c?[...p?W(p):[],...W(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=L(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(s||t(),!m||!v)return;let g=u(h),y=u(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||c(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let r=Z(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:x.anchor},middleware:[ev({mainAxis:l+S,alignmentAxis:s}),f&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...D}),f&&ew({...D}),ex({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),A&&ek({element:A,padding:d}),eX({arrowWidth:R,arrowHeight:S}),v&&eb({strategy:"referenceHidden",...D})]}),[H,B]=eU(I),z=(0,eS.c)(y);(0,ej.N)(()=>{F&&z?.()},[F,z]);let G=_.arrow?.x,K=_.arrow?.y,V=_.arrow?.centerOffset!==0,[q,U]=r.useState();return(0,ej.N)(()=>{b&&U(window.getComputedStyle(b).zIndex)},[b]),(0,eA.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...N,transform:F?N.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eB,{scope:n,placedSide:H,onArrowChange:E,arrowX:G,arrowY:K,shouldHideArrow:V,children:(0,eA.jsx)(eC.sG.div,{"data-side":H,"data-align":B,...w,ref:C,style:{...w.style,animation:F?void 0:"none"}})})})});eG.displayName=eH;var eK="PopperArrow",eW={top:"bottom",right:"left",bottom:"top",left:"right"},eV=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eK,n),i=eW[o.placedSide];return(0,eA.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eE,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eV.displayName=eK;var eX=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=eU(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=i?s:`${d}px`,h=`${-l}px`):"top"===u?(p=i?s:`${d}px`,h=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function eU(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eI,e$=e_,eZ=eG,eJ=eV},56843:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]])},58869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},65822:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},72840:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]])},72942:(e,t,n)=>{n.d(t,{RG:()=>b,bL:()=>T,q7:()=>L});var r=n(43210),o=n(70569),i=n(9510),a=n(98599),l=n(11273),u=n(96963),c=n(14163),s=n(13495),d=n(65551),f=n(43),p=n(60687),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,i.N)(v),[x,b]=(0,l.A)(v,[w]),[k,C]=x(v),A=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:t})})}));A.displayName=v;var E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:v,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:b=!1,...C}=e,A=r.useRef(null),E=(0,a.s)(t,A),M=(0,f.jH)(u),[R=null,S]=(0,d.i)({prop:v,defaultProp:g,onChange:w}),[T,L]=r.useState(!1),P=(0,s.c)(x),D=y(n),O=r.useRef(!1),[N,I]=r.useState(0);return r.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(h,P),()=>e.removeEventListener(h,P)},[P]),(0,p.jsx)(k,{scope:n,orientation:i,dir:M,loop:l,currentTabStopId:R,onItemFocus:r.useCallback(e=>S(e),[S]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:T||0===N?-1:0,"data-orientation":i,...C,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),b)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),M="RovingFocusGroupItem",R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,...s}=e,d=(0,u.B)(),f=l||d,h=C(M,n),m=h.currentTabStopId===f,v=y(n),{onFocusableItemAdd:w,onFocusableItemRemove:x}=h;return r.useEffect(()=>{if(i)return w(),()=>x()},[i,w,x]),(0,p.jsx)(g.ItemSlot,{scope:n,id:f,focusable:i,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>j(n))}})})})});R.displayName=M;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var T=A,L=R},83753:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},84027:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},92951:(e,t,n)=>{n.d(t,{H4:()=>b,_V:()=>x,bL:()=>w});var r=n(43210),o=n(11273),i=n(13495),a=n(66156),l=n(14163),u=n(60687),c="Avatar",[s,d]=(0,o.A)(c),[f,p]=s(c),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,u.jsx)(f,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,u.jsx)(l.sG.span,{...o,ref:t})})});h.displayName=c;var m="AvatarImage",v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...s}=e,d=p(m,n),f=function(e,t){let[n,o]=r.useState("idle");return(0,a.N)(()=>{if(!e){o("error");return}let n=!0,r=new window.Image,i=e=>()=>{n&&o(e)};return o("loading"),r.onload=i("loaded"),r.onerror=i("error"),r.src=e,t&&(r.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(o,s.referrerPolicy),h=(0,i.c)(e=>{c(e),d.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,u.jsx)(l.sG.img,{...s,ref:t,src:o}):null});v.displayName=m;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=p(g,n),[c,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==a.imageLoadingStatus?(0,u.jsx)(l.sG.span,{...i,ref:t}):null});y.displayName=g;var w=h,x=v,b=y},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},97051:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])}};