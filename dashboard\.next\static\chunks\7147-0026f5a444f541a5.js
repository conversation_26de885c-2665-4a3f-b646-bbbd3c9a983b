"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7147],{471:(e,t,r)=>{r.d(t,{Lf:()=>s,UU:()=>n,zO:()=>a});var o=r(31886);let s=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{return await o.A.get("/clients?page=".concat(e,"&limit=").concat(t))}catch(e){throw console.error("Error fetching clients:",e),e}},n=async e=>{try{return await o.A.post("/clients",e)}catch(e){throw console.error("Error creating client:",e),e}},a=async(e,t)=>{try{return await o.A.put("/clients/".concat(e),t)}catch(t){throw console.error("Error updating client with ID ".concat(e,":"),t),t}}},31886:(e,t,r)=>{r.d(t,{A:()=>n});var o=r(23464);class s{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let r=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),r.data}catch(t){if(t.response){let r=t.response.status;if(console.log("API Client: Request to ".concat(e," failed with status ").concat(r)),404===r){let t=Error("Resource not found: ".concat(e));throw t.status=404,t.isNotFound=!0,t}}throw t}}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let r=await this.client.delete(e,t);if(204===r.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return r.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async upload(e,t,r){let o={...r,headers:{...null==r?void 0:r.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,o)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=o.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let n=new s},53999:(e,t,r)=>{r.d(t,{cn:()=>n});var o=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,o.$)(t))}},67133:(e,t,r)=>{r.d(t,{SQ:()=>u,_2:()=>p,mB:()=>m,rI:()=>c,ty:()=>f});var o=r(95155),s=r(12115),n=r(48698),a=r(13052),i=r(5196),l=r(9428),d=r(53999);let c=n.bL,f=n.l9;n.YJ,n.ZL,n.Pb,n.z6,s.forwardRef((e,t)=>{let{className:r,inset:s,children:i,...l}=e;return(0,o.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",r),...l,children:[i,(0,o.jsx)(a.A,{className:"ml-auto"})]})}).displayName=n.ZP.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",r),...s})}).displayName=n.G5.displayName;let u=s.forwardRef((e,t)=>{let{className:r,sideOffset:s=4,...a}=e;return(0,o.jsx)(n.ZL,{children:(0,o.jsx)(n.UC,{ref:t,sideOffset:s,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",r),...a})})});u.displayName=n.UC.displayName;let p=s.forwardRef((e,t)=>{let{className:r,inset:s,...a}=e;return(0,o.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",r),...a})});p.displayName=n.q7.displayName,s.forwardRef((e,t)=>{let{className:r,children:s,checked:a,...l}=e;return(0,o.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:a,...l,children:[(0,o.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,o.jsx)(n.VF,{children:(0,o.jsx)(i.A,{className:"h-4 w-4"})})}),s]})}).displayName=n.H_.displayName,s.forwardRef((e,t)=>{let{className:r,children:s,...a}=e;return(0,o.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...a,children:[(0,o.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,o.jsx)(n.VF,{children:(0,o.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=n.hN.displayName,s.forwardRef((e,t)=>{let{className:r,inset:s,...a}=e;return(0,o.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",r),...a})}).displayName=n.JU.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...s})});m.displayName=n.wv.displayName},88145:(e,t,r)=>{r.d(t,{E:()=>i});var o=r(95155);r(12115);var s=r(74466),n=r(53999);let a=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,o.jsx)("div",{className:(0,n.cn)(a({variant:r}),t),...s})}},88524:(e,t,r)=>{r.d(t,{A0:()=>i,BF:()=>l,Hj:()=>d,XI:()=>a,nA:()=>f,nd:()=>c});var o=r(95155),s=r(12115),n=r(53999);let a=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("div",{className:"relative w-full overflow-auto",children:(0,o.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",r),...s})})});a.displayName="Table";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",r),...s})});i.displayName="TableHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",r),...s})});l.displayName="TableBody",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})});d.displayName="TableRow";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})});c.displayName="TableHead";let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})});f.displayName="TableCell",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",r),...s})}).displayName="TableCaption"},97168:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>l});var o=r(95155),s=r(12115),n=r(99708),a=r(74466),i=r(53999);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:a,asChild:d=!1,...c}=e,f=d?n.DX:"button";return(0,o.jsx)(f,{className:(0,i.cn)(l({variant:s,size:a,className:r})),ref:t,...c})});d.displayName="Button"}}]);