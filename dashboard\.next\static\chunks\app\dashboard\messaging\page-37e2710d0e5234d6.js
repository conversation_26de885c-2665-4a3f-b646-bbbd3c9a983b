(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1658],{14503:(e,t,s)=>{"use strict";s.d(t,{dj:()=>f,oR:()=>u});var n=s(12115);let r=0,a=new Map,o=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function u(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||n()}}}),{id:s,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=n.useState(c);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},31886:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var n=s(23464);class r{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(t){if(t.response){let s=t.response.status;if(console.log("API Client: Request to ".concat(e," failed with status ").concat(s)),404===s){let t=Error("Resource not found: ".concat(e));throw t.status=404,t.isNotFound=!0,t}}throw t}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let s=await this.client.delete(e,t);if(204===s.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return s.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let n={...s,headers:{...null==s?void 0:s.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,n)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=n.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let a=new r},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var n=s(52596),r=s(39688);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,n.$)(t))}},69663:(e,t,s)=>{"use strict";s.d(t,{BK:()=>l,eu:()=>i,q5:()=>c});var n=s(95155),r=s(12115),a=s(85977),o=s(53999);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)(a.bL,{ref:t,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});i.displayName=a.bL.displayName;let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)(a._V,{ref:t,className:(0,o.cn)("aspect-square h-full w-full",s),...r})});l.displayName=a._V.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)(a.H4,{ref:t,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});c.displayName=a.H4.displayName},82602:(e,t,s)=>{Promise.resolve().then(s.bind(s,83625))},83625:(e,t,s)=>{"use strict";s.d(t,{ChatList:()=>g});var n=s(95155),r=s(12115),a=s(89852),o=s(47924),i=s(69663),l=s(53999),c=s(31886),d=s(97168),u=s(51154),f=s(12486),m=s(14503);function h(e){var t;let{clientId:s}=e,{client:o,messages:h,isLoading:g,error:p,refreshMessages:x,addMessage:v}=function(e){let[t,s]=(0,r.useState)(null),[n,a]=(0,r.useState)([]),[o,i]=(0,r.useState)(!0),[l,d]=(0,r.useState)(null),u=(0,r.useCallback)(async()=>{if(!e){i(!1);return}i(!0),d(null);try{console.log("Fetching messages for client ".concat(e));let t=await c.A.get("/clients/".concat(e,"/messages"));console.log("API Response:",t);let n=t.data||t;if(n&&n.id){if(s({id:n.id,name:n.name||"Unknown",phone:n.phone||"",lastActive:n.lastActive||new Date().toISOString(),lastMessage:n.lastMessage||"",createdAt:n.createdAt||new Date().toISOString(),updatedAt:n.updatedAt||new Date().toISOString(),type:n.type||"client"}),Array.isArray(n.messages)){let e=n.messages.sort((e,t)=>{let s=new Date(e.createdAt||0).getTime(),n=new Date(t.createdAt||0).getTime();return s-n}).map(e=>({id:e.id||"temp-".concat(Date.now(),"-").concat(Math.random()),content:e.text||e.content||"",sender:e.isBot?"agent":"user",timestamp:new Date(e.createdAt||Date.now()).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),clientId:e.clientId||n.id,name:n.name||"Unknown"}));console.log("Formatted ".concat(e.length," messages")),a(e)}else console.warn("No messages array in client data:",n),a([])}else console.error("Invalid client data:",n),d("Invalid client data received from server"),s(null),a([])}catch(e){console.error("Error fetching client messages:",e),d(e.message||"Failed to load client messages"),s(null),a([])}finally{i(!1)}},[e]);return(0,r.useEffect)(()=>{u()},[u]),{client:t,messages:n,isLoading:o,error:l,refreshMessages:()=>{u()},addMessage:e=>{a(t=>[...t,e])}}}(s),[y,w]=(0,r.useState)(""),[j,b]=(0,r.useState)(!1),N=(0,r.useRef)(null),{toast:A}=(0,m.dj)();(0,r.useEffect)(()=>{var e;null===(e=N.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[h]);let E=async()=>{if(y.trim()&&(null==o?void 0:o.phone)&&!j){v({id:"temp-".concat(Date.now()),content:y,sender:"user",timestamp:new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),clientId:s,name:(null==o?void 0:o.name)||"You"}),w(""),b(!0);try{console.log("Sending message to ".concat(o.phone,": ").concat(y));let e=await c.A.post("/sendWhatsAppMessage",{to:o.phone,text:y});console.log("Message sent successfully:",e),A({title:"Message sent",description:"Your message has been sent successfully.",duration:3e3}),setTimeout(()=>{x(),b(!1)},1e3)}catch(e){console.error("Error sending message:",e),A({title:"Failed to send message",description:e.message||"There was an error sending your message.",variant:"destructive",duration:5e3}),setTimeout(()=>{x(),b(!1)},1e3)}}};return g?(0,n.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,n.jsx)(u.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading messages..."})]})}):p?(0,n.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2 max-w-md text-center",children:[(0,n.jsx)("div",{className:"rounded-full bg-red-100 p-3 dark:bg-red-900",children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-red-500",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,n.jsx)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,n.jsx)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-red-500",children:"Error Loading Messages"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:p}),(0,n.jsx)(d.$,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>x(),children:"Try Again"})]})}):(0,n.jsxs)("div",{className:"flex h-full flex-col",children:[(0,n.jsx)("div",{className:"border-b p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)(i.eu,{className:"h-10 w-10",children:[(0,n.jsx)(i.BK,{src:"/placeholder.svg?height=40&width=40"}),(0,n.jsx)(i.q5,{children:(null==o?void 0:null===(t=o.name)||void 0===t?void 0:t.substring(0,2))||"CL"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium",children:(null==o?void 0:o.name)||"Client"}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:(null==o?void 0:o.phone)||"No phone number"})]})]})}),(0,n.jsxs)("div",{className:"flex-1 overflow-auto p-4 space-y-4",children:[0===h.length?(0,n.jsx)("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:"No messages yet"}):h.map(e=>(0,n.jsxs)("div",{className:(0,l.cn)("flex items-end gap-2 max-w-[80%]","agent"===e.sender?"mr-auto":"ml-auto flex-row-reverse"),children:["agent"===e.sender&&(0,n.jsxs)(i.eu,{className:"h-8 w-8",children:[(0,n.jsx)(i.BK,{src:"/placeholder.svg?height=32&width=32"}),(0,n.jsx)(i.q5,{children:"AG"})]}),(0,n.jsxs)("div",{className:(0,l.cn)("rounded-lg p-3","agent"===e.sender?"bg-muted text-foreground":"bg-primary text-primary-foreground"),children:[(0,n.jsx)("p",{children:e.content}),(0,n.jsx)("span",{className:"mt-1 block text-right text-xs opacity-70",children:e.timestamp})]})]},e.id)),(0,n.jsx)("div",{ref:N})]}),(0,n.jsx)("div",{className:"border-t p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(a.p,{placeholder:"Type a message...",className:"flex-1",value:y,onChange:e=>w(e.target.value),onKeyPress:e=>{"Enter"===e.key&&E()}}),(0,n.jsx)(d.$,{size:"icon",onClick:E,disabled:!y.trim()||j,children:j?(0,n.jsx)(u.A,{className:"h-5 w-5 animate-spin"}):(0,n.jsx)(f.A,{className:"h-5 w-5"})})]})})]})}function g(){let[e,t]=(0,r.useState)(null),[s,d]=(0,r.useState)(""),{clients:u,isLoading:f,error:m,currentPage:g,totalPages:p,hasNext:x,hasPrevious:v,nextPage:y,previousPage:w}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,[s,n]=(0,r.useState)([]),[a,o]=(0,r.useState)(!0),[i,l]=(0,r.useState)(null),[d,u]=(0,r.useState)(e),[f,m]=(0,r.useState)(0),h=(0,r.useCallback)(async e=>{o(!0),l(null);try{console.log("Fetching clients page ".concat(e," with limit ").concat(t));let r=await c.A.get("/clients?page=".concat(e,"&limit=").concat(t));if(console.log("Client list response:",r),r){if(r.data&&r.data.data){var s;console.log("Received ".concat(r.data.data.length," clients with pagination")),n(r.data.data),m((null===(s=r.data.pagination)||void 0===s?void 0:s.total)||r.data.data.length)}else if(r.data&&Array.isArray(r.data))console.log("Received ".concat(r.data.length," clients as array in data")),n(r.data),m(r.data.length);else if(Array.isArray(r))console.log("Received ".concat(r.length," clients as direct array")),n(r),m(r.length);else{let e=r.data||r;if(e&&"object"==typeof e){let t=Object.values(e).filter(e=>Array.isArray(e)&&e.length>0&&e[0]&&"object"==typeof e[0]&&"id"in e[0]);if(t.length>0){let e=t[0];console.log("Found client array with ".concat(e.length," clients")),n(e),m(e.length)}else console.error("Unexpected response format:",r),l("Unexpected response format from server"),n([]),m(0)}else console.error("Unexpected response format:",r),l("Unexpected response format from server"),n([]),m(0)}}else console.error("Empty response received"),l("Empty response received from server"),n([]),m(0)}catch(e){console.error("Error fetching clients:",e),l(e.message||"Failed to load client list"),n([]),m(0)}finally{o(!1)}},[t]);(0,r.useEffect)(()=>{h(d)},[h,d]);let g=Math.max(1,Math.ceil(f/t)),p=d<g,x=d>1;return{clients:s,isLoading:a,error:i,currentPage:d,totalPages:g,hasNext:p,hasPrevious:x,nextPage:()=>{p&&u(e=>e+1)},previousPage:()=>{x&&u(e=>e-1)},refreshClients:()=>h(d)}}(),j=u.filter(e=>e.name.toLowerCase().includes(s.toLowerCase())||e.lastMessage.toLowerCase().includes(s.toLowerCase()));return f?(0,n.jsx)("div",{className:"flex h-full items-center justify-center",children:"Loading conversations..."}):m?(0,n.jsxs)("div",{className:"flex h-full items-center justify-center text-red-500",children:["Error: ",m]}):(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsxs)("div",{className:"w-80 border-r flex flex-col h-full",children:[(0,n.jsx)("div",{className:"border-b p-4",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(o.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(a.p,{placeholder:"Search conversations...",className:"pl-8",value:s,onChange:e=>d(e.target.value)})]})}),(0,n.jsx)("div",{className:"flex-1 overflow-auto",children:j.length>0?j.map(s=>(0,n.jsxs)("div",{className:(0,l.cn)("flex items-center gap-3 border-b p-4 hover:bg-muted/50 cursor-pointer",s.id===e&&"bg-muted"),onClick:()=>t(s.id),children:[(0,n.jsxs)(i.eu,{className:"h-10 w-10",children:[(0,n.jsx)(i.BK,{src:"/placeholder.svg"}),(0,n.jsx)(i.q5,{children:s.name.substring(0,2)||"UN"})]}),(0,n.jsxs)("div",{className:"flex-1 overflow-hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h3",{className:"font-medium",children:s.name}),(0,n.jsx)("span",{className:"text-xs text-muted-foreground",children:new Date(s.lastActive).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,n.jsx)("p",{className:"truncate text-sm text-muted-foreground",children:s.lastMessage})]})]},s.id)):(0,n.jsx)("div",{className:"flex h-40 items-center justify-center text-muted-foreground",children:"No conversations found"})}),(x||v)&&(0,n.jsxs)("div",{className:"flex justify-between border-t p-2",children:[(0,n.jsx)("button",{className:"px-3 py-1 text-sm disabled:opacity-50",onClick:w,disabled:!v,children:"Previous"}),(0,n.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",g," of ",p]}),(0,n.jsx)("button",{className:"px-3 py-1 text-sm disabled:opacity-50",onClick:y,disabled:!x,children:"Next"})]})]}),(0,n.jsx)("div",{className:"flex-1",children:e?(0,n.jsx)(h,{clientId:e}):(0,n.jsx)("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:"Select a conversation to start chatting"})})]})}},89852:(e,t,s)=>{"use strict";s.d(t,{p:()=>o});var n=s(95155),r=s(12115),a=s(53999);let o=r.forwardRef((e,t)=>{let{className:s,type:r,...o}=e;return(0,n.jsx)("input",{type:r,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...o})});o.displayName="Input"},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>l});var n=s(95155),r=s(12115),a=s(99708),o=s(74466),i=s(53999);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:o,asChild:c=!1,...d}=e,u=c?a.DX:"button";return(0,n.jsx)(u,{className:(0,i.cn)(l({variant:r,size:o,className:s})),ref:t,...d})});c.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,3464,8323,8441,1684,7358],()=>t(82602)),_N_E=e.O()}]);