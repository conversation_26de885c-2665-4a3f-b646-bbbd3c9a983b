(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12315:(e,t,r)=>{Promise.resolve().then(r.bind(r,97035))},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55192:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>o,wL:()=>h});var s=r(60687),a=r(43210),i=r(96241);let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));l.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));n.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let h=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));h.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\page.tsx","default")},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},83778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64118)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96330:e=>{"use strict";e.exports=require("@prisma/client")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97035:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var s=r(60687),a=r(55192),i=r(43210),l=r(32192),o=r(25541),n=r(41312),d=r(97992),c=r(47033),h=r(14952),x=r(24934),p=r(10452);function m(){let[e,t]=(0,i.useState)("dark");return{theme:e,setTheme:t,toggleTheme:()=>{t(e=>"light"===e?"dark":"light")},setLightTheme:()=>t("light"),setDarkTheme:()=>t("dark"),isDark:"dark"===e,isLight:"light"===e}}function g(){let{language:e,isArabic:t}=(0,p.Y)(),{isDark:r}=m(),[a,g]=(0,i.useState)(0),u=[{id:1,title:{ar:"مرحباً بك في نظام إدارة العقارات",en:"Welcome to Property Management System"},subtitle:{ar:"إدارة شاملة وذكية لجميع عقاراتك",en:"Comprehensive and smart management for all your properties"},description:{ar:"نظام متطور لإدارة العقارات مع دعم ثنائي اللغة والوضع المظلم",en:"Advanced property management system with bilingual support and dark mode"},icon:l.A,gradient:"from-emerald-500 to-teal-600",bgGradient:"from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20"},{id:2,title:{ar:"تحليلات وإحصائيات متقدمة",en:"Advanced Analytics & Statistics"},subtitle:{ar:"رؤى عميقة لأداء عقاراتك",en:"Deep insights into your property performance"},description:{ar:"تتبع الأرباح والمبيعات والعملاء مع تقارير تفصيلية",en:"Track profits, sales, and customers with detailed reports"},icon:o.A,gradient:"from-blue-500 to-indigo-600",bgGradient:"from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20"},{id:3,title:{ar:"إدارة العملاء والمواعيد",en:"Client & Appointment Management"},subtitle:{ar:"تنظيم مثالي لعملائك ومواعيدك",en:"Perfect organization for your clients and appointments"},description:{ar:"نظام شامل لإدارة العملاء وجدولة المواعيد والمتابعة",en:"Comprehensive system for client management, scheduling, and follow-up"},icon:n.A,gradient:"from-purple-500 to-pink-600",bgGradient:"from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20"},{id:4,title:{ar:"مواقع مميزة وعقارات فاخرة",en:"Premium Locations & Luxury Properties"},subtitle:{ar:"اكتشف أفضل العقارات في أرقى المواقع",en:"Discover the best properties in premium locations"},description:{ar:"مجموعة مختارة من العقارات الفاخرة في أفضل المواقع",en:"Curated collection of luxury properties in the best locations"},icon:d.A,gradient:"from-orange-500 to-red-600",bgGradient:"from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20"}],f=e=>{g(e)},b=u[a],v=b.icon;return(0,s.jsxs)("div",{className:`relative overflow-hidden rounded-3xl shadow-2xl ${"ar"===e?"rtl":"ltr"}`,dir:"ar"===e?"rtl":"ltr",children:[(0,s.jsxs)("div",{className:`relative h-96 bg-gradient-to-br ${b.bgGradient} transition-all duration-1000 ease-in-out`,children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-white/20 via-transparent to-white/10"})}),(0,s.jsxs)("div",{className:`relative h-full flex items-center ${t?"flex-row-reverse":"flex-row"} p-8 lg:p-12`,children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:`w-24 h-24 lg:w-32 lg:h-32 bg-gradient-to-br ${b.gradient} rounded-3xl flex items-center justify-center shadow-2xl transform transition-all duration-500 hover:scale-110`,children:(0,s.jsx)(v,{className:"h-12 w-12 lg:h-16 lg:w-16 text-white"})})}),(0,s.jsxs)("div",{className:`flex-1 ${t?"mr-8 lg:mr-12 text-right":"ml-8 lg:ml-12 text-left"} space-y-4`,children:[(0,s.jsx)("h2",{className:"text-3xl lg:text-4xl font-black text-slate-800 dark:text-white leading-tight",children:b.title[e]}),(0,s.jsx)("h3",{className:"text-xl lg:text-2xl font-bold text-slate-600 dark:text-slate-300",children:b.subtitle[e]}),(0,s.jsx)("p",{className:"text-lg text-slate-600 dark:text-slate-400 leading-relaxed max-w-2xl",children:b.description[e]}),(0,s.jsxs)("div",{className:`flex items-center gap-4 pt-4 ${t?"flex-row-reverse":"flex-row"}`,children:[(0,s.jsx)(x.$,{className:`px-6 py-3 bg-gradient-to-r ${b.gradient} hover:shadow-lg transition-all duration-300 text-white font-bold rounded-xl`,children:"ar"===e?"اكتشف المزيد":"Discover More"}),(0,s.jsx)(x.$,{variant:"outline",className:"px-6 py-3 border-2 border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-300 rounded-xl",children:"ar"===e?"تعرف أكثر":"Learn More"})]})]})]}),(0,s.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>{g(e=>(e-1+u.length)%u.length)},className:`absolute top-1/2 transform -translate-y-1/2 ${t?"right-4":"left-4"} w-12 h-12 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-800 shadow-lg transition-all duration-300`,children:(0,s.jsx)(c.A,{className:`h-6 w-6 text-slate-700 dark:text-slate-300 ${t?"rotate-180":""}`})}),(0,s.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>{g(e=>(e+1)%u.length)},className:`absolute top-1/2 transform -translate-y-1/2 ${t?"left-4":"right-4"} w-12 h-12 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-800 shadow-lg transition-all duration-300`,children:(0,s.jsx)(h.A,{className:`h-6 w-6 text-slate-700 dark:text-slate-300 ${t?"rotate-180":""}`})})]}),(0,s.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center gap-3",children:u.map((e,t)=>(0,s.jsx)("button",{onClick:()=>f(t),className:`w-3 h-3 rounded-full transition-all duration-300 ${t===a?`bg-gradient-to-r ${b.gradient} shadow-lg scale-125`:"bg-white/60 dark:bg-slate-600/60 hover:bg-white/80 dark:hover:bg-slate-600/80"}`},t))}),(0,s.jsx)("div",{className:`absolute top-6 ${t?"left-6":"right-6"} px-4 py-2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-full shadow-lg`,children:(0,s.jsxs)("span",{className:"text-sm font-bold text-slate-700 dark:text-slate-300",children:[a+1," / ",u.length]})})]})}function u(){let{language:e,isArabic:t}=(0,p.Y)(),[r,d]=(0,i.useState)(0),c=[{title:{ar:"إدارة العقارات",en:"Property Management"},icon:l.A,gradient:"from-emerald-500 to-teal-600"},{title:{ar:"التحليلات",en:"Analytics"},icon:o.A,gradient:"from-blue-500 to-indigo-600"},{title:{ar:"العملاء",en:"Clients"},icon:n.A,gradient:"from-purple-500 to-pink-600"}],h=c[r],x=h.icon;return(0,s.jsx)(a.Zp,{className:"overflow-hidden shadow-lg",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:`flex items-center gap-4 ${t?"flex-row-reverse":"flex-row"}`,children:[(0,s.jsx)("div",{className:`w-16 h-16 bg-gradient-to-br ${h.gradient} rounded-2xl flex items-center justify-center shadow-lg`,children:(0,s.jsx)(x,{className:"h-8 w-8 text-white"})}),(0,s.jsxs)("div",{className:`flex-1 ${t?"text-right":"text-left"}`,children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-slate-800 dark:text-white",children:h.title[e]}),(0,s.jsx)("div",{className:"flex items-center gap-1 mt-2",children:c.map((e,t)=>(0,s.jsx)("div",{className:`w-2 h-2 rounded-full transition-all duration-300 ${t===r?`bg-gradient-to-r ${h.gradient}`:"bg-slate-300 dark:bg-slate-600"}`},t))})]})]})})})}r(39266);let f=(0,r(62688).A)("Languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);function b(){let{language:e,setLanguage:t,isArabic:r}=(0,p.Y)();return(0,s.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{t("ar"===e?"en":"ar")},className:`
        flex items-center gap-2 px-4 py-2 h-10
        bg-white/80 dark:bg-slate-800/80 backdrop-blur-md
        border-2 border-slate-200 dark:border-slate-700
        hover:bg-white dark:hover:bg-slate-800
        hover:border-emerald-300 dark:hover:border-emerald-600
        hover:shadow-lg transition-all duration-300
        text-slate-700 dark:text-slate-300
        rounded-xl font-medium
        ${r?"flex-row-reverse":"flex-row"}
      `,dir:r?"rtl":"ltr",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f,{className:"h-4 w-4 text-emerald-600 dark:text-emerald-400"}),(0,s.jsx)("span",{className:"font-bold",children:"ar"===e?"العربية":"English"})]}),(0,s.jsx)("div",{className:"w-px h-4 bg-slate-300 dark:bg-slate-600"}),(0,s.jsx)("span",{className:"text-xs text-slate-500 dark:text-slate-400",children:"ar"===e?"EN":"عر"})]})}var v=r(363);function k(){let{language:e,isArabic:t}=(0,p.Y)(),r={ar:{dark:"الوضع المظلم",active:"مُفعل"},en:{dark:"Dark Mode",active:"Active"}}[e];return(0,s.jsx)("div",{className:`
        flex items-center gap-2 px-4 py-2 h-10
        bg-slate-800/90 backdrop-blur-md
        border-2 border-slate-700
        shadow-lg
        text-slate-300
        rounded-xl font-medium
        ${t?"flex-row-reverse":"flex-row"}
      `,dir:t?"rtl":"ltr",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 text-amber-400"}),(0,s.jsx)("span",{className:"font-bold",children:r.dark}),(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-xs text-green-400",children:r.active})]})})}var w=r(96474),j=r(13861),y=r(53411),N=r(64398),A=r(40228),C=r(85814),$=r.n(C);function P(){let{language:e,isArabic:t}=(0,p.Y)(),{isDark:r}=m(),i={ar:{dashboard:"لوحة التحكم",welcome:"مرحباً بك",subtitle:"إدارة شاملة لجميع عقاراتك",quickActions:"الإجراءات السريعة",statistics:"الإحصائيات",recentActivity:"النشاط الأخير",properties:"العقارات",clients:"العملاء",appointments:"المواعيد",analytics:"التحليلات",addProperty:"إضافة عقار",viewProperties:"عرض العقارات",manageClients:"إدارة العملاء",scheduleAppointment:"جدولة موعد",viewAnalytics:"عرض التحليلات",totalProperties:"إجمالي العقارات",activeListings:"الإعلانات النشطة",totalClients:"إجمالي العملاء",thisMonth:"هذا الشهر",viewAll:"عرض الكل",manage:"إدارة"},en:{dashboard:"Dashboard",welcome:"Welcome",subtitle:"Comprehensive management for all your properties",quickActions:"Quick Actions",statistics:"Statistics",recentActivity:"Recent Activity",properties:"Properties",clients:"Clients",appointments:"Appointments",analytics:"Analytics",addProperty:"Add Property",viewProperties:"View Properties",manageClients:"Manage Clients",scheduleAppointment:"Schedule Appointment",viewAnalytics:"View Analytics",totalProperties:"Total Properties",activeListings:"Active Listings",totalClients:"Total Clients",thisMonth:"This Month",viewAll:"View All",manage:"Manage"}}[e],o=[{title:i.addProperty,icon:w.A,href:"/dashboard/properties/create",gradient:"from-emerald-500 to-teal-600",description:"ar"===e?"إضافة عقار جديد":"Add a new property"},{title:i.viewProperties,icon:j.A,href:"/dashboard/properties",gradient:"from-blue-500 to-indigo-600",description:"ar"===e?"عرض جميع العقارات":"View all properties"},{title:i.manageClients,icon:n.A,href:"/dashboard/clients",gradient:"from-purple-500 to-pink-600",description:"ar"===e?"إدارة العملاء":"Manage clients"},{title:i.viewAnalytics,icon:y.A,href:"/dashboard/analytics",gradient:"from-orange-500 to-red-600",description:"ar"===e?"عرض التحليلات":"View analytics"}],d=[{title:i.totalProperties,value:"156",icon:l.A,gradient:"from-emerald-500 to-teal-600",change:"+12%"},{title:i.activeListings,value:"89",icon:N.A,gradient:"from-blue-500 to-indigo-600",change:"+8%"},{title:i.totalClients,value:"342",icon:n.A,gradient:"from-purple-500 to-pink-600",change:"+15%"},{title:i.thisMonth,value:"24",icon:A.A,gradient:"from-orange-500 to-red-600",change:"+5%"}];return(0,s.jsx)("div",{className:`min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 ${t?"rtl":"ltr"}`,dir:t?"rtl":"ltr",children:(0,s.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,s.jsxs)("div",{className:`flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 gap-6 ${t?"lg:flex-row-reverse":""}`,children:[(0,s.jsxs)("div",{className:`space-y-2 ${t?"text-right":"text-left"}`,children:[(0,s.jsx)("h1",{className:"text-4xl font-black bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent",children:i.dashboard}),(0,s.jsx)("p",{className:"text-lg text-slate-600 dark:text-slate-400",children:i.subtitle})]}),(0,s.jsxs)("div",{className:`flex items-center gap-4 ${t?"flex-row-reverse":""}`,children:[(0,s.jsx)(b,{}),(0,s.jsx)(k,{})]})]}),(0,s.jsx)("div",{className:"mb-12",children:(0,s.jsx)(g,{})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:d.map((e,r)=>{let i=e.icon;return(0,s.jsx)(a.Zp,{className:"overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[(0,s.jsxs)("div",{className:`${t?"text-right":"text-left"}`,children:[(0,s.jsx)("p",{className:"text-sm font-medium text-slate-600 dark:text-slate-400 mb-1",children:e.title}),(0,s.jsx)("p",{className:"text-3xl font-black text-slate-900 dark:text-white",children:e.value}),(0,s.jsx)("p",{className:"text-sm font-medium text-emerald-600 dark:text-emerald-400 mt-1",children:e.change})]}),(0,s.jsx)("div",{className:`w-16 h-16 bg-gradient-to-br ${e.gradient} rounded-2xl flex items-center justify-center shadow-lg`,children:(0,s.jsx)(i,{className:"h-8 w-8 text-white"})})]})})},r)})}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:`text-2xl font-bold text-slate-900 dark:text-white mb-6 ${t?"text-right":"text-left"}`,children:i.quickActions}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map((e,r)=>{let i=e.icon;return(0,s.jsx)($(),{href:e.href,children:(0,s.jsx)(a.Zp,{className:"group cursor-pointer overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:scale-105",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:`flex flex-col items-center text-center space-y-4 ${t?"text-right":"text-left"}`,children:[(0,s.jsx)("div",{className:`w-20 h-20 bg-gradient-to-br ${e.gradient} rounded-3xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`,children:(0,s.jsx)(i,{className:"h-10 w-10 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-slate-900 dark:text-white mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:e.description})]})]})})})},r)})})]}),(0,s.jsxs)("div",{className:"lg:hidden mb-8",children:[(0,s.jsx)("h2",{className:`text-2xl font-bold text-slate-900 dark:text-white mb-6 ${t?"text-right":"text-left"}`,children:i.recentActivity}),(0,s.jsx)(u,{})]})]})})}},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98243:(e,t,r)=>{Promise.resolve().then(r.bind(r,64118))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,8157,2190,3903,5153,1467,4088,9464,381],()=>r(83778));module.exports=s})();