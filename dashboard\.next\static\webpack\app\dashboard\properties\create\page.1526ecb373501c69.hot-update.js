"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyCreateForm.tsx":
/*!******************************************************!*\
  !*** ./components/properties/PropertyCreateForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyCreateForm: () => (/* binding */ PropertyCreateForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/uploadthing */ \"(app-pages-browser)/./lib/uploadthing.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyCreateForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyCreateForm(param) {\n    let { onSuccess, onCancel, initialData, isEdit = false, propertyId } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // UploadThing hook for property images\n    const { startUpload, isUploading } = (0,_lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__.useUploadThing)(\"propertyImageUploader\", {\n        onClientUploadComplete: {\n            \"PropertyCreateForm.useUploadThing\": (res)=>{\n                if (res) {\n                    const newImageUrls = res.map({\n                        \"PropertyCreateForm.useUploadThing.newImageUrls\": (file)=>file.url\n                    }[\"PropertyCreateForm.useUploadThing.newImageUrls\"]);\n                    setFormData({\n                        \"PropertyCreateForm.useUploadThing\": (prev)=>({\n                                ...prev,\n                                images: [\n                                    ...prev.images,\n                                    ...newImageUrls\n                                ]\n                            })\n                    }[\"PropertyCreateForm.useUploadThing\"]);\n                    toast({\n                        title: t('images.success'),\n                        description: t('images.success')\n                    });\n                }\n            }\n        }[\"PropertyCreateForm.useUploadThing\"],\n        onUploadError: {\n            \"PropertyCreateForm.useUploadThing\": (error)=>{\n                toast({\n                    title: t('images.error'),\n                    description: t('images.error'),\n                    variant: 'destructive'\n                });\n            }\n        }[\"PropertyCreateForm.useUploadThing\"]\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: (initialData === null || initialData === void 0 ? void 0 : initialData.title) || '',\n        description: (initialData === null || initialData === void 0 ? void 0 : initialData.description) || '',\n        price: (initialData === null || initialData === void 0 ? void 0 : initialData.price) || '',\n        currency: (initialData === null || initialData === void 0 ? void 0 : initialData.currency) || 'SAR',\n        type: (initialData === null || initialData === void 0 ? void 0 : initialData.type) || '',\n        status: (initialData === null || initialData === void 0 ? void 0 : initialData.status) || 'AVAILABLE',\n        bedrooms: (initialData === null || initialData === void 0 ? void 0 : initialData.bedrooms) || '',\n        bathrooms: (initialData === null || initialData === void 0 ? void 0 : initialData.bathrooms) || '',\n        area: (initialData === null || initialData === void 0 ? void 0 : initialData.area) || '',\n        location: (initialData === null || initialData === void 0 ? void 0 : initialData.location) || '',\n        address: (initialData === null || initialData === void 0 ? void 0 : initialData.address) || '',\n        city: (initialData === null || initialData === void 0 ? void 0 : initialData.city) || '',\n        country: (initialData === null || initialData === void 0 ? void 0 : initialData.country) || 'SAUDI',\n        images: (initialData === null || initialData === void 0 ? void 0 : initialData.images) || [],\n        features: (initialData === null || initialData === void 0 ? void 0 : initialData.features) || [],\n        amenities: (initialData === null || initialData === void 0 ? void 0 : initialData.amenities) || [],\n        yearBuilt: (initialData === null || initialData === void 0 ? void 0 : initialData.yearBuilt) || '',\n        parking: (initialData === null || initialData === void 0 ? void 0 : initialData.parking) || '',\n        furnished: (initialData === null || initialData === void 0 ? void 0 : initialData.furnished) || false,\n        petFriendly: (initialData === null || initialData === void 0 ? void 0 : initialData.petFriendly) || false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleInputChange]\": (field, value)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                        ...prev,\n                        [field]: value\n                    })\n            }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            // Clear error when user starts typing\n            if (errors[field]) {\n                setErrors({\n                    \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                            ...prev,\n                            [field]: ''\n                        })\n                }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleInputChange]\"], [\n        errors\n    ]);\n    // Handle image file selection\n    const handleImageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleImageChange]\": (files)=>{\n            if (!files) return;\n            const validFiles = [];\n            Array.from(files).forEach({\n                \"PropertyCreateForm.useCallback[handleImageChange]\": (file)=>{\n                    // Validate file type\n                    if (!file.type.startsWith('image/')) {\n                        toast({\n                            title: t('images.error'),\n                            description: t('images.fileType'),\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    // Validate file size (8MB max for UploadThing)\n                    if (file.size > 8 * 1024 * 1024) {\n                        toast({\n                            title: t('images.error'),\n                            description: t('images.fileSize'),\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    validFiles.push(file);\n                }\n            }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            if (validFiles.length > 0) {\n                setSelectedFiles({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>[\n                            ...prev,\n                            ...validFiles\n                        ]\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n                // Start upload immediately\n                startUpload(validFiles);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleImageChange]\"], [\n        toast,\n        t,\n        startUpload\n    ]);\n    // Remove image\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[removeImage]\": (index)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>({\n                        ...prev,\n                        images: prev.images.filter({\n                            \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                        }[\"PropertyCreateForm.useCallback[removeImage]\"])\n                    })\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n        }\n    }[\"PropertyCreateForm.useCallback[removeImage]\"], []);\n    // Handle drag and drop\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"PropertyCreateForm.useCallback[handleDragOver]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleImageChange(e.dataTransfer.files);\n        }\n    }[\"PropertyCreateForm.useCallback[handleDrop]\"], [\n        handleImageChange\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.title.trim()) newErrors.title = t('validation.required');\n        if (!formData.description.trim()) newErrors.description = t('validation.required');\n        if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive');\n        if (formData.price && formData.price > 100000000000) newErrors.price = 'السعر كبير جداً';\n        if (!formData.type) newErrors.type = t('validation.required');\n        if (!formData.location.trim()) newErrors.location = t('validation.required');\n        if (!formData.address.trim()) newErrors.address = t('validation.required');\n        if (!formData.city.trim()) newErrors.city = t('validation.required');\n        if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive');\n        if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive');\n        if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive');\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: t('properties.error'),\n                description: t('validation.required'),\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: Number(formData.price),\n                currency: formData.currency,\n                type: formData.type,\n                status: formData.status,\n                bedrooms: Number(formData.bedrooms),\n                bathrooms: Number(formData.bathrooms),\n                area: Number(formData.area),\n                location: formData.location,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                images: formData.images,\n                features: formData.features,\n                amenities: formData.amenities,\n                yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,\n                parking: formData.parking ? Number(formData.parking) : undefined,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly\n            };\n            if (isEdit && propertyId) {\n                await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.updateProperty(propertyId, propertyData);\n            } else {\n                await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.createProperty(propertyData);\n            }\n            toast({\n                title: t('properties.success'),\n                description: isEdit ? 'تم تحديث العقار بنجاح' : t('properties.success')\n            });\n            onSuccess();\n        } catch (error) {\n            console.error('Error creating property:', error);\n            toast({\n                title: t('properties.error'),\n                description: t('properties.error'),\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const propertyTypes = [\n        {\n            value: 'APARTMENT',\n            label: t('property.type.apartment')\n        },\n        {\n            value: 'VILLA',\n            label: t('property.type.villa')\n        },\n        {\n            value: 'TOWNHOUSE',\n            label: t('property.type.townhouse')\n        },\n        {\n            value: 'PENTHOUSE',\n            label: t('property.type.penthouse')\n        },\n        {\n            value: 'STUDIO',\n            label: t('property.type.studio')\n        },\n        {\n            value: 'OFFICE',\n            label: t('property.type.office')\n        },\n        {\n            value: 'SHOP',\n            label: t('property.type.shop')\n        },\n        {\n            value: 'WAREHOUSE',\n            label: t('property.type.warehouse')\n        },\n        {\n            value: 'LAND',\n            label: t('property.type.land')\n        },\n        {\n            value: 'BUILDING',\n            label: t('property.type.building')\n        }\n    ];\n    const propertyStatuses = [\n        {\n            value: 'AVAILABLE',\n            label: t('property.status.available')\n        },\n        {\n            value: 'SOLD',\n            label: t('property.status.sold')\n        },\n        {\n            value: 'RENTED',\n            label: t('property.status.rented')\n        },\n        {\n            value: 'PENDING',\n            label: t('property.status.pending')\n        }\n    ];\n    const countries = [\n        {\n            value: 'SAUDI',\n            label: t('country.saudi')\n        },\n        {\n            value: 'UAE',\n            label: t('country.uae')\n        },\n        {\n            value: 'QATAR',\n            label: t('country.qatar')\n        },\n        {\n            value: 'KUWAIT',\n            label: t('country.kuwait')\n        },\n        {\n            value: 'BAHRAIN',\n            label: t('country.bahrain')\n        },\n        {\n            value: 'OMAN',\n            label: t('country.oman')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"المعلومات الأساسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.title'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.title ? 'error' : ''),\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange('title', e.target.value),\n                                        placeholder: t('property.title.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.price'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.price ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.price,\n                                        onChange: (e)=>{\n                                            const value = e.target.value ? Number(e.target.value) : '';\n                                            // Limit price to reasonable maximum (100 billion)\n                                            if (typeof value === 'number' && value > 100000000000) {\n                                                return; // Don't update if price is too high\n                                            }\n                                            handleInputChange('price', value);\n                                        },\n                                        placeholder: t('property.price.placeholder'),\n                                        min: \"0\",\n                                        max: \"100000000000\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.type'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.type,\n                                        onValueChange: (value)=>handleInputChange('type', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select \".concat(errors.type ? 'error' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: t('property.type.select')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: type.value,\n                                                        children: type.label\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.status')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.status,\n                                        onValueChange: (value)=>handleInputChange('status', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: t('property.status.select')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyStatuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: status.value,\n                                                        children: status.label\n                                                    }, status.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"تفاصيل العقار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bedrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bedrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bedrooms,\n                                        onChange: (e)=>handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bedrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bathrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bathrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bathrooms,\n                                        onChange: (e)=>handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bathrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.area'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.area ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.area,\n                                        onChange: (e)=>handleInputChange('area', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.area\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.yearBuilt')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.yearBuilt,\n                                        onChange: (e)=>handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٢٠٢٤\",\n                                        min: \"1900\",\n                                        max: \"2030\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.parking')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.parking,\n                                        onChange: (e)=>handleInputChange('parking', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"معلومات الموقع\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.location'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.location ? 'error' : ''),\n                                        value: formData.location,\n                                        onChange: (e)=>handleInputChange('location', e.target.value),\n                                        placeholder: t('property.location.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.location\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.address'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.address ? 'error' : ''),\n                                        value: formData.address,\n                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                        placeholder: t('property.address.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.city'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.city ? 'error' : ''),\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                        placeholder: t('property.city.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.city\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.country')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.country,\n                                        onValueChange: (value)=>handleInputChange('country', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: country.value,\n                                                        children: country.label\n                                                    }, country.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"الوصف\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                className: \"form-label\",\n                                children: [\n                                    t('property.description'),\n                                    \" *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                className: \"form-textarea \".concat(errors.description ? 'error' : ''),\n                                value: formData.description,\n                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                placeholder: t('property.description.placeholder'),\n                                dir: \"rtl\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this),\n                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-error\",\n                                children: errors.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 34\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 495,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: t('property.images')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"image-upload-area \".concat(isUploading ? 'loading' : ''),\n                                onDragOver: handleDragOver,\n                                onDrop: handleDrop,\n                                onClick: ()=>{\n                                    const input = document.createElement('input');\n                                    input.type = 'file';\n                                    input.multiple = true;\n                                    input.accept = 'image/*';\n                                    input.onchange = (e)=>{\n                                        const target = e.target;\n                                        handleImageChange(target.files);\n                                    };\n                                    input.click();\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-medium text-gray-300 mb-2\",\n                                            children: t('images.drag')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: t('images.formats')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"loading-spinner\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: t('images.uploading')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                children: formData.images.map((url, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-800 rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: url,\n                                                    alt: \"\".concat(t('images.preview'), \" \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>removeImage(index),\n                                                className: \"absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                title: t('images.remove'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"خيارات إضافية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"furnished\",\n                                        checked: formData.furnished,\n                                        onChange: (e)=>handleInputChange('furnished', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"furnished\",\n                                        className: \"form-label\",\n                                        children: t('property.furnished')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"petFriendly\",\n                                        checked: formData.petFriendly,\n                                        onChange: (e)=>handleInputChange('petFriendly', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"petFriendly\",\n                                        className: \"form-label\",\n                                        children: t('property.petFriendly')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 579,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 flex-row-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading || isUploading,\n                        className: \"btn-primary\",\n                        children: [\n                            (isLoading || isUploading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 42\n                            }, this),\n                            isLoading || isUploading ? t('properties.loading') : t('properties.save')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        onClick: onCancel,\n                        disabled: isLoading || isUploading,\n                        className: \"btn-secondary\",\n                        children: t('properties.cancel')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 612,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyCreateForm, \"iX5sOBcOi6AYfiHUyEcXAg0msx8=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__.useUploadThing\n    ];\n});\n_c = PropertyCreateForm;\nvar _c;\n$RefreshReg$(_c, \"PropertyCreateForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\n"));

/***/ })

});