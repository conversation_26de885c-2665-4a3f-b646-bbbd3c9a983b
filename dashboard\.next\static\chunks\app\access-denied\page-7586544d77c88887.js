(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9735],{6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>s,t:()=>a});var n=t(12115);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function s(...e){return n.useCallback(a(...e),e)}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var n=t(12115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:p="",children:d,iconNode:c,...u}=e;return(0,n.createElement)("svg",{ref:r,...s,width:i,height:i,stroke:t,strokeWidth:l?24*Number(o)/Number(i):o,className:a("lucide",p),...u},[...c.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),l=(e,r)=>{let t=(0,n.forwardRef)((t,s)=>{let{className:l,...p}=t;return(0,n.createElement)(o,{ref:s,iconNode:r,className:a("lucide-".concat(i(e)),l),...p})});return t.displayName="".concat(e),t}},21719:(e,r,t)=>{Promise.resolve().then(t.bind(t,82092))},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(52596),i=t(39688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.QP)((0,n.$)(r))}},54762:(e,r,t)=>{"use strict";t.d(r,{ES:()=>i,eo:()=>n});let n=["ar"],i={ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"الذكاء الاصطناعي","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","properties.title":"العقارات","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.add":"إضافة عقار","properties.create":"إنشاء عقار جديد","properties.edit":"تعديل العقار","properties.delete":"حذف العقار","properties.view":"عرض العقار","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.currency":"العملة","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.features":"المميزات","property.amenities":"الخدمات","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","validation.required":"هذا الحقل مطلوب","validation.email":"يرجى إدخال بريد إلكتروني صحيح","validation.minLength":"يجب أن يكون الحد الأدنى {{min}} أحرف","validation.maxLength":"يجب أن يكون الحد الأقصى {{max}} أحرف","validation.number":"يرجى إدخال رقم صحيح","validation.positive":"يجب أن يكون الرقم أكبر من الصفر","images.upload":"رفع الصور","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت"}}}},57340:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},72280:(e,r,t)=>{"use strict";t.d(r,{B:()=>s});var n=t(17985),i=t(91218),a=t(54762);function s(){return(0,i.Bd)()}n.Ay.use(i.r9).init({lng:"en",fallbackLng:"en",resources:a.ES,interpolation:{escapeValue:!1}})},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});var n=t(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,s=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:o}=r,l=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==o?void 0:o[e];if(null===r)return null;let a=i(r)||i(n);return s[e][a]}),p=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,l,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...p}[r]):({...o,...p})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},82092:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var n=t(95155),i=t(12115),a=t(72280),s=t(97168),o=t(35695),l=t(95778),p=t(35169),d=t(57340),c=t(45493);function u(){var e;let{t:r}=(0,a.B)(),t=(0,o.useRouter)(),{data:u,status:m}=(0,c.wV)(),[y,f]=(0,i.useState)("loading"===m);return((0,i.useEffect)(()=>{"loading"!==m&&f(!1)},[m]),y)?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"}),(0,n.jsx)("h1",{className:"text-2xl font-bold mb-2",children:r("common.loading")})]}):(0,n.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background p-4",children:(0,n.jsxs)("div",{className:"w-full max-w-md p-6 bg-card rounded-lg shadow-xl text-center",children:[(0,n.jsx)("div",{className:"flex justify-center mb-6",children:(0,n.jsx)(l.A,{className:"h-16 w-16 text-destructive"})}),(0,n.jsx)("h1",{className:"text-2xl font-bold mb-4",children:r("auth.accessDenied")}),(0,n.jsx)("p",{className:"text-muted-foreground mb-6",children:"authenticated"===m?r("auth.insufficientPermissions",{role:(null==u?void 0:null===(e=u.user)||void 0===e?void 0:e.role)||"user"}):r("auth.notAuthenticated")}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:r("auth.contactAdminForAccess")}),(0,n.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,n.jsxs)(s.$,{onClick:()=>{t.back()},variant:"outline",className:"w-full",children:[(0,n.jsx)(p.A,{className:"mr-2 h-4 w-4"}),r("common.goBack")]}),(0,n.jsxs)(s.$,{onClick:()=>{t.push("/")},variant:"default",className:"w-full",children:[(0,n.jsx)(d.A,{className:"mr-2 h-4 w-4"}),r("common.goToHomePage")]})]})]})})}},95778:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]])},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>p,r:()=>l});var n=t(95155),i=t(12115),a=t(99708),s=t(74466),o=t(53999);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=i.forwardRef((e,r)=>{let{className:t,variant:i,size:s,asChild:p=!1,...d}=e,c=p?a.DX:"button";return(0,n.jsx)(c,{className:(0,o.cn)(l({variant:i,size:s,className:t})),ref:r,...d})});p.displayName="Button"},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>s,xV:()=>l});var n=t(12115),i=t(6101),a=t(95155),s=n.forwardRef((e,r)=>{let{children:t,...i}=e,s=n.Children.toArray(t),l=s.find(p);if(l){let e=l.props.children,t=s.map(r=>r!==l?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(o,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,t):null})}return(0,a.jsx)(o,{...i,ref:r,children:t})});s.displayName="Slot";var o=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t);return n.cloneElement(t,{...function(e,r){let t={...r};for(let n in r){let i=e[n],a=r[n];/^on[A-Z]/.test(n)?i&&a?t[n]=(...e)=>{a(...e),i(...e)}:i&&(t[n]=i):"style"===n?t[n]={...i,...a}:"className"===n&&(t[n]=[i,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props),ref:r?(0,i.t)(r,e):e})}return n.Children.count(t)>1?n.Children.only(null):null});o.displayName="SlotClone";var l=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function p(e){return n.isValidElement(e)&&e.type===l}}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,5493,3692,8441,1684,7358],()=>r(21719)),_N_E=e.O()}]);