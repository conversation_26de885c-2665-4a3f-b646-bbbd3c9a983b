(()=>{var e={};e.id=24,e.ids=[24],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26269:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>E,TabsContent:()=>S,TabsList:()=>D,TabsTrigger:()=>$});var a=s(60687),r=s(43210),i=s(70569),n=s(11273),d=s(72942),o=s(46059),c=s(14163),l=s(43),u=s(65551),p=s(96963),m="Tabs",[h,x]=(0,n.A)(m,[d.RG]),g=(0,d.RG)(),[j,v]=h(m),f=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:i,defaultValue:n,orientation:d="horizontal",dir:o,activationMode:m="automatic",...h}=e,x=(0,l.jH)(o),[g,v]=(0,u.i)({prop:r,onChange:i,defaultProp:n});return(0,a.jsx)(j,{scope:s,baseId:(0,p.B)(),value:g,onValueChange:v,orientation:d,dir:x,activationMode:m,children:(0,a.jsx)(c.sG.div,{dir:x,"data-orientation":d,...h,ref:t})})});f.displayName=m;var y="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...i}=e,n=v(y,s),o=g(s);return(0,a.jsx)(d.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:r,children:(0,a.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});b.displayName=y;var w="TabsTrigger",C=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...o}=e,l=v(w,s),u=g(s),p=k(l.baseId,r),m=T(l.baseId,r),h=r===l.value;return(0,a.jsx)(d.q7,{asChild:!0,...u,focusable:!n,active:h,children:(0,a.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:p,...o,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;h||n||!e||l.onValueChange(r)})})})});C.displayName=w;var N="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:i,forceMount:n,children:d,...l}=e,u=v(N,s),p=k(u.baseId,i),m=T(u.baseId,i),h=i===u.value,x=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(o.C,{present:n||h,children:({present:s})=>(0,a.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":p,hidden:!s,id:m,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&d})})});function k(e,t){return`${e}-trigger-${t}`}function T(e,t){return`${e}-content-${t}`}A.displayName=N;var q=s(96241);let E=f,D=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(b,{ref:s,className:(0,q.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));D.displayName=b.displayName;let $=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(C,{ref:s,className:(0,q.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));$.displayName=C.displayName;let S=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(A,{ref:s,className:(0,q.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));S.displayName=A.displayName},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47044:(e,t,s)=>{"use strict";s.d(t,{CampaignEditor:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call CampaignEditor() from the server but CampaignEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\edit\\[id]\\campaign-editor.tsx","CampaignEditor")},48161:e=>{"use strict";e.exports=require("node:os")},51089:(e,t,s)=>{Promise.resolve().then(s.bind(s,71898))},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},60675:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(37413),r=s(47044);function i({params:e}){return(0,a.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-1",children:"Edit Campaign"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"Update your campaign details and settings"}),(0,a.jsx)(r.CampaignEditor,{campaignId:e.id})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71898:(e,t,s)=>{"use strict";s.d(t,{CampaignEditor:()=>$});var a=s(60687),r=s(43210),i=s(16189),n=s(70333),d=s(59556),o=s(94792),c=s(24934),l=s(68988),u=s(39390),p=s(15616),m=s(63974),h=s(55192),x=s(26269),g=s(59821),j=s(41862),v=s(43649),f=s(28559),y=s(62688);let b=(0,y.A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),w=(0,y.A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var C=s(96362),N=s(8819),A=s(27900),k=s(89849);function T({campaignId:e,campaignName:t,onSuccess:s,variant:r="default",size:i="default",...n}){return(0,a.jsx)(k.T,{campaignId:e,campaignName:t,onSuccess:s,children:(0,a.jsxs)(c.$,{variant:r,size:i,...n,children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Send Campaign"]})})}var q=s(95033),E=s(85814),D=s.n(E);function $({campaignId:e}){let t=(0,i.useRouter)(),{campaign:s,loading:y,error:A,updateCampaign:k,updateCampaignStatus:E,deleteCampaign:$}=function(e){let[t,s]=(0,r.useState)(null),[a,c]=(0,r.useState)(!0),[l,u]=(0,r.useState)(null),p=(0,i.useRouter)(),{toast:m}=(0,n.dj)();return{campaign:t,loading:a,error:l,fetchCampaign:(0,r.useCallback)(async()=>{if(e)try{c(!0),u(null),console.log(`Fetching campaign with ID: ${e}`);let t=await (0,o.jz)(e);t?(console.log("Campaign data received:",t),s(t)):(u("Campaign not found"),m({title:"Error",description:"Campaign not found",variant:"destructive"}))}catch(t){console.error(`Error fetching campaign with ID ${e}:`,t),u(t.message||"Failed to load campaign details"),m({title:"Error",description:t.message||"Failed to load campaign details",variant:"destructive"})}finally{c(!1)}},[e,m]),updateCampaign:async t=>{if(!e)return!1;try{c(!0),console.log(`Updating campaign with ID: ${e}`,t);let a=await (0,o.SX)(e,t);if(a)return console.log("Campaign updated successfully:",a),s(a),m({title:"Success",description:"Campaign updated successfully"}),!0;throw Error("Failed to update campaign")}catch(t){return console.error(`Error updating campaign with ID ${e}:`,t),m({title:"Error",description:t.message||"Failed to update campaign",variant:"destructive"}),!1}finally{c(!1)}},updateCampaignStatus:async t=>{if(!e)return!1;try{c(!0),console.log(`Updating campaign status with ID: ${e} to ${t}`);let a=await d.A.put(`/marketing/campaigns/${e}/status`,{status:t});if(a)return console.log("Campaign status updated successfully:",a),s(e=>e?{...e,status:t}:null),m({title:"Success",description:`Campaign status updated to ${t}`}),!0;throw Error("Failed to update campaign status")}catch(t){return console.error(`Error updating campaign status with ID ${e}:`,t),m({title:"Error",description:t.message||"Failed to update campaign status",variant:"destructive"}),!1}finally{c(!1)}},deleteCampaign:async()=>{if(!e)return!1;try{return c(!0),console.log(`Deleting campaign with ID: ${e}`),await d.A.delete(`/marketing/campaigns/${e}`),m({title:"Success",description:"Campaign deleted successfully"}),p.push("/dashboard/campaigns"),!0}catch(t){return console.error(`Error deleting campaign with ID ${e}:`,t),m({title:"Error",description:t.message||"Failed to delete campaign",variant:"destructive"}),!1}finally{c(!1)}}}}(e),[S,I]=(0,r.useState)({name:"",type:"",content:"",clientTypes:[],scheduledAt:""}),[R,P]=(0,r.useState)(!1),[F,_]=(0,r.useState)(!1),L=e=>{let{name:t,value:s}=e.target;I(e=>({...e,[t]:s}))},M=(e,t)=>{I(s=>({...s,[e]:t}))},Z=async e=>{e.preventDefault(),P(!0);try{let e={...S,scheduledAt:S.scheduledAt?new Date(S.scheduledAt).toISOString():void 0};await k(e)}catch(e){console.error("Error updating campaign:",e)}finally{P(!1)}},z=async e=>{_(!0),await E(e),_(!1)},U=async()=>{await $()};return y?(0,a.jsxs)("div",{className:"flex justify-center items-center py-12",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading campaign details..."})]}):A?(0,a.jsxs)(h.Zp,{className:"border-destructive",children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center text-destructive",children:[(0,a.jsx)(v.A,{className:"mr-2 h-5 w-5"}),"Error Loading Campaign"]})}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)("p",{children:A}),(0,a.jsx)("p",{className:"mt-2",children:"The campaign might have been deleted or you don't have permission to view it."})]}),(0,a.jsx)(h.wL,{children:(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>t.push("/dashboard/campaigns"),children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Back to Campaigns"]})})]}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(D(),{href:"/dashboard/campaigns",children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,a.jsx)(g.E,{variant:"active"===s.status.toLowerCase()?"default":"completed"===s.status.toLowerCase()?"secondary":"scheduled"===s.status.toLowerCase()?"outline":"destructive",children:s.status})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(T,{campaignId:e,campaignName:s.name,variant:"outline",onSuccess:()=>{setTimeout(()=>{t.refresh()},1e3)}}),"active"===s.status.toLowerCase()?(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>z("paused"),disabled:F,children:[F?(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(b,{className:"mr-2 h-4 w-4"}),"Pause Campaign"]}):"paused"===s.status.toLowerCase()?(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>z("active"),disabled:F,children:[F?(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(w,{className:"mr-2 h-4 w-4"}),"Resume Campaign"]}):null,(0,a.jsxs)(q.Lt,{children:[(0,a.jsx)(q.tv,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"destructive",children:[(0,a.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,a.jsxs)(q.EO,{children:[(0,a.jsxs)(q.wd,{children:[(0,a.jsx)(q.r7,{children:"Are you sure?"}),(0,a.jsx)(q.$v,{children:"This action cannot be undone. This will permanently delete the campaign and all associated data."})]}),(0,a.jsxs)(q.ck,{children:[(0,a.jsx)(q.Zr,{children:"Cancel"}),(0,a.jsx)(q.Rx,{onClick:U,children:"Delete"})]})]})]})]})]}),(0,a.jsxs)(x.Tabs,{defaultValue:"details",children:[(0,a.jsxs)(x.TabsList,{children:[(0,a.jsx)(x.TabsTrigger,{value:"details",children:"Campaign Details"}),(0,a.jsx)(x.TabsTrigger,{value:"content",children:"Message Content"}),(0,a.jsx)(x.TabsTrigger,{value:"audience",children:"Audience"}),(0,a.jsx)(x.TabsTrigger,{value:"schedule",children:"Schedule"})]}),(0,a.jsxs)("form",{onSubmit:Z,children:[(0,a.jsx)(x.TabsContent,{value:"details",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Campaign Details"})}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"name",children:"Campaign Name"}),(0,a.jsx)(l.p,{id:"name",name:"name",value:S.name,onChange:L,placeholder:"Enter campaign name"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"type",children:"Campaign Type"}),(0,a.jsxs)(m.l6,{value:S.type,onValueChange:e=>M("type",e),children:[(0,a.jsx)(m.bq,{id:"type",children:(0,a.jsx)(m.yv,{placeholder:"Select campaign type"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"whatsapp",children:"WhatsApp Template"}),(0,a.jsx)(m.eb,{value:"connect",children:"Connect With Us"}),(0,a.jsx)(m.eb,{value:"promotional",children:"Promotional"}),(0,a.jsx)(m.eb,{value:"informational",children:"Informational"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"Created At"}),(0,a.jsx)("div",{className:"p-2 border rounded-md bg-muted/50",children:new Date(s.createdAt).toLocaleString()})]})]})]})}),(0,a.jsx)(x.TabsContent,{value:"content",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Message Content"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"content",children:"Message Template"}),(0,a.jsx)(p.T,{id:"content",name:"content",value:S.content,onChange:L,placeholder:"Enter your campaign message",rows:8}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Use ",{variable}," syntax for personalization variables."]})]})})]})}),(0,a.jsx)(x.TabsContent,{value:"audience",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Audience Selection"})}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"This campaign is currently targeting the following client types:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:S.clientTypes.length>0?S.clientTypes.map((e,t)=>(0,a.jsx)(g.E,{variant:"secondary",children:e},t)):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No client types selected"})}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-4",children:"Note: Changing audience selection for an active campaign may affect its delivery."})]})]})}),(0,a.jsx)(x.TabsContent,{value:"schedule",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Schedule"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"scheduledAt",children:"Scheduled Date and Time"}),(0,a.jsx)(l.p,{id:"scheduledAt",name:"scheduledAt",type:"datetime-local",value:S.scheduledAt,onChange:L}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leave empty for immediate sending when activated."})]})})]})}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(c.$,{type:"submit",disabled:R,children:R?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})})]})]})]}):(0,a.jsxs)(h.Zp,{className:"border-destructive",children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center text-destructive",children:[(0,a.jsx)(v.A,{className:"mr-2 h-5 w-5"}),"Campaign Not Found"]})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("p",{children:["The campaign with ID ",e," could not be found."]})}),(0,a.jsx)(h.wL,{children:(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>t.push("/dashboard/campaigns"),children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Back to Campaigns"]})})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83840:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let c={children:["",{children:["dashboard",{children:["campaigns",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60675)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\edit\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/campaigns/edit/[id]/page",pathname:"/dashboard/campaigns/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83997:e=>{"use strict";e.exports=require("tty")},85937:(e,t,s)=>{Promise.resolve().then(s.bind(s,47044))},94735:e=>{"use strict";e.exports=require("events")},94792:(e,t,s)=>{"use strict";s.d(t,{ME:()=>r,SX:()=>n,jz:()=>i});var a=s(59556);let r=async()=>{try{return await a.A.get("/marketing/campaigns")}catch(e){throw console.error("Error fetching campaigns:",e),e}},i=async e=>{try{return await a.A.get(`/marketing/campaigns/${e}`)}catch(t){throw console.error(`Error fetching campaign with ID ${e}:`,t),t}},n=async(e,t)=>{try{return await a.A.put(`/marketing/campaigns/${e}`,t)}catch(t){throw console.error(`Error updating campaign with ID ${e}:`,t),t}}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96362:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[7719,8157,2190,3903,5153,1467,1060,4097,7895,4088,9464,381,1941,8017],()=>s(83840));module.exports=a})();