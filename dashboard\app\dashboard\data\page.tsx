import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ClientsDataTable } from "@/components/data/clients-data-table"
import { MessagesDataTable } from "@/components/data/messages-data-table"
import { CampaignsDataTable } from "@/components/data/campaigns-data-table"
import { QAPairsDataTable } from "@/components/data/qa-pairs-data-table"

export default function DataDashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">لوحة قاعدة البيانات</h1>
        <p className="text-muted-foreground mt-2">
          عرض وإدارة جميع البيانات من قاعدة البيانات الخلفية
        </p>
      </div>

      <Tabs defaultValue="clients" className="space-y-4">
        <TabsList>
          <TabsTrigger value="clients">العملاء</TabsTrigger>
          <TabsTrigger value="messages">الرسائل</TabsTrigger>
          <TabsTrigger value="campaigns">الحملات</TabsTrigger>
          <TabsTrigger value="qa-pairs">أزواج الأسئلة والأجوبة</TabsTrigger>
        </TabsList>

        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>العملاء</CardTitle>
            </CardHeader>
            <CardContent>
              <ClientsDataTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>الرسائل</CardTitle>
            </CardHeader>
            <CardContent>
              <MessagesDataTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>الحملات</CardTitle>
            </CardHeader>
            <CardContent>
              <CampaignsDataTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="qa-pairs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>أزواج الأسئلة والأجوبة</CardTitle>
            </CardHeader>
            <CardContent>
              <QAPairsDataTable />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
