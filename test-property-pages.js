#!/usr/bin/env node

/**
 * Test script to verify property pages are working correctly
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, 'dashboard', filePath);
  const exists = fs.existsSync(fullPath);
  log(`${exists ? '✅' : '❌'} ${filePath}`, exists ? 'green' : 'red');
  return exists;
}

function checkFileContent(filePath, searchText) {
  const fullPath = path.join(__dirname, 'dashboard', filePath);
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    const hasContent = content.includes(searchText);
    log(`${hasContent ? '✅' : '❌'} ${filePath} contains "${searchText}"`, hasContent ? 'green' : 'red');
    return hasContent;
  } catch (error) {
    log(`❌ Error reading ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

function runTests() {
  log('\n🧪 Testing Property Pages Structure', 'bright');
  log('=====================================', 'cyan');

  // Check main files exist
  log('\n📁 Checking main property files:', 'blue');
  const mainFiles = [
    'app/dashboard/properties/page.tsx',
    'app/dashboard/properties/[id]/page.tsx',
    'app/dashboard/properties/[id]/edit/page.tsx',
    'app/dashboard/properties/create/page.tsx',
    'app/not-found.tsx'
  ];

  let allMainFilesExist = true;
  mainFiles.forEach(file => {
    if (!checkFileExists(file)) {
      allMainFilesExist = false;
    }
  });

  // Check component files
  log('\n🧩 Checking component files:', 'blue');
  const componentFiles = [
    'components/properties/PropertyShowComponent.tsx',
    'components/properties/PropertyCreateForm.tsx',
    'hooks/use-toast.ts',
    'services/propertyService.ts'
  ];

  let allComponentsExist = true;
  componentFiles.forEach(file => {
    if (!checkFileExists(file)) {
      allComponentsExist = false;
    }
  });

  // Check CSS files
  log('\n🎨 Checking CSS files:', 'blue');
  const cssFiles = [
    'styles/arabic-properties.css',
    'styles/arabic.css'
  ];

  let allCssFilesExist = true;
  cssFiles.forEach(file => {
    if (!checkFileExists(file)) {
      allCssFilesExist = false;
    }
  });

  // Check for specific content
  log('\n🔍 Checking file content:', 'blue');
  const contentChecks = [
    ['app/dashboard/properties/[id]/page.tsx', 'PropertyShowComponent'],
    ['app/dashboard/properties/[id]/page.tsx', 'getMockProperty'],
    ['app/not-found.tsx', 'Page Not Found'],
    ['styles/arabic-properties.css', 'property-card-dark'],
    ['components/properties/PropertyShowComponent.tsx', 'property-card-dark']
  ];

  let allContentExists = true;
  contentChecks.forEach(([file, content]) => {
    if (!checkFileContent(file, content)) {
      allContentExists = false;
    }
  });

  // Summary
  log('\n📊 Test Summary:', 'bright');
  log('================', 'cyan');
  log(`Main Files: ${allMainFilesExist ? '✅ PASS' : '❌ FAIL'}`, allMainFilesExist ? 'green' : 'red');
  log(`Components: ${allComponentsExist ? '✅ PASS' : '❌ FAIL'}`, allComponentsExist ? 'green' : 'red');
  log(`CSS Files: ${allCssFilesExist ? '✅ PASS' : '❌ FAIL'}`, allCssFilesExist ? 'green' : 'red');
  log(`Content: ${allContentExists ? '✅ PASS' : '❌ FAIL'}`, allContentExists ? 'green' : 'red');

  const overallPass = allMainFilesExist && allComponentsExist && allCssFilesExist && allContentExists;
  log(`\nOverall: ${overallPass ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`, overallPass ? 'green' : 'red');

  if (overallPass) {
    log('\n🎉 Property pages should be working correctly!', 'green');
    log('You can now test the following URLs:', 'blue');
    log('• http://localhost:3000/dashboard/properties', 'cyan');
    log('• http://localhost:3000/dashboard/properties/test-property-1', 'cyan');
    log('• http://localhost:3000/dashboard/properties/create', 'cyan');
  } else {
    log('\n⚠️  Some issues were found. Please check the failed items above.', 'yellow');
  }

  return overallPass;
}

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
