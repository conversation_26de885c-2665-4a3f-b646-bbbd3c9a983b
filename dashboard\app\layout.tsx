import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Cairo } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { dir } from "i18next"
import { languages } from "@/lib/i18n/settings"
import { AuthProvider } from "@/components/auth/auth-provider"
import { HydrationFix } from "@/components/hydration-fix"
import { DarkModeScript, DarkModeProvider } from "@/components/DarkModeProvider"

const cairo = Cairo({
  subsets: ["latin", "arabic"],
  variable: "--font-cairo",
})

export const metadata: Metadata = {
  title: "لوحة تحكم الذكاء الاصطناعي العقاري",
  description: "لوحة تحكم إدارية لمساعد الذكاء الاصطناعي للعقارات عبر واتساب",
}

export async function generateStaticParams() {
  return languages.map((lng) => ({ lng }))
}

export default function RootLayout({
  children,
  params: { lng = "ar" },
}: {
  children: React.ReactNode
  params: { lng: string }
}) {
  return (
    <html lang={lng} dir={dir(lng)} suppressHydrationWarning className="dark">
      <head>
        <DarkModeScript />
      </head>
      <body className={cairo.className} suppressHydrationWarning>
        <HydrationFix />
        <DarkModeProvider>
          <AuthProvider>
            <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
              {children}
              <Toaster />
            </ThemeProvider>
          </AuthProvider>
        </DarkModeProvider>
      </body>
    </html>
  )
}
