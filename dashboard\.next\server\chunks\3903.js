exports.id=3903,exports.ids=[3903],exports.modules={16189:(e,t,s)=>{"use strict";var i=s(65773);s.o(i,"useParams")&&s.d(t,{useParams:function(){return i.useParams}}),s.o(i,"usePathname")&&s.d(t,{usePathname:function(){return i.usePathname}}),s.o(i,"useRouter")&&s.d(t,{useRouter:function(){return i.useRouter}}),s.o(i,"useSearchParams")&&s.d(t,{useSearchParams:function(){return i.useSearchParams}})},16457:(e,t,s)=>{"use strict";let i;s.d(t,{r9:()=>F,Bd:()=>U});var r=s(43210),a=s(72954),n=s.n(a),o=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;Object.create(null);let l=(e,t,s,i)=>{let r=[s,{code:t,...i||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(r,"warn","react-i18next::",!0);f(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...r):console?.warn&&console.warn(...r)},h={},u=(e,t,s,i)=>{f(s)&&h[s]||(f(s)&&(h[s]=new Date),l(e,t,s,i))},g=(e,t)=>()=>{if(e.isInitialized)t();else{let s=()=>{setTimeout(()=>{e.off("initialized",s)},0),t()};e.on("initialized",s)}},p=(e,t,s)=>{e.loadNamespaces(t,g(e,s))},d=(e,t,s,i)=>{if(f(s)&&(s=[s]),e.options.preload&&e.options.preload.indexOf(t)>-1)return p(e,s,i);s.forEach(t=>{0>e.options.ns.indexOf(t)&&e.options.ns.push(t)}),e.loadLanguages(t,g(e,i))},c=(e,t,s={})=>t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:s.lng,precheck:(t,i)=>{if(s.bindI18n?.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!i(t.isLanguageChangingTo,e))return!1}}):(u(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0),f=e=>"string"==typeof e,m=e=>"object"==typeof e&&null!==e,y=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,v={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},x=e=>v[e],b={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(y,x)},k=(e={})=>{b={...b,...e}},S=()=>b,O=(e,t)=>{if(!e)return!1;let s=e.props?.children??e.children;return t?s.length>0:!!s},L=e=>{if(!e)return[];let t=e.props?.children??e.children;return e.props?.i18nIsDynamicList?$(t):t},w=e=>Array.isArray(e)&&e.every(isValidElement),$=e=>Array.isArray(e)?e:[e],R=(e,t)=>{let s={...t};return s.props=Object.assign(e.props,t.props),s},N=(e,t,s)=>{let i=e.key||t,r=cloneElement(e,{key:i});return!r.props||!r.props.children||0>s.indexOf(`${t}/>`)&&0>s.indexOf(`${t} />`)?r:createElement(function(){return createElement(Fragment,null,r)},{key:i})},C=(e,t)=>e.map((e,s)=>N(e,s,t)),P=(e,t)=>{let s={};return Object.keys(e).forEach(i=>{Object.assign(s,{[i]:N(e[i],i,t)})}),s},j=e=>{i=e},E=()=>i,F={type:"3rdParty",init(e){k(e.options.react),j(e)}},A=(0,r.createContext)();class I{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let T=(e,t)=>{let s=(0,r.useRef)();return(0,r.useEffect)(()=>{s.current=t?s.current:e},[e,t]),s.current},V=(e,t,s,i)=>e.getFixedT(t,s,i),D=(e,t,s,i)=>(0,r.useCallback)(V(e,t,s,i),[e,t,s,i]),U=(e,t={})=>{let{i18n:s}=t,{i18n:i,defaultNS:a}=(0,r.useContext)(A)||{},n=s||i||E();if(n&&!n.reportNamespaces&&(n.reportNamespaces=new I),!n){u(n,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");let e=(e,t)=>f(t)?t:m(t)&&f(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}n.options.react?.wait&&u(n,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let o={...S(),...n.options.react,...t},{useSuspense:l,keyPrefix:h}=o,g=e||a||n.options?.defaultNS;g=f(g)?[g]:g||["translation"],n.reportNamespaces.addUsedNamespaces?.(g);let y=(n.isInitialized||n.initializedStoreOnce)&&g.every(e=>c(e,n,o)),v=D(n,t.lng||null,"fallback"===o.nsMode?g:g[0],h),x=()=>v,b=()=>V(n,t.lng||null,"fallback"===o.nsMode?g:g[0],h),[k,O]=(0,r.useState)(x),L=g.join();t.lng&&(L=`${t.lng}${L}`);let w=T(L),$=(0,r.useRef)(!0);(0,r.useEffect)(()=>{let{bindI18n:e,bindI18nStore:s}=o;$.current=!0,y||l||(t.lng?d(n,t.lng,g,()=>{$.current&&O(b)}):p(n,g,()=>{$.current&&O(b)})),y&&w&&w!==L&&$.current&&O(b);let i=()=>{$.current&&O(b)};return e&&n?.on(e,i),s&&n?.store.on(s,i),()=>{$.current=!1,n&&e?.split(" ").forEach(e=>n.off(e,i)),s&&n&&s.split(" ").forEach(e=>n.store.off(e,i))}},[n,L]),(0,r.useEffect)(()=>{$.current&&y&&O(x)},[n,h,y]);let R=[k,n,y];if(R.t=k,R.i18n=n,R.ready=y,y||!y&&!l)return R;throw new Promise(e=>{t.lng?d(n,t.lng,g,()=>e()):p(n,g,()=>e())})}},46755:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>Q});let i=e=>"string"==typeof e,r=()=>{let e,t;let s=new Promise((s,i)=>{e=s,t=i});return s.resolve=e,s.reject=t,s},a=e=>null==e?"":""+e,n=(e,t,s)=>{e.forEach(e=>{t[e]&&(s[e]=t[e])})},o=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(o,"."):e,h=e=>!e||i(e),u=(e,t,s)=>{let r=i(t)?t.split("."):t,a=0;for(;a<r.length-1;){if(h(e))return{};let t=l(r[a]);!e[t]&&s&&(e[t]=new s),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++a}return h(e)?{}:{obj:e,k:l(r[a])}},g=(e,t,s)=>{let{obj:i,k:r}=u(e,t,Object);if(void 0!==i||1===t.length){i[r]=s;return}let a=t[t.length-1],n=t.slice(0,t.length-1),o=u(e,n,Object);for(;void 0===o.obj&&n.length;)a=`${n[n.length-1]}.${a}`,o=u(e,n=n.slice(0,n.length-1),Object),o?.obj&&void 0!==o.obj[`${o.k}.${a}`]&&(o.obj=void 0);o.obj[`${o.k}.${a}`]=s},p=(e,t,s,i)=>{let{obj:r,k:a}=u(e,t,Object);r[a]=r[a]||[],r[a].push(s)},d=(e,t)=>{let{obj:s,k:i}=u(e,t);if(s&&Object.prototype.hasOwnProperty.call(s,i))return s[i]},c=(e,t,s)=>{let i=d(e,s);return void 0!==i?i:d(t,s)},f=(e,t,s)=>{for(let r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?i(e[r])||e[r]instanceof String||i(t[r])||t[r]instanceof String?s&&(e[r]=t[r]):f(e[r],t[r],s):e[r]=t[r]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let v=e=>i(e)?e.replace(/[&<>"'\/]/g,e=>y[e]):e;class x{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}let b=[" ",",","?","!",";"],k=new x(20),S=(e,t,s)=>{t=t||"",s=s||"";let i=b.filter(e=>0>t.indexOf(e)&&0>s.indexOf(e));if(0===i.length)return!0;let r=k.getRegExp(`(${i.map(e=>"?"===e?"\\?":e).join("|")})`),a=!r.test(e);if(!a){let t=e.indexOf(s);t>0&&!r.test(e.substring(0,t))&&(a=!0)}return a},O=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}let i=t.split(s),r=e;for(let e=0;e<i.length;){let t;if(!r||"object"!=typeof r)return;let a="";for(let n=e;n<i.length;++n)if(n!==e&&(a+=s),a+=i[n],void 0!==(t=r[a])){if(["string","number","boolean"].indexOf(typeof t)>-1&&n<i.length-1)continue;e+=n-e+1;break}r=t}return r},L=e=>e?.replace("_","-"),w={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}};class ${constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||w,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,r){return r&&!this.debug?null:(i(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new $(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new $(this.logger,e)}}var R=new $;class N{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let s=this.observers[e].get(t)||0;this.observers[e].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,s=Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(e=>{let[t,i]=e;for(let e=0;e<i;e++)t(...s)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(t=>{let[i,r]=t;for(let t=0;t<r;t++)i.apply(i,[e,...s])})}}class C extends N{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s){let r,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator,o=void 0!==a.ignoreJSONStructure?a.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?r=e.split("."):(r=[e,t],s&&(Array.isArray(s)?r.push(...s):i(s)&&n?r.push(...s.split(n)):r.push(s)));let l=d(this.data,r);return(!l&&!t&&!s&&e.indexOf(".")>-1&&(e=r[0],t=r[1],s=r.slice(2).join(".")),!l&&o&&i(s))?O(this.data?.[e]?.[t],s,n):l}addResource(e,t,s,i){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},a=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,n=[e,t];s&&(n=n.concat(a?s.split(a):s)),e.indexOf(".")>-1&&(n=e.split("."),i=t,t=n[1]),this.addNamespaces(t),g(this.data,n,i),r.silent||this.emit("added",e,t,s,i)}addResources(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let r in s)(i(s[r])||Array.isArray(s[r]))&&this.addResource(e,t,r,s[r],{silent:!0});r.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,i,r){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},n=[e,t];e.indexOf(".")>-1&&(n=e.split("."),i=s,s=t,t=n[1]),this.addNamespaces(t);let o=d(this.data,n)||{};a.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?f(o,s,r):o={...o,...s},g(this.data,n,o),a.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var P={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,s,i,r){return e.forEach(e=>{t=this.processors[e]?.process(t,s,i,r)??t}),t}};let j={},E=e=>!i(e)&&"boolean"!=typeof e&&"number"!=typeof e;class F extends N{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),n(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=R.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}},s={...t};if(null==e)return!1;let i=this.resolve(e,s);return i?.res!==void 0}extractFromKey(e,t){let s=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===s&&(s=":");let r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,a=t.ns||this.options.defaultNS||[],n=s&&e.indexOf(s)>-1,o=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!S(e,s,r);if(n&&!o){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:i(a)?[a]:a};let n=e.split(s);(s!==r||s===r&&this.options.ns.indexOf(n[0])>-1)&&(a=n.shift()),e=n.join(r)}return{key:e,namespaces:i(a)?[a]:a}}translate(e,t,s){let r="object"==typeof t?{...t}:t;if("object"!=typeof r&&this.options.overloadTranslationOptionHandler&&(r=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(r={...r}),r||(r={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let a=void 0!==r.returnDetails?r.returnDetails:this.options.returnDetails,n=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,{key:o,namespaces:l}=this.extractFromKey(e[e.length-1],r),h=l[l.length-1],u=r.lng||this.language,g=r.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u?.toLowerCase()==="cimode"){if(g){let e=r.nsSeparator||this.options.nsSeparator;return a?{res:`${h}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:h,usedParams:this.getUsedParamsDetails(r)}:`${h}${e}${o}`}return a?{res:o,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:h,usedParams:this.getUsedParamsDetails(r)}:o}let p=this.resolve(e,r),d=p?.res,c=p?.usedKey||o,f=p?.exactUsedKey||o,m=void 0!==r.joinArrays?r.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,v=void 0!==r.count&&!i(r.count),x=F.hasDefaultValue(r),b=v?this.pluralResolver.getSuffix(u,r.count,r):"",k=r.ordinal&&v?this.pluralResolver.getSuffix(u,r.count,{ordinal:!1}):"",S=v&&!r.ordinal&&0===r.count,O=S&&r[`defaultValue${this.options.pluralSeparator}zero`]||r[`defaultValue${b}`]||r[`defaultValue${k}`]||r.defaultValue,L=d;y&&!d&&x&&(L=O);let w=E(L),$=Object.prototype.toString.apply(L);if(y&&L&&w&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf($)&&!(i(m)&&Array.isArray(L))){if(!r.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(c,L,{...r,ns:l}):`key '${o} (${this.language})' returned an object instead of string.`;return a?(p.res=e,p.usedParams=this.getUsedParamsDetails(r),p):e}if(n){let e=Array.isArray(L),t=e?[]:{},s=e?f:c;for(let e in L)if(Object.prototype.hasOwnProperty.call(L,e)){let i=`${s}${n}${e}`;x&&!d?t[e]=this.translate(i,{...r,defaultValue:E(O)?O[e]:void 0,joinArrays:!1,ns:l}):t[e]=this.translate(i,{...r,joinArrays:!1,ns:l}),t[e]===i&&(t[e]=L[e])}d=t}}else if(y&&i(m)&&Array.isArray(d))(d=d.join(m))&&(d=this.extendTranslation(d,e,r,s));else{let t=!1,i=!1;!this.isValidLookup(d)&&x&&(t=!0,d=O),this.isValidLookup(d)||(i=!0,d=o);let a=(r.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&i?void 0:d,l=x&&O!==d&&this.options.updateMissing;if(i||t||l){if(this.logger.log(l?"updateKey":"missingKey",u,h,o,l?O:d),n){let e=this.resolve(o,{...r,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,r.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let s=0;s<t.length;s++)e.push(t[s]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(r.lng||this.language):e.push(r.lng||this.language);let s=(e,t,s)=>{let i=x&&s!==d?s:a;this.options.missingKeyHandler?this.options.missingKeyHandler(e,h,t,i,l,r):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,h,t,i,l,r),this.emit("missingKey",e,h,t,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&v?e.forEach(e=>{let t=this.pluralResolver.getSuffixes(e,r);S&&r[`defaultValue${this.options.pluralSeparator}zero`]&&0>t.indexOf(`${this.options.pluralSeparator}zero`)&&t.push(`${this.options.pluralSeparator}zero`),t.forEach(t=>{s([e],o+t,r[`defaultValue${t}`]||O)})}):s(e,o,O))}d=this.extendTranslation(d,e,r,p,s),i&&d===o&&this.options.appendNamespaceToMissingKey&&(d=`${h}:${o}`),(i||t)&&this.options.parseMissingKeyHandler&&(d=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${h}:${o}`:o,t?d:void 0,r))}return a?(p.res=d,p.usedParams=this.getUsedParamsDetails(r),p):d}extendTranslation(e,t,s,r,a){var n=this;if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!s.skipInterpolation){let o;s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});let l=i(e)&&(s?.interpolation?.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(l){let t=e.match(this.interpolator.nestingRegexp);o=t&&t.length}let h=s.replace&&!i(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,s.lng||this.language||r.usedLng,s),l){let t=e.match(this.interpolator.nestingRegexp);o<(t&&t.length)&&(s.nest=!1)}!s.lng&&r&&r.res&&(s.lng=this.language||r.usedLng),!1!==s.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];return a?.[0]!==i[0]||s.context?n.translate(...i,t):(n.logger.warn(`It seems you are nesting recursively key: ${i[0]} in key: ${t[0]}`),null)},s)),s.interpolation&&this.interpolator.reset()}let o=s.postProcess||this.options.postProcess,l=i(o)?[o]:o;return null!=e&&l?.length&&!1!==s.applyPostProcessor&&(e=P.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e){let t,s,r,a,n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let l=this.extractFromKey(e,o),h=l.key;s=h;let u=l.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));let g=void 0!==o.count&&!i(o.count),p=g&&!o.ordinal&&0===o.count,d=void 0!==o.context&&(i(o.context)||"number"==typeof o.context)&&""!==o.context,c=o.lngs?o.lngs:this.languageUtils.toResolveHierarchy(o.lng||this.language,o.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(n=e,!j[`${c[0]}-${e}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(n)&&(j[`${c[0]}-${e}`]=!0,this.logger.warn(`key "${s}" for languages "${c.join(", ")}" won't get resolved as namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),c.forEach(s=>{let i;if(this.isValidLookup(t))return;a=s;let n=[h];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(n,h,s,e,o);else{let e;g&&(e=this.pluralResolver.getSuffix(s,o.count,o));let t=`${this.options.pluralSeparator}zero`,i=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(g&&(n.push(h+e),o.ordinal&&0===e.indexOf(i)&&n.push(h+e.replace(i,this.options.pluralSeparator)),p&&n.push(h+t)),d){let s=`${h}${this.options.contextSeparator}${o.context}`;n.push(s),g&&(n.push(s+e),o.ordinal&&0===e.indexOf(i)&&n.push(s+e.replace(i,this.options.pluralSeparator)),p&&n.push(s+t))}}for(;i=n.pop();)this.isValidLookup(t)||(r=i,t=this.getResource(s,e,i,o))}))})}),{res:t,usedKey:s,exactUsedKey:r,usedLng:a,usedNS:n}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!i(e.replace),s=t?e.replace:e;if(t&&void 0!==e.count&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!t)for(let e of(s={...s},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete s[e];return s}static hasDefaultValue(e){let t="defaultValue";for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,t.length)&&void 0!==e[s])return!0;return!1}}class A{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=R.create("languageUtils")}getScriptPartFromCode(e){if(!(e=L(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=L(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(i(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch(e){}return(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)?t:this.options.lowerCaseLng?e.toLowerCase():e}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let s=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let s=this.getScriptPartFromCode(e);if(this.isSupportedCode(s))return t=s;let i=this.getLanguagePartFromCode(e);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(e=>{if(e===i||!(0>e.indexOf("-")&&0>i.indexOf("-"))&&(e.indexOf("-")>0&&0>i.indexOf("-")&&e.substring(0,e.indexOf("-"))===i||0===e.indexOf(i)&&i.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),i(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){let s=this.getFallbackCodes(t||this.options.fallbackLng||[],e),r=[],a=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return i(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&a(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&a(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&a(this.getLanguagePartFromCode(e))):i(e)&&a(this.formatLanguageCode(e)),s.forEach(e=>{0>r.indexOf(e)&&a(this.formatLanguageCode(e))}),r}}let I={zero:0,one:1,two:2,few:3,many:4,other:5},T={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class V{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=R.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=L("dev"===e?"en":e),r=s.ordinal?"ordinal":"cardinal",a=JSON.stringify({cleanedCode:i,type:r});if(a in this.pluralRulesCache)return this.pluralRulesCache[a];try{t=new Intl.PluralRules(i,{type:r})}catch(r){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),T;if(!e.match(/-|_/))return T;let i=this.languageUtils.getLanguagePartFromCode(e);t=this.getRule(i,s)}return this.pluralRulesCache[a]=t,t}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,s).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.getRule(e,t);return(s||(s=this.getRule("dev",t)),s)?s.resolvedOptions().pluralCategories.sort((e,t)=>I[e]-I[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):[]}getSuffix(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this.getRule(e,s);return i?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}let D=function(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",a=!(arguments.length>4)||void 0===arguments[4]||arguments[4],n=c(e,t,s);return!n&&a&&i(s)&&void 0===(n=O(e,s,r))&&(n=O(t,s,r)),n},U=e=>e.replace(/\$/g,"$$$$");class K{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=R.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:r,prefixEscaped:a,suffix:n,suffixEscaped:o,formatSeparator:l,unescapeSuffix:h,unescapePrefix:u,nestingPrefix:g,nestingPrefixEscaped:p,nestingSuffix:d,nestingSuffixEscaped:c,nestingOptionsSeparator:f,maxReplaces:y,alwaysFormat:x}=e.interpolation;this.escape=void 0!==t?t:v,this.escapeValue=void 0===s||s,this.useRawValueToEscape=void 0!==i&&i,this.prefix=r?m(r):a||"{{",this.suffix=n?m(n):o||"}}",this.formatSeparator=l||",",this.unescapePrefix=h?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=g?m(g):p||m("$t("),this.nestingSuffix=d?m(d):c||m(")"),this.nestingOptionsSeparator=f||",",this.maxReplaces=y||1e3,this.alwaysFormat=void 0!==x&&x,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e?.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,r){let n,o,l;let h=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=e=>{if(0>e.indexOf(this.formatSeparator)){let i=D(t,h,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(i,void 0,s,{...r,...t,interpolationkey:e}):i}let i=e.split(this.formatSeparator),a=i.shift().trim(),n=i.join(this.formatSeparator).trim();return this.format(D(t,h,a,this.options.keySeparator,this.options.ignoreJSONStructure),n,s,{...r,...t,interpolationkey:a})};this.resetRegExp();let g=r?.missingInterpolationHandler||this.options.missingInterpolationHandler,p=r?.interpolation?.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>U(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?U(this.escape(e)):U(e)}].forEach(t=>{for(l=0;n=t.regex.exec(e);){let s=n[1].trim();if(void 0===(o=u(s))){if("function"==typeof g){let t=g(e,n,r);o=i(t)?t:""}else if(r&&Object.prototype.hasOwnProperty.call(r,s))o="";else if(p){o=n[0];continue}else this.logger.warn(`missed to pass in variable ${s} for interpolating ${e}`),o=""}else i(o)||this.useRawValueToEscape||(o=a(o));let h=t.safeValue(o);if(e=e.replace(n[0],h),p?(t.regex.lastIndex+=o.length,t.regex.lastIndex-=n[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t){let s,r,n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=(e,t)=>{let s=this.nestingOptionsSeparator;if(0>e.indexOf(s))return e;let i=e.split(RegExp(`${s}[ ]*{`)),r=`{${i[1]}`;e=i[0];let a=(r=this.interpolate(r,n)).match(/'/g),o=r.match(/"/g);((a?.length??0)%2!=0||o)&&o.length%2==0||(r=r.replace(/'/g,'"'));try{n=JSON.parse(r),t&&(n={...t,...n})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${s}${r}`}return n.defaultValue&&n.defaultValue.indexOf(this.prefix)>-1&&delete n.defaultValue,e};for(;s=this.nestingRegexp.exec(e);){let h=[];(n=(n={...o}).replace&&!i(n.replace)?n.replace:n).applyPostProcessor=!1,delete n.defaultValue;let u=!1;if(-1!==s[0].indexOf(this.formatSeparator)&&!/{.*}/.test(s[1])){let e=s[1].split(this.formatSeparator).map(e=>e.trim());s[1]=e.shift(),h=e,u=!0}if((r=t(l.call(this,s[1].trim(),n),n))&&s[0]===e&&!i(r))return r;i(r)||(r=a(r)),r||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),r=""),u&&(r=h.reduce((e,t)=>this.format(e,t,o.lng,{...o,interpolationkey:s[1].trim()}),r.trim())),e=e.replace(s[0],r),this.regexp.lastIndex=0}return e}}let M=e=>{let t=e.toLowerCase().trim(),s={};if(e.indexOf("(")>-1){let i=e.split("(");t=i[0].toLowerCase().trim();let r=i[1].substring(0,i[1].length-1);"currency"===t&&0>r.indexOf(":")?s.currency||(s.currency=r.trim()):"relativetime"===t&&0>r.indexOf(":")?s.range||(s.range=r.trim()):r.split(";").forEach(e=>{if(e){let[t,...i]=e.split(":"),r=i.join(":").trim().replace(/^'+|'+$/g,""),a=t.trim();s[a]||(s[a]=r),"false"===r&&(s[a]=!1),"true"===r&&(s[a]=!0),isNaN(r)||(s[a]=parseInt(r,10))}})}return{formatName:t,formatOptions:s}},z=e=>{let t={};return(s,i,r)=>{let a=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(a={...a,[r.interpolationkey]:void 0});let n=i+JSON.stringify(a),o=t[n];return o||(o=e(L(i),r),t[n]=o),o(s)}};class H{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=R.create("formatter"),this.options=e,this.formats={number:z((e,t)=>{let s=new Intl.NumberFormat(e,{...t});return e=>s.format(e)}),currency:z((e,t)=>{let s=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>s.format(e)}),datetime:z((e,t)=>{let s=new Intl.DateTimeFormat(e,{...t});return e=>s.format(e)}),relativetime:z((e,t)=>{let s=new Intl.RelativeTimeFormat(e,{...t});return e=>s.format(e,t.range||"day")}),list:z((e,t)=>{let s=new Intl.ListFormat(e,{...t});return e=>s.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=z(t)}format(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=t.split(this.formatSeparator);if(r.length>1&&r[0].indexOf("(")>1&&0>r[0].indexOf(")")&&r.find(e=>e.indexOf(")")>-1)){let e=r.findIndex(e=>e.indexOf(")")>-1);r[0]=[r[0],...r.splice(1,e)].join(this.formatSeparator)}return r.reduce((e,t)=>{let{formatName:r,formatOptions:a}=M(t);if(this.formats[r]){let t=e;try{let n=i?.formatParams?.[i.interpolationkey]||{},o=n.locale||n.lng||i.locale||i.lng||s;t=this.formats[r](e,o,{...a,...i,...n})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${r}`),e},e)}}let B=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class J extends N{constructor(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=R.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(s,i.backend,i)}queueLoad(e,t,s,i){let r={},a={},n={},o={};return e.forEach(e=>{let i=!0;t.forEach(t=>{let n=`${e}|${t}`;!s.reload&&this.store.hasResourceBundle(e,t)?this.state[n]=2:this.state[n]<0||(1===this.state[n]?void 0===a[n]&&(a[n]=!0):(this.state[n]=1,i=!1,void 0===a[n]&&(a[n]=!0),void 0===r[n]&&(r[n]=!0),void 0===o[t]&&(o[t]=!0)))}),i||(n[e]=!0)}),(Object.keys(r).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(r),pending:Object.keys(a),toLoadLanguages:Object.keys(n),toLoadNamespaces:Object.keys(o)}}loaded(e,t,s){let i=e.split("|"),r=i[0],a=i[1];t&&this.emit("failedLoading",r,a,t),!t&&s&&this.store.addResourceBundle(r,a,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);let n={};this.queue.forEach(s=>{p(s.loaded,[r],a),B(s,e),t&&s.errors.push(t),0!==s.pendingCount||s.done||(Object.keys(s.loaded).forEach(e=>{n[e]||(n[e]={});let t=s.loaded[e];t.length&&t.forEach(t=>{void 0===n[e][t]&&(n[e][t]=!0)})}),s.done=!0,s.errors.length?s.callback(s.errors):s.callback())}),this.emit("loaded",n),this.queue=this.queue.filter(e=>!e.done)}read(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:r,callback:a});return}this.readingCalls++;let n=(n,o)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(n&&o&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,s,i+1,2*r,a)},r);return}a(n,o)},o=this.backend[s].bind(this.backend);if(2===o.length){try{let s=o(e,t);s&&"function"==typeof s.then?s.then(e=>n(null,e)).catch(n):n(null,s)}catch(e){n(e)}return}return o(e,t,n)}prepareLoading(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();i(e)&&(e=this.languageUtils.toResolveHierarchy(e)),i(t)&&(t=[t]);let a=this.queueLoad(e,t,s,r);if(!a.toLoad.length)return a.pending.length||r(),null;a.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=e.split("|"),i=s[0],r=s[1];this.read(i,r,"read",void 0,void 0,(s,a)=>{s&&this.logger.warn(`${t}loading namespace ${r} for language ${i} failed`,s),!s&&a&&this.logger.log(`${t}loaded namespace ${r} for language ${i}`,a),this.loaded(e,s,a)})}saveMissing(e,t,s,i,r){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},n=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=s&&""!==s){if(this.backend?.create){let o={...a,isUpdate:r},l=this.backend.create.bind(this.backend);if(l.length<6)try{let r;(r=5===l.length?l(e,t,s,i,o):l(e,t,s,i))&&"function"==typeof r.then?r.then(e=>n(null,e)).catch(n):n(null,r)}catch(e){n(e)}else l(e,t,s,i,n,o)}e&&e[0]&&this.store.addResource(e[0],t,s,i)}}}let q=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),i(e[1])&&(t.defaultValue=e[1]),i(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let s=e[3]||e[2];Object.keys(s).forEach(e=>{t[e]=s[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),_=e=>(i(e.ns)&&(e.ns=[e.ns]),i(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),i(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"==typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),W=()=>{},Y=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class G extends N{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=_(e),this.services={},this.logger=R,this.modules={external:[]},Y(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(s=t,t={}),null==t.defaultNS&&t.ns&&(i(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let a=q();this.options={...a,...this.options,..._(t)},this.options.interpolation={...a.interpolation,...this.options.interpolation},void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let n=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?R.init(n(this.modules.logger),this.options):R.init(null,this.options),t=this.modules.formatter?this.modules.formatter:H;let s=new A(this.options);this.store=new C(this.options.resources,this.options);let i=this.services;i.logger=R,i.resourceStore=this.store,i.languageUtils=s,i.pluralResolver=new V(s,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===a.interpolation.format)&&(i.formatter=n(t),i.formatter.init(i,this.options),this.options.interpolation.format=i.formatter.format.bind(i.formatter)),i.interpolator=new K(this.options),i.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},i.backendConnector=new J(n(this.modules.backend),i.resourceStore,i,this.options),i.backendConnector.on("*",function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];e.emit(t,...i)}),this.modules.languageDetector&&(i.languageDetector=n(this.modules.languageDetector),i.languageDetector.init&&i.languageDetector.init(i,this.options.detection,this.options)),this.modules.i18nFormat&&(i.i18nFormat=n(this.modules.i18nFormat),i.i18nFormat.init&&i.i18nFormat.init(this)),this.translator=new F(this.services,this.options),this.translator.on("*",function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];e.emit(t,...i)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,s||(s=W),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let o=r(),l=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(t),s(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),o}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:W,s=t,r=i(e)?e:this.language;if("function"==typeof e&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if(r?.toLowerCase()==="cimode"&&(!this.options.preload||0===this.options.preload.length))return s();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};r?t(r):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload?.forEach?.(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),s(e)})}else s(null)}reloadResources(e,t,s){let i=r();return"function"==typeof e&&(s=e,e=void 0),"function"==typeof t&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=W),this.services.backendConnector.reload(e,t,e=>{i.resolve(),s(e)}),i}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&P.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&0>this.languages.indexOf(e)&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){var s=this;this.isLanguageChangingTo=e;let a=r();this.emit("languageChanging",e);let n=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},o=(i,r)=>{r?this.isLanguageChangingTo===e&&(n(r),this.translator.changeLanguage(r),this.isLanguageChangingTo=void 0,this.emit("languageChanged",r),this.logger.log("languageChanged",r)):this.isLanguageChangingTo=void 0,a.resolve(function(){return s.t(...arguments)}),t&&t(i,function(){return s.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);let s=i(t)?t:t&&t[0],r=this.store.hasLanguageSomeTranslations(s)?s:this.services.languageUtils.getBestMatchFromCodes(i(t)?[t]:t);r&&(this.language||n(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector?.cacheUserLanguage?.(r)),this.loadResources(r,e=>{o(e,r)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),a}getFixedT(e,t,s){var r=this;let a=function(e,t){let i,n;if("object"!=typeof t){for(var o=arguments.length,l=Array(o>2?o-2:0),h=2;h<o;h++)l[h-2]=arguments[h];i=r.options.overloadTranslationOptionHandler([e,t].concat(l))}else i={...t};i.lng=i.lng||a.lng,i.lngs=i.lngs||a.lngs,i.ns=i.ns||a.ns,""!==i.keyPrefix&&(i.keyPrefix=i.keyPrefix||s||a.keyPrefix);let u=r.options.keySeparator||".";return n=i.keyPrefix&&Array.isArray(e)?e.map(e=>`${i.keyPrefix}${u}${e}`):i.keyPrefix?`${i.keyPrefix}${u}${e}`:e,r.t(n,i)};return i(e)?a.lng=e:a.lngs=e,a.ns=t,a.keyPrefix=s,a}t(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.translator?.translate(...t)}exists(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.translator?.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let s=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,r=this.languages[this.languages.length-1];if("cimode"===s.toLowerCase())return!0;let a=(e,t)=>{let s=this.services.backendConnector.state[`${e}|${t}`];return -1===s||0===s||2===s};if(t.precheck){let e=t.precheck(this,a);if(void 0!==e)return e}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(s,e)&&(!i||a(r,e)))}loadNamespaces(e,t){let s=r();return this.options.ns?(i(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{s.resolve(),t&&t(e)}),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){let s=r();i(e)&&(e=[e]);let a=this.options.preload||[],n=e.filter(e=>0>a.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return n.length?(this.options.preload=a.concat(n),this.loadResources(e=>{s.resolve(),t&&t(e)}),s):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services?.languageUtils||new A(q())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new G(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:W,s=e.forkResourceStore;s&&delete e.forkResourceStore;let i={...this.options,...e,isClone:!0},r=new G(i);return(void 0!==e.debug||void 0!==e.prefix)&&(r.logger=r.logger.clone(e)),["store","services","language"].forEach(e=>{r[e]=this[e]}),r.services={...this.services},r.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},s&&(r.store=new C(Object.keys(this.store.data).reduce((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce((s,i)=>(s[i]={...e[t][i]},s),e[t]),e),{}),i),r.services.resourceStore=r.store),r.translator=new F(r.services,i),r.translator.on("*",function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];r.emit(e,...s)}),r.init(i,t),r.translator.options=i,r.translator.backendConnector.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},r}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let Q=G.createInstance();Q.createInstance=G.createInstance,Q.createInstance,Q.dir,Q.init,Q.loadResources,Q.reloadResources,Q.use,Q.changeLanguage,Q.getFixedT,Q.t,Q.exists,Q.setDefaultNamespace,Q.hasLoadedNamespace,Q.loadNamespaces,Q.loadLanguages},72954:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}}};