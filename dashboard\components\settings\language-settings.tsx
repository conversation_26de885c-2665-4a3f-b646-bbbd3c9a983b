"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"

export function LanguageSettings() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Default Language</Label>
        <RadioGroup defaultValue="ar">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="ar" id="ar" />
            <Label htmlFor="ar">Arabic (العربية)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="en" id="en" />
            <Label htmlFor="en">English</Label>
          </div>
        </RadioGroup>
      </div>
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="rtl">Right-to-Left (RTL) Support</Label>
          <div className="text-sm text-muted-foreground">Enable RTL layout for Arabic language</div>
        </div>
        <Switch id="rtl" defaultChecked />
      </div>
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="auto-translate">Auto-Translate Messages</Label>
          <div className="text-sm text-muted-foreground">
            Automatically translate incoming messages to default language
          </div>
        </div>
        <Switch id="auto-translate" />
      </div>
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="date-format">Use Local Date Format</Label>
          <div className="text-sm text-muted-foreground">Display dates according to the selected language format</div>
        </div>
        <Switch id="date-format" defaultChecked />
      </div>
      <Button>Save Language Settings</Button>
    </div>
  )
}
