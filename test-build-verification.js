#!/usr/bin/env node

/**
 * Test script to verify the build is working correctly
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, 'dashboard', filePath);
  const exists = fs.existsSync(fullPath);
  log(`${exists ? '✅' : '❌'} ${filePath}`, exists ? 'green' : 'red');
  return exists;
}

function runBuildVerification() {
  log('\n🔍 Verifying Build Status', 'bright');
  log('==========================', 'cyan');

  // Check build files
  log('\n📦 Checking build files:', 'blue');
  const buildFiles = [
    '.next/BUILD_ID',
    '.next/build-manifest.json',
    '.next/app-build-manifest.json',
    '.next/server/app/dashboard/properties/page.js',
    '.next/server/app/dashboard/properties/[id]/page.js',
    '.next/server/app/_not-found/page.js'
  ];

  let allBuildFilesExist = true;
  buildFiles.forEach(file => {
    if (!checkFileExists(file)) {
      allBuildFilesExist = false;
    }
  });

  // Check source files
  log('\n📄 Checking source files:', 'blue');
  const sourceFiles = [
    'app/dashboard/properties/page.tsx',
    'app/dashboard/properties/[id]/page.tsx',
    'app/not-found.tsx',
    'components/properties/PropertyShowComponent.tsx',
    'styles/arabic-properties.css'
  ];

  let allSourceFilesExist = true;
  sourceFiles.forEach(file => {
    if (!checkFileExists(file)) {
      allSourceFilesExist = false;
    }
  });

  // Check package.json
  log('\n📋 Checking package.json:', 'blue');
  const packageJsonPath = path.join(__dirname, 'dashboard', 'package.json');
  let packageJsonValid = false;
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    packageJsonValid = packageJson.dependencies && packageJson.dependencies['date-fns'];
    log(`✅ package.json is valid`, 'green');
    log(`   date-fns version: ${packageJson.dependencies['date-fns']}`, 'cyan');
  } catch (error) {
    log(`❌ package.json error: ${error.message}`, 'red');
  }

  // Summary
  log('\n📊 Verification Summary:', 'bright');
  log('========================', 'cyan');
  log(`Build Files: ${allBuildFilesExist ? '✅ PASS' : '❌ FAIL'}`, allBuildFilesExist ? 'green' : 'red');
  log(`Source Files: ${allSourceFilesExist ? '✅ PASS' : '❌ FAIL'}`, allSourceFilesExist ? 'green' : 'red');
  log(`Package.json: ${packageJsonValid ? '✅ PASS' : '❌ FAIL'}`, packageJsonValid ? 'green' : 'red');

  const overallPass = allBuildFilesExist && allSourceFilesExist && packageJsonValid;
  log(`\nOverall: ${overallPass ? '✅ BUILD VERIFIED' : '❌ BUILD ISSUES FOUND'}`, overallPass ? 'green' : 'red');

  if (overallPass) {
    log('\n🎉 Build is ready! You can start the server with:', 'green');
    log('   npm run dev (development)', 'cyan');
    log('   npm start (production)', 'cyan');
    log('\n🌐 Test URLs:', 'blue');
    log('   http://localhost:3000/dashboard/properties', 'cyan');
    log('   http://localhost:3000/dashboard/properties/test-id', 'cyan');
  } else {
    log('\n⚠️  Build verification failed. Please check the issues above.', 'yellow');
  }

  return overallPass;
}

// Run the verification
if (require.main === module) {
  runBuildVerification();
}

module.exports = { runBuildVerification };
