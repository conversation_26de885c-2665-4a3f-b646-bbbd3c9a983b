# Create Sample Properties for Testing

$baseUrl = "http://localhost:5000/api/v1/properties"

# Sample properties data
$properties = @(
    @{
        title = "Luxury Villa in Emirates Hills"
        description = "Stunning 5-bedroom villa with private pool and garden"
        price = 4500000
        currency = "AED"
        type = "VILLA"
        status = "AVAILABLE"
        bedrooms = 5
        bathrooms = 6
        area = 650.0
        location = "Emirates Hills"
        address = "Villa 123, Emirates Hills"
        city = "Dubai"
        country = "UAE"
        latitude = 25.1048
        longitude = 55.1708
        images = @("https://images.unsplash.com/photo-1613490493576-7fde63acd811", "https://images.unsplash.com/photo-1512917774080-9991f1c4c750")
        features = @("Private Pool", "Garden", "Maid Room", "Driver Room", "Study Room")
        amenities = @("24/7 Security", "Gym", "Tennis Court", "Kids Play Area")
        yearBuilt = 2019
        parking = 3
        furnished = $true
        petFriendly = $true
        utilities = "All utilities included"
        contactInfo = "Contact: +971-50-123-4567"
        isActive = $true
        isFeatured = $true
    },
    @{
        title = "Modern Apartment in Downtown Dubai"
        description = "2-bedroom apartment with Burj <PERSON> view"
        price = 2200000
        currency = "AED"
        type = "APARTMENT"
        status = "AVAILABLE"
        bedrooms = 2
        bathrooms = 3
        area = 120.5
        location = "Downtown Dubai"
        address = "Burj Khalifa District, Downtown"
        city = "Dubai"
        country = "UAE"
        latitude = 25.2048
        longitude = 55.2708
        images = @("https://images.unsplash.com/photo-1545324418-cc1a3fa10c00", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688")
        features = @("Balcony", "Built-in Wardrobes", "Central AC")
        amenities = @("Swimming Pool", "Gym", "Concierge", "Valet Parking")
        yearBuilt = 2021
        parking = 1
        furnished = $false
        petFriendly = $false
        utilities = "DEWA included"
        contactInfo = "Contact: +971-50-987-6543"
        isActive = $true
        isFeatured = $true
    },
    @{
        title = "Spacious Townhouse in Arabian Ranches"
        description = "3-bedroom townhouse in family-friendly community"
        price = 1800000
        currency = "AED"
        type = "TOWNHOUSE"
        status = "AVAILABLE"
        bedrooms = 3
        bathrooms = 4
        area = 200.0
        location = "Arabian Ranches"
        address = "Type 3M, Arabian Ranches"
        city = "Dubai"
        country = "UAE"
        latitude = 25.0548
        longitude = 55.2508
        images = @("https://images.unsplash.com/photo-1570129477492-45c003edd2be", "https://images.unsplash.com/photo-1564013799919-ab600027ffc6")
        features = @("Private Garden", "Covered Parking", "Storage Room")
        amenities = @("Golf Course", "Community Pool", "Kids Play Area", "BBQ Area")
        yearBuilt = 2018
        parking = 2
        furnished = $false
        petFriendly = $true
        utilities = "Basic utilities included"
        contactInfo = "Contact: +971-50-555-1234"
        isActive = $true
        isFeatured = $false
    },
    @{
        title = "Penthouse in Marina"
        description = "Luxury penthouse with panoramic marina views"
        price = 3500000
        currency = "AED"
        type = "PENTHOUSE"
        status = "AVAILABLE"
        bedrooms = 4
        bathrooms = 5
        area = 300.0
        location = "Dubai Marina"
        address = "Marina Tower, Dubai Marina"
        city = "Dubai"
        country = "UAE"
        latitude = 25.0776
        longitude = 55.1390
        images = @("https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd", "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2")
        features = @("Terrace", "Jacuzzi", "Panoramic Views", "Smart Home System")
        amenities = @("Marina Walk", "Beach Access", "Restaurants", "Shopping Mall")
        yearBuilt = 2020
        parking = 2
        furnished = $true
        petFriendly = $false
        utilities = "All utilities and internet included"
        contactInfo = "Contact: +971-50-777-8888"
        isActive = $true
        isFeatured = $true
    },
    @{
        title = "Studio Apartment in Business Bay"
        description = "Compact studio perfect for young professionals"
        price = 650000
        currency = "AED"
        type = "STUDIO"
        status = "AVAILABLE"
        bedrooms = 0
        bathrooms = 1
        area = 45.0
        location = "Business Bay"
        address = "Executive Tower, Business Bay"
        city = "Dubai"
        country = "UAE"
        latitude = 25.1890
        longitude = 55.2650
        images = @("https://images.unsplash.com/photo-1522708323590-d24dbb6b0267", "https://images.unsplash.com/photo-1631049307264-da0ec9d70304")
        features = @("Built-in Kitchen", "Floor-to-Ceiling Windows")
        amenities = @("Gym", "Swimming Pool", "Business Center")
        yearBuilt = 2022
        parking = 1
        furnished = $true
        petFriendly = $false
        utilities = "DEWA and internet included"
        contactInfo = "Contact: +971-50-333-2222"
        isActive = $true
        isFeatured = $false
    }
)

Write-Host "Creating sample properties..." -ForegroundColor Green

$createdProperties = @()

foreach ($property in $properties) {
    $jsonData = $property | ConvertTo-Json -Depth 10
    
    try {
        Write-Host "Creating: $($property.title)..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Body $jsonData -ContentType "application/json"
        $result = $response.Content | ConvertFrom-Json
        $createdProperties += $result.data.id
        Write-Host "  Created successfully with ID: $($result.data.id)" -ForegroundColor Green
    } catch {
        Write-Host "  Failed to create: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nSample properties created successfully!" -ForegroundColor Green
Write-Host "Created $($createdProperties.Count) properties" -ForegroundColor Cyan

# Test the stats endpoint
Write-Host "`nFetching updated stats..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/stats" -Method GET
    $result = $response.Content | ConvertFrom-Json
    Write-Host "Total properties: $($result.data.total)" -ForegroundColor Cyan
    Write-Host "Available: $($result.data.available)" -ForegroundColor Cyan
    Write-Host "Featured: $($result.data.featured)" -ForegroundColor Cyan
} catch {
    Write-Host "Failed to fetch stats: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nYou can now test the frontend at:" -ForegroundColor Green
Write-Host "http://localhost:3000/dashboard/properties" -ForegroundColor Cyan
