(()=>{var e={};e.id=7260,e.ids=[7260],e.modules={169:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>q});var s=t(60687),a=t(43210),i=t(16189),n=t(24934),o=t(68988),l=t(39390),d=t(15616),c=t(63974),p=t(55192),u=t(28559),x=t(96474),h=t(11860),m=t(41862),b=t(8819),g=t(70333),v=t(4068),j=t(59821),f=t(85814),y=t.n(f);function q(){let e=(0,i.useRouter)(),{addTemplate:r}=(0,v.U)(),[t,f]=(0,a.useState)({name:"",content:"",category:"custom",variables:[],language:"en"}),[q,C]=(0,a.useState)(""),[N,w]=(0,a.useState)(!1),{toast:A}=(0,g.dj)(),k=e=>{let{name:r,value:t}=e.target;f(e=>({...e,[r]:t}))},P=e=>{f(r=>({...r,variables:r.variables.filter(r=>r!==e)}))},_=async s=>{if(s.preventDefault(),!t.name.trim()){A({title:"Error",description:"Template name is required",variant:"destructive"});return}if(!t.content.trim()){A({title:"Error",description:"Template content is required",variant:"destructive"});return}w(!0);try{await r(t)?(A({title:"Template created",description:"The template has been successfully created."}),e.push("/dashboard/templates")):A({title:"Error",description:"Failed to create the template. Please try again.",variant:"destructive"})}catch(e){console.error("Error creating template:",e),A({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{w(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(y(),{href:"/dashboard/templates",children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"mr-4",children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Back to Templates"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Create New Template"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create a new WhatsApp message template"})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Template Details"}),(0,s.jsx)(p.BT,{children:"Fill in the details to create a new template"})]}),(0,s.jsxs)("form",{onSubmit:_,children:[(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"name",children:"Template Name"}),(0,s.jsx)(o.p,{id:"name",name:"name",value:t.name,onChange:k,placeholder:"Enter template name"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"category",children:"Category"}),(0,s.jsxs)(c.l6,{value:t.category,onValueChange:e=>{f(r=>({...r,category:e}))},children:[(0,s.jsx)(c.bq,{id:"category",children:(0,s.jsx)(c.yv,{placeholder:"Select category"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"greeting",children:"Greeting"}),(0,s.jsx)(c.eb,{value:"reminder",children:"Reminder"}),(0,s.jsx)(c.eb,{value:"update",children:"Update"}),(0,s.jsx)(c.eb,{value:"payment",children:"Payment"}),(0,s.jsx)(c.eb,{value:"marketing",children:"Marketing"}),(0,s.jsx)(c.eb,{value:"custom",children:"Custom"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"language",children:"Language"}),(0,s.jsxs)(c.l6,{value:t.language,onValueChange:e=>{f(r=>({...r,language:e}))},children:[(0,s.jsx)(c.bq,{id:"language",children:(0,s.jsx)(c.yv,{placeholder:"Select language"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"en",children:"English"}),(0,s.jsx)(c.eb,{value:"ar",children:"Arabic"})]})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Select the language for this template. You can create templates in multiple languages."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"content",children:"Template Content"}),(0,s.jsx)(d.T,{id:"content",name:"content",value:t.content,onChange:k,placeholder:"Enter template content",rows:5}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Use ","{{",(0,s.jsx)("span",{children:"variable"}),"}}"," syntax for personalization variables."]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Variables"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(o.p,{value:q,onChange:e=>C(e.target.value),placeholder:"Add a variable"}),(0,s.jsx)(n.$,{type:"button",onClick:()=>{q.trim()&&!t.variables.includes(q.trim())&&(f(e=>({...e,variables:[...e.variables,q.trim()]})),C(""))},size:"sm",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[t.variables.map((e,r)=>(0,s.jsxs)(j.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>P(e),className:"text-muted-foreground hover:text-foreground",children:(0,s.jsx)(h.A,{className:"h-3 w-3"})})]},r)),0===t.variables.length&&(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"No variables added"})]})]})]}),(0,s.jsx)(p.wL,{className:"flex justify-end",children:(0,s.jsx)(n.$,{type:"submit",disabled:N,children:N?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Create Template"]})})})]})]})]})}},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14559:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\templates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\templates\\new\\page.tsx","default")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},51858:(e,r,t)=>{Promise.resolve().then(t.bind(t,169))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57140:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["dashboard",{children:["templates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,14559)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\templates\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\templates\\new\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/templates/new/page",pathname:"/dashboard/templates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88810:(e,r,t)=>{Promise.resolve().then(t.bind(t,14559))},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,8157,2190,3903,5153,1467,1060,4097,4088,9464,381,1941,9450],()=>t(57140));module.exports=s})();