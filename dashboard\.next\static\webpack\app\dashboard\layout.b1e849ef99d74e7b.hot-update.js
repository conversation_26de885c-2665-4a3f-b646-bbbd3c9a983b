"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/megaphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-alert.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Calendar,FileText,Home,Megaphone,Menu,MessageSquare,Settings,ShieldAlert,User,UserCog,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_i18n_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/i18n/client */ \"(app-pages-browser)/./lib/i18n/client.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Define routes with role-based access control\nconst routes = [\n    {\n        label: \"sidebar.analytics\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/dashboard/analytics\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ]\n    },\n    {\n        label: \"sidebar.user\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: \"/dashboard/user\",\n        roles: [\n            \"USER\",\n            \"CLIENT\",\n            \"AGENT\",\n            \"ADMIN\"\n        ]\n    },\n    {\n        label: \"sidebar.clients\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: \"/dashboard/clients\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ]\n    },\n    {\n        label: \"sidebar.messaging\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: \"/dashboard/messaging\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    // Marketing section\n    {\n        label: \"sidebar.marketing\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        href: \"/dashboard/marketing\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ],\n        children: [\n            {\n                label: \"sidebar.campaigns\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                href: \"/dashboard/campaigns\",\n                roles: [\n                    \"ADMIN\",\n                    \"AGENT\"\n                ]\n            },\n            {\n                label: \"sidebar.templates\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                href: \"/dashboard/marketing/templates\",\n                roles: [\n                    \"ADMIN\",\n                    \"AGENT\"\n                ]\n            }\n        ]\n    },\n    {\n        label: \"sidebar.appointments\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        href: \"/dashboard/appointments\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    {\n        label: \"sidebar.ai-chatbot\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        href: \"/dashboard/ai-chatbot\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\"\n        ]\n    },\n    {\n        label: \"sidebar.database\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        href: \"/dashboard/data\",\n        roles: [\n            \"ADMIN\"\n        ]\n    },\n    {\n        label: \"sidebar.users\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        href: \"/dashboard/users\",\n        roles: [\n            \"ADMIN\"\n        ]\n    },\n    {\n        label: \"sidebar.properties\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        href: \"/dashboard/properties\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    {\n        label: \"sidebar.settings\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        href: \"/dashboard/settings\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    },\n    {\n        label: \"sidebar.profile\",\n        icon: _barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: \"/dashboard/profile\",\n        roles: [\n            \"ADMIN\",\n            \"AGENT\",\n            \"CLIENT\",\n            \"USER\"\n        ]\n    }\n];\nfunction Sidebar(param) {\n    let { userRole = \"USER\" } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const { t } = (0,_lib_i18n_client__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    // Filter routes based on user role\n    const filteredRoutes = routes.filter((route)=>route.roles.includes(userRole));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative h-full border-r bg-card transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-between px-4 border-b\",\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/dashboard/analytics\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-primary\",\n                            children: \"الذكاء الاصطناعي العقاري\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"ml-auto\",\n                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 57\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Calendar_FileText_Home_Megaphone_Menu_MessageSquare_Settings_ShieldAlert_User_UserCog_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"capitalize\",\n                            children: [\n                                userRole,\n                                \" Role\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 py-4\",\n                children: filteredRoutes.map((route)=>{\n                    // Check if the route has children\n                    if (route.children) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center px-4 py-2 text-sm font-medium text-muted-foreground\", isCollapsed && \"justify-center px-0\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(route.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-5 w-5\", isCollapsed ? \"mr-0\" : \"mr-3\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t(route.label)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && route.children.map((childRoute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: childRoute.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center pl-8 pr-4 py-2 text-sm font-medium transition-colors\", pathname === childRoute.href || pathname.startsWith(\"\".concat(childRoute.href, \"/\")) ? \"bg-primary/10 text-primary\" : \"text-muted-foreground hover:bg-primary/5 hover:text-primary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(childRoute.icon, {\n                                                className: \"h-4 w-4 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t(childRoute.label)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, childRoute.href, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, this)),\n                                isCollapsed && route.children.map((childRoute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: childRoute.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center justify-center px-0 py-2 text-sm font-medium transition-colors\", pathname === childRoute.href || pathname.startsWith(\"\".concat(childRoute.href, \"/\")) ? \"bg-primary/10 text-primary\" : \"text-muted-foreground hover:bg-primary/5 hover:text-primary\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(childRoute.icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, childRoute.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, route.href, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 15\n                        }, this);\n                    }\n                    // Regular route without children\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: route.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center px-4 py-3 text-sm font-medium transition-colors\", pathname === route.href || pathname.startsWith(\"\".concat(route.href, \"/\")) ? \"bg-primary/10 text-primary\" : \"text-muted-foreground hover:bg-primary/5 hover:text-primary\", isCollapsed && \"justify-center px-0\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(route.icon, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-5 w-5\", isCollapsed ? \"mr-0\" : \"mr-3\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t(route.label)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 32\n                            }, this)\n                        ]\n                    }, route.href, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\sidebar.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"ITYO8WGB9Zwa9Eu/QN0nB2DrOmM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _lib_i18n_client__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvc2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDZTtBQUNGO0FBQ2pCO0FBZ0JQO0FBQ1c7QUFDa0I7QUFFbEQsK0NBQStDO0FBQy9DLE1BQU1vQixTQUFTO0lBQ2I7UUFDRUMsT0FBTztRQUNQQyxNQUFNbEIsdUxBQVNBO1FBQ2ZtQixNQUFNO1FBQ05DLE9BQU87WUFBQztZQUFTO1NBQVE7SUFDM0I7SUFDQTtRQUNFSCxPQUFPO1FBQ1BDLE1BQU1YLHVMQUFJQTtRQUNWWSxNQUFNO1FBQ05DLE9BQU87WUFBQztZQUFRO1lBQVU7WUFBUztTQUFRO0lBQzdDO0lBQ0E7UUFDRUgsT0FBTztRQUNQQyxNQUFNakIsdUxBQUtBO1FBQ1hrQixNQUFNO1FBQ05DLE9BQU87WUFBQztZQUFTO1NBQVE7SUFDM0I7SUFDQTtRQUNFSCxPQUFPO1FBQ1BDLE1BQU1oQix3TEFBYUE7UUFDbkJpQixNQUFNO1FBQ05DLE9BQU87WUFBQztZQUFTO1lBQVM7WUFBVTtTQUFPO0lBQzdDO0lBQ0Esb0JBQW9CO0lBQ3BCO1FBQ0VILE9BQU87UUFDUEMsTUFBTWYsd0xBQVNBO1FBQ2ZnQixNQUFNO1FBQ05DLE9BQU87WUFBQztZQUFTO1NBQVE7UUFDekJDLFVBQVU7WUFDUjtnQkFDRUosT0FBTztnQkFDUEMsTUFBTWYsd0xBQVNBO2dCQUNmZ0IsTUFBTTtnQkFDTkMsT0FBTztvQkFBQztvQkFBUztpQkFBUTtZQUMzQjtZQUNBO2dCQUNFSCxPQUFPO2dCQUNQQyxNQUFNTCx3TEFBUUE7Z0JBQ2RNLE1BQU07Z0JBQ05DLE9BQU87b0JBQUM7b0JBQVM7aUJBQVE7WUFDM0I7U0FDRDtJQUNIO0lBQ0E7UUFDRUgsT0FBTztRQUNQQyxNQUFNUix3TEFBUUE7UUFDZFMsTUFBTTtRQUNOQyxPQUFPO1lBQUM7WUFBUztZQUFTO1lBQVU7U0FBTztJQUM3QztJQUNBO1FBQ0VILE9BQU87UUFDUEMsTUFBTVAsd0xBQUdBO1FBQ1RRLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQVM7U0FBUTtJQUMzQjtJQUNBO1FBQ0VILE9BQU87UUFDUEMsTUFBTU4sd0xBQVdBO1FBQ2pCTyxNQUFNO1FBQ05DLE9BQU87WUFBQztTQUFRO0lBQ2xCO0lBQ0E7UUFDRUgsT0FBTztRQUNQQyxNQUFNZCx3TEFBT0E7UUFDYmUsTUFBTTtRQUNOQyxPQUFPO1lBQUM7U0FBUTtJQUNsQjtJQUNBO1FBQ0VILE9BQU87UUFDUEMsTUFBTWIsd0xBQUlBO1FBQ1ZjLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQVM7WUFBUztZQUFVO1NBQU87SUFDN0M7SUFDQTtRQUNFSCxPQUFPO1FBQ1BDLE1BQU1aLHdMQUFRQTtRQUNkYSxNQUFNO1FBQ05DLE9BQU87WUFBQztZQUFTO1lBQVM7WUFBVTtTQUFPO0lBQzdDO0lBQ0E7UUFDRUgsT0FBTztRQUNQQyxNQUFNWCx1TEFBSUE7UUFDVlksTUFBTTtRQUNOQyxPQUFPO1lBQUM7WUFBUztZQUFTO1lBQVU7U0FBTztJQUM3QztDQUNEO0FBTU0sU0FBU0UsUUFBUSxLQUFtQztRQUFuQyxFQUFFQyxXQUFXLE1BQU0sRUFBZ0IsR0FBbkM7O0lBQ3RCLE1BQU1DLFdBQVcxQiw0REFBV0E7SUFDNUIsTUFBTSxDQUFDMkIsYUFBYUMsZUFBZSxHQUFHWiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLEVBQUVhLENBQUMsRUFBRSxHQUFHWixnRUFBY0E7SUFFNUIsbUNBQW1DO0lBQ25DLE1BQU1hLGlCQUFpQlosT0FBT2EsTUFBTSxDQUFDQyxDQUFBQSxRQUNuQ0EsTUFBTVYsS0FBSyxDQUFDVyxRQUFRLENBQUNSO0lBR3ZCLHFCQUNFLDhEQUFDUztRQUFJQyxXQUFXckMsOENBQUVBLENBQUMsZ0VBQWdFNkIsY0FBYyxTQUFTOzswQkFDeEcsOERBQUNPO2dCQUFJQyxXQUFVOztvQkFDWixDQUFDUiw2QkFDQSw4REFBQzFCLGtEQUFJQTt3QkFBQ29CLE1BQUs7a0NBQ1QsNEVBQUNlOzRCQUFHRCxXQUFVO3NDQUFpQzs7Ozs7Ozs7Ozs7a0NBR25ELDhEQUFDcEMseURBQU1BO3dCQUFDc0MsU0FBUTt3QkFBUUMsTUFBSzt3QkFBT0gsV0FBVTt3QkFBVUksU0FBUyxJQUFNWCxlQUFlLENBQUNEO2tDQUNwRkEsNEJBQWMsOERBQUNqQix3TEFBSUE7NEJBQUN5QixXQUFVOzs7OztpREFBZSw4REFBQ3hCLHdMQUFDQTs0QkFBQ3dCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBSzlELENBQUNSLDZCQUNBLDhEQUFDTztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDckIsd0xBQVdBOzRCQUFDcUIsV0FBVTs7Ozs7O3NDQUN2Qiw4REFBQ0s7NEJBQUtMLFdBQVU7O2dDQUFjVjtnQ0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUs3Qyw4REFBQ1M7Z0JBQUlDLFdBQVU7MEJBQ1pMLGVBQWVXLEdBQUcsQ0FBQyxDQUFDVDtvQkFDbkIsa0NBQWtDO29CQUNsQyxJQUFJQSxNQUFNVCxRQUFRLEVBQUU7d0JBQ2xCLHFCQUNFLDhEQUFDVzs0QkFBcUJDLFdBQVU7OzhDQUU5Qiw4REFBQ0Q7b0NBQ0NDLFdBQVdyQyw4Q0FBRUEsQ0FDWCx5RUFDQTZCLGVBQWU7O3NEQUdqQiw4REFBQ0ssTUFBTVosSUFBSTs0Q0FBQ2UsV0FBV3JDLDhDQUFFQSxDQUFDLFdBQVc2QixjQUFjLFNBQVM7Ozs7Ozt3Q0FDM0QsQ0FBQ0EsNkJBQWUsOERBQUNhO3NEQUFNWCxFQUFFRyxNQUFNYixLQUFLOzs7Ozs7Ozs7Ozs7Z0NBSXRDLENBQUNRLGVBQWVLLE1BQU1ULFFBQVEsQ0FBQ2tCLEdBQUcsQ0FBQyxDQUFDQywyQkFDbkMsOERBQUN6QyxrREFBSUE7d0NBRUhvQixNQUFNcUIsV0FBV3JCLElBQUk7d0NBQ3JCYyxXQUFXckMsOENBQUVBLENBQ1gsMEVBQ0E0QixhQUFhZ0IsV0FBV3JCLElBQUksSUFBSUssU0FBU2lCLFVBQVUsQ0FBQyxHQUFtQixPQUFoQkQsV0FBV3JCLElBQUksRUFBQyxRQUNuRSwrQkFDQTs7MERBR04sOERBQUNxQixXQUFXdEIsSUFBSTtnREFBQ2UsV0FBVTs7Ozs7OzBEQUMzQiw4REFBQ0s7MERBQU1YLEVBQUVhLFdBQVd2QixLQUFLOzs7Ozs7O3VDQVZwQnVCLFdBQVdyQixJQUFJOzs7OztnQ0FldkJNLGVBQWVLLE1BQU1ULFFBQVEsQ0FBQ2tCLEdBQUcsQ0FBQyxDQUFDQywyQkFDbEMsOERBQUN6QyxrREFBSUE7d0NBRUhvQixNQUFNcUIsV0FBV3JCLElBQUk7d0NBQ3JCYyxXQUFXckMsOENBQUVBLENBQ1gsb0ZBQ0E0QixhQUFhZ0IsV0FBV3JCLElBQUksSUFBSUssU0FBU2lCLFVBQVUsQ0FBQyxHQUFtQixPQUFoQkQsV0FBV3JCLElBQUksRUFBQyxRQUNuRSwrQkFDQTtrREFHTiw0RUFBQ3FCLFdBQVd0QixJQUFJOzRDQUFDZSxXQUFVOzs7Ozs7dUNBVHRCTyxXQUFXckIsSUFBSTs7Ozs7OzJCQWhDaEJXLE1BQU1YLElBQUk7Ozs7O29CQThDeEI7b0JBRUEsaUNBQWlDO29CQUNqQyxxQkFDRSw4REFBQ3BCLGtEQUFJQTt3QkFFSG9CLE1BQU1XLE1BQU1YLElBQUk7d0JBQ2hCYyxXQUFXckMsOENBQUVBLENBQ1gscUVBQ0E0QixhQUFhTSxNQUFNWCxJQUFJLElBQUlLLFNBQVNpQixVQUFVLENBQUMsR0FBYyxPQUFYWCxNQUFNWCxJQUFJLEVBQUMsUUFDekQsK0JBQ0EsK0RBQ0pNLGVBQWU7OzBDQUdqQiw4REFBQ0ssTUFBTVosSUFBSTtnQ0FBQ2UsV0FBV3JDLDhDQUFFQSxDQUFDLFdBQVc2QixjQUFjLFNBQVM7Ozs7Ozs0QkFDM0QsQ0FBQ0EsNkJBQWUsOERBQUNhOzBDQUFNWCxFQUFFRyxNQUFNYixLQUFLOzs7Ozs7O3VCQVhoQ2EsTUFBTVgsSUFBSTs7Ozs7Z0JBY3JCOzs7Ozs7Ozs7Ozs7QUFJUjtHQTNHZ0JHOztRQUNHeEIsd0RBQVdBO1FBRWRpQiw0REFBY0E7OztLQUhkTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxjb21wb25lbnRzXFxzaWRlYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiXG5pbXBvcnQge1xuICBCYXJDaGFydDMsXG4gIFVzZXJzLFxuICBNZXNzYWdlU3F1YXJlLFxuICBNZWdhcGhvbmUsXG4gIFVzZXJDb2csXG4gIEhvbWUsXG4gIFNldHRpbmdzLFxuICBVc2VyLFxuICBNZW51LFxuICBYLFxuICBDYWxlbmRhcixcbiAgQm90LFxuICBTaGllbGRBbGVydCxcbiAgRmlsZVRleHQsXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwiQC9saWIvaTE4bi9jbGllbnRcIlxuXG4vLyBEZWZpbmUgcm91dGVzIHdpdGggcm9sZS1iYXNlZCBhY2Nlc3MgY29udHJvbFxuY29uc3Qgcm91dGVzID0gW1xuICB7XG4gICAgbGFiZWw6IFwic2lkZWJhci5hbmFseXRpY3NcIixcbiAgICBpY29uOiBCYXJDaGFydDMsXG4gICAgaHJlZjogXCIvZGFzaGJvYXJkL2FuYWx5dGljc1wiLFxuICAgIHJvbGVzOiBbXCJBRE1JTlwiLCBcIkFHRU5UXCJdLCAvLyBPbmx5IGFkbWlucyBhbmQgYWdlbnRzIGNhbiBzZWUgYW5hbHl0aWNzXG4gIH0sXG4gIHtcbiAgICBsYWJlbDogXCJzaWRlYmFyLnVzZXJcIixcbiAgICBpY29uOiBVc2VyLFxuICAgIGhyZWY6IFwiL2Rhc2hib2FyZC91c2VyXCIsXG4gICAgcm9sZXM6IFtcIlVTRVJcIiwgXCJDTElFTlRcIiwgXCJBR0VOVFwiLCBcIkFETUlOXCJdLCAvLyBBbGwgcm9sZXMgY2FuIGFjY2VzcyB1c2VyIGRhc2hib2FyZFxuICB9LFxuICB7XG4gICAgbGFiZWw6IFwic2lkZWJhci5jbGllbnRzXCIsXG4gICAgaWNvbjogVXNlcnMsXG4gICAgaHJlZjogXCIvZGFzaGJvYXJkL2NsaWVudHNcIixcbiAgICByb2xlczogW1wiQURNSU5cIiwgXCJBR0VOVFwiXSwgLy8gT25seSBhZG1pbnMgYW5kIGFnZW50cyBjYW4gc2VlIGNsaWVudHNcbiAgfSxcbiAge1xuICAgIGxhYmVsOiBcInNpZGViYXIubWVzc2FnaW5nXCIsXG4gICAgaWNvbjogTWVzc2FnZVNxdWFyZSxcbiAgICBocmVmOiBcIi9kYXNoYm9hcmQvbWVzc2FnaW5nXCIsXG4gICAgcm9sZXM6IFtcIkFETUlOXCIsIFwiQUdFTlRcIiwgXCJDTElFTlRcIiwgXCJVU0VSXCJdLCAvLyBBbGwgcm9sZXMgY2FuIGFjY2VzcyBtZXNzYWdpbmdcbiAgfSxcbiAgLy8gTWFya2V0aW5nIHNlY3Rpb25cbiAge1xuICAgIGxhYmVsOiBcInNpZGViYXIubWFya2V0aW5nXCIsXG4gICAgaWNvbjogTWVnYXBob25lLFxuICAgIGhyZWY6IFwiL2Rhc2hib2FyZC9tYXJrZXRpbmdcIixcbiAgICByb2xlczogW1wiQURNSU5cIiwgXCJBR0VOVFwiXSxcbiAgICBjaGlsZHJlbjogW1xuICAgICAge1xuICAgICAgICBsYWJlbDogXCJzaWRlYmFyLmNhbXBhaWduc1wiLFxuICAgICAgICBpY29uOiBNZWdhcGhvbmUsXG4gICAgICAgIGhyZWY6IFwiL2Rhc2hib2FyZC9jYW1wYWlnbnNcIixcbiAgICAgICAgcm9sZXM6IFtcIkFETUlOXCIsIFwiQUdFTlRcIl0sIC8vIE9ubHkgYWRtaW5zIGFuZCBhZ2VudHMgY2FuIHNlZSBjYW1wYWlnbnNcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGxhYmVsOiBcInNpZGViYXIudGVtcGxhdGVzXCIsXG4gICAgICAgIGljb246IEZpbGVUZXh0LFxuICAgICAgICBocmVmOiBcIi9kYXNoYm9hcmQvbWFya2V0aW5nL3RlbXBsYXRlc1wiLFxuICAgICAgICByb2xlczogW1wiQURNSU5cIiwgXCJBR0VOVFwiXSwgLy8gT25seSBhZG1pbnMgYW5kIGFnZW50cyBjYW4gbWFuYWdlIHRlbXBsYXRlc1xuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICB7XG4gICAgbGFiZWw6IFwic2lkZWJhci5hcHBvaW50bWVudHNcIixcbiAgICBpY29uOiBDYWxlbmRhcixcbiAgICBocmVmOiBcIi9kYXNoYm9hcmQvYXBwb2ludG1lbnRzXCIsXG4gICAgcm9sZXM6IFtcIkFETUlOXCIsIFwiQUdFTlRcIiwgXCJDTElFTlRcIiwgXCJVU0VSXCJdLCAvLyBBbGwgcm9sZXMgY2FuIGFjY2VzcyBhcHBvaW50bWVudHNcbiAgfSxcbiAge1xuICAgIGxhYmVsOiBcInNpZGViYXIuYWktY2hhdGJvdFwiLFxuICAgIGljb246IEJvdCxcbiAgICBocmVmOiBcIi9kYXNoYm9hcmQvYWktY2hhdGJvdFwiLFxuICAgIHJvbGVzOiBbXCJBRE1JTlwiLCBcIkFHRU5UXCJdLCAvLyBPbmx5IGFkbWlucyBhbmQgYWdlbnRzIGNhbiBhY2Nlc3MgQUkgY2hhdGJvdFxuICB9LFxuICB7XG4gICAgbGFiZWw6IFwic2lkZWJhci5kYXRhYmFzZVwiLFxuICAgIGljb246IFNoaWVsZEFsZXJ0LFxuICAgIGhyZWY6IFwiL2Rhc2hib2FyZC9kYXRhXCIsXG4gICAgcm9sZXM6IFtcIkFETUlOXCJdLCAvLyBPbmx5IGFkbWlucyBjYW4gYWNjZXNzIHRoZSBkYXRhYmFzZSBkYXNoYm9hcmRcbiAgfSxcbiAge1xuICAgIGxhYmVsOiBcInNpZGViYXIudXNlcnNcIixcbiAgICBpY29uOiBVc2VyQ29nLFxuICAgIGhyZWY6IFwiL2Rhc2hib2FyZC91c2Vyc1wiLFxuICAgIHJvbGVzOiBbXCJBRE1JTlwiXSwgLy8gT25seSBhZG1pbnMgY2FuIG1hbmFnZSB1c2Vyc1xuICB9LFxuICB7XG4gICAgbGFiZWw6IFwic2lkZWJhci5wcm9wZXJ0aWVzXCIsXG4gICAgaWNvbjogSG9tZSxcbiAgICBocmVmOiBcIi9kYXNoYm9hcmQvcHJvcGVydGllc1wiLFxuICAgIHJvbGVzOiBbXCJBRE1JTlwiLCBcIkFHRU5UXCIsIFwiQ0xJRU5UXCIsIFwiVVNFUlwiXSwgLy8gQWxsIHJvbGVzIGNhbiBzZWUgcHJvcGVydGllc1xuICB9LFxuICB7XG4gICAgbGFiZWw6IFwic2lkZWJhci5zZXR0aW5nc1wiLFxuICAgIGljb246IFNldHRpbmdzLFxuICAgIGhyZWY6IFwiL2Rhc2hib2FyZC9zZXR0aW5nc1wiLFxuICAgIHJvbGVzOiBbXCJBRE1JTlwiLCBcIkFHRU5UXCIsIFwiQ0xJRU5UXCIsIFwiVVNFUlwiXSwgLy8gQWxsIHJvbGVzIGNhbiBhY2Nlc3Mgc2V0dGluZ3NcbiAgfSxcbiAge1xuICAgIGxhYmVsOiBcInNpZGViYXIucHJvZmlsZVwiLFxuICAgIGljb246IFVzZXIsXG4gICAgaHJlZjogXCIvZGFzaGJvYXJkL3Byb2ZpbGVcIixcbiAgICByb2xlczogW1wiQURNSU5cIiwgXCJBR0VOVFwiLCBcIkNMSUVOVFwiLCBcIlVTRVJcIl0sIC8vIEFsbCByb2xlcyBjYW4gYWNjZXNzIHRoZWlyIHByb2ZpbGVcbiAgfSxcbl1cblxuaW50ZXJmYWNlIFNpZGViYXJQcm9wcyB7XG4gIHVzZXJSb2xlPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gU2lkZWJhcih7IHVzZXJSb2xlID0gXCJVU0VSXCIgfTogU2lkZWJhclByb3BzKSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuICBjb25zdCBbaXNDb2xsYXBzZWQsIHNldElzQ29sbGFwc2VkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKClcblxuICAvLyBGaWx0ZXIgcm91dGVzIGJhc2VkIG9uIHVzZXIgcm9sZVxuICBjb25zdCBmaWx0ZXJlZFJvdXRlcyA9IHJvdXRlcy5maWx0ZXIocm91dGUgPT5cbiAgICByb3V0ZS5yb2xlcy5pbmNsdWRlcyh1c2VyUm9sZSlcbiAgKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKFwicmVsYXRpdmUgaC1mdWxsIGJvcmRlci1yIGJnLWNhcmQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCIsIGlzQ29sbGFwc2VkID8gXCJ3LTE2XCIgOiBcInctNjRcIil9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtMTYgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC00IGJvcmRlci1iXCI+XG4gICAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkL2FuYWx5dGljc1wiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtcHJpbWFyeVwiPtin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yog2KfZhNi52YLYp9ix2Yo8L2gxPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgKX1cbiAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIGNsYXNzTmFtZT1cIm1sLWF1dG9cIiBvbkNsaWNrPXsoKSA9PiBzZXRJc0NvbGxhcHNlZCghaXNDb2xsYXBzZWQpfT5cbiAgICAgICAgICB7aXNDb2xsYXBzZWQgPyA8TWVudSBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gOiA8WCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz59XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBSb2xlIGluZGljYXRvciAqL31cbiAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMiBib3JkZXItYlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgIDxTaGllbGRBbGVydCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNhcGl0YWxpemVcIj57dXNlclJvbGV9IFJvbGU8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgcHktNFwiPlxuICAgICAgICB7ZmlsdGVyZWRSb3V0ZXMubWFwKChyb3V0ZSkgPT4ge1xuICAgICAgICAgIC8vIENoZWNrIGlmIHRoZSByb3V0ZSBoYXMgY2hpbGRyZW5cbiAgICAgICAgICBpZiAocm91dGUuY2hpbGRyZW4pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtyb3V0ZS5ocmVmfSBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICB7LyogUGFyZW50IHJvdXRlIGFzIGEgc2VjdGlvbiBoZWFkZXIgKi99XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgXCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIixcbiAgICAgICAgICAgICAgICAgICAgaXNDb2xsYXBzZWQgJiYgXCJqdXN0aWZ5LWNlbnRlciBweC0wXCIsXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxyb3V0ZS5pY29uIGNsYXNzTmFtZT17Y24oXCJoLTUgdy01XCIsIGlzQ29sbGFwc2VkID8gXCJtci0wXCIgOiBcIm1yLTNcIil9IC8+XG4gICAgICAgICAgICAgICAgICB7IWlzQ29sbGFwc2VkICYmIDxzcGFuPnt0KHJvdXRlLmxhYmVsKX08L3NwYW4+fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIENoaWxkIHJvdXRlcyAqL31cbiAgICAgICAgICAgICAgICB7IWlzQ29sbGFwc2VkICYmIHJvdXRlLmNoaWxkcmVuLm1hcCgoY2hpbGRSb3V0ZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAga2V5PXtjaGlsZFJvdXRlLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2NoaWxkUm91dGUuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIHBsLTggcHItNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgICAgICAgICAgICAgICAgICBwYXRobmFtZSA9PT0gY2hpbGRSb3V0ZS5ocmVmIHx8IHBhdGhuYW1lLnN0YXJ0c1dpdGgoYCR7Y2hpbGRSb3V0ZS5ocmVmfS9gKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXByaW1hcnkvMTAgdGV4dC1wcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6YmctcHJpbWFyeS81IGhvdmVyOnRleHQtcHJpbWFyeVwiLFxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8Y2hpbGRSb3V0ZS5pY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPnt0KGNoaWxkUm91dGUubGFiZWwpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgIHsvKiBXaGVuIGNvbGxhcHNlZCwgc2hvdyBjaGlsZCByb3V0ZXMgYXMgc2VwYXJhdGUgaXRlbXMgKi99XG4gICAgICAgICAgICAgICAge2lzQ29sbGFwc2VkICYmIHJvdXRlLmNoaWxkcmVuLm1hcCgoY2hpbGRSb3V0ZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAga2V5PXtjaGlsZFJvdXRlLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2NoaWxkUm91dGUuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTAgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCIsXG4gICAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWUgPT09IGNoaWxkUm91dGUuaHJlZiB8fCBwYXRobmFtZS5zdGFydHNXaXRoKGAke2NoaWxkUm91dGUuaHJlZn0vYClcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5LzEwIHRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkvNSBob3Zlcjp0ZXh0LXByaW1hcnlcIixcbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGNoaWxkUm91dGUuaWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFJlZ3VsYXIgcm91dGUgd2l0aG91dCBjaGlsZHJlblxuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBrZXk9e3JvdXRlLmhyZWZ9XG4gICAgICAgICAgICAgIGhyZWY9e3JvdXRlLmhyZWZ9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgXCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTMgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc1wiLFxuICAgICAgICAgICAgICAgIHBhdGhuYW1lID09PSByb3V0ZS5ocmVmIHx8IHBhdGhuYW1lLnN0YXJ0c1dpdGgoYCR7cm91dGUuaHJlZn0vYClcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5LzEwIHRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICA6IFwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkvNSBob3Zlcjp0ZXh0LXByaW1hcnlcIixcbiAgICAgICAgICAgICAgICBpc0NvbGxhcHNlZCAmJiBcImp1c3RpZnktY2VudGVyIHB4LTBcIixcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHJvdXRlLmljb24gY2xhc3NOYW1lPXtjbihcImgtNSB3LTVcIiwgaXNDb2xsYXBzZWQgPyBcIm1yLTBcIiA6IFwibXItM1wiKX0gLz5cbiAgICAgICAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiA8c3Bhbj57dChyb3V0ZS5sYWJlbCl9PC9zcGFuPn1cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICApO1xuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiY24iLCJCdXR0b24iLCJ1c2VQYXRobmFtZSIsIkxpbmsiLCJCYXJDaGFydDMiLCJVc2VycyIsIk1lc3NhZ2VTcXVhcmUiLCJNZWdhcGhvbmUiLCJVc2VyQ29nIiwiSG9tZSIsIlNldHRpbmdzIiwiVXNlciIsIk1lbnUiLCJYIiwiQ2FsZW5kYXIiLCJCb3QiLCJTaGllbGRBbGVydCIsIkZpbGVUZXh0IiwidXNlU3RhdGUiLCJ1c2VUcmFuc2xhdGlvbiIsInJvdXRlcyIsImxhYmVsIiwiaWNvbiIsImhyZWYiLCJyb2xlcyIsImNoaWxkcmVuIiwiU2lkZWJhciIsInVzZXJSb2xlIiwicGF0aG5hbWUiLCJpc0NvbGxhcHNlZCIsInNldElzQ29sbGFwc2VkIiwidCIsImZpbHRlcmVkUm91dGVzIiwiZmlsdGVyIiwicm91dGUiLCJpbmNsdWRlcyIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwic3BhbiIsIm1hcCIsImNoaWxkUm91dGUiLCJzdGFydHNXaXRoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});