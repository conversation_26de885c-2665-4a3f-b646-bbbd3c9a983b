(()=>{var e={};e.id=9735,e.ids=[9735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12484:(e,r,t)=>{"use strict";t.d(r,{ES:()=>o,eo:()=>s});let s=["ar"],o={ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"الذكاء الاصطناعي","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","properties.title":"العقارات","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.add":"إضافة عقار","properties.create":"إنشاء عقار جديد","properties.edit":"تعديل العقار","properties.delete":"حذف العقار","properties.view":"عرض العقار","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.currency":"العملة","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.features":"المميزات","property.amenities":"الخدمات","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","validation.required":"هذا الحقل مطلوب","validation.email":"يرجى إدخال بريد إلكتروني صحيح","validation.minLength":"يجب أن يكون الحد الأدنى {{min}} أحرف","validation.maxLength":"يجب أن يكون الحد الأقصى {{max}} أحرف","validation.number":"يرجى إدخال رقم صحيح","validation.positive":"يجب أن يكون الرقم أكبر من الصفر","images.upload":"رفع الصور","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت"}}}},14218:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=t(65239),o=t(48088),a=t(88170),i=t.n(a),p=t(30893),n={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>p[e]);t.d(r,n);let d={children:["",{children:["access-denied",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,33896)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\access-denied\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\access-denied\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/access-denied/page",pathname:"/access-denied",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33896:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\access-denied\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\access-denied\\page.tsx","default")},41705:(e,r,t)=>{Promise.resolve().then(t.bind(t,33896))},59042:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),o=t(43210),a=t(68082),i=t(24934),p=t(16189),n=t(72840),d=t(28559),l=t(32192),c=t(99208);function u(){let{t:e}=(0,a.B)(),r=(0,p.useRouter)(),{data:t,status:u}=(0,c.wV)(),[m,y]=(0,o.useState)("loading"===u);return m?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold mb-2",children:e("common.loading")})]}):(0,s.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-6 bg-card rounded-lg shadow-xl text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)(n.A,{className:"h-16 w-16 text-destructive"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:e("auth.accessDenied")}),(0,s.jsx)("p",{className:"text-muted-foreground mb-6",children:"authenticated"===u?e("auth.insufficientPermissions",{role:t?.user?.role||"user"}):e("auth.notAuthenticated")}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e("auth.contactAdminForAccess")}),(0,s.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,s.jsxs)(i.$,{onClick:()=>{r.back()},variant:"outline",className:"w-full",children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),e("common.goBack")]}),(0,s.jsxs)(i.$,{onClick:()=>{r.push("/")},variant:"default",className:"w-full",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),e("common.goToHomePage")]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68082:(e,r,t)=>{"use strict";t.d(r,{B:()=>i});var s=t(46755),o=t(16457),a=t(12484);function i(){return(0,o.Bd)()}s.Ay.use(o.r9).init({lng:"en",fallbackLng:"en",resources:a.ES,interpolation:{escapeValue:!1}})},72840:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]])},83561:(e,r,t)=>{Promise.resolve().then(t.bind(t,59042))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,8157,3903,4088],()=>t(14218));module.exports=s})();