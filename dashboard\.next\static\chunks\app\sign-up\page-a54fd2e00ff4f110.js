(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3884],{27971:(e,r,t)=>{"use strict";t.d(r,{U:()=>l});var s=t(95155);t(12115);var a=t(51362),i=t(97168),o=t(62098),n=t(93509);function l(){let{setTheme:e,theme:r}=(0,a.D)();return(0,s.jsxs)(i.$,{variant:"ghost",size:"icon",onClick:()=>{e("light"===r?"dark":"light")},children:[(0,s.jsx)(o.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(n.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}},50980:(e,r,t)=>{Promise.resolve().then(t.bind(t,88438))},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(52596),a=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},54762:(e,r,t)=>{"use strict";t.d(r,{ES:()=>a,eo:()=>s});let s=["ar"],a={ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"الذكاء الاصطناعي","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","properties.title":"العقارات","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.add":"إضافة عقار","properties.create":"إنشاء عقار جديد","properties.edit":"تعديل العقار","properties.delete":"حذف العقار","properties.view":"عرض العقار","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.currency":"العملة","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.features":"المميزات","property.amenities":"الخدمات","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","validation.required":"هذا الحقل مطلوب","validation.email":"يرجى إدخال بريد إلكتروني صحيح","validation.minLength":"يجب أن يكون الحد الأدنى {{min}} أحرف","validation.maxLength":"يجب أن يكون الحد الأقصى {{max}} أحرف","validation.number":"يرجى إدخال رقم صحيح","validation.positive":"يجب أن يكون الرقم أكبر من الصفر","images.upload":"رفع الصور","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت"}}}},72280:(e,r,t)=>{"use strict";t.d(r,{B:()=>o});var s=t(17985),a=t(91218),i=t(54762);function o(){return(0,a.Bd)()}s.Ay.use(a.r9).init({lng:"en",fallbackLng:"en",resources:i.ES,interpolation:{escapeValue:!1}})},73911:(e,r,t)=>{"use strict";t.d(r,{c:()=>n});var s=t(95155),a=t(72280),i=t(97168),o=t(34869);function n(){let{i18n:e}=(0,a.B)(),r=r=>{e.changeLanguage(r),document.documentElement.dir="ar"===r?"rtl":"ltr"};return(0,s.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>{r("en"===e.language?"ar":"en")},title:"en"===e.language?"Switch to Arabic":"Switch to English",children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}},78519:(e,r,t)=>{"use strict";t.d(r,{F:()=>F});var s=t(95155),a=t(54785),i=t(54416),o=t(51154),n=t(42355),l=t(13052),d=t(74126),c=t(381),p=t(71007),u=t(84616),m=t(1243),g=t(92138),h=t(94788),f=t(62530),y=t(30130),x=t(93509),v=t(42148),b=t(18175),w=t(5196),j=t(44020),N=t(99890),A=t(27213),k=t(57434),C=t(81586),R=t(59099),S=t(57340),z=t(34835),P=t(9428);let F={logo:a.A,close:i.A,spinner:o.A,chevronLeft:n.A,chevronRight:l.A,trash:d.A,settings:c.A,user:p.A,add:u.A,warning:m.A,arrowRight:g.A,help:h.A,pizza:f.A,sun:y.A,moon:x.A,laptop:v.A,gitHub:R.A,twitter:b.A,check:w.A,more:j.A,page:N.A,media:A.A,post:k.A,billing:C.A,ellipsis:j.A,google:e=>{let{...r}=e;return(0,s.jsx)("svg",{"aria-hidden":"true",focusable:"false","data-prefix":"fab","data-icon":"google",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 488 512",...r,children:(0,s.jsx)("path",{fill:"currentColor",d:"M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"})})},home:S.A,logOut:z.A,circle:P.A}},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(95155),a=t(12115),i=t(40968),o=t(74466),n=t(53999);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(i.b,{ref:r,className:(0,n.cn)(l(),t),...a})});d.displayName=i.b.displayName},88438:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w});var s=t(95155),a=t(12115),i=t(72280),o=t(35695),n=t(45493),l=t(62177),d=t(90221),c=t(55594),p=t(97168),u=t(89852),m=t(82714),g=t(78519),h=t(56671);let f=c.Ik({firstName:c.Yj().min(1),lastName:c.Yj().optional(),email:c.Yj().email(),password:c.Yj().min(6),confirmPassword:c.Yj().min(6)}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function y(){let{t:e}=(0,i.B)(),r=(0,o.useRouter)(),t=(0,o.useSearchParams)().get("redirect_url")||"/dashboard/analytics";console.log("Redirect URL:",t);let[c,y]=(0,a.useState)(!1),{register:x,handleSubmit:v,formState:{errors:b}}=(0,l.mN)({resolver:(0,d.u)(f)});async function w(s){y(!0);try{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:s.firstName,lastName:s.lastName,email:s.email,password:s.password})});if(!a.ok){let r=await a.json();h.oR.error(r.message||e("auth.registrationFailed")),y(!1);return}let i=await a.json();console.log("Registration response:",i);let o=await (0,n.Jv)("credentials",{email:s.email,password:s.password,redirect:!1,callbackUrl:t});if(null==o?void 0:o.error){h.oR.error(e("auth.signInAfterRegistrationFailed")),y(!1);return}h.oR.success(e("auth.registrationSuccessful")),r.push(t),r.refresh()}catch(r){console.error("Sign up error:",r),h.oR.error(e("common.somethingWentWrong")),y(!1)}}return(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsx)("form",{onSubmit:v(w),children:(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(m.J,{htmlFor:"firstName",children:e("auth.firstName")}),(0,s.jsx)(u.p,{id:"firstName",type:"text",autoCapitalize:"words",autoComplete:"given-name",disabled:c,...x("firstName")}),b.firstName&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:b.firstName.message})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(m.J,{htmlFor:"lastName",children:e("auth.lastName")}),(0,s.jsx)(u.p,{id:"lastName",type:"text",autoCapitalize:"words",autoComplete:"family-name",disabled:c,...x("lastName")}),b.lastName&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:b.lastName.message})]})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(m.J,{htmlFor:"email",children:e("auth.email")}),(0,s.jsx)(u.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:c,...x("email")}),b.email&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:b.email.message})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(m.J,{htmlFor:"password",children:e("auth.password")}),(0,s.jsx)(u.p,{id:"password",type:"password",autoCapitalize:"none",autoComplete:"new-password",disabled:c,...x("password")}),b.password&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:b.password.message})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(m.J,{htmlFor:"confirmPassword",children:e("auth.confirmPassword")}),(0,s.jsx)(u.p,{id:"confirmPassword",type:"password",autoCapitalize:"none",autoComplete:"new-password",disabled:c,...x("confirmPassword")}),b.confirmPassword&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:b.confirmPassword.message})]}),(0,s.jsxs)(p.$,{type:"submit",disabled:c,children:[c&&(0,s.jsx)(g.F.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),e("auth.signUp")]})]})}),(0,s.jsxs)("div",{className:"text-center text-sm",children:[e("auth.alreadyHaveAccount")," ",(0,s.jsx)(p.$,{variant:"link",className:"px-0",asChild:!0,children:(0,s.jsx)("a",{href:"/sign-in".concat(t?"?redirect_url=".concat(encodeURIComponent(t)):""),children:e("auth.signIn")})})]})]})}var x=t(73911),v=t(27971);function b(){let{t:e}=(0,i.B)();return(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,s.jsx)(g.F.logo,{className:"mx-auto h-6 w-6"}),(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:e("auth.createAccount")}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e("auth.enterDetailsToCreateAccount")})]}),(0,s.jsx)(y,{}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(x.c,{}),(0,s.jsx)(v.U,{})]})]})})}function w(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}),children:(0,s.jsx)(b,{})})}},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(95155),a=t(12115),i=t(53999);let o=a.forwardRef((e,r)=>{let{className:t,type:a,...o}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...o})});o.displayName="Input"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>l});var s=t(95155),a=t(12115),i=t(99708),o=t(74466),n=t(53999);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:o,asChild:d=!1,...c}=e,p=d?i.DX:"button";return(0,s.jsx)(p,{className:(0,n.cn)(l({variant:a,size:o,className:t})),ref:r,...c})});d.displayName="Button"}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,5493,3692,7322,8896,8441,1684,7358],()=>r(50980)),_N_E=e.O()}]);