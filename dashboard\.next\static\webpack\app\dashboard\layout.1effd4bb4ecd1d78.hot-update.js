"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/topbar.tsx":
/*!*******************************!*\
  !*** ./components/topbar.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Topbar: () => (/* binding */ Topbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Moon,ShieldAlert,Sun,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _lib_i18n_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/i18n/client */ \"(app-pages-browser)/./lib/i18n/client.ts\");\n/* harmony import */ var _lib_i18n_settings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/i18n/settings */ \"(app-pages-browser)/./lib/i18n/settings.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Topbar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Topbar(param) {\n    let { user } = param;\n    _s();\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const { i18n, t } = (0,_lib_i18n_client__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const changeLanguage = (lng)=>{\n        i18n.changeLanguage(lng);\n        document.documentElement.dir = lng === \"ar\" ? \"rtl\" : \"ltr\";\n    };\n    // Get user's display name\n    const userName = user ? user.firstName && user.lastName ? \"\".concat(user.firstName, \" \").concat(user.lastName) : user.email.split('@')[0] : 'User';\n    // Get user's initials for avatar\n    const userInitials = user ? user.firstName && user.lastName ? \"\".concat(user.firstName[0]).concat(user.lastName[0]).toUpperCase() : user.email[0].toUpperCase() : 'U';\n    // Handle logout\n    const handleLogout = async ()=>{\n        try {\n            const response = await fetch('/api/auth/logout', {\n                method: 'POST'\n            });\n            if (response.ok) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.toast)({\n                    title: t('auth.logoutSuccess'),\n                    description: t('auth.redirectingToLogin')\n                });\n                router.push('/sign-in');\n            } else {\n                throw new Error('Logout failed');\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.toast)({\n                variant: 'destructive',\n                title: t('auth.logoutFailed'),\n                description: error instanceof Error ? error.message : String(error)\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-16 border-b bg-card flex items-center px-6 justify-between\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                        variant: \"outline\",\n                        className: \"flex items-center gap-1 px-3 py-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-3.5 w-3.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"capitalize\",\n                                children: user.role.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block text-sm mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-muted-foreground\",\n                                children: \"أهلاً وسهلاً،\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: userName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                align: \"end\",\n                                children: _lib_i18n_settings__WEBPACK_IMPORTED_MODULE_6__.languages.map((lng)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>changeLanguage(lng),\n                                        className: \"cursor-pointer\",\n                                        children: lng === \"en\" ? \"English\" : \"العربية\"\n                                    }, lng, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Toggle theme\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                align: \"end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>setTheme(\"light\"),\n                                        children: \"فاتح\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>setTheme(\"dark\"),\n                                        children: \"داكن\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>setTheme(\"system\"),\n                                        children: \"النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"relative h-9 w-9 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                src: (user === null || user === void 0 ? void 0 : user.profileImage) || \"\",\n                                                alt: userName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                children: userInitials\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                align: \"end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        className: \"cursor-default\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: userName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: handleLogout,\n                                        className: \"cursor-pointer text-destructive\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Moon_ShieldAlert_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('auth.logout')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\topbar.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(Topbar, \"QVPpSZP5zoxuJBp8oG1FPJGt63o=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme,\n        _lib_i18n_client__WEBPACK_IMPORTED_MODULE_5__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = Topbar;\nvar _c;\n$RefreshReg$(_c, \"Topbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/topbar.tsx\n"));

/***/ })

});