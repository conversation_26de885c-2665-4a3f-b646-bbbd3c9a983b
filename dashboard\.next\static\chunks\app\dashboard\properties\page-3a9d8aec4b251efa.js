(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3711],{10996:(e,t,r)=>{"use strict";function a(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ar-AE",a=t&&3===t.length?t.toUpperCase():"USD";if("number"!=typeof e||isNaN(e))return"0 "+a;try{return new Intl.NumberFormat(r,{style:"currency",currency:a,minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}catch(t){try{return new Intl.NumberFormat(r,{minimumFractionDigits:0,maximumFractionDigits:0}).format(e)+" "+a}catch(t){return e.toLocaleString()+" "+a}}}function s(e,t){return a(e,t,"ar-AE")}r.d(t,{$g:()=>a,PW:()=>s})},13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},32002:(e,t,r)=>{Promise.resolve().then(r.bind(r,63098))},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},45300:(e,t,r)=>{"use strict";function a(){["__next_hmr_refresh_hash__","__clerk_db_jwt_NFfxy5s4","__refresh_NFfxy5s4","__session_NFfxy5s4","__client_uat_NFfxy5s4","__clerk_db_jwt_TYLMw0H7","__refresh_TYLMw0H7","__session_TYLMw0H7","__client_uat_TYLMw0H7","__clerk_db_jwt","__clerk_db_jwt_kCaGdcWF","__client_uat_kCaGdcWF","__client_uat","NEXT_LOCALE","authjs.csrf-token","authjs.callback-url"].forEach(e=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"),[window.location.hostname,".".concat(window.location.hostname),"localhost",".localhost"].forEach(t=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=").concat(t,";")})}),document.cookie="language=ar; path=/; max-age=31536000",console.log("\uD83E\uDDF9 Cookies cleaned up for Arabic Properties system")}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a]}r.d(t,{Ei:()=>s,ss:()=>a})},47924:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(52596),s=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63098:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(95155),s=r(12115),i=r(35695),l=r(84616),n=r(47924),o=r(19946);let d=(0,o.A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),c=(0,o.A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var u=r(92657),p=r(13717),m=r(62525),h=r(97168),x=r(89852),f=r(88482),y=r(88145),g=r(95784),b=r(86132),v=r(10996);function j(){let e=(0,i.useRouter)(),{language:t}=(0,b.Y)(),[r,o]=(0,s.useState)([]),[j,N]=(0,s.useState)(null),[w,A]=(0,s.useState)(!0),[k,E]=(0,s.useState)("grid"),[_,S]=(0,s.useState)(""),[L,C]=(0,s.useState)("الكل"),[P,R]=(0,s.useState)("الكل");(0,s.useEffect)(()=>{D(),T()},[_,L,P]);let D=async()=>{try{A(!0);let e=new URLSearchParams;_&&e.append("search",_),L&&"الكل"!==L&&"ALL"!==L&&e.append("type",L),P&&"الكل"!==P&&"ALL"!==P&&e.append("status",P),e.append("isActive","true");let t=await fetch("/api/v1/properties?".concat(e));if(t.ok){let e=await t.json();if(e.success&&e.data.properties&&e.data.properties.length>0){o(e.data.properties),console.log("API data loaded:",e.data.properties.length,"properties");return}}console.log("Using mock data (API status:",t.status,")"),o([{id:"prop-available-1",title:"Luxury Villa in Dubai Marina",titleAr:"فيلا فاخرة في دبي مارينا",description:"Beautiful 4-bedroom villa with sea view",descriptionAr:"فيلا جميلة من 4 غرف نوم مع إطلالة على البحر",price:25e5,currency:"AED",type:"VILLA",status:"AVAILABLE",bedrooms:4,bathrooms:3,area:350,location:"Dubai Marina",locationAr:"دبي مارينا",address:"123 Marina Walk",addressAr:"123 ممشى المارينا",city:"Dubai",cityAr:"دبي",images:["https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop"],features:["Swimming Pool","Gym","Parking"],featuresAr:["مسبح","صالة رياضية","موقف سيارات"],amenities:["24/7 Security","Concierge"],amenitiesAr:["أمن 24/7","خدمة الكونسيرج"],isFeatured:!0,isActive:!0,viewCount:125,createdAt:new Date().toISOString()},{id:"prop-sold-1",title:"SOLD - Apartment in Burj Khalifa",titleAr:"مباع - شقة في برج خليفة",description:"Luxury apartment that was recently sold",descriptionAr:"شقة فاخرة تم بيعها مؤخراً",price:32e5,currency:"AED",type:"APARTMENT",status:"SOLD",bedrooms:3,bathrooms:2,area:180,location:"Downtown Dubai",locationAr:"وسط مدينة دبي",address:"Burj Khalifa, Floor 45",addressAr:"برج خليفة، الطابق 45",city:"Dubai",cityAr:"دبي",images:["https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop"],features:["Burj Khalifa View","High Speed Elevator"],featuresAr:["إطلالة على برج خليفة","مصعد عالي السرعة"],amenities:["Gym","Pool","Spa"],amenitiesAr:["صالة رياضية","مسبح","سبا"],isFeatured:!1,isActive:!1,viewCount:89,createdAt:new Date().toISOString()},{id:"prop-rented-1",title:"RENTED - Family House in Jumeirah",titleAr:"مؤجر - بيت عائلي في جميرا",description:"Family house currently rented",descriptionAr:"بيت عائلي مؤجر حالياً",price:18e4,currency:"AED",type:"HOUSE",status:"RENTED",bedrooms:5,bathrooms:4,area:280,location:"Jumeirah",locationAr:"جميرا",address:"456 Jumeirah Street",addressAr:"456 شارع جميرا",city:"Dubai",cityAr:"دبي",images:["https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop"],features:["Large Garden","Swimming Pool","3 Car Parking"],featuresAr:["حديقة كبيرة","مسبح","موقف 3 سيارات"],amenities:["Near Schools","Quiet Area"],amenitiesAr:["قريب من المدارس","منطقة هادئة"],isFeatured:!0,isActive:!0,viewCount:67,createdAt:new Date().toISOString()}])}catch(e){console.error("Error fetching properties:",e),o([])}finally{A(!1)}},T=async()=>{try{let e=await fetch("/api/v1/properties/stats");if(e.ok){let t=await e.json();if(t.success&&t.data){N(t.data),console.log("API stats loaded");return}}console.log("Using mock stats (API status:",e.status,")"),N({total:3,available:1,sold:1,rented:1,featured:2,byType:{APARTMENT:1,VILLA:1,HOUSE:1,TOWNHOUSE:0,PENTHOUSE:0,STUDIO:0,OFFICE:0,SHOP:0,WAREHOUSE:0,LAND:0,BUILDING:0}})}catch(e){console.error("Error fetching stats:",e),N({total:0,available:0,sold:0,rented:0,featured:0,byType:{APARTMENT:0,VILLA:0,TOWNHOUSE:0,PENTHOUSE:0,STUDIO:0,OFFICE:0,SHOP:0,WAREHOUSE:0,LAND:0,BUILDING:0}})}},F=async e=>{if(confirm("ar"===t?"هل أنت متأكد من حذف هذا العقار؟":"Are you sure you want to delete this property?"))try{(await fetch("/api/v1/properties/".concat(e),{method:"DELETE"})).ok&&(D(),T())}catch(e){console.error("Error deleting property:",e)}},M=(e,r)=>(0,v.$g)(e,r,"ar"===t?"ar-AE":"en-US"),I=e=>{switch(e){case"AVAILABLE":return"bg-green-600 text-white";case"SOLD":return"bg-red-600 text-white";case"RENTED":return"bg-blue-600 text-white";case"PENDING":return"bg-yellow-600 text-black";case"RESERVED":return"bg-orange-600 text-white";default:return"bg-gray-600 text-white"}},O=e=>{switch(e){case"AVAILABLE":return"متاح";case"SOLD":return"مباع";case"RENTED":return"مؤجر";case"PENDING":return"قيد المراجعة";case"RESERVED":return"محجوز";default:return e}},U=e=>"ar"===t&&e.titleAr?e.titleAr:e.title,z=e=>"ar"===t&&e.locationAr?e.locationAr:e.location;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"ar"===t?"العقارات":"Properties"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"ar"===t?"إدارة العقارات والممتلكات":"Manage properties and real estate"})]}),(0,a.jsxs)(h.$,{onClick:()=>e.push("/dashboard/properties/create"),className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"ar"===t?"إضافة عقار":"Add Property"]})]}),j&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsxs)(f.Zp,{children:[(0,a.jsx)(f.aR,{className:"pb-2",children:(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===t?"إجمالي العقارات":"Total Properties"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:j.total})})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsx)(f.aR,{className:"pb-2",children:(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===t?"متاح":"Available"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:j.available})})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsx)(f.aR,{className:"pb-2",children:(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===t?"مباع":"Sold"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:j.sold})})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsx)(f.aR,{className:"pb-2",children:(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===t?"مؤجر":"Rented"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:j.rented})})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsx)(f.aR,{className:"pb-2",children:(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===t?"مميز":"Featured"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:j.featured})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(x.p,{placeholder:"ar"===t?"البحث في العقارات...":"Search properties...",value:_,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(g.l6,{value:L,onValueChange:C,children:[(0,a.jsx)(g.bq,{className:"w-full sm:w-48",children:(0,a.jsx)(g.yv,{placeholder:"ar"===t?"نوع العقار":"Property Type"})}),(0,a.jsxs)(g.gC,{children:[(0,a.jsx)(g.eb,{value:"الكل",children:"جميع الأنواع"}),(0,a.jsx)(g.eb,{value:"APARTMENT",children:"شقة"}),(0,a.jsx)(g.eb,{value:"VILLA",children:"فيلا"}),(0,a.jsx)(g.eb,{value:"TOWNHOUSE",children:"تاون هاوس"}),(0,a.jsx)(g.eb,{value:"OFFICE",children:"مكتب"})]})]}),(0,a.jsxs)(g.l6,{value:P,onValueChange:R,children:[(0,a.jsx)(g.bq,{className:"w-full sm:w-48",children:(0,a.jsx)(g.yv,{placeholder:"ar"===t?"الحالة":"Status"})}),(0,a.jsxs)(g.gC,{children:[(0,a.jsx)(g.eb,{value:"الكل",children:"جميع الحالات"}),(0,a.jsx)(g.eb,{value:"AVAILABLE",children:"متاح"}),(0,a.jsx)(g.eb,{value:"SOLD",children:"مباع"}),(0,a.jsx)(g.eb,{value:"RENTED",children:"مؤجر"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(h.$,{variant:"grid"===k?"default":"outline",size:"sm",onClick:()=>E("grid"),children:(0,a.jsx)(d,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{variant:"list"===k?"default":"outline",size:"sm",onClick:()=>E("list"),children:(0,a.jsx)(c,{className:"h-4 w-4"})})]})]}),w?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"})}):(0,a.jsx)("div",{className:"grid"===k?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:r.map(r=>(0,a.jsx)(f.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:"grid"===k?(0,a.jsxs)(a.Fragment,{children:[r.images.length>0&&(0,a.jsxs)("div",{className:"relative h-48 bg-gray-200 dark:bg-gray-700",children:[(0,a.jsx)("img",{src:r.images[0],alt:U(r),className:"w-full h-full object-cover"}),r.isFeatured&&(0,a.jsx)(y.E,{className:"absolute top-2 left-2 bg-purple-600",children:"ar"===t?"مميز":"Featured"})]}),(0,a.jsxs)(f.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg truncate",children:U(r)}),(0,a.jsx)(y.E,{className:I(r.status),children:O(r.status)})]}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-2",children:z(r)}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-blue-600",children:M(r.price,r.currency)}),(0,a.jsxs)("div",{className:"flex gap-2 text-sm text-gray-500",children:[r.bedrooms&&(0,a.jsxs)("span",{children:[r.bedrooms," ","ar"===t?"غرف":"bed"]}),r.bathrooms&&(0,a.jsxs)("span",{children:[r.bathrooms," ","ar"===t?"حمام":"bath"]}),r.area&&(0,a.jsxs)("span",{children:[r.area,"m\xb2"]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>e.push("/dashboard/properties/".concat(r.id)),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>e.push("/dashboard/properties/".concat(r.id,"/edit")),className:"hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>F(r.id),children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["ar"===t?"المشاهدات:":"Views:"," ",r.viewCount]})]})]})]}):(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex gap-4",children:[r.images.length>0&&(0,a.jsx)("div",{className:"relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded",children:(0,a.jsx)("img",{src:r.images[0],alt:U(r),className:"w-full h-full object-cover rounded"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:U(r)}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.E,{className:I(r.status),children:O(r.status)}),r.isFeatured&&(0,a.jsx)(y.E,{className:"bg-purple-600",children:"ar"===t?"مميز":"Featured"})]})]}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-2",children:z(r)}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-blue-600",children:M(r.price,r.currency)}),(0,a.jsxs)("div",{className:"flex gap-4 text-sm text-gray-500",children:[r.bedrooms&&(0,a.jsxs)("span",{children:[r.bedrooms," ","ar"===t?"غرف":"bed"]}),r.bathrooms&&(0,a.jsxs)("span",{children:[r.bathrooms," ","ar"===t?"حمام":"bath"]}),r.area&&(0,a.jsxs)("span",{children:[r.area,"m\xb2"]}),(0,a.jsxs)("span",{children:["ar"===t?"المشاهدات:":"Views:"," ",r.viewCount]})]}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>e.push("/dashboard/properties/".concat(r.id)),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>e.push("/dashboard/properties/".concat(r.id,"/edit")),className:"hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>F(r.id),children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]})]})]})]})})},r.id))})]})}},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},86132:(e,t,r)=>{"use strict";r.d(t,{Y:()=>i});var a=r(12115),s=r(45300);function i(){let[e]=(0,a.useState)("ar");(0,a.useEffect)(()=>{(0,s.ss)(),localStorage.setItem("properties-language","ar"),(0,s.Ei)("\uD83C\uDFE0 Arabic Properties system initialized")},[]),(0,a.useEffect)(()=>{document.documentElement.lang="ar",document.documentElement.dir="rtl",document.documentElement.className="rtl arabic-interface",document.body.style.fontFamily="'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif",(0,s.Ei)("\uD83C\uDF10 Arabic language interface active")},[]);let t={"properties.create":"إنشاء عقار جديد","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت","validation.required":"هذا الحقل مطلوب","validation.positive":"يجب أن يكون الرقم أكبر من الصفر"};return{language:e,setLanguage:()=>{},isRTL:!0,isArabic:!0,isEnglish:!1,t:e=>{let r=t[e];return r||(console.warn("Missing translation for: ".concat(e)),e)}}}},88145:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(95155);r(12115);var s=r(74466),i=r(53999);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)(l({variant:r}),t),...s})}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>u});var a=r(95155),s=r(12115),i=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});l.displayName="Card";let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...s})});n.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var a=r(95155),s=r(12115),i=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,type:s,...l}=e;return(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...l})});l.displayName="Input"},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95784:(e,t,r)=>{"use strict";r.d(t,{bq:()=>p,eb:()=>f,gC:()=>x,l6:()=>c,yv:()=>u});var a=r(95155),s=r(12115),i=r(31992),l=r(66474),n=r(47863),o=r(5196),d=r(53999);let c=i.bL;i.YJ;let u=i.WT,p=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsxs)(i.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...n,children:[s,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=i.l9.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(i.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});m.displayName=i.PP.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(i.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})});h.displayName=i.wn.displayName;let x=s.forwardRef((e,t)=>{let{className:r,children:s,position:l="popper",...n}=e;return(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:l,...n,children:[(0,a.jsx)(m,{}),(0,a.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});x.displayName=i.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(i.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=i.JU.displayName;let f=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(i.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:s})]})});f.displayName=i.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(i.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=i.wv.displayName},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var a=r(95155),s=r(12115),i=r(99708),l=r(74466),n=r(53999);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:d=!1,...c}=e,u=d?i.DX:"button";return(0,a.jsx)(u,{className:(0,n.cn)(o({variant:s,size:l,className:r})),ref:t,...c})});d.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6071,9509,9855,8441,1684,7358],()=>t(32002)),_N_E=e.O()}]);