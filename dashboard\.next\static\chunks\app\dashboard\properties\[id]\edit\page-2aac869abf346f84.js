(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3580],{31041:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(95155),a=s(12115),i=s(35695),c=s(86132),n=s(78137),l=s(83485),d=s(97168),o=s(92138),m=s(53580);function h(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),{t:s}=(0,c.Y)(),{toast:h}=(0,m.dj)(),[p,x]=(0,a.useState)(null),[u,y]=(0,a.useState)(!0),f=e.id;(0,a.useEffect)(()=>{f&&j()},[f]);let j=async()=>{try{y(!0);let e=await n.Jf.getPropertyById(f);x(e)}catch(e){console.error("Error fetching property:",e),h({title:"خطأ في تحميل العقار",description:"حدث خطأ أثناء تحميل بيانات العقار",variant:"destructive"})}finally{y(!1)}},N=()=>{t.push("/dashboard/properties/".concat(f))};if(u)return(0,r.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[(0,r.jsx)("div",{className:"loading-spinner"}),(0,r.jsx)("span",{className:"mr-3 text-lg",children:"جاري تحميل العقار..."})]})})});if(!p)return(0,r.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"العقار غير موجود"}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"لم يتم العثور على العقار المطلوب"}),(0,r.jsxs)(d.$,{onClick:N,className:"btn-primary",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 ml-2"}),"العودة إلى قائمة العقارات"]})]})})});let b={title:p.title,description:p.description,price:p.price,currency:p.currency,type:p.type,status:p.status,bedrooms:p.bedrooms||0,bathrooms:p.bathrooms||0,area:p.area||0,location:p.location,address:p.address,city:p.city,country:p.country,images:p.images,features:p.features,amenities:p.amenities,yearBuilt:p.yearBuilt,parking:p.parking,furnished:p.furnished,petFriendly:p.petFriendly,utilities:p.utilities,contactInfo:p.contactInfo,agentId:p.agentId,isActive:p.isActive,isFeatured:p.isFeatured};return(0,r.jsx)("div",{className:"min-h-screen property-form-dark rtl arabic-text",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,r.jsxs)("div",{className:"property-header-dark mb-6",children:[(0,r.jsxs)(d.$,{onClick:N,variant:"ghost",className:"text-gray-400 hover:text-white mb-4",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 ml-2"}),"العودة إلى العقار"]}),(0,r.jsx)("h1",{className:"arabic-heading text-3xl font-bold text-white",children:"تعديل العقار"}),(0,r.jsxs)("p",{className:"text-gray-400 mt-2",children:["تحديث معلومات العقار: ",p.title]})]}),(0,r.jsx)(l.o,{initialData:b,isEdit:!0,propertyId:f,onSuccess:()=>{h({title:"تم تحديث العقار",description:"تم تحديث العقار بنجاح"}),t.push("/dashboard/properties/".concat(f))},onCancel:()=>{t.push("/dashboard/properties/".concat(f))}})]})})}s(6721)},92138:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},95327:(e,t,s)=>{Promise.resolve().then(s.bind(s,31041))}},e=>{var t=t=>e(e.s=t);e.O(0,[1368,4277,6071,9509,3464,9855,6738,446,3485,8441,1684,7358],()=>t(95327)),_N_E=e.O()}]);