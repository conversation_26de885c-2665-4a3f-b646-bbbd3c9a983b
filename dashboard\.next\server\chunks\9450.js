"use strict";exports.id=9450,exports.ids=[9450],exports.modules={4068:(e,t,a)=>{a.d(t,{U:()=>p});var r=a(43210),l=a(59556);let n=e=>!!e&&/[\u0600-\u06FF]/.test(e),o=e=>"ar"===e.language||e.id&&e.id.includes("_ar")||e.name&&n(e.name)||e.content&&n(e.content)?"ar":"en",s=e=>"ar"===e||"en"===e?e:"en",i={en:[{id:"hello_world_en",name:"Hello World",content:"Hello, {{1}}! Welcome to our service.",category:"greeting",variables:["name"],language:"en",createdAt:new Date().toISOString()},{id:"appointment_reminder_en",name:"Appointment Reminder",content:"Hi {{1}}, this is a reminder about your appointment on {{2}} at {{3}}.",category:"reminder",variables:["name","date","time"],language:"en",createdAt:new Date().toISOString()}],ar:[{id:"hello_world_ar",name:"مرحبا بالعالم",content:"مرحبًا، {{1}}! مرحبًا بك في خدمتنا.",category:"greeting",variables:["name"],language:"ar",createdAt:new Date().toISOString()},{id:"appointment_reminder_ar",name:"تذكير بالموعد",content:"مرحبًا {{1}}، هذا تذكير بموعدك في {{2}} الساعة {{3}}.",category:"reminder",variables:["name","date","time"],language:"ar",createdAt:new Date().toISOString()}]},c=async(e="en")=>{try{if("all"===e)try{let e=await l.A.get("/marketing/templates",{params:{language:"en"}}),t=await l.A.get("/marketing/templates",{params:{language:"ar"}});return[...e,...t].map(e=>{let t=Array.isArray(e.variables)?e.variables:[],a=o(e);return{...e,language:a,variables:t,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}})}catch(e){if(e.message&&e.message.includes("Network Error"))return console.warn("Network error detected, using mock templates"),[...i.en,...i.ar];throw e}let t=s(e);try{return(await l.A.get("/marketing/templates",{params:{language:t}})).map(e=>{let t=Array.isArray(e.variables)?e.variables:[],a=o(e);return{...e,language:a,variables:t,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}})}catch(e){if(e.message&&e.message.includes("Network Error"))return console.warn("Network error detected, using mock templates"),i[t]||[];throw e}}catch(e){throw console.error("Error fetching templates:",e),e}},d=async e=>{try{let t=s(e.language),a={...e,language:t,name:e.name.trim(),variables:Array.isArray(e.variables)?e.variables:[]};return await l.A.post("/marketing/templates",a)}catch(e){throw console.error("Error creating template:",e),e}},g=async(e,t)=>{try{let a={...t};return t.language&&(a.language=s(t.language)),t.name&&(a.name=t.name.trim()),t.variables&&(a.variables=Array.isArray(t.variables)?t.variables:[]),await l.A.put(`/marketing/templates/${e}`,a)}catch(t){throw console.error(`Error updating template with ID ${e}:`,t),t}},u=async e=>{try{if(!e)throw console.error("Invalid template ID for deletion:",e),Error("Invalid template ID");let t=e.trim();return console.log(`Deleting template with ID: ${t}`),await l.A.delete(`/marketing/templates/${t}`),console.log(`Template with ID ${t} deleted successfully`),!0}catch(t){if(console.error(`Error deleting template with ID ${e}:`,t),t.response&&404===t.response.status)return console.warn(`Template with ID ${e} not found, it may have been already deleted`),!0;if(t.response&&t.response.data&&t.response.data.error)throw console.error(`Server error: ${t.response.data.error}`),Error(t.response.data.error);throw t}};var m=a(70333);function p(){let[e,t]=(0,r.useState)([]),[a,l]=(0,r.useState)(!0),[n,o]=(0,r.useState)(null),[s,i]=(0,r.useState)({}),[p,y]=(0,r.useState)(!1),[w,h]=(0,r.useState)(null),{toast:A}=(0,m.dj)(),v=(0,r.useCallback)(async()=>{try{l(!0),o(null);let[e,a]=await Promise.all([c("en").catch(()=>[]),c("ar").catch(()=>[])]),r=[...e,...a].sort((e,t)=>e.createdAt&&t.createdAt?new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime():0);t(r)}catch(e){console.error("Error fetching templates:",e),o(e.message||"Failed to load templates"),t([])}finally{l(!1)}},[]),f=(0,r.useMemo)(()=>{let t=[...e];if(s.search){let e=s.search.toLowerCase();t=t.filter(t=>t.name.toLowerCase().includes(e)||t.content.toLowerCase().includes(e)||t.category&&t.category.toLowerCase().includes(e))}return s.category&&"all"!==s.category&&(t=t.filter(e=>e.category===s.category)),s.language&&"all"!==s.language&&(t=t.filter(e=>e.language===s.language)),t},[e,s]),S=(0,r.useCallback)(e=>{i(t=>({...t,...e}))},[]),b=(0,r.useCallback)(()=>{i({})},[]),k=(0,r.useCallback)(()=>{y(e=>!e)},[]),D=(0,r.useCallback)(()=>{v()},[v]),I=async e=>{try{l(!0);let a=await d(e);return t(e=>[a,...e]),A({title:"Success",description:"Template created successfully"}),!0}catch(e){return console.error("Error adding template:",e),o(e.message||"Failed to add template"),A({title:"Error",description:e.message||"Failed to create template",variant:"destructive"}),!1}finally{l(!1)}},E=async(e,a)=>{try{l(!0);let r=await g(e,a);return t(t=>t.map(t=>t.id===e?r:t)),A({title:"Success",description:"Template updated successfully"}),!0}catch(t){return console.error(`Error updating template with ID ${e}:`,t),o(t.message||"Failed to update template"),A({title:"Error",description:t.message||"Failed to update template",variant:"destructive"}),!1}finally{l(!1)}},C=async e=>{try{if(l(!0),o(null),await u(e))return t(t=>t.filter(t=>t.id!==e)),A({title:"Success",description:"Template deleted successfully"}),!0;throw Error("Failed to delete template")}catch(t){return console.error(`Error deleting template with ID ${e}:`,t),o(t.message||"Failed to delete template"),A({title:"Error",description:t.message||"Failed to delete template",variant:"destructive"}),!1}finally{l(!1)}},F=(0,r.useMemo)(()=>["all",...new Set(e.map(e=>e.category).filter(Boolean))],[e]),$=(0,r.useMemo)(()=>["all",...new Set(e.map(e=>e.language).filter(Boolean))],[e]);return{templates:f,allTemplates:e,loading:a,error:n,filters:s,updateFilters:S,clearFilters:b,categories:F,languages:$,autoRefresh:p,toggleAutoRefresh:k,refreshTemplates:D,addTemplate:I,editTemplate:E,removeTemplate:C,fetchTemplates:v,currentLanguage:"all",displayLanguage:"all",showAllLanguages:!0}}},41862:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},96474:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};