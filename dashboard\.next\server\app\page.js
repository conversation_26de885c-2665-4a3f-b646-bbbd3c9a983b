(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26527:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(60687);t(43210);var o=t(16189);function a(){return(0,o.useRouter)(),(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Welcome to Real Estate Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-8",children:"Redirecting to the appropriate page..."}),(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47053:(e,r,t)=>{Promise.resolve().then(t.bind(t,26527))},59192:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>p,routeModule:()=>c,tree:()=>u});var s=t(65239),o=t(48088),a=t(88170),n=t.n(a),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(r,i);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90597)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],p=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65613:(e,r,t)=>{Promise.resolve().then(t.bind(t,90597))},90597:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\page.tsx","default")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,8157,4088],()=>t(59192));module.exports=s})();