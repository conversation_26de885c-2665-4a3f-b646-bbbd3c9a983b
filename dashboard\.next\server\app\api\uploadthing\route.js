(()=>{var e={};e.id=7735,e.ids=[7735],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16593:(e,t,r)=>{"use strict";let i,n,s,a,o,l,u,c,h,p,f,d,m,g,b,x,y,v,S,_,w,k,O,C,E,I,F,T;r.r(t),r.d(t,{patchFetch:()=>Wc,routeModule:()=>Wa,serverHooks:()=>Wu,workAsyncStorage:()=>Wo,workUnitAsyncStorage:()=>Wl});var R,N,A={};r.r(A),r.d(A,{LoggerTypeId:()=>K7,add:()=>Ve,addEffect:()=>Vt,addScoped:()=>Vr,batched:()=>Vo,defaultLogger:()=>VO,filterLogLevel:()=>Vs,isLogger:()=>VD,json:()=>VA,jsonLogger:()=>VC,logFmt:()=>Vj,logfmtLogger:()=>VE,make:()=>K9,map:()=>Va,mapInput:()=>Vi,mapInputOptions:()=>Vn,minimumLogLevel:()=>VP,none:()=>Vh,pretty:()=>VM,prettyLogger:()=>VF,prettyLoggerDefault:()=>VT,remove:()=>Vp,replace:()=>Vf,replaceEffect:()=>Vd,replaceScoped:()=>Vm,simple:()=>Vg,stringLogger:()=>VI,structured:()=>Vz,structuredLogger:()=>VR,succeed:()=>Vb,sync:()=>Vx,test:()=>Vy,tracerLogger:()=>VN,withConsoleError:()=>Vc,withConsoleLog:()=>Vl,withLeveledConsole:()=>Vu,withMinimumLogLevel:()=>Vv,withSpanAnnotations:()=>VS,zip:()=>V_,zipLeft:()=>Vw,zipRight:()=>Vk});var j={};r.r(j),r.d(j,{GET:()=>Wn,POST:()=>Ws});var M=r(96559),z=r(48088),P=r(37719);let D=function(e,t){if("function"==typeof e)return function(){return e(arguments)?t.apply(this,arguments):e=>t(e,...arguments)};switch(e){case 0:case 1:throw RangeError(`Invalid arity ${e}`);case 2:return function(e,r){return arguments.length>=2?t(e,r):function(r){return t(r,e)}};case 3:return function(e,r,i){return arguments.length>=3?t(e,r,i):function(i){return t(i,e,r)}};case 4:return function(e,r,i,n){return arguments.length>=4?t(e,r,i,n):function(n){return t(n,e,r,i)}};case 5:return function(e,r,i,n,s){return arguments.length>=5?t(e,r,i,n,s):function(s){return t(s,e,r,i,n)}};default:return function(){if(arguments.length>=e)return t.apply(this,arguments);let r=arguments;return function(e){return t(e,...r)}}}},$=e=>e,L=e=>()=>e,U=L(!0),q=L(!1),B=L(void 0);function J(e,t,r,i,n,s,a,o,l){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return r(t(e));case 4:return i(r(t(e)));case 5:return n(i(r(t(e))));case 6:return s(n(i(r(t(e)))));case 7:return a(s(n(i(r(t(e))))));case 8:return o(a(s(n(i(r(t(e)))))));case 9:return l(o(a(s(n(i(r(t(e))))))));default:{let e=arguments[0];for(let t=1;t<arguments.length;t++)e=arguments[t](e);return e}}}let H=()=>"3.14.21",K=`effect/GlobalValue/globalStoreId/${H()}`,V=(e,t)=>(i||(globalThis[K]??=new Map,i=globalThis[K]),i.has(e)||i.set(e,t()),i.get(e)),W=e=>"string"==typeof e,G=e=>"number"==typeof e,Y=e=>"boolean"==typeof e,Z=e=>"bigint"==typeof e,Q=e=>"symbol"==typeof e,X=e=>"function"==typeof e,ee=e=>void 0===e,et=e=>void 0!==e,er=e=>null!==e,ei=e=>!1,en=e=>"object"==typeof e&&null!==e,es=e=>en(e)||X(e),ea=D(2,(e,t)=>es(e)&&t in e),eo=D(2,(e,t)=>ea(e,"_tag")&&e._tag===t),el=e=>null==e,eu=e=>null!=e,ec=e=>e instanceof Uint8Array,eh=e=>e instanceof Date,ep=e=>ea(e,Symbol.iterator),ef=e=>en(e)&&!Array.isArray(e),ed=e=>ea(e,"then")&&X(e.then),em=((e,t)=>r=>e(r)||t(r),e=>`BUG: ${e} - please report an issue at https://github.com/Effect-TS/effect/issues`);Symbol.iterator;class eg{constructor(e){this.called=!1,this.self=e}next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new eg(this.self)}}class eb{constructor(e,t,r,i){return el(t)&&el(e)?(t=0xffffffff*Math.random()>>>0,e=0):el(t)&&(t=e,e=0),el(i)&&el(r)?(i=this._state?this._state[3]:0xf767814f,r=this._state?this._state[2]:0x14057b7e):el(i)&&(i=r,r=0),this._state=new Int32Array([0,0,r>>>0,(1|(i||0))>>>0]),this._next(),ex(this._state,this._state[0],this._state[1],e>>>0,t>>>0),this._next(),this}getState(){return[this._state[0],this._state[1],this._state[2],this._state[3]]}setState(e){this._state[0]=e[0],this._state[1]=e[1],this._state[2]=e[2],this._state[3]=1|e[3]}integer(e){return Math.round(this.number()*Number.MAX_SAFE_INTEGER)%e}number(){return(0x8000000*((0x3ffffff&this._next())*1)+(0x7ffffff&this._next())*1)/0x20000000000000}_next(){var e,t,r,i;let n,s,a,o,l=this._state[0]>>>0,u=this._state[1]>>>0;e=this._state,t=l,i=0x4c957f2d,n=((r=u)>>>16)*32557>>>0,s=(65535&r)*(i>>>16)>>>0,a=(65535&r)*(65535&i)>>>0,o=(r>>>16)*(i>>>16)+((s>>>16)+(n>>>16))>>>0,(a=a+(s=s<<16>>>0)>>>0)>>>0<s>>>0&&(o=o+1>>>0),(a=a+(n=n<<16>>>0)>>>0)>>>0<n>>>0&&(o=o+1>>>0),o=(o=o+Math.imul(r,0x5851f42d)>>>0)+Math.imul(t,i)>>>0,e[0]=o,e[1]=a,ex(this._state,this._state[0],this._state[1],this._state[2],this._state[3]);let c=l>>>18,h=(u>>>18|l<<14)>>>0;c=(c^l)>>>0;let p=((h=(h^u)>>>0)>>>27|c<<5)>>>0,f=l>>>27;return(p>>>f|p<<((-f>>>0&31)>>>0))>>>0}}function ex(e,t,r,i,n){let s=t+i>>>0,a=r+n>>>0;a>>>0<r>>>0&&(s=s+1|0),e[0]=s,e[1]=a}let ey=Symbol.for("effect/Utils/YieldWrap");class ev{#e;constructor(e){this.#e=e}[ey](){return this.#e}}function eS(e){if("object"==typeof e&&null!==e&&ey in e)return e[ey]();throw Error(em("yieldWrapGet"))}let e_=V("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),ew={effect_internal_function:e=>e()},ek=ew.effect_internal_function(()=>Error().stack)?.includes("effect_internal_function")===!0?ew.effect_internal_function:e=>e(),eO=(function*(){}).constructor,eC=e=>es(e)&&e.constructor===eO,eE=V(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),eI=Symbol.for("effect/Hash"),eF=e=>{if(!0===e_.enabled)return 0;switch(typeof e){case"number":return ej(e);case"bigint":return eM(e.toString(10));case"boolean":case"symbol":return eM(String(e));case"string":return eM(e);case"undefined":return eM("undefined");case"function":case"object":if(null===e)return eM("null");if(e instanceof Date)return eF(e.toISOString());if(e instanceof URL)return eF(e.href);else if(eA(e))return e[eI]();else return eT(e);default:throw Error(`BUG: unhandled typeof ${typeof e} - please report an issue at https://github.com/Effect-TS/effect/issues`)}},eT=e=>(eE.has(e)||eE.set(e,ej(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),eE.get(e)),eR=e=>t=>53*t^e,eN=e=>0xbfffffff&e|e>>>1&0x40000000,eA=e=>ea(e,eI),ej=e=>{if(e!=e||e===1/0)return 0;let t=0|e;for(t!==e&&(t^=0xffffffff*e);e>0xffffffff;)t^=e/=0xffffffff;return eN(t)},eM=e=>{let t=5381,r=e.length;for(;r;)t=33*t^e.charCodeAt(--r);return eN(t)},ez=(e,t)=>{let r=12289;for(let i=0;i<t.length;i++)r^=J(eM(t[i]),eR(eF(e[t[i]])));return eN(r)},eP=e=>ez(e,Object.keys(e)),eD=e=>{let t=6151;for(let r=0;r<e.length;r++)t=J(t,eR(eF(e[r])));return eN(t)},e$=function(){if(1==arguments.length){let e=arguments[0];return function(t){return Object.defineProperty(e,eI,{value:()=>t,enumerable:!1}),t}}let e=arguments[0],t=arguments[1];return Object.defineProperty(e,eI,{value:()=>t,enumerable:!1}),t},eL=Symbol.for("effect/Equal");function eU(){return 1==arguments.length?e=>eq(e,arguments[0]):eq(arguments[0],arguments[1])}function eq(e,t){if(e===t)return!0;let r=typeof e;if(r!==typeof t)return!1;if("object"===r||"function"===r){if(null!==e&&null!==t){if(eB(e)&&eB(t))return!!(eF(e)===eF(t)&&e[eL](t))||!!e_.enabled&&!!e_.tester&&e_.tester(e,t);if(e instanceof Date&&t instanceof Date)return e.toISOString()===t.toISOString();if(e instanceof URL&&t instanceof URL)return e.href===t.href}if(e_.enabled){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,r)=>eq(e,t[r]));if(Object.getPrototypeOf(e)===Object.prototype&&Object.getPrototypeOf(e)===Object.prototype){let r=Object.keys(e),i=Object.keys(t);if(r.length===i.length){for(let i of r)if(!(i in t&&eq(e[i],t[i])))return!!e_.tester&&e_.tester(e,t);return!0}}return!!e_.tester&&e_.tester(e,t)}}return!!e_.enabled&&!!e_.tester&&e_.tester(e,t)}let eB=e=>ea(e,eL),eJ=()=>eU,eH=Symbol.for("nodejs.util.inspect.custom"),eK=e=>{try{if(ea(e,"toJSON")&&X(e.toJSON)&&0===e.toJSON.length)return e.toJSON();if(Array.isArray(e))return e.map(eK)}catch(e){return{}}return e2(e)},eV=e=>JSON.stringify(e,null,2),eW={toJSON(){return eK(this)},[eH](){return this.toJSON()},toString(){return eV(this.toJSON())}};class eG{[eH](){return this.toJSON()}toString(){return eV(this.toJSON())}}let eY=(e,t=2)=>{if("string"==typeof e)return e;try{return"object"==typeof e?eZ(e,t):String(e)}catch(t){return String(e)}},eZ=(e,t)=>{let r=[],i=JSON.stringify(e,(e,t)=>"object"==typeof t&&null!==t?r.includes(t)?void 0:r.push(t)&&(void 0!==e0.fiberRefs&&eX(t)?t[eQ](e0.fiberRefs):t):t,t);return r=void 0,i},eQ=Symbol.for("effect/Inspectable/Redactable"),eX=e=>"object"==typeof e&&null!==e&&eQ in e,e0=V("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),e1=(e,t)=>{let r=e0.fiberRefs;e0.fiberRefs=e;try{return t()}finally{e0.fiberRefs=r}},e2=e=>eX(e)&&void 0!==e0.fiberRefs?e[eQ](e0.fiberRefs):e,e3=(e,t)=>{switch(t.length){case 0:return e;case 1:return t[0](e);case 2:return t[1](t[0](e));case 3:return t[2](t[1](t[0](e)));case 4:return t[3](t[2](t[1](t[0](e))));case 5:return t[4](t[3](t[2](t[1](t[0](e)))));case 6:return t[5](t[4](t[3](t[2](t[1](t[0](e))))));case 7:return t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))));case 8:return t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e))))))));case 9:return t[8](t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))))));default:{let r=e;for(let e=0,i=t.length;e<i;e++)r=t[e](r);return r}}},e5="Async",e4="Commit",e6="Failure",e8="OnFailure",e7="OnSuccess",e9="OnSuccessAndFailure",te="Success",tt="Sync",tr="UpdateRuntimeFlags",ti="While",tn="Iterator",ts="WithRuntime",ta="Yield",to="RevertFlags",tl=Symbol.for("effect/Effect"),tu=Symbol.for("effect/Stream"),tc=Symbol.for("effect/Sink"),th=Symbol.for("effect/Channel"),tp={_R:e=>e,_E:e=>e,_A:e=>e,_V:H()},tf={[tl]:tp,[tu]:tp,[tc]:{_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e},[th]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},[eL](e){return this===e},[eI](){return e$(this,eT(this))},[Symbol.iterator](){return new eg(new ev(this))},pipe(){return e3(this,arguments)}},td={[eI](){return e$(this,eP(this))},[eL](e){let t=Object.keys(this),r=Object.keys(e);if(t.length!==r.length)return!1;for(let r of t)if(!(r in e&&eU(this[r],e[r])))return!1;return!0}},tm={...tf,_op:e4},tg={...tm,...td},tb=function(){function e(){}return e.prototype=tm,e}(),tx=function(){function e(){}return e.prototype=tg,e}(),ty=Symbol.for("effect/Option"),tv={...tf,[ty]:{_A:e=>e},[eH](){return this.toJSON()},toString(){return eV(this.toJSON())}},tS=Object.assign(Object.create(tv),{_tag:"Some",_op:"Some",[eL](e){return tk(e)&&tC(e)&&eU(this.value,e.value)},[eI](){return e$(this,eR(eF(this._tag))(eF(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:eK(this.value)}}}),t_=eF("None"),tw=Object.assign(Object.create(tv),{_tag:"None",_op:"None",[eL]:e=>tk(e)&&tO(e),[eI]:()=>t_,toJSON(){return{_id:"Option",_tag:this._tag}}}),tk=e=>ea(e,ty),tO=e=>"None"===e._tag,tC=e=>"Some"===e._tag,tE=Object.create(tw),tI=e=>{let t=Object.create(tS);return t.value=e,t},tF=Symbol.for("effect/Either"),tT={...tf,[tF]:{_R:e=>e},[eH](){return this.toJSON()},toString(){return eV(this.toJSON())}},tR=Object.assign(Object.create(tT),{_tag:"Right",_op:"Right",[eL](e){return tA(e)&&tM(e)&&eU(this.right,e.right)},[eI](){return eR(eF(this._tag))(eF(this.right))},toJSON(){return{_id:"Either",_tag:this._tag,right:eK(this.right)}}}),tN=Object.assign(Object.create(tT),{_tag:"Left",_op:"Left",[eL](e){return tA(e)&&tj(e)&&eU(this.left,e.left)},[eI](){return eR(eF(this._tag))(eF(this.left))},toJSON(){return{_id:"Either",_tag:this._tag,left:eK(this.left)}}}),tA=e=>ea(e,tF),tj=e=>"Left"===e._tag,tM=e=>"Right"===e._tag,tz=e=>{let t=Object.create(tN);return t.left=e,t},tP=e=>{let t=Object.create(tR);return t.right=e,t},tD=D(2,(e,t)=>tO(e)?tz(t()):tP(e.value)),t$=D(2,(e,{onLeft:t,onRight:r})=>tj(e)?tz(t(e.left)):tP(r(e.right))),tL=D(2,(e,t)=>tj(e)?tz(t(e.left)):tP(e.right)),tU=D(2,(e,t)=>tM(e)?tP(t(e.right)):tz(e.left)),tq=D(2,(e,{onLeft:t,onRight:r})=>tj(e)?t(e.left):r(e.right)),tB=tq({onLeft:$,onRight:$}),tJ=D(2,(e,t)=>{if(tM(e))return e.right;throw t(e.left)}),tH=tJ(()=>Error("getOrThrow called on a Left")),tK=e=>(t,r)=>t===r||e(t,r),tV=(e,t)=>e===t,tW=D(2,(e,t)=>tK((r,i)=>e(t(r),t(i)))),tG=tW(tV,e=>e.getTime()),tY=e=>tK((t,r)=>{if(t.length!==r.length)return!1;for(let i=0;i<t.length;i++)if(!e(t[i],r[i]))return!1;return!0}),tZ=e=>e.length>0,tQ=()=>tE,tX=D(2,(e,{onNone:t,onSome:r})=>tO(e)?t():r(e.value)),t0=D(2,(e,t)=>tO(e)?t():e.value),t1=D(2,(e,t)=>tO(e)?t():e),t2=D(2,(e,t)=>tO(e)?tI(t()):e),t3=e=>null==e?tQ():tI(e),t5=t0(B),t4=e=>(...t)=>{try{return tI(e(...t))}catch(e){return tQ()}},t6=D(2,(e,t)=>{if(tC(e))return e.value;throw t()}),t8=t6(()=>Error("getOrThrow called on a None")),t7=D(2,(e,t)=>tO(e)?tQ():tI(t(e.value))),t9=D(2,(e,t)=>tO(e)?tQ():t(e.value)),re=D(2,(e,t)=>tO(e)?tQ():t3(t(e.value))),rt=e=>{if(Symbol.iterator in e){let t=[];for(let r of e){if(tO(r))return tQ();t.push(r.value)}return tI(t)}let t={};for(let r of Object.keys(e)){let i=e[r];if(tO(i))return tQ();t[r]=i.value}return tI(t)},rr=D(2,(e,t)=>t9(e,e=>t(e)?tI(e):tE)),ri=e=>tK((t,r)=>tO(t)?tO(r):!tO(r)&&e(t.value,r.value)),rn=(x=eJ(),D(2,(e,t)=>!tO(e)&&x(e.value,t))),rs=D(2,(e,t)=>!tO(e)&&t(e.value)),ra=e=>(t,r)=>tO(t)?r:tO(r)?t:tI(e(t.value,r.value)),ro=e=>{let t=e[Symbol.iterator]().next();if(t.done)throw Error("unsafeHead: empty iterable");return t.value};Symbol.iterator,()=>rl;let rl={next:()=>({done:!0,value:void 0})},ru=e=>(t,r)=>t===r?0:e(t,r),rc=ru((e,t)=>e<t?-1:1),rh=D(2,(e,t)=>ru((r,i)=>e(t(r),t(i)))),rp=e=>0===rg(e).length;Object.fromEntries,(e,t)=>{let r=[];for(let i of rg(e))r.push(t(i,e[i]));return r};let rf=D(2,(e,t)=>Object.prototype.hasOwnProperty.call(e,t)),rd=D(2,(e,t)=>{if(!rf(e,t))return{...e};let r={...e};return delete r[t],r}),rm=D(2,(e,t)=>{let r={...e};for(let i of rg(e))r[i]=t(e[i],i);return r}),rg=e=>Object.keys(e),rb=D(3,(e,t,r)=>({...e,[t]:r})),rx=e=>D(2,(t,r)=>{for(let i of rg(t))if(!rf(r,i)||!e(t[i],r[i]))return!1;return!0}),ry=(...e)=>e,rv=(...e)=>e,rS=e=>Array(e),r_=D(2,(e,t)=>{let r=Math.max(1,Math.floor(e)),i=Array(r);for(let e=0;e<r;e++)i[e]=t(e);return i}),rw=e=>Array.isArray(e)?e:Array.from(e),rk=e=>Array.isArray(e)?e:[e],rO=D(2,(e,{onEmpty:t,onNonEmpty:r})=>rN(e)?r(e):t()),rC=D(2,(e,{onEmpty:t,onNonEmpty:r})=>rN(e)?r(rD(e),rU(e)):t()),rE=D(2,(e,t)=>[t,...e]),rI=D(2,(e,t)=>[...e,t]),rF=D(2,(e,t)=>rw(e).concat(rw(t))),rT=Array.isArray,rR=e=>0===e.length,rN=tZ,rA=(e,t)=>e<0||e>=t.length,rj=(e,t)=>Math.floor(Math.min(Math.max(0,e),t.length)),rM=D(2,(e,t)=>{let r=Math.floor(t);return rA(r,e)?tQ():tI(e[r])}),rz=D(2,(e,t)=>{let r=Math.floor(t);if(rA(r,e))throw Error(`Index ${r} out of bounds`);return e[r]}),rP=rM(0),rD=rz(0),r$=e=>rN(e)?tI(rL(e)):tQ(),rL=e=>e[e.length-1],rU=e=>e.slice(1),rq=(e,t)=>{let r=0;for(let i of e){if(!t(i,r))break;r++}return r},rB=D(2,(e,t)=>rY(e,rq(e,t))),rJ=D(2,(e,t)=>{let r=rw(e);return r.slice(rj(t,r),r.length)}),rH=((e,t)=>{let r=rw(e);for(let e=r.length-1;e>=0;e--){let i=r[e],n=t(i,e);if(Y(n)){if(n)return tI(i)}else if(tC(n))return n}return tQ()},e=>Array.from(e).reverse()),rK=D(2,(e,t)=>{let r=Array.from(e);return r.sort(t),r}),rV=D(2,(e,t)=>rW(e,t,ry)),rW=D(3,(e,t,r)=>{let i=rw(e),n=rw(t);if(rN(i)&&rN(n)){let e=[r(rD(i),rD(n))],t=Math.min(i.length,n.length);for(let s=1;s<t;s++)e[s]=r(i[s],n[s]);return e}return[]}),rG=eJ(),rY=D(2,(e,t)=>{let r=Array.from(e),i=Math.floor(t);return rN(r)?i>=1?rZ(r,i):[[],r]:[r,[]]}),rZ=D(2,(e,t)=>{let r=Math.max(1,Math.floor(t));return r>=e.length?[rQ(e),[]]:[rE(e.slice(1,r),rD(e)),e.slice(r)]}),rQ=e=>e.slice(),rX=D(3,(e,t,r)=>{let i=rw(e),n=rw(t);return rN(i)?rN(n)?io(r)(rF(i,n)):i:n}),r0=D(2,(e,t)=>rX(e,t,rG)),r1=()=>[],r2=e=>[e],r3=D(2,(e,t)=>e.map(t)),r5=D(2,(e,t)=>{if(rR(e))return[];let r=[];for(let i=0;i<e.length;i++){let n=t(e[i],i);for(let e=0;e<n.length;e++)r.push(n[e])}return r}),r4=r5($),r6=D(2,(e,t)=>{let r=rw(e),i=[];for(let e=0;e<r.length;e++){let n=t(r[e],e);tC(n)&&i.push(n.value)}return i}),r8=D(2,(e,t)=>{let r=0,i=[];for(let n of e){let e=t(n,r);if(tC(e))i.push(e.value);else break;r++}return i}),r7=D(2,(e,t)=>{let r=[],i=[],n=rw(e);for(let e=0;e<n.length;e++){let s=t(n[e],e);tj(s)?r.push(s.left):i.push(s.right)}return[r,i]}),r9=r6($),ie=D(2,(e,t)=>{let r=rw(e),i=[];for(let e=0;e<r.length;e++)t(r[e],e)&&i.push(r[e]);return i}),it=D(3,(e,t,r)=>rw(e).reduce((e,t,i)=>r(e,t,i),t)),ir=D(3,(e,t,r)=>rw(e).reduceRight((e,t,i)=>r(e,t,i),t)),ii=D(2,(e,t)=>e.every(t)),is=(e,t)=>{let r;let i=[],n=e;for(;tC(r=t(n));){let[e,t]=r.value;i.push(e),n=t}return i},ia=tY,io=D(2,(e,t)=>{let r=rw(e);if(rN(r)){let e=[rD(r)];for(let i of rU(r))e.every(e=>!t(i,e))&&e.push(i);return e}return[]}),il=e=>io(e,eJ()),iu=D(2,(e,t)=>rw(e).join(t)),ic=D(3,(e,t,r)=>{let i=0,n=t,s=[];for(let t of e){let e=r(n,t,i);n=e[0],s.push(e[1]),i++}return[n,s]}),ih=Symbol.for("effect/Chunk"),ip=[],id=tK((e,t)=>e.length===t.length&&ik(e).every((e,r)=>eU(e,iF(t,r)))),im={[ih]:{_A:e=>e},toString(){return eV(this.toJSON())},toJSON(){return{_id:"Chunk",values:ik(this).map(eK)}},[eH](){return this.toJSON()},[eL](e){return ib(e)&&id(this,e)},[eI](){return e$(this,eD(ik(this)))},[Symbol.iterator](){switch(this.backing._tag){case"IArray":return this.backing.array[Symbol.iterator]();case"IEmpty":return ip[Symbol.iterator]();default:return ik(this)[Symbol.iterator]()}},pipe(){return e3(this,arguments)}},ig=e=>{let t=Object.create(im);switch(t.backing=e,e._tag){case"IEmpty":t.length=0,t.depth=0,t.left=t,t.right=t;break;case"IConcat":t.length=e.left.length+e.right.length,t.depth=1+Math.max(e.left.depth,e.right.depth),t.left=e.left,t.right=e.right;break;case"IArray":t.length=e.array.length,t.depth=0,t.left=ix,t.right=ix;break;case"ISingleton":t.length=1,t.depth=0,t.left=ix,t.right=ix;break;case"ISlice":t.length=e.length,t.depth=e.chunk.depth+1,t.left=ix,t.right=ix}return t},ib=e=>ea(e,ih),ix=ig({_tag:"IEmpty"}),iy=()=>ix,iv=(...e)=>iI(e),iS=e=>ig({_tag:"ISingleton",a:e}),i_=e=>ib(e)?e:iE(rw(e)),iw=(e,t,r)=>{switch(e.backing._tag){case"IArray":!function(e,t,r,i,n){for(let s=0;s<Math.min(e.length,0+n);s++)r[i+s-t]=e[s]}(e.backing.array,0,t,r,e.length);break;case"IConcat":iw(e.left,t,r),iw(e.right,t,r+e.left.length);break;case"ISingleton":t[r]=e.backing.a;break;case"ISlice":{let i=0,n=r;for(;i<e.length;)t[n]=iF(e,i),i+=1,n+=1}}},ik=e=>{switch(e.backing._tag){case"IEmpty":return ip;case"IArray":return e.backing.array;default:{let t=Array(e.length);return iw(e,t,0),e.backing={_tag:"IArray",array:t},e.left=ix,e.right=ix,e.depth=0,t}}},iO=e=>{switch(e.backing._tag){case"IEmpty":case"ISingleton":return e;case"IArray":return ig({_tag:"IArray",array:rH(e.backing.array)});case"IConcat":return ig({_tag:"IConcat",left:iO(e.backing.right),right:iO(e.backing.left)});case"ISlice":return iE(rH(ik(e)))}},iC=D(2,(e,t)=>t<0||t>=e.length?tQ():tI(iF(e,t))),iE=e=>0===e.length?iy():1===e.length?iS(e[0]):ig({_tag:"IArray",array:e}),iI=e=>iE(e),iF=D(2,(e,t)=>{switch(e.backing._tag){case"IEmpty":throw Error("Index out of bounds");case"ISingleton":if(0!==t)throw Error("Index out of bounds");return e.backing.a;case"IArray":if(t>=e.length||t<0)throw Error("Index out of bounds");return e.backing.array[t];case"IConcat":return t<e.left.length?iF(e.left,t):iF(e.right,t-e.left.length);case"ISlice":return iF(e.backing.chunk,t+e.backing.offset)}}),iT=D(2,(e,t)=>iM(e,iS(t))),iR=D(2,(e,t)=>iM(iS(t),e)),iN=D(2,(e,t)=>{if(t<=0)return ix;if(t>=e.length)return e;switch(e.backing._tag){case"ISlice":return ig({_tag:"ISlice",chunk:e.backing.chunk,length:t,offset:e.backing.offset});case"IConcat":if(t>e.left.length)return ig({_tag:"IConcat",left:e.left,right:iN(e.right,t-e.left.length)});return iN(e.left,t);default:return ig({_tag:"ISlice",chunk:e,offset:0,length:t})}}),iA=D(2,(e,t)=>{if(t<=0)return e;if(t>=e.length)return ix;switch(e.backing._tag){case"ISlice":return ig({_tag:"ISlice",chunk:e.backing.chunk,offset:e.backing.offset+t,length:e.backing.length-t});case"IConcat":if(t>e.left.length)return iA(e.right,t-e.left.length);return ig({_tag:"IConcat",left:iA(e.left,t),right:e.right});default:return ig({_tag:"ISlice",chunk:e,offset:t,length:e.length-t})}}),ij=D(2,(e,t)=>{let r=ik(e),i=r.length,n=0;for(;n<i&&t(r[n]);)n++;return iA(e,n)}),iM=D(2,(e,t)=>{if("IEmpty"===e.backing._tag)return t;if("IEmpty"===t.backing._tag)return e;let r=t.depth-e.depth;if(1>=Math.abs(r))return ig({_tag:"IConcat",left:e,right:t});if(r<-1){if(e.left.depth>=e.right.depth){let r=iM(e.right,t);return ig({_tag:"IConcat",left:e.left,right:r})}{let r=iM(e.right.right,t);if(r.depth===e.depth-3){let t=ig({_tag:"IConcat",left:e.right.left,right:r});return ig({_tag:"IConcat",left:e.left,right:t})}{let t=ig({_tag:"IConcat",left:e.left,right:e.right.left});return ig({_tag:"IConcat",left:t,right:r})}}}if(t.right.depth>=t.left.depth)return ig({_tag:"IConcat",left:iM(e,t.left),right:t.right});{let r=iM(e,t.left.left);if(r.depth===t.depth-3){let e=ig({_tag:"IConcat",left:r,right:t.left.right});return ig({_tag:"IConcat",left:e,right:t.right})}{let e=ig({_tag:"IConcat",left:t.left.right,right:t.right});return ig({_tag:"IConcat",left:r,right:e})}}}),iz=D(2,(e,t)=>iE(r6(e,t))),iP=D(2,(e,t)=>iE(ie(e,t))),iD=((e,t)=>iE(r8(e,t)),D(2,(e,t)=>{if("ISingleton"===e.backing._tag)return t(e.backing.a,0);let r=ix,i=0;for(let n of e)r=iM(r,t(n,i++));return r})),i$=D(2,(e,t)=>ik(e).forEach(t)),iL=iD($),iU=e=>0===e.length,iq=e=>e.length>0,iB=iC(0),iJ=e=>iF(e,0),iH=e=>iF(e,e.length-1),iK=D(2,(e,t)=>"ISingleton"===e.backing._tag?iS(t(e.backing.a,0)):iE(J(ik(e),r3((e,r)=>t(e,r))))),iV=((e,t,r)=>{let[i,n]=ic(e,t,r);return[i,iE(n)]},D(2,(e,t)=>[iN(e,t),iA(e,t)])),iW=D(2,(e,t)=>{let r=0;for(let i of ik(e)){if(t(i))break;r++}return iV(e,r)}),iG=e=>iA(e,1),iY=D(2,(e,t)=>iA(e,e.length-t)),iZ=D(2,(e,t)=>{let r=[];for(let i of ik(e))if(t(i))r.push(i);else break;return iE(r)}),iQ=D(3,(e,t,r)=>iE(rW(e,t,r))),iX=D(2,(e,t)=>i_(r_(e,t))),i0=(e,t)=>e<=t?iX(t-e+1,t=>e+t):iS(e),i1=D(2,(e,t)=>rw(e).some(t));function i2(e,t){var r;return r=e&t-1,r-=r>>1&0x55555555,r=(r=(0x33333333&r)+(r>>2&0x33333333))+(r>>4)&0xf0f0f0f,r+=r>>8,127&(r+=r>>16)}let i3=(e,t)=>({value:e,previous:t});function i5(e,t,r,i){let n=i;if(!e){let e=i.length;n=Array(e);for(let t=0;t<e;++t)n[t]=i[t]}return n[t]=r,n}function i4(e,t,r){let i=r.length-1,n=0,s=0,a=r;if(e)n=s=t;else for(a=Array(i);n<t;)a[s++]=r[n++];for(++n;n<=i;)a[s++]=r[n++];return e&&(a.length=i),a}class i6{modify(e,t,r,i,n,s){let a=r(tQ());return tO(a)?new i6:(++s.value,new i9(e,i,n,a))}constructor(){this._tag="EmptyNode"}}function i8(e){return eo(e,"EmptyNode")}function i7(e,t){return!i8(e)&&t===e.edit}class i9{constructor(e,t,r,i){this._tag="LeafNode",this.edit=e,this.hash=t,this.key=r,this.value=i}modify(e,t,r,i,n,s){if(eU(n,this.key)){let t=r(this.value);return t===this.value?this:tO(t)?(--s.value,new i6):i7(this,e)?(this.value=t,this):new i9(e,i,n,t)}let a=r(tQ());return tO(a)?this:(++s.value,ni(e,t,this.hash,this,i,new i9(e,i,n,a)))}}class ne{constructor(e,t,r){this._tag="CollisionNode",this.edit=e,this.hash=t,this.children=r}modify(e,t,r,i,n,s){if(i===this.hash){let t=i7(this,e),i=this.updateCollisionList(t,e,this.hash,this.children,r,n,s);return i===this.children?this:i.length>1?new ne(e,this.hash,i):i[0]}let a=r(tQ());return tO(a)?this:(++s.value,ni(e,t,this.hash,this,i,new i9(e,i,n,a)))}updateCollisionList(e,t,r,i,n,s,a){let o=i.length;for(let l=0;l<o;++l){let o=i[l];if("key"in o&&eU(s,o.key)){let u=o.value,c=n(u);if(c===u)return i;if(tO(c))return--a.value,i4(e,l,i);return i5(e,l,new i9(t,r,s,c),i)}}let l=n(tQ());return tO(l)?i:(++a.value,i5(e,o,new i9(t,r,s,l),i))}}class nt{constructor(e,t,r){this._tag="IndexedNode",this.edit=e,this.mask=t,this.children=r}modify(e,t,r,i,n,s){let a;let o=this.mask,l=this.children,u=i>>>t&31,c=1<<u,h=i2(o,c),p=o&c,f=i7(this,e);if(!p){let a=new i6().modify(e,t+5,r,i,n,s);return a?l.length>=16?function(e,t,r,i,n){let s=[],a=i,o=0;for(let e=0;a;++e)1&a&&(s[e]=n[o++]),a>>>=1;return s[t]=r,new nr(e,o+1,s)}(e,u,a,o,l):new nt(e,o|c,function(e,t,r,i){let n=i.length;if(e){let e=n;for(;e>=t;)i[e--]=i[e];return i[t]=r,i}let s=0,a=0,o=Array(n+1);for(;s<t;)o[a++]=i[s++];for(o[t]=r;s<n;)o[++a]=i[s++];return o}(f,h,a,l)):this}let d=l[h],m=d.modify(e,t+5,r,i,n,s);if(d===m)return this;let g=o;if(i8(m)){var b;if(!(g&=~c))return new i6;if(l.length<=2&&(i8(b=l[1^h])||"LeafNode"===b._tag||"CollisionNode"===b._tag))return l[1^h];a=i4(f,h,l)}else a=i5(f,h,m,l);return f?(this.mask=g,this.children=a,this):new nt(e,g,a)}}class nr{constructor(e,t,r){this._tag="ArrayNode",this.edit=e,this.size=t,this.children=r}modify(e,t,r,i,n,s){let a,o=this.size,l=this.children,u=i>>>t&31,c=l[u],h=(c||new i6).modify(e,t+5,r,i,n,s);if(c===h)return this;let p=i7(this,e);if(i8(c)&&!i8(h))++o,a=i5(p,u,h,l);else if(!i8(c)&&i8(h)){if(--o<=8)return function(e,t,r,i){let n=Array(t-1),s=0,a=0;for(let e=0,t=i.length;e<t;++e)if(e!==r){let t=i[e];t&&!i8(t)&&(n[s++]=t,a|=1<<e)}return new nt(e,a,n)}(e,o,u,l);a=i5(p,u,new i6,l)}else a=i5(p,u,h,l);return p?(this.size=o,this.children=a,this):new nr(e,o,a)}}function ni(e,t,r,i,n,s){let a;let o=t;for(;;){let t=function(e,t,r,i,n,s){if(r===n)return new ne(e,r,[s,i]);let a=r>>>t&31,o=n>>>t&31;return a===o?t=>new nt(e,1<<a|1<<o,[t]):new nt(e,1<<a|1<<o,a<o?[i,s]:[s,i])}(e,o,r,i,n,s);if("function"==typeof t)a=i3(t,a),o+=5;else{let e=t;for(;null!=a;)e=a.value(e),a=a.previous;return e}}}let nn="effect/HashMap",ns=Symbol.for(nn),na={[ns]:ns,[Symbol.iterator](){return new nl(this,(e,t)=>[e,t])},[eI](){let e=eF(nn);for(let t of this)e^=J(eF(t[0]),eR(eF(t[1])));return e$(this,e)},[eL](e){if(nd(e)){if(e._size!==this._size)return!1;for(let t of this){let r=J(e,ng(t[0],eF(t[0])));if(tO(r)||!eU(t[1],r.value))return!1}return!0}return!1},toString(){return eV(this.toJSON())},toJSON(){return{_id:"HashMap",values:Array.from(this).map(eK)}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},no=(e,t,r,i)=>{let n=Object.create(na);return n._editable=e,n._edit=t,n._root=r,n._size=i,n};class nl{constructor(e,t){this.map=e,this.f=t,this.v=nc(this.map._root,this.f,void 0)}next(){if(tO(this.v))return{done:!0,value:void 0};let e=this.v.value;return this.v=nu(e.cont),{done:!1,value:e.value}}[Symbol.iterator](){return new nl(this.map,this.f)}}let nu=e=>e?nh(e[0],e[1],e[2],e[3],e[4]):tQ(),nc=(e,t,r)=>{switch(e._tag){case"LeafNode":if(tC(e.value))return tI({value:t(e.key,e.value.value),cont:r});return nu(r);case"CollisionNode":case"ArrayNode":case"IndexedNode":{let i=e.children;return nh(i.length,i,0,t,r)}default:return nu(r)}},nh=(e,t,r,i,n)=>{for(;r<e;){let s=t[r++];if(s&&!i8(s))return nc(s,i,[e,t,r,i,n])}return nu(n)},np=no(!1,0,new i6,0),nf=()=>np,nd=e=>ea(e,ns),nm=D(2,(e,t)=>ng(e,t,eF(t))),ng=D(3,(e,t,r)=>{let i=e._root,n=0;for(;;)switch(i._tag){case"LeafNode":return eU(t,i.key)?i.value:tQ();case"CollisionNode":if(r===i.hash){let e=i.children;for(let r=0,i=e.length;r<i;++r){let i=e[r];if("key"in i&&eU(t,i.key))return i.value}}return tQ();case"IndexedNode":{let e=1<<(r>>>n&31);if(i.mask&e){i=i.children[i2(i.mask,e)],n+=5;break}return tQ()}case"ArrayNode":if(i=i.children[r>>>n&31]){n+=5;break}return tQ();default:return tQ()}}),nb=D(2,(e,t)=>tC(ng(e,t,eF(t)))),nx=D(3,(e,t,r)=>nk(e,t,()=>tI(r))),ny=D(3,(e,t,r)=>e._editable?(e._root=t,e._size=r,e):t===e._root?e:no(e._editable,e._edit,t,r)),nv=e=>new nl(e,e=>e),nS=e=>e._size,n_=e=>no(!0,e._edit+1,e._root,e._size),nw=e=>(e._editable=!1,e),nk=((e,t)=>{let r=n_(e);return t(r),nw(r)},D(3,(e,t,r)=>nO(e,t,eF(t),r))),nO=D(4,(e,t,r,i)=>{let n={value:e._size},s=e._root.modify(e._editable?e._edit:NaN,0,i,r,t,n);return J(e,ny(s,n.value))}),nC=D(2,(e,t)=>nk(e,t,tQ)),nE=D(2,(e,t)=>nF(e,nf(),(e,r,i)=>nx(e,i,t(r,i)))),nI=D(2,(e,t)=>nF(e,void 0,(e,r,i)=>t(r,i))),nF=D(3,(e,t,r)=>{let i;let n=e._root;if("LeafNode"===n._tag)return tC(n.value)?r(t,n.value.value,n.key):t;if("EmptyNode"===n._tag)return t;let s=[n.children];for(;i=s.pop();)for(let e=0,n=i.length;e<n;){let n=i[e++];n&&!i8(n)&&("LeafNode"===n._tag?tC(n.value)&&(t=r(t,n.value.value,n.key)):s.push(n.children))}return t}),nT=((e,t)=>{for(let r of e)if(t(r[1],r[0]))return!0;return!1},"effect/HashSet"),nR=Symbol.for(nT),nN={[nR]:nR,[Symbol.iterator](){return nv(this._keyMap)},[eI](){return e$(this,eR(eF(this._keyMap))(eF(nT)))},[eL](e){return!!nj(e)&&nS(this._keyMap)===nS(e._keyMap)&&eU(this._keyMap,e._keyMap)},toString(){return eV(this.toJSON())},toJSON(){return{_id:"HashSet",values:Array.from(this).map(eK)}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},nA=e=>{let t=Object.create(nN);return t._keyMap=e,t},nj=e=>ea(e,nR),nM=nA(nf()),nz=()=>nM,nP=D(2,(e,t)=>nb(e._keyMap,t)),nD=D(2,(e,t)=>{let r=!1;for(let i of e)if(r=t(i))break;return r}),n$=D(2,(e,t)=>!nD(e,e=>!t(e))),nL=e=>nA(n_(e._keyMap)),nU=e=>(e._keyMap._editable=!1,e),nq=D(2,(e,t)=>{let r=nL(e);return t(r),nU(r)}),nB=D(2,(e,t)=>e._keyMap._editable?(nx(t,!0)(e._keyMap),e):nA(nx(t,!0)(e._keyMap))),nJ=D(2,(e,t)=>e._keyMap._editable?(nC(t)(e._keyMap),e):nA(nC(t)(e._keyMap))),nH=D(2,(e,t)=>nq(e,e=>{for(let r of t)nJ(e,r)})),nK=D(2,(e,t)=>nq(nz(),r=>{for(let i of(nV(e,e=>nB(r,e)),t))nB(r,i)})),nV=D(2,(e,t)=>nI(e._keyMap,(e,r)=>t(r))),nW=D(3,(e,t,r)=>nF(e._keyMap,t,(e,t,i)=>r(e,i))),nG=e=>{let t=nL(nz());for(let r of e)nB(t,r);return nU(t)},nY=(...e)=>{let t=nL(nz());for(let r of e)nB(t,r);return nU(t)},nZ=e=>nS(e._keyMap),nQ="Empty",nX="Fail",n0="Interrupt",n1="Parallel",n2="Sequential",n3="effect/Cause",n5=Symbol.for(n3),n4={[n5]:{_E:e=>e},[eI](){return J(eF(n3),eR(eF(sS(this))),e$(this))},[eL](e){return sr(e)&&sv(this,e)},pipe(){return e3(this,arguments)},toJSON(){switch(this._tag){case"Empty":return{_id:"Cause",_tag:this._tag};case"Die":return{_id:"Cause",_tag:this._tag,defect:eK(this.defect)};case"Interrupt":return{_id:"Cause",_tag:this._tag,fiberId:this.fiberId.toJSON()};case"Fail":return{_id:"Cause",_tag:this._tag,failure:eK(this.error)};case"Sequential":case"Parallel":return{_id:"Cause",_tag:this._tag,left:eK(this.left),right:eK(this.right)}}},toString(){return sR(this)},[eH](){return this.toJSON()}},n6=(()=>{let e=Object.create(n4);return e._tag=nQ,e})(),n8=e=>{let t=Object.create(n4);return t._tag=nX,t.error=e,t},n7=e=>{let t=Object.create(n4);return t._tag="Die",t.defect=e,t},n9=e=>{let t=Object.create(n4);return t._tag=n0,t.fiberId=e,t},se=(e,t)=>{let r=Object.create(n4);return r._tag=n1,r.left=e,r.right=t,r},st=(e,t)=>{let r=Object.create(n4);return r._tag=n2,r.left=e,r.right=t,r},sr=e=>ea(e,n5),si=e=>e._tag===nQ,sn=e=>"Die"===e._tag,ss=e=>e._tag===nQ||sF(e,!0,(e,t)=>{switch(t._tag){case nQ:return tI(e);case"Die":case nX:case n0:return tI(!1);default:return tQ()}}),sa=e=>tC(sf(e)),so=e=>sT(void 0,sO)(e),sl=e=>iO(sF(e,iy(),(e,t)=>t._tag===nX?tI(J(e,iR(t.error))):tQ())),su=e=>iO(sF(e,iy(),(e,t)=>"Die"===t._tag?tI(J(e,iR(t.defect))):tQ())),sc=e=>sF(e,nz(),(e,t)=>t._tag===n0?tI(J(e,nB(t.fiberId))):tQ()),sh=e=>sw(e,e=>e._tag===nX?tI(e.error):tQ()),sp=e=>{let t=sh(e);switch(t._tag){case"None":return tP(e);case"Some":return tz(t.value)}},sf=e=>sw(e,e=>e._tag===n0?tI(e.fiberId):tQ()),sd=e=>sI(e,{onEmpty:tQ(),onFail:e=>tI(n7(e)),onDie:e=>tI(n7(e)),onInterrupt:()=>tQ(),onSequential:ra(st),onParallel:ra(se)}),sm=e=>sI(e,{onEmpty:n6,onFail:()=>n6,onDie:n7,onInterrupt:n9,onSequential:st,onParallel:se}),sg=e=>sI(e,{onEmpty:n6,onFail:n7,onDie:n7,onInterrupt:n9,onSequential:st,onParallel:se}),sb=D(2,(e,t)=>sI(e,{onEmpty:tI(n6),onFail:e=>tI(n8(e)),onDie:e=>tC(t(e))?tQ():tI(n7(e)),onInterrupt:e=>tI(n9(e)),onSequential:ra(st),onParallel:ra(se)})),sx=D(2,(e,t)=>sy(e,e=>n8(t(e)))),sy=D(2,(e,t)=>sI(e,{onEmpty:n6,onFail:e=>t(e),onDie:e=>n7(e),onInterrupt:e=>n9(e),onSequential:(e,t)=>st(e,t),onParallel:(e,t)=>se(e,t)})),sv=(e,t)=>{let r=iS(e),i=iS(t);for(;iq(r)&&iq(i);){let[e,t]=J(iJ(r),sF([nz(),iy()],([e,t],r)=>{let[i,n]=sk(r);return tI([J(e,nK(i)),J(t,iM(n))])})),[n,s]=J(iJ(i),sF([nz(),iy()],([e,t],r)=>{let[i,n]=sk(r);return tI([J(e,nK(i)),J(t,iM(n))])}));if(!eU(e,n))return!1;r=t,i=s}return!0},sS=e=>s_(iS(e),iy()),s_=(e,t)=>{for(;;){let[r,i]=J(e,it([nz(),iy()],([e,t],r)=>{let[i,n]=sk(r);return[J(e,nK(i)),J(t,iM(n))]})),n=nZ(r)>0?J(t,iR(r)):t;if(iU(i))return iO(n);e=i,t=n}throw Error(em("Cause.flattenCauseLoop"))},sw=D(2,(e,t)=>{let r=[e];for(;r.length>0;){let e=r.pop(),i=t(e);switch(i._tag){case"None":switch(e._tag){case n2:case n1:r.push(e.right),r.push(e.left)}break;case"Some":return i}}return tQ()}),sk=e=>{let t=e,r=[],i=nz(),n=iy();for(;void 0!==t;)switch(t._tag){case nQ:if(0===r.length)return[i,n];t=r.pop();break;case nX:if(i=nB(i,iv(t._tag,t.error)),0===r.length)return[i,n];t=r.pop();break;case"Die":if(i=nB(i,iv(t._tag,t.defect)),0===r.length)return[i,n];t=r.pop();break;case n0:if(i=nB(i,iv(t._tag,t.fiberId)),0===r.length)return[i,n];t=r.pop();break;case n2:switch(t.left._tag){case nQ:t=t.right;break;case n2:t=st(t.left.left,st(t.left.right,t.right));break;case n1:t=se(st(t.left.left,t.right),st(t.left.right,t.right));break;default:n=iR(n,t.right),t=t.left}break;case n1:r.push(t.right),t=t.left}throw Error(em("Cause.evaluateCauseLoop"))},sO={emptyCase:U,failCase:q,dieCase:q,interruptCase:U,sequentialCase:(e,t,r)=>t&&r,parallelCase:(e,t,r)=>t&&r},sC="SequentialCase",sE="ParallelCase",sI=D(2,(e,{onDie:t,onEmpty:r,onFail:i,onInterrupt:n,onParallel:s,onSequential:a})=>sT(e,void 0,{emptyCase:()=>r,failCase:(e,t)=>i(t),dieCase:(e,r)=>t(r),interruptCase:(e,t)=>n(t),sequentialCase:(e,t,r)=>a(t,r),parallelCase:(e,t,r)=>s(t,r)})),sF=D(3,(e,t,r)=>{let i=t,n=e,s=[];for(;void 0!==n;){let e=r(i,n);switch(i=tC(e)?e.value:i,n._tag){case n2:case n1:s.push(n.right),n=n.left;break;default:n=void 0}void 0===n&&s.length>0&&(n=s.pop())}return i}),sT=D(3,(e,t,r)=>{let i=[e],n=[];for(;i.length>0;){let e=i.pop();switch(e._tag){case nQ:n.push(tP(r.emptyCase(t)));break;case nX:n.push(tP(r.failCase(t,e.error)));break;case"Die":n.push(tP(r.dieCase(t,e.defect)));break;case n0:n.push(tP(r.interruptCase(t,e.fiberId)));break;case n2:i.push(e.right),i.push(e.left),n.push(tz({_tag:sC}));break;case n1:i.push(e.right),i.push(e.left),n.push(tz({_tag:sE}))}}let s=[];for(;n.length>0;){let e=n.pop();switch(e._tag){case"Left":switch(e.left._tag){case sC:{let e=s.pop(),i=s.pop(),n=r.sequentialCase(t,e,i);s.push(n);break}case sE:{let e=s.pop(),i=s.pop(),n=r.parallelCase(t,e,i);s.push(n)}}break;case"Right":s.push(e.right)}}if(0===s.length)throw Error("BUG: Cause.reduceWithContext - please report an issue at https://github.com/Effect-TS/effect/issues");return s.pop()}),sR=(e,t)=>so(e)?"All fibers interrupted without errors.":s$(e).map(function(e){return t?.renderErrorCause!==!0||void 0===e.cause?e.stack:`${e.stack} {
${sN(e.cause,"  ")}
}`}).join("\n"),sN=(e,t)=>{let r=e.stack.split("\n"),i=`${t}[cause]: ${r[0]}`;for(let e=1,n=r.length;e<n;e++)i+=`
${t}${r[e]}`;return e.cause&&(i+=` {
${sN(e.cause,`${t}  `)}
${t}}`),i};class sA extends globalThis.Error{constructor(e){let t="object"==typeof e&&null!==e,r=Error.stackTraceLimit;Error.stackTraceLimit=1,super(sj(e),t&&"cause"in e&&void 0!==e.cause?{cause:new sA(e.cause)}:void 0),this.span=void 0,""===this.message&&(this.message="An error has occurred"),Error.stackTraceLimit=r,this.name=e instanceof Error?e.name:"Error",t&&(sD in e&&(this.span=e[sD]),Object.keys(e).forEach(t=>{t in this||(this[t]=e[t])})),this.stack=sP(`${this.name}: ${this.message}`,e instanceof Error&&e.stack?e.stack:"",this.span)}}let sj=e=>{if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&e instanceof Error)return e.message;try{if(ea(e,"toString")&&X(e.toString)&&e.toString!==Object.prototype.toString&&e.toString!==globalThis.Array.prototype.toString)return e.toString()}catch{}return eZ(e)},sM=/\((.*)\)/g,sz=V("effect/Tracer/spanToTrace",()=>new WeakMap),sP=(e,t,r)=>{let i=[e],n=t.startsWith(e)?t.slice(e.length).split("\n"):t.split("\n");for(let e=1;e<n.length;e++){if(n[e].includes(" at new BaseEffectError")||n[e].includes(" at new YieldableError")){e++;continue}if(n[e].includes("Generator.next")||n[e].includes("effect_internal_function"))break;i.push(n[e].replace(/at .*effect_instruction_i.*\((.*)\)/,"at $1").replace(/EffectPrimitive\.\w+/,"<anonymous>"))}if(r){let e=r,t=0;for(;e&&"Span"===e._tag&&t<10;){let r=sz.get(e);if("function"==typeof r){let t=r();if("string"==typeof t){let r=t.matchAll(sM),n=!1;for(let[,t]of r)n=!0,i.push(`    at ${e.name} (${t})`);n||i.push(`    at ${e.name} (${t.replace(/^at /,"")})`)}else i.push(`    at ${e.name}`)}else i.push(`    at ${e.name}`);e=t5(e.parent),t++}}return i.join("\n")},sD=Symbol.for("effect/SpanAnnotation"),s$=e=>sT(e,void 0,{emptyCase:()=>[],dieCase:(e,t)=>[new sA(t)],failCase:(e,t)=>[new sA(t)],interruptCase:()=>[],parallelCase:(e,t,r)=>[...t,...r],sequentialCase:(e,t,r)=>[...t,...r]}),sL=Symbol.for("effect/Context/Tag"),sU=Symbol.for("effect/Context/Reference"),sq=Symbol.for("effect/STM"),sB={...tf,_op:"Tag",[sq]:tp,[sL]:{_Service:e=>e,_Identifier:e=>e},toString(){return eV(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[eH](){return this.toJSON()},of:e=>e,context(e){return sQ(this,e)}},sJ={...sB,[sU]:sU},sH=Symbol.for("effect/Context"),sK={[sH]:{_Services:e=>e},[eL](e){if(sG(e)&&this.unsafeMap.size===e.unsafeMap.size){for(let t of this.unsafeMap.keys())if(!e.unsafeMap.has(t)||!eU(this.unsafeMap.get(t),e.unsafeMap.get(t)))return!1;return!0}return!1},[eI](){return e$(this,ej(this.unsafeMap.size))},pipe(){return e3(this,arguments)},toString(){return eV(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(eK)}},[eH](){return this.toJSON()}},sV=e=>{let t=Object.create(sK);return t.unsafeMap=e,t},sW=e=>{let t=Error(`Service not found${e.key?`: ${String(e.key)}`:""}`);if(e.stack){let r=e.stack.split("\n");if(r.length>2){let e=r[2].match(/at (.*)/);e&&(t.message=t.message+` (defined at ${e[1]})`)}}if(t.stack){let e=t.stack.split("\n");e.splice(1,3),t.stack=e.join("\n")}return t},sG=e=>ea(e,sH),sY=e=>ea(e,sU),sZ=sV(new Map),sQ=(e,t)=>sV(new Map([[e.key,t]])),sX=D(3,(e,t,r)=>{let i=new Map(e.unsafeMap);return i.set(t.key,r),sV(i)}),s0=V("effect/Context/defaultValueCache",()=>new Map),s1=e=>{if(s0.has(e.key))return s0.get(e.key);let t=e.defaultValue();return s0.set(e.key,t),t},s2=(e,t)=>e.unsafeMap.has(t.key)?e.unsafeMap.get(t.key):s1(t),s3=D(2,(e,t)=>{if(!e.unsafeMap.has(t.key)){if(sU in t)return s1(t);throw sW(t)}return e.unsafeMap.get(t.key)}),s5=D(2,(e,t)=>e.unsafeMap.has(t.key)?tI(e.unsafeMap.get(t.key)):sY(t)?tI(s1(t)):tE),s4=D(2,(e,t)=>{let r=new Map(e.unsafeMap);for(let[e,i]of t.unsafeMap)r.set(e,i);return sV(r)}),s6=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();Error.stackTraceLimit=t;let i=Object.create(sB);return Object.defineProperty(i,"stack",{get:()=>r.stack}),i.key=e,i},s8=e=>ea(e,sL),s7=()=>sZ,s9=e=>()=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();function i(){}return Error.stackTraceLimit=t,Object.setPrototypeOf(i,sB),i.key=e,Object.defineProperty(i,"stack",{get:()=>r.stack}),i},ae=()=>(e,t)=>{let r=Error.stackTraceLimit;Error.stackTraceLimit=2;let i=Error();function n(){}return Error.stackTraceLimit=r,Object.setPrototypeOf(n,sJ),n.key=e,n.defaultValue=t.defaultValue,Object.defineProperty(n,"stack",{get:()=>i.stack}),n},at=Symbol.for("effect/Duration"),ar=BigInt(0),ai=BigInt(24),an=BigInt(60),as=BigInt(1e3),aa=BigInt(1e6),ao=BigInt(1e9),al=/^(-?\d+(?:\.\d+)?)\s+(nanos?|micros?|millis?|seconds?|minutes?|hours?|days?|weeks?)$/,au=e=>{if(ad(e))return e;if(G(e))return aS(e);if(Z(e))return ay(e);if(Array.isArray(e)&&2===e.length&&e.every(G))return e[0]===-1/0||e[1]===-1/0||Number.isNaN(e[0])||Number.isNaN(e[1])?ab:e[0]===1/0||e[1]===1/0?ax:ay(BigInt(Math.round(1e9*e[0]))+BigInt(Math.round(e[1])));if(W(e)){let t=al.exec(e);if(t){let[e,r,i]=t,n=Number(r);switch(i){case"nano":case"nanos":return ay(BigInt(r));case"micro":case"micros":return av(BigInt(r));case"milli":case"millis":return aS(n);case"second":case"seconds":return a_(n);case"minute":case"minutes":return aw(n);case"hour":case"hours":return ak(n);case"day":case"days":return aO(n);case"week":case"weeks":return aC(n)}}}throw Error("Invalid DurationInput")},ac={_tag:"Millis",millis:0},ah={_tag:"Infinity"},ap={[at]:at,[eI](){return e$(this,eP(this.value))},[eL](e){return ad(e)&&a$(this,e)},toString(){return`Duration(${aU(this)})`},toJSON(){switch(this.value._tag){case"Millis":return{_id:"Duration",_tag:"Millis",millis:this.value.millis};case"Nanos":return{_id:"Duration",_tag:"Nanos",hrtime:aT(this)};case"Infinity":return{_id:"Duration",_tag:"Infinity"}}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},af=e=>{let t=Object.create(ap);return G(e)?isNaN(e)||e<=0?t.value=ac:Number.isFinite(e)?Number.isInteger(e)?t.value={_tag:"Millis",millis:e}:t.value={_tag:"Nanos",nanos:BigInt(Math.round(1e6*e))}:t.value=ah:e<=ar?t.value=ac:t.value={_tag:"Nanos",nanos:e},t},ad=e=>ea(e,at),am=e=>"Infinity"!==e.value._tag,ag=e=>{switch(e.value._tag){case"Millis":return 0===e.value.millis;case"Nanos":return e.value.nanos===ar;case"Infinity":return!1}},ab=af(0),ax=af(1/0),ay=e=>af(e),av=e=>af(e*as),aS=e=>af(e),a_=e=>af(1e3*e),aw=e=>af(6e4*e),ak=e=>af(36e5*e),aO=e=>af(864e5*e),aC=e=>af(6048e5*e),aE=e=>aR(e,{onMillis:e=>e,onNanos:e=>Number(e)/1e6}),aI=e=>aR(e,{onMillis:e=>e/1e3,onNanos:e=>Number(e)/1e9}),aF=e=>{let t=au(e);switch(t.value._tag){case"Infinity":throw Error("Cannot convert infinite duration to nanos");case"Nanos":return t.value.nanos;case"Millis":return BigInt(Math.round(1e6*t.value.millis))}},aT=e=>{let t=au(e);switch(t.value._tag){case"Infinity":return[1/0,0];case"Nanos":return[Number(t.value.nanos/ao),Number(t.value.nanos%ao)];case"Millis":return[Math.floor(t.value.millis/1e3),Math.round(t.value.millis%1e3*1e6)]}},aR=D(2,(e,t)=>{let r=au(e);switch(r.value._tag){case"Nanos":return t.onNanos(r.value.nanos);case"Infinity":return t.onMillis(1/0);case"Millis":return t.onMillis(r.value.millis)}}),aN=D(3,(e,t,r)=>{let i=au(e),n=au(t);if("Infinity"===i.value._tag||"Infinity"===n.value._tag)return r.onMillis(aE(i),aE(n));if("Nanos"===i.value._tag||"Nanos"===n.value._tag){let e="Nanos"===i.value._tag?i.value.nanos:BigInt(Math.round(1e6*i.value.millis)),t="Nanos"===n.value._tag?n.value.nanos:BigInt(Math.round(1e6*n.value.millis));return r.onNanos(e,t)}return r.onMillis(i.value.millis,n.value.millis)}),aA=(e,t)=>aN(e,t,{onMillis:(e,t)=>e===t,onNanos:(e,t)=>e===t}),aj=D(2,(e,t)=>aN(e,t,{onMillis:(e,t)=>af(e+t),onNanos:(e,t)=>af(e+t)})),aM=D(2,(e,t)=>aN(e,t,{onMillis:(e,t)=>e<t,onNanos:(e,t)=>e<t})),az=D(2,(e,t)=>aN(e,t,{onMillis:(e,t)=>e<=t,onNanos:(e,t)=>e<=t})),aP=D(2,(e,t)=>aN(e,t,{onMillis:(e,t)=>e>t,onNanos:(e,t)=>e>t})),aD=D(2,(e,t)=>aN(e,t,{onMillis:(e,t)=>e>=t,onNanos:(e,t)=>e>=t})),a$=D(2,(e,t)=>aA(au(e),au(t))),aL=e=>{let t=au(e);if("Infinity"===t.value._tag)return{days:1/0,hours:1/0,minutes:1/0,seconds:1/0,millis:1/0,nanos:1/0};let r=aF(t),i=r/aa,n=i/as,s=n/an,a=s/an;return{days:Number(a/ai),hours:Number(a%ai),minutes:Number(s%an),seconds:Number(n%an),millis:Number(i%as),nanos:Number(r%aa)}},aU=e=>{let t=au(e);if("Infinity"===t.value._tag)return"Infinity";if(ag(t))return"0";let r=aL(t),i=[];return 0!==r.days&&i.push(`${r.days}d`),0!==r.hours&&i.push(`${r.hours}h`),0!==r.minutes&&i.push(`${r.minutes}m`),0!==r.seconds&&i.push(`${r.seconds}s`),0!==r.millis&&i.push(`${r.millis}ms`),0!==r.nanos&&i.push(`${r.nanos}ns`),i.join(" ")},aq=Symbol.for("effect/MutableRef"),aB={[aq]:aq,toString(){return eV(this.toJSON())},toJSON(){return{_id:"MutableRef",current:eK(this.current)}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},aJ=e=>{let t=Object.create(aB);return t.current=e,t},aH=D(3,(e,t,r)=>!!eU(t,e.current)&&(e.current=r,!0)),aK=e=>e.current,aV=D(2,(e,t)=>(e.current=t,e)),aW="effect/FiberId",aG=Symbol.for(aW),aY="None",aZ="Runtime",aQ="Composite",aX=eM(`${aW}-${aY}`);class a0{[eI](){return aX}[eL](e){return a5(e)&&e._tag===aY}toString(){return eV(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag}}[eH](){return this.toJSON()}constructor(){this[aG]=aG,this._tag=aY,this.id=-1,this.startTimeMillis=-1}}class a1{constructor(e,t){this[aG]=aG,this._tag=aZ,this.id=e,this.startTimeMillis=t}[eI](){return e$(this,eM(`${aW}-${this._tag}-${this.id}-${this.startTimeMillis}`))}[eL](e){return a5(e)&&e._tag===aZ&&this.id===e.id&&this.startTimeMillis===e.startTimeMillis}toString(){return eV(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,id:this.id,startTimeMillis:this.startTimeMillis}}[eH](){return this.toJSON()}}class a2{constructor(e,t){this[aG]=aG,this._tag=aQ,this.left=e,this.right=t}[eI](){return J(eM(`${aW}-${this._tag}`),eR(eF(this.left)),eR(eF(this.right)),e$(this))}[eL](e){return a5(e)&&e._tag===aQ&&eU(this.left,e.left)&&eU(this.right,e.right)}toString(){return eV(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,left:eK(this.left),right:eK(this.right)}}[eH](){return this.toJSON()}}let a3=new a0,a5=e=>ea(e,aG),a4=e=>e._tag===aY||J(ot(e),n$(e=>a4(e))),a6=D(2,(e,t)=>e._tag===aY?t:t._tag===aY?e:new a2(e,t)),a8=D(2,(e,t)=>a4(e)?t:e),a7=e=>{switch(e._tag){case aY:return nz();case aZ:return nY(e.id);case aQ:return J(a7(e.left),nK(a7(e.right)))}},a9=V(Symbol.for("effect/Fiber/Id/_fiberCounter"),()=>aJ(0)),oe=e=>Array.from(a7(e)).map(e=>`#${e}`).join(","),ot=e=>{switch(e._tag){case aY:return nz();case aZ:return nY(e);case aQ:return J(ot(e.left),nK(ot(e.right)))}},or=a3,oi=(e,t)=>new a1(e,t),on=(e,t)=>new a2(e,t),os=a5,oa=e=>J(e,nW(a3,(e,t)=>a6(t)(e))),oo=()=>{let e=aK(a9);return J(a9,aV(e+1)),new a1(e,Date.now())},ol=e=>{let t=n_(nf());for(let r of e)nx(t,r[0],r[1]);return nw(t)},ou=e=>e&&i8(e._root),oc=Symbol.for("effect/List"),oh=e=>rw(e),op=tW(ia(eU),oh),of={[oc]:oc,_tag:"Cons",toString(){return eV(this.toJSON())},toJSON(){return{_id:"List",_tag:"Cons",values:oh(this).map(eK)}},[eH](){return this.toJSON()},[eL](e){return ob(e)&&this._tag===e._tag&&op(this,e)},[eI](){return e$(this,eD(oh(this)))},[Symbol.iterator](){let e=!1,t=this;return{next(){if(e)return this.return();if("Nil"===t._tag)return e=!0,this.return();let r=t.head;return t=t.tail,{done:e,value:r}},return:t=>(e||(e=!0),{done:!0,value:t})}},pipe(){return e3(this,arguments)}},od=(e,t)=>{let r=Object.create(of);return r.head=e,r.tail=t,r},om=eM("Nil"),og=Object.create({[oc]:oc,_tag:"Nil",toString(){return eV(this.toJSON())},toJSON:()=>({_id:"List",_tag:"Nil"}),[eH](){return this.toJSON()},[eI]:()=>om,[eL](e){return ob(e)&&this._tag===e._tag},[Symbol.iterator]:()=>({next:()=>({done:!0,value:void 0})}),pipe(){return e3(this,arguments)}}),ob=e=>ea(e,oc),ox=e=>"Nil"===e._tag,oy=e=>"Cons"===e._tag,ov=(e,t)=>od(e,t),oS=()=>og,o_=e=>od(e,og),ow=D(2,(e,t)=>oO(t,e)),ok=D(2,(e,t)=>ov(t,e)),oO=D(2,(e,t)=>{if(ox(e))return t;if(ox(t))return e;{let r=od(t.head,e),i=r,n=t.tail;for(;!ox(n);){let t=od(n.head,e);i.tail=t,i=t,n=n.tail}return r}}),oC=D(3,(e,t,r)=>{let i=t,n=e;for(;!ox(n);)i=r(i,n.head),n=n.tail;return i}),oE=e=>{let t=oS(),r=e;for(;!ox(r);)t=ok(t,r.head),r=r.tail;return t};Array.prototype;let oI=function(){function e(e){e&&Object.assign(this,e)}return e.prototype=td,e}(),oF=e=>Object.assign(Object.create(td),e),oT=Symbol.for("effect/DifferChunkPatch");function oR(e){return e}let oN=Object.assign(Object.create({...oI.prototype,[oT]:{_Value:oR,_Patch:oR}}),{_tag:"AndThen"}),oA=Symbol.for("effect/DifferContextPatch");function oj(e){return e}let oM={...oI.prototype,[oA]:{_Value:oj,_Patch:oj}},oz=Object.create(Object.assign(Object.create(oM),{_tag:"Empty"})),oP=()=>oz,oD=Object.assign(Object.create(oM),{_tag:"AndThen"}),o$=(e,t)=>{let r=Object.create(oD);return r.first=e,r.second=t,r},oL=Object.assign(Object.create(oM),{_tag:"AddService"}),oU=(e,t)=>{let r=Object.create(oL);return r.key=e,r.service=t,r},oq=Object.assign(Object.create(oM),{_tag:"RemoveService"}),oB=e=>{let t=Object.create(oq);return t.key=e,t},oJ=Object.assign(Object.create(oM),{_tag:"UpdateService"}),oH=(e,t)=>{let r=Object.create(oJ);return r.key=e,r.update=t,r},oK=(e,t)=>{let r=new Map(e.unsafeMap),i=oP();for(let[e,n]of t.unsafeMap.entries())if(r.has(e)){let t=r.get(e);r.delete(e),eU(t,n)||(i=oV(oH(e,()=>n))(i))}else r.delete(e),i=oV(oU(e,n))(i);for(let[e]of r.entries())i=oV(oB(e))(i);return i},oV=D(2,(e,t)=>o$(e,t)),oW=D(2,(e,t)=>{if("Empty"===e._tag)return t;let r=!1,i=iS(e),n=new Map(t.unsafeMap);for(;iq(i);){let e=iJ(i),t=iG(i);switch(e._tag){case"Empty":i=t;break;case"AddService":n.set(e.key,e.service),i=t;break;case"AndThen":i=iR(iR(t,e.second),e.first);break;case"RemoveService":n.delete(e.key),i=t;break;case"UpdateService":n.set(e.key,e.update(n.get(e.key))),r=!0,i=t}}if(!r)return sV(n);let s=new Map;for(let[e]of t.unsafeMap)n.has(e)&&(s.set(e,n.get(e)),n.delete(e));for(let[e,t]of n)s.set(e,t);return sV(s)}),oG=Symbol.for("effect/DifferHashMapPatch");function oY(e){return e}let oZ=Object.assign(Object.create({...oI.prototype,[oG]:{_Value:oY,_Key:oY,_Patch:oY}}),{_tag:"AndThen"}),oQ=Symbol.for("effect/DifferHashSetPatch");function oX(e){return e}let o0={...oI.prototype,[oQ]:{_Value:oX,_Key:oX,_Patch:oX}},o1=Object.create(Object.assign(Object.create(o0),{_tag:"Empty"})),o2=()=>o1,o3=Object.assign(Object.create(o0),{_tag:"AndThen"}),o5=(e,t)=>{let r=Object.create(o3);return r.first=e,r.second=t,r},o4=Object.assign(Object.create(o0),{_tag:"Add"}),o6=e=>{let t=Object.create(o4);return t.value=e,t},o8=Object.assign(Object.create(o0),{_tag:"Remove"}),o7=e=>{let t=Object.create(o8);return t.value=e,t},o9=(e,t)=>{let[r,i]=nW([e,o2()],([e,t],r)=>nP(r)(e)?[nJ(r)(e),t]:[e,le(o6(r))(t)])(t);return nW(i,(e,t)=>le(o7(t))(e))(r)},le=D(2,(e,t)=>o5(e,t)),lt=D(2,(e,t)=>{if("Empty"===e._tag)return t;let r=t,i=iS(e);for(;iq(i);){let e=iJ(i),t=iG(i);switch(e._tag){case"Empty":i=t;break;case"AndThen":i=iR(e.first)(iR(e.second)(t));break;case"Add":r=nB(e.value)(r),i=t;break;case"Remove":r=nJ(e.value)(r),i=t}}return r}),lr=Symbol.for("effect/DifferOrPatch");function li(e){return e}let ln={...oI.prototype,[lr]:{_Value:li,_Key:li,_Patch:li}},ls=Object.create(Object.assign(Object.create(ln),{_tag:"Empty"})),la=()=>ls,lo=Object.assign(Object.create(ln),{_tag:"AndThen"}),ll=(e,t)=>{let r=Object.create(lo);return r.first=e,r.second=t,r},lu=Object.assign(Object.create(ln),{_tag:"SetLeft"}),lc=e=>{let t=Object.create(lu);return t.value=e,t},lh=Object.assign(Object.create(ln),{_tag:"SetRight"}),lp=e=>{let t=Object.create(lh);return t.value=e,t},lf=Object.assign(Object.create(ln),{_tag:"UpdateLeft"}),ld=e=>{let t=Object.create(lf);return t.patch=e,t},lm=Object.assign(Object.create(ln),{_tag:"UpdateRight"}),lg=e=>{let t=Object.create(lm);return t.patch=e,t},lb=((e,t)=>ll(e,t),Symbol.for("effect/DifferReadonlyArrayPatch"));function lx(e){return e}let ly={...oI.prototype,[lb]:{_Value:lx,_Patch:lx}},lv=Object.create(Object.assign(Object.create(ly),{_tag:"Empty"})),lS=()=>lv,l_=Object.assign(Object.create(ly),{_tag:"AndThen"}),lw=(e,t)=>{let r=Object.create(l_);return r.first=e,r.second=t,r},lk=Object.assign(Object.create(ly),{_tag:"Append"}),lO=e=>{let t=Object.create(lk);return t.values=e,t},lC=Object.assign(Object.create(ly),{_tag:"Slice"}),lE=(e,t)=>{let r=Object.create(lC);return r.from=e,r.until=t,r},lI=Object.assign(Object.create(ly),{_tag:"Update"}),lF=(e,t)=>{let r=Object.create(lI);return r.index=e,r.patch=t,r},lT=e=>{let t=0,r=lS();for(;t<e.oldValue.length&&t<e.newValue.length;){let i=e.oldValue[t],n=e.newValue[t],s=e.differ.diff(i,n);eU(s,e.differ.empty)||(r=lR(r,lF(t,s))),t+=1}return t<e.oldValue.length&&(r=lR(r,lE(0,t))),t<e.newValue.length&&(r=lR(r,lO(rJ(t)(e.newValue)))),r},lR=D(2,(e,t)=>lw(e,t)),lN=D(3,(e,t,r)=>{if("Empty"===e._tag)return t;let i=t.slice(),n=r2(e);for(;tZ(n);){let e=rD(n),t=rU(n);switch(e._tag){case"Empty":n=t;break;case"AndThen":t.unshift(e.first,e.second),n=t;break;case"Append":for(let t of e.values)i.push(t);n=t;break;case"Slice":i=i.slice(e.from,e.until),n=t;break;case"Update":i[e.index]=r.patch(e.patch,i[e.index]),n=t}}return i}),lA={[Symbol.for("effect/Differ")]:{_P:$,_V:$},pipe(){return e3(this,arguments)}},lj=e=>{let t=Object.create(lA);return t.empty=e.empty,t.diff=e.diff,t.combine=e.combine,t.patch=e.patch,t},lM=()=>lj({empty:oP(),combine:(e,t)=>oV(t)(e),diff:(e,t)=>oK(e,t),patch:(e,t)=>oW(t)(e)}),lz=()=>lj({empty:o2(),combine:(e,t)=>le(t)(e),diff:(e,t)=>o9(e,t),patch:(e,t)=>lt(t)(e)}),lP=e=>lj({empty:lS(),combine:(e,t)=>lR(e,t),diff:(t,r)=>lT({oldValue:t,newValue:r,differ:e}),patch:(t,r)=>lN(t,r,e)}),lD=()=>l$((e,t)=>t),l$=e=>lj({empty:$,combine:(e,t)=>e===$?t:t===$?e:r=>t(e(r)),diff:(e,t)=>eU(e,t)?$:L(t),patch:(t,r)=>e(r,t(r))}),lL=e=>255&e,lU=e=>e>>8&255,lq=(e,t)=>(255&e)+((t&e&255)<<8),lB=lq(0,0),lJ=D(2,(e,t)=>lq(lL(e)&~t,lU(e))),lH=D(2,(e,t)=>e|t),lK=e=>~e>>>0&255,lV=e=>lZ(e,32),lW=((e,t)=>e&~t,D(2,(e,t)=>e|t)),lG=e=>lY(e)&&!l1(e),lY=e=>lZ(e,1),lZ=D(2,(e,t)=>(e&t)!=0),lQ=(...e)=>e.reduce((e,t)=>e|t,0),lX=lQ(0),l0=e=>lZ(e,4),l1=e=>lZ(e,16),l2=D(2,(e,t)=>lq(e^t,t)),l3=D(2,(e,t)=>e&(lK(lL(t))|lU(t))|lL(t)&lU(t)),l5=lj({empty:lB,diff:(e,t)=>l2(e,t),combine:(e,t)=>lH(t)(e),patch:(e,t)=>l3(t,e)}),l4=e=>lq(e,e),l6=e=>lq(e,0),l8="Pending",l7="Done",l9=Symbol.for("effect/Deferred"),ue={_E:e=>e,_A:e=>e},ut=e=>({_tag:l8,joiners:e}),ur=e=>({_tag:l7,effect:e});class ui{constructor(e){this.called=!1,this.self=e}next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new ui(this.self)}}let un=(e,t)=>{let r=new ul("Blocked");return r.effect_instruction_i0=e,r.effect_instruction_i1=t,r},us=e=>{let t=new ul("RunBlocked");return t.effect_instruction_i0=e,t},ua=Symbol.for("effect/Effect");class uo{constructor(e,t){this._op=to,this.patch=e,this.op=t}}class ul{constructor(e){this.effect_instruction_i0=void 0,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.trace=void 0,this[ua]=tp,this._op=e}[eL](e){return this===e}[eI](){return e$(this,eT(this))}pipe(){return e3(this,arguments)}toJSON(){return{_id:"Effect",_op:this._op,effect_instruction_i0:eK(this.effect_instruction_i0),effect_instruction_i1:eK(this.effect_instruction_i1),effect_instruction_i2:eK(this.effect_instruction_i2)}}toString(){return eV(this.toJSON())}[eH](){return this.toJSON()}[Symbol.iterator](){return new ui(new ev(this))}}class uu{constructor(e){this.effect_instruction_i0=void 0,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.trace=void 0,this[ua]=tp,this._op=e,this._tag=e}[eL](e){return hv(e)&&"Failure"===e._op&&eU(this.effect_instruction_i0,e.effect_instruction_i0)}[eI](){return J(eM(this._tag),eR(eF(this.effect_instruction_i0)),e$(this))}get cause(){return this.effect_instruction_i0}pipe(){return e3(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,cause:this.cause.toJSON()}}toString(){return eV(this.toJSON())}[eH](){return this.toJSON()}[Symbol.iterator](){return new ui(new ev(this))}}class uc{constructor(e){this.effect_instruction_i0=void 0,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.trace=void 0,this[ua]=tp,this._op=e,this._tag=e}[eL](e){return hv(e)&&"Success"===e._op&&eU(this.effect_instruction_i0,e.effect_instruction_i0)}[eI](){return J(eM(this._tag),eR(eF(this.effect_instruction_i0)),e$(this))}get value(){return this.effect_instruction_i0}pipe(){return e3(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,value:eK(this.value)}}toString(){return eV(this.toJSON())}[eH](){return this.toJSON()}[Symbol.iterator](){return new ui(new ev(this))}}let uh=e=>ea(e,ua),up=e=>{let t=new ul(ts);return t.effect_instruction_i0=e,t},uf=D(3,(e,t,r)=>ct(i=>uM(e,e=>uM(uI(u4(()=>i(t(e)))),t=>u4(()=>r(e,t)).pipe(uL({onFailure:e=>{switch(t._tag){case e6:return uR(st(t.effect_instruction_i0,e));case te:return uR(e)}},onSuccess:()=>t})))))),ud=D(2,(e,t)=>uM(e,()=>u5(t))),um=e=>ud(e,void 0),ug=function(){let e=new ul(e4);switch(arguments.length){case 2:e.effect_instruction_i0=arguments[0],e.commit=arguments[1];break;case 3:e.effect_instruction_i0=arguments[0],e.effect_instruction_i1=arguments[1],e.commit=arguments[2];break;case 4:e.effect_instruction_i0=arguments[0],e.effect_instruction_i1=arguments[1],e.effect_instruction_i2=arguments[2],e.commit=arguments[3];break;default:throw Error(em("you're not supposed to end up here"))}return e},ub=(e,t=or)=>{let r;let i=new ul(e5);return i.effect_instruction_i0=t=>{r=e(t)},i.effect_instruction_i1=t,uX(i,e=>uh(r)?r:cr)},ux=(e,t=or)=>u4(()=>ub(e,t)),uy=(e,t=or)=>ug(e,function(){let e,r,i,n;function s(t){e?e(t):void 0===r&&(r=t)}let a=new ul(e5);return a.effect_instruction_i0=t=>{e=t,r&&t(r)},a.effect_instruction_i1=t,1!==this.effect_instruction_i0.length?(n=new AbortController,i=ek(()=>this.effect_instruction_i0(s,n.signal))):i=ek(()=>this.effect_instruction_i0(s)),i||n?uX(a,e=>(n&&n.abort(),i??cr)):a}),uv=D(2,(e,t)=>{let r=new ul(e8);return r.effect_instruction_i0=e,r.effect_instruction_i1=t,r}),uS=D(2,(e,t)=>uU(e,{onFailure:t,onSuccess:u5})),u_=D(3,(e,t,r)=>uv(e,e=>{let i=sp(e);switch(i._tag){case"Left":return t(i.left)?r(i.left):uR(e);case"Right":return uR(i.right)}})),uw=Symbol.for("effect/OriginalAnnotation"),uk=(e,t)=>tC(t)?new Proxy(e,{has:(e,t)=>t===sD||t===uw||t in e,get:(r,i)=>i===sD?t.value:i===uw?e:r[i]}):e,uO=e=>!es(e)||sD in e?uR(n7(e)):up(t=>uR(n7(uk(e,h5(t))))),uC=e=>uN(()=>n7(new ha(e))),uE=e=>uU(e,{onFailure:e=>u5(tz(e)),onSuccess:e=>u5(tP(e))}),uI=e=>u$(e,{onFailure:hI,onSuccess:hM}),uF=e=>!es(e)||sD in e?uR(n8(e)):up(t=>uR(n8(uk(e,h5(t))))),uT=e=>uM(u6(e),uF),uR=e=>{let t=new uu(e6);return t.effect_instruction_i0=e,t},uN=e=>uM(u6(e),uR),uA=up(e=>u5(e.id())),uj=e=>up(t=>e(t.id())),uM=D(2,(e,t)=>{let r=new ul(e7);return r.effect_instruction_i0=e,r.effect_instruction_i1=t,r}),uz=D(2,(e,t)=>uM(e,e=>{let r="function"==typeof t?t(e):t;return uh(r)?r:ed(r)?ub(e=>{r.then(t=>e(u5(t)),t=>e(uF(new hy(t,"An unknown error occurred in Effect.andThen"))))}):u5(r)})),uP=e=>{let t=new ul("OnStep");return t.effect_instruction_i0=e,t},uD=e=>uM(e,$),u$=D(2,(e,t)=>uL(e,{onFailure:e=>u5(t.onFailure(e)),onSuccess:e=>u5(t.onSuccess(e))})),uL=D(2,(e,t)=>{let r=new ul(e9);return r.effect_instruction_i0=e,r.effect_instruction_i1=t.onFailure,r.effect_instruction_i2=t.onSuccess,r}),uU=D(2,(e,t)=>uL(e,{onFailure:e=>{if(su(e).length>0)return uR(sg(e));let r=sl(e);return r.length>0?t.onFailure(iJ(r)):uR(e)},onSuccess:t.onSuccess})),uq=D(2,(e,t)=>u4(()=>{let r=rw(e),i=rS(r.length),n=0;return ud(cs({while:()=>n<r.length,body:()=>t(r[n],n),step:e=>{i[n++]=e}}),i)})),uB=D(2,(e,t)=>u4(()=>{let r=rw(e),i=0;return cs({while:()=>i<r.length,body:()=>t(r[i],i),step:()=>{i++}})})),uJ=D(e=>"boolean"==typeof e[0]||uh(e[0]),(e,t)=>uh(e)?uM(e,e=>e?t.onTrue():t.onFalse()):e?t.onTrue():t.onFalse()),uH=uM(uA,e=>uK(e)),uK=e=>uR(n9(e)),uV=e=>{let t=new ul(tr);return t.effect_instruction_i0=l4(1),t.effect_instruction_i1=()=>e,t},uW=D(2,(e,t)=>ct(r=>uM(uI(r(e)),e=>hK(t,e)))),uG=D(2,(e,t)=>uM(e,e=>u6(()=>t(e)))),uY=D(2,(e,t)=>uU(e,{onFailure:e=>uT(()=>t.onFailure(e)),onSuccess:e=>u6(()=>t.onSuccess(e))})),uZ=D(2,(e,t)=>uL(e,{onFailure:e=>{let r=sp(e);switch(r._tag){case"Left":return uT(()=>t(r.left));case"Right":return uR(r.right)}},onSuccess:u5})),uQ=((e,t)=>uQ(e,e=>h_(e)?cr:t(e.effect_instruction_i0)),D(2,(e,t)=>ct(r=>uL(r(e),{onFailure:e=>{let r=hI(e);return uL(t(r),{onFailure:t=>hI(st(e,t)),onSuccess:()=>r})},onSuccess:e=>{let r=hM(e);return ch(t(r),r)}})))),uX=D(2,(e,t)=>uQ(e,hA({onFailure:e=>so(e)?um(t(sc(e))):cr,onSuccess:()=>cr}))),u0=D(2,(e,t)=>u9(e,t,u5)),u1=e=>u2(e,$),u2=D(2,(e,t)=>uU(e,{onFailure:e=>uO(t(e)),onSuccess:u5})),u3=up((e,t)=>u5(t.runtimeFlags)),u5=e=>{let t=new uc(te);return t.effect_instruction_i0=e,t},u4=e=>{let t=new ul(e4);return t.commit=e,t},u6=e=>{let t=new ul(tt);return t.effect_instruction_i0=e,t},u8=D(e=>3===e.length||2===e.length&&!(es(e[1])&&"onlyEffect"in e[1]),(e,t)=>uM(e,e=>{let r="function"==typeof t?t(e):t;return uh(r)?ud(r,e):ed(r)?ub(t=>{r.then(r=>t(u5(e)),e=>t(uF(new hy(e,"An unknown error occurred in Effect.tap"))))}):u5(e)})),u7=e=>up(t=>{let r=J(t.getFiberRef(c0),t0(()=>t.scope()));return e(cz(c0,tI(r)))}),u9=D(3,(e,t,r)=>uL(e,{onFailure:e=>su(e).length>0?uR(t8(sd(e))):t(),onSuccess:r})),ce=e=>{let t=new ul(tr);return t.effect_instruction_i0=l6(1),t.effect_instruction_i1=()=>e,t},ct=e=>ug(e,function(){let e=new ul(tr);return e.effect_instruction_i0=l6(1),e.effect_instruction_i1=e=>lY(e)?ek(()=>this.effect_instruction_i0(uV)):ek(()=>this.effect_instruction_i0(ce)),e}),cr=u5(void 0),ci=e=>{let t=new ul(tr);return t.effect_instruction_i0=e,t.effect_instruction_i1=void 0,t},cn=D(2,(e,t)=>uM(t,t=>t?J(e,uG(tI)):u5(tQ()))),cs=e=>{let t=new ul(ti);return t.effect_instruction_i0=e.while,t.effect_instruction_i1=e.body,t.effect_instruction_i2=e.step,t},ca=e=>u4(()=>{let t=new ul(tn);return t.effect_instruction_i0=e(),t}),co=D(2,(e,t)=>{let r=new ul(tr);return r.effect_instruction_i0=t,r.effect_instruction_i1=()=>e,r}),cl=e=>{let t=new ul(ta);return void 0!==e?.priority?cG(t,e.priority):t},cu=D(2,(e,t)=>uM(e,e=>uG(t,t=>[e,t]))),cc=D(2,(e,t)=>uM(e,e=>ud(t,e))),ch=D(2,(e,t)=>uM(e,()=>t)),cp=D(3,(e,t,r)=>uM(e,e=>uG(t,t=>r(e,t)))),cf=ux(()=>{let e=setInterval(()=>{},0x80000000-1);return u6(()=>clearInterval(e))}),cd=e=>uM(uA,t=>J(e,cm(t))),cm=D(2,(e,t)=>uM(e.interruptAsFork(t),()=>e.await)),cg={_tag:"All",syslog:0,label:"ALL",ordinal:Number.MIN_SAFE_INTEGER,pipe(){return e3(this,arguments)}},cb={_tag:"Fatal",syslog:2,label:"FATAL",ordinal:5e4,pipe(){return e3(this,arguments)}},cx={_tag:"Error",syslog:3,label:"ERROR",ordinal:4e4,pipe(){return e3(this,arguments)}},cy={_tag:"Warning",syslog:4,label:"WARN",ordinal:3e4,pipe(){return e3(this,arguments)}},cv={_tag:"Info",syslog:6,label:"INFO",ordinal:2e4,pipe(){return e3(this,arguments)}},cS={_tag:"Debug",syslog:7,label:"DEBUG",ordinal:1e4,pipe(){return e3(this,arguments)}},c_={_tag:"Trace",syslog:7,label:"TRACE",ordinal:0,pipe(){return e3(this,arguments)}},cw={_tag:"None",syslog:7,label:"OFF",ordinal:Number.MAX_SAFE_INTEGER,pipe(){return e3(this,arguments)}},ck=[cg,c_,cS,cv,cy,cx,cb,cw],cO=Symbol.for("effect/FiberRef"),cC={_A:e=>e},cE=e=>up(t=>hM(t.getFiberRef(e))),cI=D(2,(e,t)=>uM(cE(e),t)),cF=D(2,(e,t)=>cT(e,()=>[void 0,t])),cT=D(2,(e,t)=>up(r=>{let[i,n]=t(r.getFiberRef(e));return r.setFiberRef(e,n),u5(i)})),cR=D(2,(e,t)=>cT(e,e=>[void 0,t(e)])),cN=Symbol.for("effect/RequestResolver"),cA={_A:e=>e,_R:e=>e};class cj{constructor(e,t){this[cN]=cA,this.runAll=e,this.target=t}[eI](){return e$(this,this.target?eF(this.target):eT(this))}[eL](e){return this.target?cM(e)&&eU(this.target,e.target):this===e}identified(...e){return new cj(this.runAll,i_(e))}pipe(){return e3(this,arguments)}}let cM=e=>ea(e,cN),cz=D(3,(e,t,r)=>uf(cc(cE(t),cF(t,r)),()=>e,e=>cF(t,e))),cP=D(3,(e,t,r)=>cI(t,i=>cz(e,t,r(i)))),cD=(e,t)=>cq(e,{differ:lD(),fork:t?.fork??$,join:t?.join}),c$=e=>{let t=lz();return cq(e,{differ:t,fork:t.empty})},cL=e=>{let t=lP(lD());return cq(e,{differ:t,fork:t.empty})},cU=e=>{let t=lM();return cq(e,{differ:t,fork:t.empty})},cq=(e,t)=>({...tm,[cO]:cC,initial:e,commit(){return cE(this)},diff:(e,r)=>t.differ.diff(e,r),combine:(e,r)=>t.differ.combine(e,r),patch:e=>r=>t.differ.patch(e,r),fork:t.fork,join:t.join??((e,t)=>t)}),cB=V(Symbol.for("effect/FiberRef/currentContext"),()=>cU(s7())),cJ=V(Symbol.for("effect/FiberRef/currentSchedulingPriority"),()=>cD(0)),cH=V(Symbol.for("effect/FiberRef/currentMaxOpsBeforeYield"),()=>cD(2048)),cK=V(Symbol.for("effect/FiberRef/currentLogAnnotation"),()=>cD(nf())),cV=V(Symbol.for("effect/FiberRef/currentLogLevel"),()=>cD(cv)),cW=V(Symbol.for("effect/FiberRef/currentLogSpan"),()=>cD(oS())),cG=D(2,(e,t)=>cz(e,cJ,t)),cY=V(Symbol.for("effect/FiberRef/currentConcurrency"),()=>cD("unbounded")),cZ=V(Symbol.for("effect/FiberRef/currentRequestBatching"),()=>cD(!0)),cQ=V(Symbol.for("effect/FiberRef/currentUnhandledErrorLogLevel"),()=>cD(tI(cS))),cX=V(Symbol.for("effect/FiberRef/currentMetricLabels"),()=>cL(r1())),c0=V(Symbol.for("effect/FiberRef/currentForkScopeOverride"),()=>cD(tQ(),{fork:()=>tQ(),join:(e,t)=>e})),c1=V(Symbol.for("effect/FiberRef/currentInterruptedCause"),()=>cD(n6,{fork:()=>n6,join:(e,t)=>e})),c2=V(Symbol.for("effect/FiberRef/currentTracerEnabled"),()=>cD(!0)),c3=V(Symbol.for("effect/FiberRef/currentTracerTiming"),()=>cD(!0)),c5=V(Symbol.for("effect/FiberRef/currentTracerSpanAnnotations"),()=>cD(nf())),c4=V(Symbol.for("effect/FiberRef/currentTracerSpanLinks"),()=>cD(iy())),c6=Symbol.for("effect/Scope"),c8=Symbol.for("effect/CloseableScope"),c7=(e,t)=>e.addFinalizer(()=>um(t)),c9=(e,t)=>e.addFinalizer(t),he=(e,t)=>e.close(t),ht=(e,t)=>e.fork(t),hr=D(2,(e,t)=>{let r=J(e,sh,t7(t));switch(r._tag){case"None":return J(su(e),iB,tX({onNone:()=>{let t=rw(sc(e)).flatMap(e=>rw(a7(e)).map(e=>`#${e}`));return new hl(t?`Interrupted by fibers: ${t.join(", ")}`:void 0)},onSome:$}));case"Some":return r.value}}),hi=function(){class e extends globalThis.Error{commit(){return uF(this)}toJSON(){let e={...this};return this.message&&(e.message=this.message),this.cause&&(e.cause=this.cause),e}[eH](){return this.toString!==globalThis.Error.prototype.toString?this.stack?`${this.toString()}
${this.stack.split("\n").slice(1).join("\n")}`:this.toString():"Bun"in globalThis?sR(n8(this),{renderErrorCause:!0}):this}}return Object.assign(e.prototype,tg),e}(),hn=(e,t)=>{class r extends hi{constructor(...e){super(...e),this._tag=t}}return Object.assign(r.prototype,e),r.prototype.name=t,r},hs=Symbol.for("effect/Cause/errors/RuntimeException"),ha=hn({[hs]:hs},"RuntimeException"),ho=Symbol.for("effect/Cause/errors/InterruptedException"),hl=hn({[ho]:ho},"InterruptedException"),hu=e=>ea(e,ho),hc=Symbol.for("effect/Cause/errors/IllegalArgument"),hh=hn({[hc]:hc},"IllegalArgumentException"),hp=Symbol.for("effect/Cause/errors/NoSuchElement"),hf=hn({[hp]:hp},"NoSuchElementException"),hd=Symbol.for("effect/Cause/errors/InvalidPubSubCapacityException"),hm=hn({[hd]:hd},"InvalidPubSubCapacityException"),hg=Symbol.for("effect/Cause/errors/Timeout"),hb=hn({[hg]:hg},"TimeoutException"),hx=Symbol.for("effect/Cause/errors/UnknownException"),hy=function(){class e extends hi{constructor(e,t){super(t??"An unknown error occurred",{cause:e}),this._tag="UnknownException",this.error=e}}return Object.assign(e.prototype,{[hx]:hx,name:"UnknownException"}),e}(),hv=e=>uh(e)&&"_tag"in e&&("Success"===e._tag||"Failure"===e._tag),hS=e=>"Failure"===e._tag,h_=e=>"Success"===e._tag,hw=D(2,(e,t)=>{switch(e._tag){case e6:return hI(e.effect_instruction_i0);case te:return hM(t)}}),hk=e=>hw(e,void 0),hO=(e,t)=>hL(e,t?.parallel?se:st),hC=e=>hI(n7(e)),hE=e=>hI(n8(e)),hI=e=>{let t=new uu(e6);return t.effect_instruction_i0=e,t},hF=D(2,(e,t)=>{switch(e._tag){case e6:return hI(e.effect_instruction_i0);case te:return t(e.effect_instruction_i0)}}),hT=D(2,(e,t)=>{switch(e._tag){case e6:return u5(hI(e.effect_instruction_i0));case te:return uI(t(e.effect_instruction_i0))}}),hR=e=>hI(n9(e)),hN=D(2,(e,t)=>{switch(e._tag){case e6:return hI(e.effect_instruction_i0);case te:return hM(t(e.effect_instruction_i0))}}),hA=D(2,(e,{onFailure:t,onSuccess:r})=>{switch(e._tag){case e6:return t(e.effect_instruction_i0);case te:return r(e.effect_instruction_i0)}}),hj=D(2,(e,{onFailure:t,onSuccess:r})=>{switch(e._tag){case e6:return t(e.effect_instruction_i0);case te:return r(e.effect_instruction_i0)}}),hM=e=>{let t=new uc(te);return t.effect_instruction_i0=e,t},hz=hM(void 0),hP=D(2,(e,t)=>h$(e,t,{onSuccess:(e,t)=>[e,t],onFailure:st})),hD=D(2,(e,t)=>h$(e,t,{onSuccess:(e,t)=>t,onFailure:st})),h$=D(3,(e,t,{onFailure:r,onSuccess:i})=>{switch(e._tag){case e6:switch(t._tag){case te:return hI(e.effect_instruction_i0);case e6:return hI(r(e.effect_instruction_i0,t.effect_instruction_i0))}case te:switch(t._tag){case te:return hM(i(e.effect_instruction_i0,t.effect_instruction_i0));case e6:return hI(t.effect_instruction_i0)}}}),hL=(e,t)=>{let r=i_(e);return iq(r)?J(iG(r),it(J(iJ(r),hN(iS)),(e,r)=>J(e,h$(r,{onSuccess:(e,t)=>J(e,iR(t)),onFailure:t}))),hN(iO),hN(e=>ik(e)),tI):tQ()},hU=e=>({...tm,[l9]:ue,state:aJ(ut([])),commit(){return hJ(this)},blockingOn:e}),hq=()=>uM(uA,e=>hB(e)),hB=e=>u6(()=>hU(e)),hJ=e=>ux(t=>{let r=aK(e.state);switch(r._tag){case l7:return t(r.effect);case l8:return r.joiners.push(t),hZ(e,t)}},e.blockingOn),hH=D(2,(e,t)=>u6(()=>{let r=aK(e.state);switch(r._tag){case l7:return!1;case l8:aV(e.state,ur(t));for(let e=0,i=r.joiners.length;e<i;e++)r.joiners[e](t);return!0}})),hK=D(2,(e,t)=>hH(e,t)),hV=((e,t)=>hH(e,uF(t)),D(2,(e,t)=>hH(e,uR(t)))),hW=D(2,(e,t)=>hH(e,uK(t))),hG=D(2,(e,t)=>hH(e,u5(t))),hY=(e,t)=>{let r=aK(e.state);if(r._tag===l8){aV(e.state,ur(t));for(let e=0,i=r.joiners.length;e<i;e++)r.joiners[e](t)}},hZ=(e,t)=>u6(()=>{let r=aK(e.state);if(r._tag===l8){let e=r.joiners.indexOf(t);e>=0&&r.joiners.splice(e,1)}}),hQ=up(e=>hM(e.currentContext)),hX=()=>hQ,h0=e=>uM(hX(),e),h1=D(2,(e,t)=>cz(cB,t)(e)),h2=D(2,(e,t)=>cP(cB,e=>s4(e,t))(e)),h3=D(2,(e,t)=>h0(r=>h1(e,t(r)))),h5=((e,t)=>uM(e,e=>uM(t.predicate(e),r=>r?u5(e):t.orElse(e))),e=>{let t=e.currentSpan;return void 0!==t&&"Span"===t._tag?tI(t):tQ()}),h4={_tag:"Span",spanId:"noop",traceId:"noop",sampled:!1,status:{_tag:"Ended",startTime:BigInt(0),endTime:BigInt(0),exit:hz},attributes:new Map,links:[],kind:"internal",attribute(){},event(){},end(){},addLinks(){}},h6=e=>Object.assign(Object.create(h4),e),h8=Symbol.for("effect/Clock"),h7=s6("effect/Clock"),h9=0x80000000-1,pe={unsafeSchedule(e,t){let r=aE(t);if(r>h9)return q;let i=!1,n=setTimeout(()=>{i=!0,e()},r);return()=>(clearTimeout(n),!i)}},pt=function(){let e;let t=BigInt(1e6);return"undefined"==typeof performance?()=>BigInt(Date.now())*t:()=>(void 0===e&&(e=BigInt(Date.now())*t-BigInt(Math.round(1e6*performance.now()))),e+BigInt(Math.round(1e6*performance.now())))}(),pr=function(){let e="object"==typeof process&&"hrtime"in process&&"function"==typeof process.hrtime.bigint?process.hrtime:void 0;if(!e)return pt;let t=pt()-e.bigint();return()=>t+e.bigint()}();class pi{unsafeCurrentTimeMillis(){return Date.now()}unsafeCurrentTimeNanos(){return pr()}scheduler(){return u5(pe)}sleep(e){return uy(t=>um(u6(pe.unsafeSchedule(()=>t(cr),e))))}constructor(){this[h8]=h8,this.currentTimeMillis=u6(()=>this.unsafeCurrentTimeMillis()),this.currentTimeNanos=u6(()=>this.unsafeCurrentTimeNanos())}}let pn=e=>Math.max(Math.pow(2,Math.ceil(Math.log(e)/Math.log(2))),2),ps=e=>{if("NaN"===e)return tI(NaN);if("Infinity"===e)return tI(1/0);if("-Infinity"===e)return tI(-1/0);if(""===e.trim())return tE;let t=Number(e);return Number.isNaN(t)?tE:tI(t)},pa=e=>e.replace(/[/\\^$*+?.()|[\]{}]/g,"\\$&"),po="InvalidData",pl="MissingData",pu="SourceUnavailable",pc="Unsupported",ph=Symbol.for("effect/ConfigError"),pp={_tag:"ConfigError",[ph]:ph},pf=(e,t)=>{let r=Object.create(pp);return r._op="And",r.left=e,r.right=t,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} and ${this.right}`}}),r},pd=(e,t)=>{let r=Object.create(pp);return r._op="Or",r.left=e,r.right=t,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} or ${this.right}`}}),r},pm=(e,t,r={pathDelim:"."})=>{let i=Object.create(pp);return i._op=po,i.path=e,i.message=t,Object.defineProperty(i,"toString",{enumerable:!1,value(){let e=J(this.path,iu(r.pathDelim));return`(Invalid data at ${e}: "${this.message}")`}}),i},pg=(e,t,r={pathDelim:"."})=>{let i=Object.create(pp);return i._op=pl,i.path=e,i.message=t,Object.defineProperty(i,"toString",{enumerable:!1,value(){let e=J(this.path,iu(r.pathDelim));return`(Missing data at ${e}: "${this.message}")`}}),i},pb=(e,t,r,i={pathDelim:"."})=>{let n=Object.create(pp);return n._op=pu,n.path=e,n.message=t,n.cause=r,Object.defineProperty(n,"toString",{enumerable:!1,value(){let e=J(this.path,iu(i.pathDelim));return`(Source unavailable at ${e}: "${this.message}")`}}),n},px=(e,t,r={pathDelim:"."})=>{let i=Object.create(pp);return i._op=pc,i.path=e,i.message=t,Object.defineProperty(i,"toString",{enumerable:!1,value(){let e=J(this.path,iu(r.pathDelim));return`(Unsupported operation at ${e}: "${this.message}")`}}),i},py=D(2,(e,t)=>{switch(e._op){case"And":return pf(py(e.left,t),py(e.right,t));case"Or":return pd(py(e.left,t),py(e.right,t));case po:return pm([...t,...e.path],e.message);case pl:return pg([...t,...e.path],e.message);case pu:return pb([...t,...e.path],e.message,e.cause);case pc:return px([...t,...e.path],e.message)}}),pv={andCase:(e,t,r)=>t&&r,orCase:(e,t,r)=>t&&r,invalidDataCase:q,missingDataCase:U,sourceUnavailableCase:q,unsupportedCase:q},pS=D(3,(e,t,r)=>{let i=[e],n=[];for(;i.length>0;){let e=i.pop();switch(e._op){case"And":i.push(e.right),i.push(e.left),n.push(tz({_op:"AndCase"}));break;case"Or":i.push(e.right),i.push(e.left),n.push(tz({_op:"OrCase"}));break;case po:n.push(tP(r.invalidDataCase(t,e.path,e.message)));break;case pl:n.push(tP(r.missingDataCase(t,e.path,e.message)));break;case pu:n.push(tP(r.sourceUnavailableCase(t,e.path,e.message,e.cause)));break;case pc:n.push(tP(r.unsupportedCase(t,e.path,e.message)))}}let s=[];for(;n.length>0;){let e=n.pop();switch(e._op){case"Left":switch(e.left._op){case"AndCase":{let e=s.pop(),i=s.pop(),n=r.andCase(t,e,i);s.push(n);break}case"OrCase":{let e=s.pop(),i=s.pop(),n=r.orCase(t,e,i);s.push(n)}}break;case"Right":s.push(e.right)}}if(0===s.length)throw Error("BUG: ConfigError.reduceWithContext - please report an issue at https://github.com/Effect-TS/effect/issues");return s.pop()}),p_={_tag:"Empty"},pw=D(2,(e,t)=>({_tag:"AndThen",first:e,second:t})),pk=D(2,(e,t)=>pw(e,{_tag:"MapName",f:t})),pO=D(2,(e,t)=>pw(e,{_tag:"Nested",name:t})),pC=D(2,(e,t)=>pw(e,{_tag:"Unnested",name:t})),pE=D(2,(e,t)=>{let r=o_(t),i=e;for(;oy(r);){let e=r.head;switch(e._tag){case"Empty":r=r.tail;break;case"AndThen":r=ov(e.first,ov(e.second,r.tail));break;case"MapName":i=r3(i,e.f),r=r.tail;break;case"Nested":i=rE(i,e.name),r=r.tail;break;case"Unnested":if(!J(rP(i),rn(e.name)))return tz(pg(i,`Expected ${e.name} to be in path in ConfigProvider#unnested`));i=rU(i),r=r.tail}}return tP(i)}),pI="Constant",pF="Fallback",pT="Lazy",pR="MapOrFail",pN="Nested",pA="Primitive",pj="ZipWith",pM=e=>e.toLowerCase(),pz=e=>e.toUpperCase(),pP=(e,t,r)=>t instanceof RegExp?e.replace(t,r):t.reduce((e,t)=>e.replace(t,r),e),pD=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],p$=/[^A-Z0-9]+/gi,pL=(e,t={})=>{let{delimiter:r=" ",splitRegexp:i=pD,stripRegexp:n=p$,transform:s=pM}=t,a=pP(pP(e,i,"$1\0$2"),n,"\0"),o=0,l=a.length;for(;"\0"===a.charAt(o);)o++;for(;"\0"===a.charAt(l-1);)l--;return a.slice(o,l).split("\0").map(s).join(r)},pU=(e,t)=>pL(e,{delimiter:"_",transform:pz,...t}),pq=(e,t)=>[...e,...t],pB=Symbol.for("effect/ConfigProvider"),pJ=s6("effect/ConfigProvider"),pH=Symbol.for("effect/ConfigProviderFlat"),pK=e=>({[pB]:pB,pipe(){return e3(this,arguments)},...e}),pV=e=>({[pH]:pH,patch:e.patch,load:(t,r,i=!0)=>e.load(t,r,i),enumerateChildren:e.enumerateChildren}),pW=e=>pK({load:t=>uM(pX(e,r1(),t,!1),e=>tX(rP(e),{onNone:()=>uF(pg(r1(),`Expected a single value having structure: ${t}`)),onSome:u5})),flattened:e}),pG=e=>{let{pathDelim:t,seqDelim:r}=Object.assign({},{pathDelim:"_",seqDelim:","},e),i=e=>J(e,iu(t)),n=e=>e.split(t),s=()=>"undefined"!=typeof process&&"env"in process&&"object"==typeof process.env?process.env:{};return pW(pV({load:(e,t,n=!0)=>{let a=i(e),o=s();return J(a in o?tI(o[a]):tQ(),uZ(()=>pg(e,`Expected ${a} to exist in the process context`)),uM(i=>p8(i,e,t,r,n)))},enumerateChildren:e=>u6(()=>nG(Object.keys(s()).map(e=>n(e.toUpperCase())).filter(t=>{for(let r=0;r<e.length;r++){let i=J(e,rz(r)),n=t[r];if(void 0===n||i!==n)return!1}return!0}).flatMap(t=>t.slice(e.length,e.length+1)))),patch:p_}))},pY=(e,t)=>{let{pathDelim:r,seqDelim:i}=Object.assign({seqDelim:",",pathDelim:"."},t),n=e=>J(e,iu(r)),s=e=>e.split(r),a=fi(e,e=>s(e),n);return pW(pV({load:(e,t,r=!0)=>{let s=n(e);return J(a.has(s)?tI(a.get(s)):tQ(),uZ(()=>pg(e,`Expected ${s} to exist in the provided map`)),uM(n=>p8(n,e,t,i,r)))},enumerateChildren:e=>u6(()=>nG(rw(a.keys()).map(s).filter(t=>{for(let r=0;r<e.length;r++){let i=J(e,rz(r)),n=t[r];if(void 0===n||i!==n)return!1}return!0}).flatMap(t=>t.slice(e.length,e.length+1)))),patch:p_}))},pZ=(e,t,r,i)=>{let n=is(r.length,t=>t>=i.length?tQ():tI([e(t),t+1])),s=is(i.length,e=>e>=r.length?tQ():tI([t(e),e+1]));return[pq(r,n),pq(i,s)]},pQ=(e,t)=>{let r=t;if("Nested"===r._tag){let t=e.slice();for(;"Nested"===r._tag;)t.push(r.name),r=r.config;return t}return e},pX=(e,t,r,i)=>{switch(r._tag){case pI:return u5(r2(r.value));case"Described":return u4(()=>pX(e,t,r.config,i));case"Fail":return uF(pg(t,r.message));case pF:return J(u4(()=>pX(e,t,r.first,i)),uS(n=>r.condition(n)?J(pX(e,t,r.second,i),uS(e=>uF(pd(n,e)))):uF(n)));case pT:return u4(()=>pX(e,t,r.config(),i));case pR:return u4(()=>J(pX(e,t,r.original,i),uM(uq(e=>J(r.mapOrFail(e),uZ(py(pQ(t,r.original))))))));case pN:return u4(()=>pX(e,pq(t,r2(r.name)),r.config,i));case pA:return J(pE(t,e.patch),uM(t=>J(e.load(t,r,i),uM(e=>{if(0===e.length){let e=J(r$(t),t0(()=>"<n/a>"));return uF(pg([],`Expected ${r.description} with name ${e}`))}return u5(e)}))));case"Sequence":return J(pE(t,e.patch),uM(i=>J(e.enumerateChildren(i),uM(p9),uM(i=>0===i.length?u4(()=>uG(pX(e,t,r.config,!0),r2)):J(uq(i,i=>pX(e,rI(t,`[${i}]`),r.config,!0)),uG(e=>{let t=r4(e);return 0===t.length?r2(r1()):r2(t)}))))));case"HashMap":return u4(()=>J(pE(t,e.patch),uM(t=>J(e.enumerateChildren(t),uM(n=>J(n,uq(n=>pX(e,pq(t,r2(n)),r.valueConfig,i)),uG(e=>0===e.length?r2(nf()):J(p7(e),r3(e=>ol(rV(rw(n),e)))))))))));case pj:return u4(()=>J(pX(e,t,r.left,i),uE,uM(n=>J(pX(e,t,r.right,i),uE,uM(e=>{if(tj(n)&&tj(e))return uF(pf(n.left,e.left));if(tj(n)&&tM(e))return uF(n.left);if(tM(n)&&tj(e))return uF(e.left);if(tM(n)&&tM(e)){let i=J(t,iu(".")),s=p0(t,i),[a,o]=pZ(s,s,J(n.right,r3(tP)),J(e.right,r3(tP)));return J(a,rV(o),uq(([e,t])=>J(cu(e,t),uG(([e,t])=>r.zip(e,t)))))}throw Error("BUG: ConfigProvider.fromFlatLoop - please report an issue at https://github.com/Effect-TS/effect/issues")})))))}},p0=(e,t)=>r=>tz(pg(e,`The element at index ${r} in a sequence at path "${t}" was missing`)),p1=D(2,(e,t)=>pW(p2(e.flattened,t))),p2=(e,t)=>pV({load:(t,r,i=!0)=>e.load(t,r,i),enumerateChildren:t=>e.enumerateChildren(t),patch:pk(e.patch,t)}),p3=D(2,(e,t)=>pW(pV({load:(t,r)=>e.flattened.load(t,r,!0),enumerateChildren:t=>e.flattened.enumerateChildren(t),patch:pO(e.flattened.patch,t)}))),p5=((e,t)=>pW(pV({load:(t,r)=>e.flattened.load(t,r,!0),enumerateChildren:t=>e.flattened.enumerateChildren(t),patch:pC(e.flattened.patch,t)})),D(2,(e,t)=>pW(p4(e.flattened,()=>t().flattened)))),p4=(e,t)=>pV({load:(r,i,n)=>J(pE(r,e.patch),uM(t=>e.load(t,i,n)),uS(e=>J(u6(t),uM(t=>J(pE(r,t.patch),uM(e=>t.load(e,i,n)),uS(t=>uF(pd(e,t)))))))),enumerateChildren:r=>J(pE(r,e.patch),uM(t=>e.enumerateChildren(t)),uE,uM(e=>J(u6(t),uM(t=>J(pE(r,t.patch),uM(e=>t.enumerateChildren(e)),uE,uM(t=>{if(tj(e)&&tj(t))return uF(pf(e.left,t.left));if(tj(e)&&tM(t))return u5(t.right);if(tM(e)&&tj(t))return u5(e.right);if(tM(e)&&tM(t))return u5(J(e.right,nK(t.right)));throw Error("BUG: ConfigProvider.orElseFlat - please report an issue at https://github.com/Effect-TS/effect/issues")})))))),patch:p_}),p6=(e,t)=>e.split(RegExp(`\\s*${pa(t)}\\s*`)),p8=(e,t,r,i,n)=>n?J(p6(e,i),uq(e=>r.parse(e.trim())),uZ(py(t))):J(r.parse(e),uY({onFailure:py(t),onSuccess:r2})),p7=e=>Object.keys(e[0]).map(t=>e.map(e=>e[t])),p9=e=>J(uq(e,fr),uY({onFailure:()=>r1(),onSuccess:rK(rc)}),uE,uG(tB)),fe=/(^.+)(\[(\d+)\])$/,ft=/^(\[(\d+)\])$/,fr=e=>{let t=e.match(ft);if(null!==t){let e=t[2];return J(void 0!==e&&e.length>0?tI(e):tQ(),t9(fs))}return tQ()},fi=(e,t,r)=>{let i=new Map;for(let[n,s]of e){let e=J(t(n),r5(e=>tX(fn(e),{onNone:()=>r2(e),onSome:([e,t])=>rv(e,`[${t}]`)})));i.set(r(e),s)}return i},fn=e=>{let t=e.match(fe);if(null!==t){let e=t[1],r=t[3];return rt([void 0!==e&&e.length>0?tI(e):tQ(),J(void 0!==r&&r.length>0?tI(r):tQ(),t9(fs))])}return tQ()},fs=e=>{let t=Number.parseInt(e);return Number.isNaN(t)?tQ():tI(t)},fa=e=>({_tag:"KeyName",name:e}),fo=e=>({_tag:"KeyIndex",index:e}),fl=e=>{let t=[],r=0;for(;r<e.length;){let i=e[r];if("KeyName"===i._tag){if(r+1<e.length){let n=e[r+1];"KeyIndex"===n._tag?(t.push(`${i.name}[${n.index}]`),r+=2):(t.push(i.name),r+=1)}else t.push(i.name),r+=1}}return t},fu=e=>{let t=(e,t)=>"string"==typeof t?rv([e,t]):"number"==typeof t||"boolean"==typeof t?rv([e,String(t)]):rT(t)?r(e,t):"object"==typeof t&&null!==t?i(e,t):r1(),r=(e,r)=>rO(r,{onEmpty:()=>rv([e,"<nil>"]),onNonEmpty:r5((r,i)=>t(rI(e,fo(i)),r))}),i=(e,r)=>Object.entries(r).filter(([,e])=>eu(e)).flatMap(([r,i])=>{let n=rI(e,fa(r)),s=t(n,i);return rR(s)?rv([n,""]):s});return i(r1(),e)},fc=Symbol.for("effect/Console"),fh=s6("effect/Console"),fp={[fc]:fc,assert:(e,...t)=>u6(()=>{console.assert(e,...t)}),clear:u6(()=>{console.clear()}),count:e=>u6(()=>{console.count(e)}),countReset:e=>u6(()=>{console.countReset(e)}),debug:(...e)=>u6(()=>{console.debug(...e)}),dir:(e,t)=>u6(()=>{console.dir(e,t)}),dirxml:(...e)=>u6(()=>{console.dirxml(...e)}),error:(...e)=>u6(()=>{console.error(...e)}),group:e=>e?.collapsed?u6(()=>console.groupCollapsed(e?.label)):u6(()=>console.group(e?.label)),groupEnd:u6(()=>{console.groupEnd()}),info:(...e)=>u6(()=>{console.info(...e)}),log:(...e)=>u6(()=>{console.log(...e)}),table:(e,t)=>u6(()=>{console.table(e,t)}),time:e=>u6(()=>console.time(e)),timeEnd:e=>u6(()=>console.timeEnd(e)),timeLog:(e,...t)=>u6(()=>{console.timeLog(e,...t)}),trace:(...e)=>u6(()=>{console.trace(...e)}),warn:(...e)=>u6(()=>{console.warn(...e)}),unsafe:console},ff=Symbol.for("effect/Random"),fd=s6("effect/Random");class fm{constructor(e){this[ff]=ff,this.seed=e,this.PRNG=new eb(e)}get next(){return u6(()=>this.PRNG.number())}get nextBoolean(){return uG(this.next,e=>e>.5)}get nextInt(){return u6(()=>this.PRNG.integer(Number.MAX_SAFE_INTEGER))}nextRange(e,t){return uG(this.next,r=>(t-e)*r+e)}nextIntBetween(e,t){return u6(()=>this.PRNG.integer(t-e)+e)}shuffle(e){return fg(e,e=>this.nextIntBetween(0,e))}}let fg=(e,t)=>u4(()=>J(u6(()=>Array.from(e)),uM(e=>{let r=[];for(let t=e.length;t>=2;t-=1)r.push(t);return J(r,uB(r=>J(t(r),uG(t=>fb(e,r-1,t)))),ud(i_(e)))}))),fb=(e,t,r)=>{let i=e[t];return e[t]=e[r],e[r]=i,e},fx=Symbol.for("effect/Tracer"),fy=s6("effect/Tracer"),fv=s6("effect/ParentSpan"),fS=function(){let e="abcdef0123456789",t=e.length;return function(r){let i="";for(let n=0;n<r;n++)i+=e.charAt(Math.floor(Math.random()*t));return i}}();class f_{constructor(e,t,r,i,n,s){this._tag="Span",this.traceId="native",this.sampled=!0,this.events=[],this.name=e,this.parent=t,this.context=r,this.startTime=n,this.kind=s,this.status={_tag:"Started",startTime:n},this.attributes=new Map,this.traceId="Some"===t._tag?t.value.traceId:fS(32),this.spanId=fS(16),this.links=Array.from(i)}end(e,t){this.status={_tag:"Ended",endTime:e,exit:t,startTime:this.status.startTime}}attribute(e,t){this.attributes.set(e,t)}event(e,t,r){this.events.push([e,t,r??{}])}addLinks(e){this.links.push(...e)}}let fw=e=>{if(e?.captureStackTrace===!1||e?.captureStackTrace!==void 0&&"boolean"!=typeof e.captureStackTrace)return e;let t=Error.stackTraceLimit;Error.stackTraceLimit=3;let r=Error();Error.stackTraceLimit=t;let i=!1;return{...e,captureStackTrace:()=>{if(!1!==i)return i;if(void 0!==r.stack){let e=r.stack.split("\n");if(void 0!==e[3])return i=e[3].trim()}}}},fk=ae()("effect/Tracer/DisablePropagation",{defaultValue:q}),fO=J(s7(),sX(h7,new pi),sX(fh,fp),sX(fd,new fm(eF(Math.random()))),sX(pJ,pG()),sX(fy,{[fx]:fx,span:(e,t,r,i,n,s)=>new f_(e,t,r,i,n,s),context:e=>e()})),fC=V(Symbol.for("effect/DefaultServices/currentServices"),()=>cU(fO)),fE=e=>up(t=>e(t.currentDefaultServices)),fI=e=>fE(t=>e(t.unsafeMap.get(h7.key))),fF=fI(e=>e.currentTimeMillis),fT=e=>fE(t=>e(t.unsafeMap.get(pJ.key))),fR=e=>fT(t=>t.load(e));y=e=>e.next,fE(e=>y(e.unsafeMap.get(fd.key)));let fN=e=>!e,fA=e=>uM(uA,t=>hH(e,uK(t))),fj=e=>u6(()=>aK(e.state)._tag===l7),fM=tb,fz=tx;class fP extends fM{}class fD extends fz{}let f$="Sequential",fL="Parallel",fU={_tag:f$},fq=e=>e._tag===f$,fB=e=>e._tag===fL,fJ={_tag:fL},fH=e=>({_tag:"ParallelN",parallelism:e});function fK(){return new fW(new Map)}let fV=Symbol.for("effect/FiberRefs");class fW{constructor(e){this[fV]=fV,this.locals=e}pipe(){return e3(this,arguments)}}let fG=(e,t,r,i=!1)=>{let n,s=t,a=r,o=i;for(;void 0===n;)if(rN(s)&&rN(a)){let e=rD(s)[0],t=rU(s),r=rD(a)[0],i=rD(a)[1],l=rU(a);e.startTimeMillis<r.startTimeMillis?(a=l,o=!0):e.startTimeMillis>r.startTimeMillis?s=t:e.id<r.id?(a=l,o=!0):e.id>r.id?s=t:n=[i,o]}else n=[e.initial,!0];return n},fY=D(3,(e,t,r)=>{let i=new Map(e.locals);return r.locals.forEach((e,r)=>{let n=e[0][1];if(!e[0][0][eL](t)){if(!i.has(r)){if(eU(n,r.initial))return;i.set(r,[[t,r.join(r.initial,n)]]);return}let s=i.get(r),[a,o]=fG(r,s,e);if(o){let e=r.diff(a,n),o=s[0][1],l=r.join(o,r.patch(e)(o));if(!eU(o,l)){let e;let n=s[0][0];e=n[eL](t)?[[n,l],...s.slice(1)]:[[t,l],...s],i.set(r,e)}}}}),new fW(i)}),fZ=D(2,(e,t)=>{let r=new Map;return fQ(e,r,t),new fW(r)}),fQ=(e,t,r)=>{e.locals.forEach((e,i)=>{let n=e[0][1],s=i.patch(i.fork)(n);eU(n,s)?t.set(i,e):t.set(i,[[r,s],...e])})},fX=D(2,(e,t)=>{let r=new Map(e.locals);return r.delete(t),new fW(r)}),f0=D(2,(e,t)=>e.locals.has(t)?tI(rD(e.locals.get(t))[1]):tQ()),f1=D(2,(e,t)=>J(f0(e,t),t0(()=>t.initial))),f2=D(2,(e,{fiberId:t,fiberRef:r,value:i})=>{if(0===e.locals.size)return new fW(new Map([[r,[[t,i]]]]));let n=new Map(e.locals);return f3(n,t,r,i),new fW(n)}),f3=(e,t,r,i)=>{let n;let s=e.get(r)??[];if(rN(s)){let[e,r]=rD(s);if(e[eL](t)){if(eU(r,i))return;n=[[t,i],...s.slice(1)]}else n=[[t,i],...s]}else n=[[t,i]];e.set(r,n)},f5=D(2,(e,{entries:t,forkAs:r})=>{if(0===e.locals.size)return new fW(new Map(t));let i=new Map(e.locals);return void 0!==r&&fQ(e,i,r),t.forEach(([e,t])=>{1===t.length?f3(i,t[0][0],e,t[0][1]):t.forEach(([t,r])=>{f3(i,t,e,r)})}),new fW(i)}),f4="Empty",f6="Remove",f8="Update",f7="AndThen",f9={_tag:f4},de=(e,t)=>{let r=new Map(e.locals),i=f9;for(let[e,n]of t.locals.entries()){let t=rD(n)[1],s=r.get(e);if(void 0!==s){let r=rD(s)[1];eU(r,t)||(i=dt({_tag:f8,fiberRef:e,patch:e.diff(r,t)})(i))}else i=dt({_tag:"Add",fiberRef:e,value:t})(i);r.delete(e)}for(let[e]of r.entries())i=dt({_tag:f6,fiberRef:e})(i);return i},dt=D(2,(e,t)=>({_tag:f7,first:e,second:t})),dr=D(3,(e,t,r)=>{let i=r,n=r2(e);for(;rN(n);){let e=rD(n),r=rU(n);switch(e._tag){case f4:n=r;break;case"Add":i=f2(i,{fiberId:t,fiberRef:e.fiberRef,value:e.value}),n=r;break;case f6:i=fX(i,e.fiberRef),n=r;break;case f8:{let s=f1(i,e.fiberRef);i=f2(i,{fiberId:t,fiberRef:e.fiberRef,value:e.fiberRef.patch(e.patch)(s)}),n=r;break}case f7:n=rE(e.first)(rE(e.second)(r))}}return i}),di="effect/FiberStatus",dn=Symbol.for(di),ds="Done",da="Running",dl="Suspended",du=eM(`${di}-${ds}`);class dc{[eI](){return du}[eL](e){return dd(e)&&e._tag===ds}constructor(){this[dn]=dn,this._tag=ds}}class dh{constructor(e){this[dn]=dn,this._tag=da,this.runtimeFlags=e}[eI](){return J(eF(di),eR(eF(this._tag)),eR(eF(this.runtimeFlags)),e$(this))}[eL](e){return dd(e)&&e._tag===da&&this.runtimeFlags===e.runtimeFlags}}class dp{constructor(e,t){this[dn]=dn,this._tag=dl,this.runtimeFlags=e,this.blockingOn=t}[eI](){return J(eF(di),eR(eF(this._tag)),eR(eF(this.runtimeFlags)),eR(eF(this.blockingOn)),e$(this))}[eL](e){return dd(e)&&e._tag===dl&&this.runtimeFlags===e.runtimeFlags&&eU(this.blockingOn,e.blockingOn)}}let df=new dc,dd=e=>ea(e,dn),dm=e=>new dh(e),dg=(e,t)=>new dp(e,t),db=e=>e._tag===ds,dx=(E=J(rc,rh(e=>e.ordinal)),D(2,(e,t)=>1===E(e,t))),dy=e=>{switch(e){case"All":return cg;case"Debug":return cS;case"Error":return cx;case"Fatal":return cb;case"Info":return cv;case"Trace":return c_;case"None":return cw;case"Warning":return cy}},dv=Symbol.for("effect/Micro"),dS=Symbol.for("effect/Micro/MicroExit"),d_=Symbol.for("effect/Micro/MicroCause"),dw={_E:$};class dk extends globalThis.Error{constructor(e,t,r){let i,n,s;let a=`MicroCause.${e}`;if(t instanceof globalThis.Error){i=`(${a}) ${t.name}`;let e=(n=t.message).split("\n").length;s=t.stack?`(${a}) ${t.stack.split("\n").slice(0,e+3).join("\n")}`:`${i}: ${n}`}else i=a,n=eY(t,0),s=`${i}: ${n}`;r.length>0&&(s+=`
    ${r.join("\n    ")}`),super(n),this._tag=e,this.traces=r,this[d_]=dw,this.name=i,this.stack=s}pipe(){return e3(this,arguments)}toString(){return this.stack}[eH](){return this.stack}}class dO extends dk{constructor(e,t=[]){super("Fail",e,t),this.error=e}}let dC=(e,t=[])=>new dO(e,t);class dE extends dk{constructor(e,t=[]){super("Die",e,t),this.defect=e}}let dI=(e,t=[])=>new dE(e,t);class dF extends dk{constructor(e=[]){super("Interrupt","interrupted",e)}}let dT=(e=[])=>new dF(e),dR=e=>"Fail"===e._tag,dN=e=>"Interrupt"===e._tag,dA=D(2,(e,t)=>{let r=[...e.traces,t];switch(e._tag){case"Die":return dI(e.defect,r);case"Interrupt":return dT(r);case"Fail":return dC(e.error,r)}}),dj=Symbol.for("effect/Micro/MicroFiber"),dM={_A:$,_E:$};class dz{constructor(e,t=!0){this._stack=[],this._observers=[],this.currentOpCount=0,this._interrupted=!1,this._yielded=void 0,this.context=e,this.interruptible=t,this[dj]=dM}getRef(e){return s2(this.context,e)}addObserver(e){return this._exit?(e(this._exit),B):(this._observers.push(e),()=>{let t=this._observers.indexOf(e);t>=0&&this._observers.splice(t,1)})}unsafeInterrupt(){!this._exit&&(this._interrupted=!0,this.interruptible&&this.evaluate(mo))}unsafePoll(){return this._exit}evaluate(e){if(this._exit)return;if(void 0!==this._yielded){let e=this._yielded;this._yielded=void 0,e()}let t=this.runLoop(e);if(t===dJ)return;let r=dP.interruptChildren&&dP.interruptChildren(this);if(void 0!==r)return this.evaluate(mr(r,()=>t));this._exit=t;for(let e=0;e<this._observers.length;e++)this._observers[e](t);this._observers.length=0}runLoop(e){let t=!1,r=e;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!t&&this.getRef(mg).shouldYield(this)){t=!0;let e=r;r=mr(d1,()=>e)}if((r=r[dL](this))===dJ){let e=this._yielded;if(dS in e)return this._yielded=void 0,e;return dJ}}}catch(e){if(!ea(r,dL))return ml(`MicroFiber.runLoop: Not a valid effect: ${String(r)}`);return ml(e)}}getCont(e){for(;;){let t=this._stack.pop();if(!t)return;let r=t[dB]&&t[dB](this);if(r)return{[e]:r};if(t[e])return t}}yieldWith(e){return this._yielded=e,dJ}children(){return this._children??=new Set}}let dP=V("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),dD=Symbol.for("effect/Micro/identifier"),d$=Symbol.for("effect/Micro/args"),dL=Symbol.for("effect/Micro/evaluate"),dU=Symbol.for("effect/Micro/successCont"),dq=Symbol.for("effect/Micro/failureCont"),dB=Symbol.for("effect/Micro/ensureCont"),dJ=Symbol.for("effect/Micro/Yield"),dH={...tf,_op:"Micro",[dv]:{_A:$,_E:$,_R:$},pipe(){return e3(this,arguments)},[Symbol.iterator](){return new eg(new ev(this))},toJSON(){return{_id:"Micro",op:this[dD],...d$ in this?{args:this[d$]}:void 0}},toString(){return eV(this)},[eH](){return eV(this)}};function dK(e){return ml("Micro.evaluate: Not implemented")}let dV=e=>({...dH,[dD]:e.op,[dL]:e.eval??dK,[dU]:e.contA,[dq]:e.contE,[dB]:e.ensure}),dW=e=>{let t=dV(e);return function(){let r=Object.create(t);return r[d$]=!1===e.single?arguments:arguments[0],r}},dG=e=>{let t={...dV(e),[dS]:dS,_tag:e.op,get[e.prop](){return this[d$]},toJSON(){return{_id:"MicroExit",_tag:e.op,[e.prop]:this[d$]}},[eL](t){return ms(t)&&t._tag===e.op&&eU(this[d$],t[d$])},[eI](){return e$(this,eR(eM(e.op))(eF(this[d$])))}};return function(e){let r=Object.create(t);return r[d$]=e,r[dU]=void 0,r[dq]=void 0,r[dB]=void 0,r}},dY=dG({op:"Success",prop:"value",eval(e){let t=e.getCont(dU);return t?t[dU](this[d$],e):e.yieldWith(this)}}),dZ=dG({op:"Failure",prop:"cause",eval(e){let t=e.getCont(dq);for(;dN(this[d$])&&t&&e.interruptible;)t=e.getCont(dq);return t?t[dq](this[d$],e):e.yieldWith(this)}}),dQ=e=>dZ(dC(e)),dX=dW({op:"Sync",eval(e){let t=this[d$](),r=e.getCont(dU);return r?r[dU](t,e):e.yieldWith(ma(t))}}),d0=dW({op:"Suspend",eval(e){return this[d$]()}}),d1=dW({op:"Yield",eval(e){let t=!1;return e.getRef(mg).scheduleTask(()=>{t||e.evaluate(mc)},this[d$]??0),e.yieldWith(()=>{t=!0})}})(0),d2=e=>ml(e),d3=e=>"Right"===e._tag?dY(e.right):dQ(e.left),d5=dY(void 0),d4=e=>d7(function(t,r){e(r).then(e=>t(dY(e)),e=>t(d2(e)))},0!==e.length),d6=e=>d7(function(t,r){try{e.try(r).then(e=>t(dY(e)),r=>t(dQ(e.catch(r))))}catch(r){t(dQ(e.catch(r)))}},0!==e.try.length),d8=dW({op:"WithMicroFiber",eval(e){return this[d$](e)}}),d7=dW({op:"Async",single:!1,eval(e){let t=this[d$][0],r=!1,i=!1,n=this[d$][1]?new AbortController:void 0,s=t(t=>{r||(r=!0,i?e.evaluate(t):i=t)},n?.signal);return!1!==i?i:(i=!0,e._yielded=()=>{r=!0},void 0===n&&void 0===s||e._stack.push(d9(()=>(r=!0,n?.abort(),s??mc))),dJ)}}),d9=dW({op:"AsyncFinalizer",ensure(e){e.interruptible&&(e.interruptible=!1,e._stack.push(mI(!0)))},contE(e,t){return dN(e)?mr(this[d$](),()=>dZ(e)):dZ(e)}}),me=(...e)=>d0(()=>mt(1===e.length?e[0]():e[1].call(e[0]))),mt=dW({op:"Iterator",contA(e,t){let r=this[d$].next(e);return r.done?dY(r.value):(t._stack.push(this),eS(r.value))},eval(e){return this[dU](void 0,e)}}),mr=D(2,(e,t)=>{let r=Object.create(mi);return r[d$]=e,r[dU]=t,r}),mi=dV({op:"OnSuccess",eval(e){return e._stack.push(this),this[d$]}}),mn=D(2,(e,t)=>mr(e,e=>dY(t(e)))),ms=e=>ea(e,dS),ma=dY,mo=dZ(dT()),ml=e=>dZ(dI(e)),mu=e=>"Failure"===e._tag,mc=ma(void 0),mh="setImmediate"in globalThis?globalThis.setImmediate:e=>setTimeout(e,0);class mp{scheduleTask(e,t){this.tasks.push(e),this.running||(this.running=!0,mh(this.afterScheduled))}runTasks(){let e=this.tasks;this.tasks=[];for(let t=0,r=e.length;t<r;t++)e[t]()}shouldYield(e){return e.currentOpCount>=e.getRef(mm)}flush(){for(;this.tasks.length>0;)this.runTasks()}constructor(){this.tasks=[],this.running=!1,this.afterScheduled=()=>{this.running=!1,this.runTasks()}}}let mf=D(2,(e,t)=>d8(r=>{let i=r.context;return r.context=t(i),mO(e,()=>(r.context=i,d5))})),md=D(2,(e,t)=>mf(e,s4(t)));class mm extends ae()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class mg extends ae()("effect/Micro/currentScheduler",{defaultValue:()=>new mp}){}let mb=D(2,(e,t)=>{let r=Object.create(mx);return r[d$]=e,r[dq]=t,r}),mx=dV({op:"OnFailure",eval(e){return e._stack.push(this),this[d$]}}),my=D(3,(e,t,r)=>mb(e,e=>t(e)?r(e):dZ(e))),mv=D(2,(e,t)=>my(e,dR,e=>t(e.error))),mS=D(2,(e,t)=>mv(e,e=>dX(t))),m_=function(){let e=globalThis.Error.stackTraceLimit;globalThis.Error.stackTraceLimit=2;let t=new globalThis.Error;globalThis.Error.stackTraceLimit=e;let r=e=>r=>mE(r,r=>dZ(function(e,r){let i=t.stack;if(!i)return r;let n=i.split("\n")[2]?.trim().replace(/^at /,"");if(!n)return r;let s=n.match(/\((.*)\)$/);return dA(r,`at ${e} (${s?s[1]:n})`)}(e,r)));return 2==arguments.length?r(arguments[1])(arguments[0]):r(arguments[0])},mw=D(2,(e,t)=>{let r=Object.create(mk);return r[d$]=e,r[dU]=t.onSuccess,r[dq]=t.onFailure,r}),mk=dV({op:"OnSuccessAndFailure",eval(e){return e._stack.push(this),this[d$]}}),mO=D(2,(e,t)=>mT(r=>mw(r(e),{onFailure:e=>mr(t(dZ(e)),()=>dZ(e)),onSuccess:e=>mr(t(ma(e)),()=>dY(e))}))),mC=D(3,(e,t,r)=>mO(e,e=>t(e)?r(e):mc)),mE=D(2,(e,t)=>mC(e,mu,e=>t(e.cause))),mI=dW({op:"SetInterruptible",ensure(e){if(e.interruptible=this[d$],e._interrupted&&e.interruptible)return()=>mo}}),mF=e=>d8(t=>t.interruptible?e:(t.interruptible=!0,t._stack.push(mI(!1)),t._interrupted)?mo:e),mT=e=>d8(t=>t.interruptible?(t.interruptible=!1,t._stack.push(mI(!0)),e(mF)):e($)),mR=(e,t)=>{let r=new dz(mg.context(t?.scheduler??new mp));if(r.evaluate(e),t?.signal){if(t.signal.aborted)r.unsafeInterrupt();else{let e=()=>r.unsafeInterrupt();t.signal.addEventListener("abort",e,{once:!0}),r.addObserver(()=>t.signal.removeEventListener("abort",e))}}return r},mN=function(){class e extends globalThis.Error{}return Object.assign(e.prototype,dH,td,{[dD]:"Failure",[dL](){return dQ(this)},toString(){return this.message?`${this.name}: ${this.message}`:this.name},toJSON(){return{...this}},[eH](){let e=this.stack;return e?`${this.toString()}
${e.split("\n").slice(1).join("\n")}`:this.toString()}}),e}(),mA=class extends mN{constructor(e){super(),e&&Object.assign(this,e)}},mj=e=>{class t extends mA{constructor(...t){super(...t),this._tag=e}}return t.prototype.name=e,t},mM=Symbol.for("effect/Readable"),mz=Symbol.for("effect/Ref"),mP={_A:e=>e};class mD extends fP{static{n=mM}commit(){return this.get}constructor(e){super(),this[mz]=mP,this[n]=mM,this.ref=e,this.get=u6(()=>aK(this.ref))}modify(e){return u6(()=>{let t=aK(this.ref),[r,i]=e(t);return t!==i&&aV(i)(this.ref),r})}}let m$=e=>new mD(aJ(e)),mL=e=>u6(()=>m$(e)),mU=e=>e.get,mq=D(2,(e,t)=>e.modify(()=>[void 0,t])),mB=D(2,(e,t)=>e.modify(e=>[e,t])),mJ=D(2,(e,t)=>e.modify(t)),mH=D(2,(e,t)=>e.modify(e=>[void 0,t(e)]));class mK{scheduleTask(e,t){let r;let i=this.buckets.length,n=0;for(;n<i;n++)if(this.buckets[n][0]<=t)r=this.buckets[n];else break;r&&r[0]===t?r[1].push(e):n===i?this.buckets.push([t,[e]]):this.buckets.splice(n,0,[t,[e]])}constructor(){this.buckets=[]}}class mV{constructor(e){this.running=!1,this.tasks=new mK,this.maxNextTickBeforeTimer=e}starveInternal(e){let t=this.tasks.buckets;for(let[e,r]of(this.tasks.buckets=[],t))for(let e=0;e<r.length;e++)r[e]();0===this.tasks.buckets.length?this.running=!1:this.starve(e)}starve(e=0){e>=this.maxNextTickBeforeTimer?setTimeout(()=>this.starveInternal(0),0):Promise.resolve(void 0).then(()=>this.starveInternal(e+1))}shouldYield(e){return e.currentOpCount>e.getFiberRef(cH)&&e.getFiberRef(cJ)}scheduleTask(e,t){this.tasks.scheduleTask(e,t),this.running||(this.running=!0,this.starve())}}let mW=V(Symbol.for("effect/Scheduler/defaultScheduler"),()=>new mV(2048));class mG{scheduleTask(e,t){this.deferred?mW.scheduleTask(e,t):this.tasks.scheduleTask(e,t)}shouldYield(e){return e.currentOpCount>e.getFiberRef(cH)&&e.getFiberRef(cJ)}flush(){for(;this.tasks.buckets.length>0;){let e=this.tasks.buckets;for(let[t,r]of(this.tasks.buckets=[],e))for(let e=0;e<r.length;e++)r[e]()}this.deferred=!0}constructor(){this.tasks=new mK,this.deferred=!1}}let mY=V(Symbol.for("effect/FiberRef/currentScheduler"),()=>cD(mW)),mZ=(e,t)=>({_tag:"Par",left:e,right:t}),mQ=(e,t)=>({_tag:"Seq",left:e,right:t}),mX=e=>{let t=o_(e),r=oS();for(;;){let[e,i]=oC(t,[m7(),oS()],([e,t],r)=>{let[i,n]=m0(r);return[ge(e,i),ow(t,n)]});if(r=m1(r,e),ox(i))return oE(r);t=i}throw Error("BUG: BlockedRequests.flatten - please report an issue at https://github.com/Effect-TS/effect/issues")},m0=e=>{let t=e,r=m7(),i=oS(),n=oS();for(;;)switch(t._tag){case"Empty":if(ox(i))return[r,n];t=i.head,i=i.tail;break;case"Par":i=ov(t.right,i),t=t.left;break;case"Seq":{let e=t.left,r=t.right;switch(e._tag){case"Empty":t=r;break;case"Par":{let i=e.left,n=e.right;t=mZ(mQ(i,r),mQ(n,r));break}case"Seq":t=mQ(e.left,mQ(e.right,r));break;case"Single":t=e,n=ov(r,n)}break}case"Single":if(r=m9(r,t),ox(i))return[r,n];t=i.head,i=i.tail}throw Error("BUG: BlockedRequests.step - please report an issue at https://github.com/Effect-TS/effect/issues")},m1=(e,t)=>{if(ox(e))return o_(gi(t));if(gt(t))return e;let r=gu(e.head),i=gr(t);return 1===r.length&&1===i.length&&eU(r[0],i[0])?ov(gl(e.head,gi(t)),e.tail):ov(gi(t),e)},m2=Symbol.for("effect/RequestBlock/Entry");class m3{constructor(e,t,r,i,n){this[m2]=m5,this.request=e,this.result=t,this.listeners=r,this.ownerId=i,this.state=n}}let m5={_R:e=>e},m4=Symbol.for("effect/RequestBlock/RequestBlockParallel"),m6={_R:e=>e};class m8{constructor(e){this[m4]=m6,this.map=e}}let m7=()=>new m8(nf()),m9=(e,t)=>new m8(nk(e.map,t.dataSource,e=>t2(t7(e,iT(t.blockedRequest)),()=>iS(t.blockedRequest)))),ge=(e,t)=>new m8(nF(e.map,t.map,(e,t,r)=>nx(e,r,tX(nm(e,r),{onNone:()=>t,onSome:e=>iM(t,e)})))),gt=e=>ou(e.map),gr=e=>Array.from(nv(e.map)),gi=e=>go(nE(e.map,e=>iS(e))),gn=Symbol.for("effect/RequestBlock/RequestBlockSequential"),gs={_R:e=>e};class ga{constructor(e){this[gn]=gs,this.map=e}}let go=e=>new ga(e),gl=(e,t)=>new ga(nF(t.map,e.map,(e,t,r)=>nx(e,r,tX(nm(e,r),{onNone:()=>iy(),onSome:e=>iM(e,t)})))),gu=e=>Array.from(nv(e.map)),gc=e=>Array.from(e.map),gh=V(Symbol.for("effect/FiberRef/currentRequestMap"),()=>cD(new Map)),gp=(e,t,r,i)=>{switch(e){case void 0:return t();case"unbounded":return r();case"inherit":return cI(cY,e=>"unbounded"===e?r():e>1?i(e):t());default:return e>1?i(e):t()}},gf=(e,t,r)=>{switch(e){case void 0:return t();case"unbounded":return r();case"inherit":return cI(cY,e=>"unbounded"===e||e>1?r():t());default:return e>1?r():t()}},gd=e=>{let t=au(e);return fI(e=>e.sleep(t))},gm=e=>e.replace(/[\s="]/g,"_"),gg=e=>t=>{let r=gm(t.label);return`${r}=${e-t.startTime}ms`},gb=(e,t)=>({label:e,startTime:t}),gx=e=>({_tag:"ExternalSpan",spanId:e.spanId,traceId:e.traceId,sampled:e.sampled??!0,context:e.context??s7()}),gy="effect/MetricLabel",gv=Symbol.for(gy);class gS{constructor(e,t){this[gv]=gv,this.key=e,this.value=t,this._hash=eM(gy+this.key+this.value)}[eI](){return this._hash}[eL](e){return gw(e)&&this.key===e.key&&this.value===e.value}pipe(){return e3(this,arguments)}}let g_=(e,t)=>new gS(e,t),gw=e=>ea(e,gv),gk=D(e=>uh(e[0]),function(){let e=arguments;return cP(e[0],cK,"string"==typeof e[1]?nx(e[1],e[2]):t=>Object.entries(e[1]).reduce((e,[t,r])=>nx(e,t,r),t))}),gO=e=>uG(e,tI),gC=e=>{let t,r;return"function"==typeof e?t=e:(t=e.try,r=e.catch),u4(()=>{try{return u5(ek(t))}catch(e){return uF(r?ek(()=>r(e)):new hy(e,"An unknown error occurred in Effect.try"))}})},gE=D(2,(e,t)=>uL(e,{onFailure:e=>{let r=t(e);switch(r._tag){case"None":return uR(e);case"Some":return r.value}},onSuccess:u5})),gI=D(3,(e,t,r)=>u_(e,eo(t),r)),gF=D(2,(e,t)=>{let r;return u_(e,e=>(r??=Object.keys(t),ea(e,"_tag")&&W(e._tag)&&r.includes(e._tag)),e=>t[e._tag](e))}),gT=e=>g0(e,gD,de),gR=e=>g0(e,cu(gD,u3),([e,t],[r,i])=>[de(e,r),l2(t,i)]),gN=D(3,(e,t,r)=>uM(e,e=>uG(r(e),r=>Object.assign({},e,{[t]:r})))),gA=D(2,(e,t)=>uG(e,e=>({[t]:e}))),gj=((e,t)=>u4(()=>{let r;let i=e[Symbol.iterator](),n=[],s=u5(!1),a=0;for(;(r=i.next())&&!r.done;){let e=r.value,i=a++;s=uM(s,r=>r?(n.push(e),u5(!0)):t(e,i))}return uG(s,()=>n)}),D(3,(e,t,r)=>uM(e,e=>t(e)?u5(e):r(e)))),gM=(e=>uh(e[0]),(e,t,r,i)=>uM(r(i,t),n=>{if(n)return u5(tI(i));let s=e.next();return s.done?u5(tQ()):gM(e,t+1,r,s.value)})),gz=D(2,(e,t)=>uU(e,{onFailure:e=>u5(t.onFailure(e)),onSuccess:e=>u5(t.onSuccess(e))})),gP=e=>{let t=uM(uM(e,()=>cl()),()=>t);return t},gD=up(e=>u5(e.getFiberRefs())),g$=e=>(...t)=>{let r;let i=t3(e);for(let e=0,i=t.length;e<i;e++){let i=t[e];sr(i)&&(r=void 0!==r?st(r,i):i,t=[...t.slice(0,e),...t.slice(e+1)],e--)}return void 0===r&&(r=n6),up(e=>(e.log(t,r,i),cr))},gL=g$(),gU=g$(cS),gq=g$(cv),gB=g$(cy),gJ=g$(cx),gH=D(2,(e,t)=>uM(fF,r=>cP(e,cW,ok(gb(t,r))))),gK=D(2,(e,t)=>uL(e,{onFailure:e=>uN(()=>t(e)),onSuccess:u5})),gV=e=>uG(e,e=>!e),gW=e=>uG(mL(!0),t=>um(cn(e,mB(t,!1)))),gG=e=>g6((t,r)=>J(e,dr(t,r))),gY=D(3,(e,t,r)=>h0(i=>h1(e,sX(i,t,r)))),gZ=D(3,(e,t,r)=>h0(i=>uM(r,r=>h1(e,J(i,sX(t,r)))))),gQ=D(3,(e,t,r)=>rw(e).reduce((e,t,i)=>uM(e,e=>r(e,t,i)),u5(t))),gX=u5(tQ()),g0=D(3,(e,t,r)=>uM(t,i=>uM(e,e=>uG(t,t=>[r(i,t),e]))));(e,t)=>u4(()=>{let r;let i=e[Symbol.iterator](),n=[],s=u5(!0),a=0;for(;(r=i.next())&&!r.done;){let e=r.value,i=a++;s=uM(s,r=>J(r?t(e,i):u5(!1),uG(t=>(t&&n.push(e),t))))}return uG(s,()=>n)});let g1=D(2,(e,{onFailure:t,onSuccess:r})=>uL(e,{onFailure:e=>{let r=sp(e);switch(r._tag){case"Left":return ch(t(r.left),uR(e));case"Right":return uR(e)}},onSuccess:e=>ud(r(e),e)})),g2=D(2,(e,t)=>uL(e,{onFailure:e=>{let r=sp(e);switch(r._tag){case"Left":return ch(t(r.left),uR(e));case"Right":return uR(e)}},onSuccess:u5})),g3=D(2,(e,t)=>uL(e,{onFailure:e=>ch(t(e),uR(e)),onSuccess:u5})),g5=e=>{let t,r;"function"==typeof e?t=e:(t=e.try,r=e.catch);let i=e=>r?uT(()=>r(e)):uF(new hy(e,"An unknown error occurred in Effect.tryPromise"));return t.length>=1?uy((e,r)=>{try{t(r).then(t=>e(hM(t)),t=>e(i(t)))}catch(t){e(i(t))}}):uy(e=>{try{t().then(t=>e(hM(t)),t=>e(i(t)))}catch(t){e(i(t))}})},g4=D(2,(e,t)=>uM(e,e=>gC({try:()=>t.try(e),catch:t.catch}))),g6=e=>up(t=>(t.setFiberRefs(e(t.id(),t.getFiberRefs())),cr)),g8=D(2,(e,t)=>u4(()=>t()?uG(e,tI):u5(tQ()))),g7=BigInt(0),g9=t9(e=>s3(e.context,fk)?"Span"===e._tag?g9(e.parent):tQ():tI(e)),be=(e,t,r)=>{let i;let n=!e.getFiberRef(c2)||r.context&&s3(r.context,fk),s=e.getFiberRef(cB),a=r.parent?tI(r.parent):r.root?tQ():g9(s5(s,fv));if(n)i=h6({name:t,parent:a,context:sX(r.context??s7(),fk,!0)});else{let n=e.getFiberRef(fC),s=s3(n,fy),o=s3(n,h7),l=e.getFiberRef(c3),u=e.getFiberRefs(),c=f0(u,c5),h=f0(u,c4),p="Some"===h._tag?void 0!==r.links?[...ik(h.value),...r.links??[]]:ik(h.value):r.links??r1();i=s.span(t,a,r.context??s7(),p,l?o.unsafeCurrentTimeNanos():g7,r.kind??"internal"),"Some"===c._tag&&nI(c.value,(e,t)=>i.attribute(t,e)),void 0!==r.attributes&&Object.entries(r.attributes).forEach(([e,t])=>i.attribute(e,t))}return"function"==typeof r.captureStackTrace&&sz.set(i,r.captureStackTrace),i},bt=(e,t,r,i)=>u6(()=>{"Ended"!==e.status._tag&&(hS(t)&&sz.has(e)&&e.attribute("code.stacktrace",sz.get(e)()),e.end(i?r.unsafeCurrentTimeNanos():g7,t))}),br=(e,...t)=>{let r=fw(1===t.length?void 0:t[0]),i=t[t.length-1];return up(t=>{let n=be(t,e,r),s=t.getFiberRef(c3),a=s3(t.getFiberRef(fC),h7);return uQ(i(n),e=>bt(n,e,a,s))})},bi=D(2,(e,t)=>gY(e,fv,t)),bn=e=>{switch(e._tag){case e6:return sa(e.effect_instruction_i0);case te:return!1}},bs=e=>J(e,hF($)),ba="InterruptSignal",bo="Stateful",bl="Resume",bu="YieldNow",bc=e=>({_tag:ba,cause:e}),bh=e=>({_tag:bo,onFiber:e}),bp=e=>({_tag:bl,effect:e}),bf=()=>({_tag:bu}),bd=Symbol.for("effect/FiberScope");class bm{add(e,t){this.roots.add(t),t.addObserver(()=>{this.roots.delete(t)})}constructor(){this[bd]=bd,this.fiberId=or,this.roots=new Set}}class bg{constructor(e,t){this[bd]=bd,this.fiberId=e,this.parent=t}add(e,t){this.parent.tell(bh(e=>{e.addChild(t),t.addObserver(()=>{e.removeChild(t)})}))}}let bb=e=>new bg(e.id(),e),bx=V(Symbol.for("effect/FiberScope/Global"),()=>new bm),by=Symbol.for("effect/Fiber"),bv={_E:e=>e,_A:e=>e},bS={[by]:bv,pipe(){return e3(this,arguments)}},b_=Symbol.for("effect/Fiber"),bw=e=>e.await,bk=e=>({...tm,commit(){return bO(this)},...bS,id:()=>or,await:u5(e),children:u5([]),inheritAll:cr,poll:u5(tI(e)),interruptAsFork:()=>cr}),bO=((e,t)=>e.interruptAsFork(t),e=>cc(uD(e.await),e.inheritAll)),bC=((e,t)=>bC(e,e=>u6(()=>t(e))),D(2,(e,t)=>({...tm,commit(){return bO(this)},...bS,id:()=>e.id(),await:uM(e.await,hT(t)),children:e.children,inheritAll:e.inheritAll,poll:uM(e.poll,e=>{switch(e._tag){case"None":return u5(tQ());case"Some":return J(hT(e.value,t),uG(tI))}}),interruptAsFork:t=>e.interruptAsFork(t)})));({...tm,...bS,children:u5([]),poll:u5(tQ())}),(e,t)=>({...tm,commit(){return bO(this)},...bS,id:()=>a8(e.id(),t.id()),await:cp(e.await,t.await,(e,t)=>h_(e)?e:t),children:e.children,inheritAll:ch(t.inheritAll,e.inheritAll),poll:cp(e.poll,t.poll,(e,t)=>{switch(e._tag){case"None":return tQ();case"Some":return h_(e.value)?e:t}}),interruptAsFork:r=>J(cm(e,r),ch(J(t,cm(r))),um)});let bE="effect/FiberCurrent",bI=Symbol.for("effect/Logger"),bF={_Message:e=>e,_Output:e=>e},bT=e=>({[bI]:bF,log:e,pipe(){return e3(this,arguments)}}),bR=D(2,(e,t)=>bT(r=>e.log({...r,message:t(r.message)}))),bN=D(2,(e,t)=>bT(r=>e.log(t(r)))),bA=D(2,(e,t)=>bT(r=>t(r.logLevel)?tI(e.log(r)):tQ())),bj=D(2,(e,t)=>bT(r=>t(e.log(r)))),bM=e=>({[bI]:bF,log:({message:t})=>e(t),pipe(){return e3(this,arguments)}}),bz=D(2,(e,t)=>bT(r=>[e.log(r),t.log(r)])),bP=D(2,(e,t)=>bj(bz(e,t),e=>e[0])),bD=D(2,(e,t)=>bj(bz(e,t),e=>e[1])),b$=/^[^\s"=]*$/,bL=(e,t)=>({annotations:r,cause:i,date:n,fiberId:s,logLevel:a,message:o,spans:l})=>{let u=t=>t.match(b$)?t:e(t),c=(e,t)=>`${gm(e)}=${u(t)}`,h=(e,t)=>" "+c(e,t),p=c("timestamp",n.toISOString());p+=h("level",a.label),p+=h("fiber",oe(s));let f=rk(o);for(let e=0;e<f.length;e++)p+=h("message",eY(f[e],t));for(let e of(si(i)||(p+=h("cause",sR(i,{renderErrorCause:!0}))),l))p+=" "+gg(n.getTime())(e);for(let[e,i]of r)p+=h(e,eY(i,t));return p},bU=bT(bL(e=>`"${e.replace(/\\([\s\S])|(")/g,"\\$1$2")}"`)),bq=bT(bL(JSON.stringify,0)),bB=bT(({annotations:e,cause:t,date:r,fiberId:i,logLevel:n,message:s,spans:a})=>{let o=r.getTime(),l={},u={};if(nS(e)>0)for(let[t,r]of e)l[t]=bJ(r);if(oy(a))for(let e of a)u[e.label]=o-e.startTime;let c=rk(s);return{message:1===c.length?bJ(c[0]):c.map(bJ),logLevel:n.label,timestamp:r.toISOString(),cause:ss(t)?void 0:sR(t,{renderErrorCause:!0}),annotations:l,spans:u,fiberId:oe(i)}}),bJ=e=>{switch(typeof e){case"bigint":case"function":case"symbol":return String(e);default:return eK(e)}},bH=bj(bB,eZ),bK=(e,...t)=>{let r="";for(let e=0;e<t.length;e++)r+=`\x1b[${t[e]}m`;return r+e+"\x1b[0m"},bV=(e,...t)=>e,bW={bold:"1",red:"31",green:"32",yellow:"33",blue:"34",cyan:"36",white:"37",gray:"90",black:"30",bgBrightRed:"101"},bG={None:[],All:[],Trace:[bW.gray],Debug:[bW.blue],Info:[bW.green],Warning:[bW.yellow],Error:[bW.red],Fatal:[bW.bgBrightRed,bW.black]},bY={None:"",All:"",Trace:"color:gray",Debug:"color:blue",Info:"color:green",Warning:"color:orange",Error:"color:red",Fatal:"background-color:red;color:white"},bZ=e=>`${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}:${e.getSeconds().toString().padStart(2,"0")}.${e.getMilliseconds().toString().padStart(3,"0")}`,bQ="object"==typeof process&&null!==process&&"object"==typeof process.stdout&&null!==process.stdout,bX=bQ&&!0===process.stdout.isTTY,b0=bQ||"Deno"in globalThis,b1=e=>{let t=e?.mode??"auto",r="browser"===("auto"===t?b0?"tty":"browser":t),i="boolean"==typeof e?.colors?e.colors:bX||r,n=e?.formatDate??bZ;return r?b3({colors:i,formatDate:n}):b2({colors:i,formatDate:n,stderr:e?.stderr===!0})},b2=e=>{let t="object"==typeof process&&"isBun"in process&&!0===process.isBun,r=e.colors?bK:bV;return bT(({annotations:i,cause:n,context:s,date:a,fiberId:o,logLevel:l,message:u,spans:c})=>{let h=s3(f1(s,fC),fh).unsafe,p=!0===e.stderr?h.error:h.log,f=rk(u),d=r(`[${e.formatDate(a)}]`,bW.white)+` ${r(l.label,...bG[l._tag])}`+` (${oe(o)})`;if(oy(c)){let e=gg(a.getTime());for(let t of c)d+=" "+e(t)}d+=":";let m=0;if(f.length>0){let e=bJ(f[0]);"string"==typeof e&&(d+=" "+r(e,bW.bold,bW.cyan),m++)}if(p(d),t||h.group(),ss(n)||p(sR(n,{renderErrorCause:!0})),m<f.length)for(;m<f.length;m++)p(e2(f[m]));if(nS(i)>0)for(let[e,t]of i)p(r(`${e}:`,bW.bold,bW.white),e2(t));t||h.groupEnd()})},b3=e=>{let t=e.colors?"%c":"";return bT(({annotations:r,cause:i,context:n,date:s,fiberId:a,logLevel:o,message:l,spans:u})=>{let c=s3(f1(n,fC),fh).unsafe,h=rk(l),p=`${t}[${e.formatDate(s)}]`,f=[];if(e.colors&&f.push("color:gray"),p+=` ${t}${o.label}${t} (${oe(a)})`,e.colors&&f.push(bY[o._tag],""),oy(u)){let e=gg(s.getTime());for(let t of u)p+=" "+e(t)}p+=":";let d=0;if(h.length>0){let r=bJ(h[0]);"string"==typeof r&&(p+=` ${t}${r}`,e.colors&&f.push("color:deepskyblue"),d++)}if(c.groupCollapsed(p,...f),ss(i)||c.error(sR(i,{renderErrorCause:!0})),d<h.length)for(;d<h.length;d++)c.log(e2(h[d]));if(nS(r)>0)for(let[t,i]of r){let r=e2(i);e.colors?c.log(`%c${t}:`,"color:gray",r):c.log(`${t}:`,r)}c.groupEnd()})},b5=V("effect/Logger/prettyLoggerDefault",()=>b1()),b4=Symbol.for("effect/MetricKeyType"),b6="effect/MetricKeyType/Counter",b8=Symbol.for(b6),b7="effect/MetricKeyType/Frequency",b9=Symbol.for(b7),xe="effect/MetricKeyType/Gauge",xt=Symbol.for(xe),xr="effect/MetricKeyType/Histogram",xi=Symbol.for(xr),xn=Symbol.for("effect/MetricKeyType/Summary"),xs={_In:e=>e,_Out:e=>e};class xa{constructor(e,t){this[b4]=xs,this[b8]=b8,this.incremental=e,this.bigint=t,this._hash=eM(b6)}[eI](){return this._hash}[eL](e){return xc(e)}pipe(){return e3(this,arguments)}}class xo{constructor(e){this[b4]=xs,this[xi]=xi,this.boundaries=e,this._hash=J(eM(xr),eR(eF(this.boundaries)))}[eI](){return this._hash}[eL](e){return xf(e)&&eU(this.boundaries,e.boundaries)}pipe(){return e3(this,arguments)}}let xl=e=>new xa(e?.incremental??!1,e?.bigint??!1),xu=e=>new xo(e),xc=e=>ea(e,b8),xh=e=>ea(e,b9),xp=e=>ea(e,xt),xf=e=>ea(e,xi),xd=e=>ea(e,xn),xm=Symbol.for("effect/MetricKey"),xg={_Type:e=>e},xb=ia(eU);class xx{constructor(e,t,r,i=[]){this[xm]=xg,this.name=e,this.keyType=t,this.description=r,this.tags=i,this._hash=J(eM(this.name+this.description),eR(eF(this.keyType)),eR(eD(this.tags)))}[eI](){return this._hash}[eL](e){return xy(e)&&this.name===e.name&&eU(this.keyType,e.keyType)&&eU(this.description,e.description)&&xb(this.tags,e.tags)}pipe(){return e3(this,arguments)}}let xy=e=>ea(e,xm),xv=(e,t)=>new xx(e,xl(t),t3(t?.description)),xS=D(2,(e,t)=>0===t.length?e:new xx(e.name,e.keyType,e.description,r0(e.tags,t))),x_=Symbol.for("effect/MutableHashMap"),xw={[x_]:x_,[Symbol.iterator](){return new xk(this)},toString(){return eV(this.toJSON())},toJSON(){return{_id:"MutableHashMap",values:Array.from(this).map(eK)}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}};class xk{constructor(e){this.self=e,this.referentialIterator=e.referential[Symbol.iterator]()}next(){if(void 0!==this.bucketIterator)return this.bucketIterator.next();let e=this.referentialIterator.next();return e.done?(this.bucketIterator=new xO(this.self.buckets.values()),this.next()):e}[Symbol.iterator](){return new xk(this.self)}}class xO{constructor(e){this.backing=e}next(){if(void 0===this.currentBucket){let e=this.backing.next();if(e.done)return e;this.currentBucket=e.value[Symbol.iterator]()}let e=this.currentBucket.next();return e.done?(this.currentBucket=void 0,this.next()):e}}let xC=()=>{let e=Object.create(xw);return e.referential=new Map,e.buckets=new Map,e.bucketsSize=0,e},xE=D(2,(e,t)=>{if(!1===eB(t))return e.referential.has(t)?tI(e.referential.get(t)):tQ();let r=t[eI](),i=e.buckets.get(r);return void 0===i?tQ():xI(e,i,t)}),xI=(e,t,r,i=!1)=>{for(let n=0,s=t.length;n<s;n++)if(r[eL](t[n][0])){let r=t[n][1];return i&&(t.splice(n,1),e.bucketsSize--),tI(r)}return tQ()},xF=D(2,(e,t)=>tC(xE(e,t))),xT=D(3,(e,t,r)=>{if(!1===eB(t))return e.referential.set(t,r),e;let i=t[eI](),n=e.buckets.get(i);return void 0===n?e.buckets.set(i,[[t,r]]):(xR(e,n,t),n.push([t,r])),e.bucketsSize++,e}),xR=(e,t,r)=>{for(let i=0,n=t.length;i<n;i++)if(r[eL](t[i][0])){t.splice(i,1),e.bucketsSize--;return}},xN=D(2,(e,t)=>{if(!1===eB(t))return e.referential.delete(t),e;let r=t[eI](),i=e.buckets.get(r);return void 0===i||(xR(e,i,t),0===i.length&&e.buckets.delete(r)),e}),xA=e=>e.referential.size+e.bucketsSize,xj=Symbol.for("effect/MetricState"),xM="effect/MetricState/Counter",xz=Symbol.for(xM),xP="effect/MetricState/Frequency",xD=Symbol.for(xP),x$="effect/MetricState/Gauge",xL=Symbol.for(x$),xU="effect/MetricState/Histogram",xq=Symbol.for(xU),xB="effect/MetricState/Summary",xJ=Symbol.for(xB),xH={_A:e=>e};class xK{constructor(e){this[xj]=xH,this[xz]=xz,this.count=e}[eI](){return J(eF(xM),eR(eF(this.count)),e$(this))}[eL](e){return x3(e)&&this.count===e.count}pipe(){return e3(this,arguments)}}let xV=ia(eU);class xW{constructor(e){this[xj]=xH,this[xD]=xD,this.occurrences=e}[eI](){return J(eM(xP),eR(eD(rw(this.occurrences.entries()))),e$(this))}[eL](e){return x5(e)&&xV(rw(this.occurrences.entries()),rw(e.occurrences.entries()))}pipe(){return e3(this,arguments)}}class xG{constructor(e){this[xj]=xH,this[xL]=xL,this.value=e}[eI](){return J(eF(x$),eR(eF(this.value)),e$(this))}[eL](e){return x4(e)&&this.value===e.value}pipe(){return e3(this,arguments)}}class xY{constructor(e,t,r,i,n){this[xj]=xH,this[xq]=xq,this.buckets=e,this.count=t,this.min=r,this.max=i,this.sum=n}[eI](){return J(eF(xU),eR(eF(this.buckets)),eR(eF(this.count)),eR(eF(this.min)),eR(eF(this.max)),eR(eF(this.sum)),e$(this))}[eL](e){return x6(e)&&eU(this.buckets,e.buckets)&&this.count===e.count&&this.min===e.min&&this.max===e.max&&this.sum===e.sum}pipe(){return e3(this,arguments)}}class xZ{constructor(e,t,r,i,n,s){this[xj]=xH,this[xJ]=xJ,this.error=e,this.quantiles=t,this.count=r,this.min=i,this.max=n,this.sum=s}[eI](){return J(eF(xB),eR(eF(this.error)),eR(eF(this.quantiles)),eR(eF(this.count)),eR(eF(this.min)),eR(eF(this.max)),eR(eF(this.sum)),e$(this))}[eL](e){return x8(e)&&this.error===e.error&&eU(this.quantiles,e.quantiles)&&this.count===e.count&&this.min===e.min&&this.max===e.max&&this.sum===e.sum}pipe(){return e3(this,arguments)}}let xQ=e=>new xK(e),xX=e=>new xW(e),x0=e=>new xG(e),x1=e=>new xY(e.buckets,e.count,e.min,e.max,e.sum),x2=e=>new xZ(e.error,e.quantiles,e.count,e.min,e.max,e.sum),x3=e=>ea(e,xz),x5=e=>ea(e,xD),x4=e=>ea(e,xL),x6=e=>ea(e,xq),x8=e=>ea(e,xJ),x7=Symbol.for("effect/MetricHook"),x9={_In:e=>e,_Out:e=>e},ye=e=>({[x7]:x9,pipe(){return e3(this,arguments)},...e}),yt=BigInt(0),yr=e=>{let t=e.keyType.bigint?yt:0,r=e.keyType.incremental?e.keyType.bigint?e=>e>=yt:e=>e>=0:e=>!0,i=e=>{r(e)&&(t+=e)};return ye({get:()=>xQ(t),update:i,modify:i})},yi=e=>{let t=new Map;for(let r of e.keyType.preregisteredWords)t.set(r,0);let r=e=>{let r=t.get(e)??0;t.set(e,r+1)};return ye({get:()=>xX(t),update:r,modify:r})},yn=(e,t)=>{let r=t;return ye({get:()=>x0(r),update:e=>{r=e},modify:e=>{r+=e}})},ys=e=>{let t=e.keyType.boundaries.values,r=t.length,i=new Uint32Array(r+1),n=new Float32Array(r),s=0,a=0,o=Number.MAX_VALUE,l=Number.MIN_VALUE;J(t,rK(rc),r3((e,t)=>{n[t]=e}));let u=e=>{let t=0,u=r;for(;t!==u;){let r=Math.floor(t+(u-t)/2);e<=n[r]?u=r:t=r,u===t+1&&(e<=n[t]?u=t:t=u)}i[t]=i[t]+1,s+=1,a+=e,e<o&&(o=e),e>l&&(l=e)},c=()=>{let e=rS(r),t=0;for(let s=0;s<r;s++){let r=n[s];t+=i[s],e[s]=[r,t]}return e};return ye({get:()=>x1({buckets:c(),count:s,min:o,max:l,sum:a}),update:u,modify:u})},ya=e=>{let{error:t,maxAge:r,maxSize:i,quantiles:n}=e.keyType,s=J(n,rK(rc)),a=rS(i),o=0,l=0,u=0,c=Number.MAX_VALUE,h=Number.MIN_VALUE,p=e=>{let n=[],o=0;for(;o!==i-1;){let t=a[o];if(null!=t){let[i,s]=t,a=aS(e-i);aD(a,ab)&&az(a,r)&&n.push(s)}o+=1}return yo(t,s,rK(n,rc))},f=(e,t)=>{i>0&&(a[(o+=1)%i]=[t,e]),l+=1,u+=e,e<c&&(c=e),e>h&&(h=e)};return ye({get:()=>x2({error:t,quantiles:p(Date.now()),count:l,min:c,max:h,sum:u}),update:([e,t])=>f(e,t),modify:([e,t])=>f(e,t)})},yo=(e,t,r)=>{let i=r.length;if(!rN(t))return r1();let n=t[0],s=t.slice(1),a=yl(e,i,tQ(),0,n,r),o=r2(a);return s.forEach(t=>{o.push(yl(e,i,a.value,a.consumed,t,a.rest))}),r3(o,e=>[e.quantile,e.value])},yl=(e,t,r,i,n,s)=>{let a=e,o=t,l=r,u=i,c=n,h=s,p=e,f=t,d=r,m=i,g=n,b=s;for(;;){if(!rN(h))return{quantile:c,value:tQ(),consumed:u,rest:[]};if(1===c)return{quantile:c,value:tI(rL(h)),consumed:u+h.length,rest:[]};let e=rD(h),t=rB(h,t=>t===e),r=c*o,i=a/2*r,n=u+t[0].length,s=Math.abs(n-r);if(n<r-i){p=a,f=o,d=rP(h),m=n,g=c,b=t[1],a=p,o=f,l=d,u=m,c=g,h=b;continue}if(n>r+i)return{quantile:c,value:tO(l)?tI(e):l,consumed:u,rest:h};switch(l._tag){case"None":p=a,f=o,d=rP(h),m=n,g=c,b=t[1],a=p,o=f,l=d,u=m,c=g,h=b;continue;case"Some":if(s<Math.abs(r-l.value)){p=a,f=o,d=rP(h),m=n,g=c,b=t[1],a=p,o=f,l=d,u=m,c=g,h=b;continue}return{quantile:c,value:tI(l.value),consumed:u,rest:h}}}throw Error("BUG: MetricHook.resolveQuantiles - please report an issue at https://github.com/Effect-TS/effect/issues")},yu=Symbol.for("effect/MetricPair"),yc={_Type:e=>e},yh=(e,t)=>({[yu]:yc,metricKey:e,metricState:t,pipe(){return e3(this,arguments)}}),yp=Symbol.for("effect/MetricRegistry");class yf{snapshot(){let e=[];for(let[t,r]of this.map)e.push(yh(t,r.get()));return e}get(e){let t=J(this.map,xE(e),t5);if(null!=t)return t;if(xc(e.keyType))return this.getCounter(e);if(xp(e.keyType))return this.getGauge(e);if(xh(e.keyType))return this.getFrequency(e);if(xf(e.keyType))return this.getHistogram(e);if(xd(e.keyType))return this.getSummary(e);throw Error("BUG: MetricRegistry.get - unknown MetricKeyType - please report an issue at https://github.com/Effect-TS/effect/issues")}getCounter(e){let t=J(this.map,xE(e),t5);if(null==t){let r=yr(e);J(this.map,xF(e))||J(this.map,xT(e,r)),t=r}return t}getFrequency(e){let t=J(this.map,xE(e),t5);if(null==t){let r=yi(e);J(this.map,xF(e))||J(this.map,xT(e,r)),t=r}return t}getGauge(e){let t=J(this.map,xE(e),t5);if(null==t){let r=yn(e,e.keyType.bigint?BigInt(0):0);J(this.map,xF(e))||J(this.map,xT(e,r)),t=r}return t}getHistogram(e){let t=J(this.map,xE(e),t5);if(null==t){let r=ys(e);J(this.map,xF(e))||J(this.map,xT(e,r)),t=r}return t}getSummary(e){let t=J(this.map,xE(e),t5);if(null==t){let r=ya(e);J(this.map,xF(e))||J(this.map,xT(e,r)),t=r}return t}constructor(){this[yp]=yp,this.map=xC()}}let yd=()=>new yf,ym=Symbol.for("effect/Metric"),yg={_Type:e=>e,_In:e=>e,_Out:e=>e},yb=V(Symbol.for("effect/Metric/globalMetricRegistry"),()=>yd()),yx=function(e,t,r,i){let n=Object.assign(e=>u8(e,e=>yw(n,e)),{[ym]:yg,keyType:e,unsafeUpdate:t,unsafeValue:r,unsafeModify:i,register(){return this.unsafeValue([]),this},pipe(){return e3(this,arguments)}});return n},yy=(e,t)=>yv(xv(e,t)),yv=e=>{let t;let r=new WeakMap,i=i=>{if(0===i.length)return void 0!==t?t:t=yb.get(e);let n=r.get(i);return void 0!==n||(n=yb.get(xS(e,i)),r.set(i,n)),n};return yx(e.keyType,(e,t)=>i(t).update(e),e=>i(e).get(),(e,t)=>i(t).modify(e))},yS=D(3,(e,t,r)=>y_(e,[g_(t,r)])),y_=D(2,(e,t)=>yx(e.keyType,(r,i)=>e.unsafeUpdate(r,r0(t,i)),r=>e.unsafeValue(r0(t,r)),(r,i)=>e.unsafeModify(r,r0(t,i)))),yw=D(2,(e,t)=>cI(cX,r=>u6(()=>e.unsafeUpdate(t,r)))),yk="effect/MetricBoundaries",yO=Symbol.for(yk);class yC{constructor(e){this[yO]=yO,this.values=e,this._hash=J(eM(yk),eR(eD(this.values)))}[eI](){return this._hash}[eL](e){return yE(e)&&eU(this.values,e.values)}pipe(){return e3(this,arguments)}}let yE=e=>ea(e,yO),yI=e=>new yC(J(e,rF(iS(Number.POSITIVE_INFINITY)),il)),yF=Symbol.for("effect/Request"),yT=D(2,(e,t)=>cI(gh,r=>u6(()=>{if(r.has(e)){let i=r.get(e);i.state.completed||(i.state.completed=!0,hY(i.result,t))}})));class yR{addObserver(e){this.observers.add(e)}removeObserver(e){this.observers.delete(e)}increment(){this.count++,this.observers.forEach(e=>e(this.count))}decrement(){this.count--,this.observers.forEach(e=>e(this.count))}constructor(){this.count=0,this.observers=new Set,this.interrupted=!1}}let yN=Symbol.for("effect/Supervisor"),yA={_T:e=>e};class yj{constructor(e,t){this[yN]=yA,this.underlying=e,this.value0=t}get value(){return this.value0}onStart(e,t,r,i){this.underlying.onStart(e,t,r,i)}onEnd(e,t){this.underlying.onEnd(e,t)}onEffect(e,t){this.underlying.onEffect(e,t)}onSuspend(e){this.underlying.onSuspend(e)}onResume(e){this.underlying.onResume(e)}map(e){return new yj(this,J(this.value,uG(e)))}zip(e){return new yM(this,e)}}class yM{constructor(e,t){this._tag="Zip",this[yN]=yA,this.left=e,this.right=t}get value(){return cu(this.left.value,this.right.value)}onStart(e,t,r,i){this.left.onStart(e,t,r,i),this.right.onStart(e,t,r,i)}onEnd(e,t){this.left.onEnd(e,t),this.right.onEnd(e,t)}onEffect(e,t){this.left.onEffect(e,t),this.right.onEffect(e,t)}onSuspend(e){this.left.onSuspend(e),this.right.onSuspend(e)}onResume(e){this.left.onResume(e),this.right.onResume(e)}map(e){return new yj(this,J(this.value,uG(e)))}zip(e){return new yM(this,e)}}let yz=e=>ea(e,yN)&&eo(e,"Zip");class yP{get value(){return u6(()=>Array.from(this.fibers))}onStart(e,t,r,i){this.fibers.add(i)}onEnd(e,t){this.fibers.delete(t)}onEffect(e,t){}onSuspend(e){}onResume(e){}map(e){return new yj(this,J(this.value,uG(e)))}zip(e){return new yM(this,e)}onRun(e,t){return e()}constructor(){this[yN]=yA,this.fibers=new Set}}class yD{constructor(e){this[yN]=yA,this.effect=e}get value(){return this.effect}onStart(e,t,r,i){}onEnd(e,t){}onEffect(e,t){}onSuspend(e){}onResume(e){}map(e){return new yj(this,J(this.value,uG(e)))}zip(e){return new yM(this,e)}onRun(e,t){return e()}}let y$=u6(()=>new yP),yL=e=>new yD(e),yU=V("effect/Supervisor/none",()=>yL(cr)),yq="Empty",yB="AddSupervisor",yJ="RemoveSupervisor",yH="AndThen",yK={_tag:yq},yV=(e,t)=>({_tag:yH,first:e,second:t}),yW=(e,t)=>{let r=e,i=t;for(;iq(i);){let e=iJ(i);switch(e._tag){case yq:i=iG(i);break;case yB:r=r.zip(e.supervisor),i=iG(i);break;case yJ:r=yG(r,e.supervisor),i=iG(i);break;case yH:i=iR(e.first)(iR(e.second)(iG(i)))}}return r},yG=(e,t)=>eU(e,t)?yU:yz(e)?yG(e.left,t).zip(yG(e.right,t)):e,yY=e=>eU(e,yU)?nz():yz(e)?J(yY(e.left),nK(yY(e.right))):nY(e),yZ=lj({empty:yK,patch:(e,t)=>yW(t,iS(e)),combine:yV,diff:(e,t)=>{if(eU(e,t))return yK;let r=yY(e),i=yY(t),n=J(i,nH(r),nW(yK,(e,t)=>yV(e,{_tag:yB,supervisor:t}))),s=J(r,nH(i),nW(yK,(e,t)=>yV(e,{_tag:yJ,supervisor:t})));return yV(n,s)}}),yQ=yy("effect_fiber_started",{incremental:!0}),yX=yy("effect_fiber_active"),y0=yy("effect_fiber_successes",{incremental:!0}),y1=yy("effect_fiber_failures",{incremental:!0}),y2=yS(yv((I="effect_fiber_lifetimes",F=(e=>J(r_(e.count-1,t=>e.start*Math.pow(e.factor,t)),iE,yI))({start:.5,factor:2,count:35}),T=v,new xx(I,xu(F),t3(T)))),"time_unit","milliseconds"),y3="Continue",y5="Yield",y4={_E:e=>e,_A:e=>e},y6=e=>{throw Error(`BUG: FiberRuntime - ${eY(e)} - please report an issue at https://github.com/Effect-TS/effect/issues`)},y8=Symbol.for("effect/internal/fiberRuntime/YieldedOp"),y7=V("effect/internal/fiberRuntime/yieldedOpChannel",()=>({currentOp:null})),y9={[e7]:(e,t,r)=>ek(()=>t.effect_instruction_i1(r)),OnStep:(e,t,r)=>hM(hM(r)),[e9]:(e,t,r)=>ek(()=>t.effect_instruction_i2(r)),[to]:(e,t,r)=>(e.patchRuntimeFlags(e.currentRuntimeFlags,t.patch),lG(e.currentRuntimeFlags)&&e.isInterrupted())?hI(e.getInterruptedCause()):hM(r),[ti]:(e,t,r)=>(ek(()=>t.effect_instruction_i2(r)),ek(()=>t.effect_instruction_i0()))?(e.pushStack(t),ek(()=>t.effect_instruction_i1())):cr,[tn]:(e,t,r)=>{let i=ek(()=>t.effect_instruction_i0.next(r));return i.done?hM(i.value):(e.pushStack(t),eS(i.value))}},ve={[ba]:(e,t,r,i)=>(e.processNewInterruptSignal(i.cause),lG(t)?hI(i.cause):r),[bl]:(e,t,r,i)=>{throw Error("It is illegal to have multiple concurrent run loops in a single fiber")},[bo]:(e,t,r,i)=>(i.onFiber(e,dm(t)),r),[bu]:(e,t,r,i)=>uM(cl(),()=>r)},vt=e=>uB(mX(e),e=>v_(gc(e),([e,t])=>{let r=new Map,i=[];for(let e of t)for(let t of(i.push(ik(e)),e))r.set(t.request,t);let n=i.flat();return cz(v3(e.runAll(i),n,()=>n.forEach(e=>{e.listeners.interrupted=!0})),gh,r)},!1,!1)),vr=H();class vi extends fP{static{s=by,a=b_}constructor(e,t,r){if(super(),this[s]=bv,this[a]=y4,this._queue=[],this._children=null,this._observers=[],this._running=!1,this._stack=[],this._asyncInterruptor=null,this._asyncBlockingOn=null,this._exitValue=null,this._steps=[],this._isYielding=!1,this.currentOpCount=0,this.run=()=>{this.drainQueueOnCurrentThread()},this.currentRuntimeFlags=r,this._fiberId=e,this._fiberRefs=t,l0(r)){let e=this.getFiberRef(cX);yQ.unsafeUpdate(1,e),yX.unsafeUpdate(1,e)}this.refreshRefCache()}commit(){return bO(this)}id(){return this._fiberId}resume(e){this.tell(bp(e))}get status(){return this.ask((e,t)=>t)}get runtimeFlags(){return this.ask((e,t)=>db(t)?e.currentRuntimeFlags:t.runtimeFlags)}scope(){return bb(this)}get children(){return this.ask(e=>Array.from(e.getChildren()))}getChildren(){return null===this._children&&(this._children=new Set),this._children}getInterruptedCause(){return this.getFiberRef(c1)}fiberRefs(){return this.ask(e=>e.getFiberRefs())}ask(e){return u4(()=>{let t=hU(this._fiberId);return this.tell(bh((r,i)=>{hY(t,u6(()=>e(r,i)))})),hJ(t)})}tell(e){this._queue.push(e),this._running||(this._running=!0,this.drainQueueLaterOnExecutor())}get await(){return uy(e=>{let t=t=>e(u5(t));return this.tell(bh((e,r)=>{null!==e._exitValue?t(this._exitValue):e.addObserver(t)})),u6(()=>this.tell(bh((e,r)=>{e.removeObserver(t)})))},this.id())}get inheritAll(){return up((e,t)=>{let r=e.id(),i=e.getFiberRefs(),n=t.runtimeFlags,s=fY(i,r,this.getFiberRefs());return e.setFiberRefs(s),ci(J(l2(n,e.getFiberRef(vY)),lJ(1),lJ(16)))})}get poll(){return u6(()=>t3(this._exitValue))}unsafePoll(){return this._exitValue}interruptAsFork(e){return u6(()=>this.tell(bc(n9(e))))}unsafeInterruptAsFork(e){this.tell(bc(n9(e)))}addObserver(e){null!==this._exitValue?e(this._exitValue):this._observers.push(e)}removeObserver(e){this._observers=this._observers.filter(t=>t!==e)}getFiberRefs(){return this.setFiberRef(vY,this.currentRuntimeFlags),this._fiberRefs}unsafeDeleteFiberRef(e){this._fiberRefs=fX(this._fiberRefs,e)}getFiberRef(e){return this._fiberRefs.locals.has(e)?this._fiberRefs.locals.get(e)[0][1]:e.initial}setFiberRef(e,t){this._fiberRefs=f2(this._fiberRefs,{fiberId:this._fiberId,fiberRef:e,value:t}),this.refreshRefCache()}refreshRefCache(){this.currentDefaultServices=this.getFiberRef(fC),this.currentTracer=this.currentDefaultServices.unsafeMap.get(fy.key),this.currentSupervisor=this.getFiberRef(vZ),this.currentScheduler=this.getFiberRef(mY),this.currentContext=this.getFiberRef(cB),this.currentSpan=this.currentContext.unsafeMap.get(fv.key)}setFiberRefs(e){this._fiberRefs=e,this.refreshRefCache()}addChild(e){this.getChildren().add(e)}removeChild(e){this.getChildren().delete(e)}transferChildren(e){let t=this._children;if(this._children=null,null!==t&&t.size>0)for(let r of t)null===r._exitValue&&e.add(this.currentRuntimeFlags,r)}drainQueueOnCurrentThread(){let e=!0;for(;e;){let t=y3,r=globalThis[bE];globalThis[bE]=this;try{for(;t===y3;)t=0===this._queue.length?"Done":this.evaluateMessageWhileSuspended(this._queue.splice(0,1)[0])}finally{this._running=!1,globalThis[bE]=r}this._queue.length>0&&!this._running?(this._running=!0,t===y5?(this.drainQueueLaterOnExecutor(),e=!1):e=!0):e=!1}}drainQueueLaterOnExecutor(){this.currentScheduler.scheduleTask(this.run,this.getFiberRef(cJ))}drainQueueWhileRunning(e,t){let r=t;for(;this._queue.length>0;){let t=this._queue.splice(0,1)[0];r=ve[t._tag](this,e,r,t)}return r}isInterrupted(){return!ss(this.getFiberRef(c1))}addInterruptedCause(e){let t=this.getFiberRef(c1);this.setFiberRef(c1,st(t,e))}processNewInterruptSignal(e){this.addInterruptedCause(e),this.sendInterruptSignalToAllChildren()}sendInterruptSignalToAllChildren(){if(null===this._children||0===this._children.size)return!1;let e=!1;for(let t of this._children)t.tell(bc(n9(this.id()))),e=!0;return e}interruptAllChildren(){if(this.sendInterruptSignalToAllChildren()){let e=this._children.values();this._children=null;let t=!1;return cs({while:()=>!t,body:()=>{let r=e.next();return r.done?u6(()=>{t=!0}):um(r.value.await)},step:()=>{}})}return null}reportExitValue(e){if(l0(this.currentRuntimeFlags)){let t=this.getFiberRef(cX),r=this.id().startTimeMillis,i=Date.now();switch(y2.unsafeUpdate(i-r,t),yX.unsafeUpdate(-1,t),e._tag){case te:y0.unsafeUpdate(1,t);break;case e6:y1.unsafeUpdate(1,t)}}if("Failure"===e._tag){let t=this.getFiberRef(cQ);so(e.cause)||"Some"!==t._tag||this.log("Fiber terminated with an unhandled error",e.cause,t)}}setExitValue(e){this._exitValue=e,this.reportExitValue(e);for(let t=this._observers.length-1;t>=0;t--)this._observers[t](e);this._observers=[]}getLoggers(){return this.getFiberRef(vp)}log(e,t,r){let i=tC(r)?r.value:this.getFiberRef(cV);if(dx(this.getFiberRef(vn),i))return;let n=this.getFiberRef(cW),s=this.getFiberRef(cK),a=this.getLoggers(),o=this.getFiberRefs();if(nZ(a)>0){let r=new Date(s3(this.getFiberRef(fC),h7).unsafeCurrentTimeMillis());e1(o,()=>{for(let l of a)l.log({fiberId:this.id(),logLevel:i,message:e,cause:t,context:o,spans:n,annotations:s,date:r})})}}evaluateMessageWhileSuspended(e){switch(e._tag){case bu:return y5;case ba:return this.processNewInterruptSignal(e.cause),null!==this._asyncInterruptor&&(this._asyncInterruptor(hI(e.cause)),this._asyncInterruptor=null),y3;case bl:return this._asyncInterruptor=null,this._asyncBlockingOn=null,this.evaluateEffect(e.effect),y3;case bo:return e.onFiber(this,null!==this._exitValue?df:dg(this.currentRuntimeFlags,this._asyncBlockingOn)),y3;default:return y6(e)}}evaluateEffect(e){this.currentSupervisor.onResume(this);try{let t=lG(this.currentRuntimeFlags)&&this.isInterrupted()?hI(this.getInterruptedCause()):e;for(;null!==t;){let e=t,r=this.runLoop(e);if(r===y8){let e=y7.currentOp;y7.currentOp=null,e._op===ta?lV(this.currentRuntimeFlags)?(this.tell(bf()),this.tell(bp(hz)),t=null):t=hz:e._op===e5&&(t=null)}else{this.currentRuntimeFlags=J(this.currentRuntimeFlags,lW(16));let e=this.interruptAllChildren();null!==e?t=uM(e,()=>r):(0===this._queue.length?this.setExitValue(r):this.tell(bp(r)),t=null)}}}finally{this.currentSupervisor.onSuspend(this)}}start(e){if(this._running)this.tell(bp(e));else{this._running=!0;let t=globalThis[bE];globalThis[bE]=this;try{this.evaluateEffect(e)}finally{this._running=!1,globalThis[bE]=t,this._queue.length>0&&this.drainQueueLaterOnExecutor()}}}startFork(e){this.tell(bp(e))}patchRuntimeFlags(e,t){let r=l3(e,t);return globalThis[bE]=this,this.currentRuntimeFlags=r,r}initiateAsync(e,t){let r=!1,i=e=>{r||(r=!0,this.tell(bp(e)))};lG(e)&&(this._asyncInterruptor=i);try{t(i)}catch(e){i(uR(n7(e)))}}pushStack(e){this._stack.push(e),"OnStep"===e._op&&this._steps.push({refs:this.getFiberRefs(),flags:this.currentRuntimeFlags})}popStack(){let e=this._stack.pop();if(e)return"OnStep"===e._op&&this._steps.pop(),e}getNextSuccessCont(){let e=this.popStack();for(;e;){if(e._op!==e8)return e;e=this.popStack()}}getNextFailCont(){let e=this.popStack();for(;e;){if(e._op!==e7&&e._op!==ti&&e._op!==tn)return e;e=this.popStack()}}Tag(e){return u6(()=>s3(this.currentContext,e))}Left(e){return uF(e.left)}None(e){return uF(new hf)}Right(e){return hM(e.right)}Some(e){return hM(e.value)}Micro(e){return ub(t=>{let r=t,i=mR(md(e,this.currentContext));return i.addObserver(e=>{if("Success"===e._tag)return r(hM(e.value));switch(e.cause._tag){case"Interrupt":return r(hI(n9(or)));case"Fail":return r(uF(e.cause.error));case"Die":return r(uO(e.cause.defect))}}),ub(e=>{r=t=>{e(cr)},i.unsafeInterrupt()})})}[tt](e){let t=ek(()=>e.effect_instruction_i0()),r=this.getNextSuccessCont();return void 0!==r?(r._op in y9||y6(r),y9[r._op](this,r,t)):(y7.currentOp=hM(t),y8)}[te](e){let t=this.getNextSuccessCont();return void 0!==t?(t._op in y9||y6(t),y9[t._op](this,t,e.effect_instruction_i0)):(y7.currentOp=e,y8)}[e6](e){let t=e.effect_instruction_i0,r=this.getNextFailCont();if(void 0===r)return y7.currentOp=hI(t),y8;switch(r._op){case e8:case e9:if(!(lG(this.currentRuntimeFlags)&&this.isInterrupted()))return ek(()=>r.effect_instruction_i1(t));return hI(sm(t));case"OnStep":if(!(lG(this.currentRuntimeFlags)&&this.isInterrupted()))return hM(hI(t));return hI(sm(t));case to:if(this.patchRuntimeFlags(this.currentRuntimeFlags,r.patch),lG(this.currentRuntimeFlags)&&this.isInterrupted())return hI(st(t,this.getInterruptedCause()));return hI(t);default:y6(r)}}[ts](e){return ek(()=>e.effect_instruction_i0(this,dm(this.currentRuntimeFlags)))}Blocked(e){let t=this.getFiberRefs(),r=this.currentRuntimeFlags;if(this._steps.length>0){let i=[],n=this._steps[this._steps.length-1],s=this.popStack();for(;s&&"OnStep"!==s._op;)i.push(s),s=this.popStack();this.setFiberRefs(n.refs),this.currentRuntimeFlags=n.flags;let a=de(n.refs,t),o=l2(n.flags,r);return hM(un(e.effect_instruction_i0,up(t=>{for(;i.length>0;)t.pushStack(i.pop());return t.setFiberRefs(dr(t.id(),t.getFiberRefs())(a)),t.currentRuntimeFlags=l3(o)(t.currentRuntimeFlags),e.effect_instruction_i1})))}return ct(t=>uM(vk(us(e.effect_instruction_i0)),()=>t(e.effect_instruction_i1)))}RunBlocked(e){return vt(e.effect_instruction_i0)}[tr](e){let t=e.effect_instruction_i0,r=this.currentRuntimeFlags,i=l3(r,t);if(lG(i)&&this.isInterrupted())return hI(this.getInterruptedCause());if(this.patchRuntimeFlags(this.currentRuntimeFlags,t),!e.effect_instruction_i1)return hz;{let t=l2(i,r);return this.pushStack(new uo(t,e)),ek(()=>e.effect_instruction_i1(r))}}[e7](e){return this.pushStack(e),e.effect_instruction_i0}OnStep(e){return this.pushStack(e),e.effect_instruction_i0}[e8](e){return this.pushStack(e),e.effect_instruction_i0}[e9](e){return this.pushStack(e),e.effect_instruction_i0}[e5](e){return this._asyncBlockingOn=e.effect_instruction_i1,this.initiateAsync(this.currentRuntimeFlags,e.effect_instruction_i0),y7.currentOp=e,y8}[ta](e){return this._isYielding=!1,y7.currentOp=e,y8}[ti](e){let t=e.effect_instruction_i0,r=e.effect_instruction_i1;return t()?(this.pushStack(e),r()):hz}[tn](e){return y9[tn](this,e,void 0)}[e4](e){return ek(()=>e.commit())}runLoop(e){let t=e;for(this.currentOpCount=0;;){if((2&this.currentRuntimeFlags)!=0&&this.currentSupervisor.onEffect(this,t),this._queue.length>0&&(t=this.drainQueueWhileRunning(this.currentRuntimeFlags,t)),!this._isYielding){this.currentOpCount+=1;let e=this.currentScheduler.shouldYield(this);if(!1!==e){this._isYielding=!0,this.currentOpCount=0;let r=t;t=uM(cl({priority:e}),()=>r)}}try{if((t=this.currentTracer.context(()=>vr!==t[ua]._V?uC(`Cannot execute an Effect versioned ${t[ua]._V} with a Runtime of version ${H()}`):this[t._op](t),this))===y8){let e=y7.currentOp;if(e._op===ta||e._op===e5)return y8;return y7.currentOp=null,e._op===te||e._op===e6?e:hI(n7(e))}}catch(e){t=(t===y8||ea(t,"_op"))&&t._op in this?hu(e)?hI(st(n7(e),n9(or))):uO(e):uC(`Not a valid effect: ${eY(t)}`)}}}}let vn=V("effect/FiberRef/currentMinimumLogLevel",()=>cD(dy("Info"))),vs=e=>bT(t=>{s3(f1(t.context,fC),fh).unsafe.log(e.log(t))}),va=V(Symbol.for("effect/Logger/defaultLogger"),()=>vs(bU)),vo=V(Symbol.for("effect/Logger/jsonLogger"),()=>vs(bH)),vl=V(Symbol.for("effect/Logger/logFmtLogger"),()=>vs(bq)),vu=V(Symbol.for("effect/Logger/prettyLogger"),()=>b5),vc=V(Symbol.for("effect/Logger/structuredLogger"),()=>vs(bB)),vh=V(Symbol.for("effect/Logger/tracerLogger"),()=>bT(({annotations:e,cause:t,context:r,fiberId:i,logLevel:n,message:s})=>{let a=s5(f1(r,cB),fv);if("None"===a._tag||"ExternalSpan"===a.value._tag)return;let o=s3(f1(r,fC),h7),l={};for(let[t,r]of e)l[t]=r;l["effect.fiberId"]=oe(i),l["effect.logLevel"]=n.label,null!==t&&"Empty"!==t._tag&&(l["effect.cause"]=sR(t,{renderErrorCause:!0})),a.value.event(eY(Array.isArray(s)?s[0]:s),o.unsafeCurrentTimeNanos(),l)})),vp=V(Symbol.for("effect/FiberRef/currentLoggers"),()=>c$(nY(va,vh))),vf=D(3,(e,t,r)=>uM(vU,i=>{let n=[],s=u4(()=>{if(0===n.length)return cr;let e=n;return n=[],r(e)});return ct(r=>J(gd(t),ch(s),gP,r,vk,uM(e=>c7(i,cd(e))),ch(vm(()=>s)),ud(bT(t=>{n.push(e.log(t))}))))})),vd=D(e=>uh(e[0]),(e,t)=>ce(u8(e,e=>vm(r=>t(e,r))))),vm=e=>up(t=>{let r=t.getFiberRefs(),i=t.currentRuntimeFlags;return uM(vU,t=>c9(t,t=>up(n=>{let s=n.getFiberRefs(),a=n.currentRuntimeFlags,o=de(s,r),l=l2(a,i),u=de(r,s);return n.setFiberRefs(dr(o,n.id(),r)),v2(co(e(t),l),u6(()=>{n.setFiberRefs(dr(u,n.id(),n.getFiberRefs()))}))})))}),vg=(e=>ep(e[0])&&!uh(e[0]),e=>{if(Array.isArray(e)||ep(e))return[e,tQ()];let t=Object.keys(e),r=t.length;return[t.map(t=>e[t]),tI(e=>{let i={};for(let n=0;n<r;n++)i[t[n]]=e[n];return i})]}),vb=(e,t,r)=>{let i=[];for(let t of e)i.push(uE(t));return uM(vv(i,$,{concurrency:r?.concurrency,batching:r?.batching,concurrentFinalizers:r?.concurrentFinalizers}),e=>{let i=tQ(),n=e.length,s=Array(n),a=Array(n),o=!1;for(let t=0;t<n;t++){let r=e[t];"Left"===r._tag?(s[t]=tI(r.left),o=!0):(a[t]=r.right,s[t]=i)}return o?"Some"===t._tag?uF(t.value(s)):uF(s):r?.discard?cr:"Some"===t._tag?u5(t.value(a)):u5(a)})},vx=(e,t,r)=>{let i=[];for(let t of e)i.push(uE(t));return r?.discard?vv(i,$,{concurrency:r?.concurrency,batching:r?.batching,discard:!0,concurrentFinalizers:r?.concurrentFinalizers}):uG(vv(i,$,{concurrency:r?.concurrency,batching:r?.batching,concurrentFinalizers:r?.concurrentFinalizers}),e=>"Some"===t._tag?t.value(e):e)},vy=(e,t)=>{let[r,i]=vg(e);return t?.mode==="validate"?vb(r,i,t):t?.mode==="either"?vx(r,i,t):t?.discard!==!0&&"Some"===i._tag?uG(vv(r,$,t),i.value):vv(r,$,t)},vv=((e,t)=>Array.from({length:t},()=>e),D(e=>ep(e[0]),(e,t,r)=>up(i=>{let n=r?.batching===!0||r?.batching==="inherit"&&i.getFiberRef(cZ);return r?.discard?gp(r.concurrency,()=>vR(fU,r?.concurrentFinalizers)(r=>n?v_(e,(e,i)=>r(t(e,i)),!0,!1,1):uB(e,(e,i)=>r(t(e,i)))),()=>vR(fJ,r?.concurrentFinalizers)(r=>v_(e,(e,i)=>r(t(e,i)),n,!1)),i=>vR(fH(i),r?.concurrentFinalizers)(r=>v_(e,(e,i)=>r(t(e,i)),n,!1,i))):gp(r?.concurrency,()=>vR(fU,r?.concurrentFinalizers)(r=>n?vw(e,1,(e,i)=>r(t(e,i)),!0):uq(e,(e,i)=>r(t(e,i)))),()=>vR(fJ,r?.concurrentFinalizers)(r=>vS(e,(e,i)=>r(t(e,i)),n)),i=>vR(fH(i),r?.concurrentFinalizers)(r=>vw(e,i,(e,i)=>r(t(e,i)),n)))}))),vS=(e,t,r)=>u4(()=>{let i=rw(e),n=Array(i.length);return ch(v_(i,(e,r)=>uM(t(e,r),e=>u6(()=>n[r]=e)),r,!1),u5(n))}),v_=(e,t,r,i,n)=>ct(s=>u7(a=>up(o=>{let l=Array.from(e).reverse(),u=l.length;if(0===u)return cr;let c=0,h=!1,p=n?Math.min(l.length,n):l.length,f=new Set,d=[],m=()=>f.forEach(e=>{e.currentScheduler.scheduleTask(()=>{e.unsafeInterruptAsFork(o.id())},0)}),g=[],b=[],x=[],y=()=>{let e=d.filter(({exit:e})=>"Failure"===e._tag).sort((e,t)=>e.index<t.index?-1:+(e.index!==t.index)).map(({exit:e})=>e);return 0===e.length&&e.push(hz),e},v=(e,t=!1)=>{let r=ce(a(e)),i=vC(r,o,o.currentRuntimeFlags,bx);return o.currentScheduler.scheduleTask(()=>{t&&i.unsafeInterruptAsFork(o.id()),i.resume(r)},0),i},S=()=>{i||(u-=l.length,l=[]),h=!0,m()},_=r?uP:uI,w=v(uy(e=>{let i=(e,t)=>{"Blocked"===e._op?x.push(e):(d.push({index:t,exit:e}),"Failure"!==e._op||h||S())},a=()=>{if(l.length>0){let p=l.pop(),m=c++,S=()=>{let e=l.pop();return m=c++,uM(cl(),()=>uM(_(s(t(e,m))),w))},w=e=>l.length>0&&(i(e,m),l.length>0)?S():u5(e),k=v(uM(_(s(t(p,m))),w));g.push(k),f.add(k),h&&k.currentScheduler.scheduleTask(()=>{k.unsafeInterruptAsFork(o.id())},0),k.addObserver(t=>{let s;if(s="Failure"===t._op?t:t.effect_instruction_i0,b.push(k),f.delete(k),i(s,m),d.length===u)e(u5(t0(hO(y(),{parallel:!0}),()=>hz)));else if(x.length+d.length===u){let t=y();e(u5(un(x.map(e=>e.effect_instruction_i0).reduce(mZ),v_([t0(hO(t,{parallel:!0}),()=>hz),...x.map(e=>e.effect_instruction_i1)],e=>e,r,!0,n))))}else a()})}};for(let e=0;e<p;e++)a()}));return um(uQ(uD(s(bO(w))),hA({onFailure:e=>{S();let t=x.length+1,r=Math.min("number"==typeof n?n:x.length,x.length),i=Array.from(x);return uy(n=>{let s=[],a=0,o=0,l=(r,o)=>l=>{s[r]=l,++a===t&&n(hM(hI(e))),i.length>0&&o&&u()},u=()=>{v(i.pop(),!0).addObserver(l(o,!0)),o++};w.addObserver(l(o,!1)),o++;for(let e=0;e<r;e++)u()})},onSuccess:()=>uq(b,e=>e.inheritAll)})))}))),vw=(e,t,r,i)=>u4(()=>{let n=rw(e),s=Array(n.length);return ch(v_(n,(e,t)=>uG(r(e,t),e=>s[t]=e),i,!1,t),u5(s))}),vk=e=>vI(e,bx),vO=(e,t,r,i=null)=>{let n=vE(e,t,r,i);return n.resume(e),n},vC=(e,t,r,i=null)=>vE(e,t,r,i),vE=(e,t,r,i=null)=>{let n=oo(),s=fZ(t.getFiberRefs(),n),a=new vi(n,s,r),o=f1(s,cB),l=a.currentSupervisor;return l.onStart(o,e,tI(t),a),a.addObserver(e=>l.onEnd(e,a)),(null!==i?i:J(t.getFiberRef(c0),t0(()=>t.scope()))).add(r,a),a},vI=(e,t)=>up((r,i)=>u5(vO(e,r,i.runtimeFlags,t))),vF=(e=>X(e[2]),e=>h0(t=>tX(s5(t,vL),{onNone:()=>e,onSome:t=>{switch(t.strategy._tag){case"Parallel":return e;case"Sequential":case"ParallelN":return uM(ht(t,fJ),t=>vK(e,t))}}}))),vT=e=>t=>h0(r=>tX(s5(r,vL),{onNone:()=>t,onSome:r=>"ParallelN"===r.strategy._tag&&r.strategy.parallelism===e?t:uM(ht(r,fH(e)),e=>vK(t,e))})),vR=(e,t)=>r=>h0(i=>tX(s5(i,vL),{onNone:()=>r($),onSome:i=>{if(!0!==t)return r($);{let t="Parallel"===e._tag?vF:"Sequential"===e._tag?vj:vT(e.parallelism);switch(i.strategy._tag){case"Parallel":return t(r(vF));case"Sequential":return t(r(vj));case"ParallelN":return t(r(vT(i.strategy.parallelism)))}}}})),vN=e=>uM(vL,e),vA=e=>uM(vH(),t=>uQ(e(t),e=>t.close(e))),vj=e=>h0(t=>tX(s5(t,vL),{onNone:()=>e,onSome:t=>{switch(t.strategy._tag){case"Sequential":return e;case"Parallel":case"ParallelN":return uM(ht(t,fU),t=>vK(e,t))}}})),vM=(e=>uh(e[1]),e=>vG(fC,sX(pJ,e))),vz=D(e=>uh(e[1]),(e,t,r)=>v$(e,t,(e,t)=>[e,t],r)),vP=D(e=>uh(e[1]),(e,t,r)=>r?.concurrent!==!0&&(r?.batching===void 0||!1===r.batching)?cc(e,t):v$(e,t,(e,t)=>e,r)),vD=D(e=>uh(e[1]),(e,t,r)=>r?.concurrent!==!0&&(r?.batching===void 0||!1===r.batching)?ch(e,t):v$(e,t,(e,t)=>t,r)),v$=D(e=>uh(e[1]),(e,t,r,i)=>uG(vy([e,t],{concurrency:i?.concurrent?2:1,batching:i?.batching,concurrentFinalizers:i?.concurrentFinalizers}),([e,t])=>r(e,t))),vL=s6("effect/Scope"),vU=vL,vq=(e,t)=>{"Open"===e.state._tag&&e.state.finalizers.set({},t)},vB={[c6]:c6,[c8]:c8,pipe(){return e3(this,arguments)},fork(e){return u6(()=>{let t=vJ(e);if("Closed"===this.state._tag)return t.state=this.state,t;let r={};return this.state.finalizers.set(r,e=>t.close(e)),vq(t,e=>u6(()=>{"Open"===this.state._tag&&this.state.finalizers.delete(r)})),t})},close(e){return u4(()=>{if("Closed"===this.state._tag)return cr;let t=Array.from(this.state.finalizers.values()).reverse();return(this.state={_tag:"Closed",exit:e},0===t.length)?cr:fq(this.strategy)?J(uq(t,t=>uI(t(e))),uM(e=>J(hO(e),t7(hk),t0(()=>hz)))):fB(this.strategy)?J(vS(t,t=>uI(t(e)),!1),uM(e=>J(hO(e,{parallel:!0}),t7(hk),t0(()=>hz)))):J(vw(t,this.strategy.parallelism,t=>uI(t(e)),!1),uM(e=>J(hO(e,{parallel:!0}),t7(hk),t0(()=>hz))))})},addFinalizer(e){return u4(()=>"Closed"===this.state._tag?e(this.state.exit):(this.state.finalizers.set({},e),cr))}},vJ=(e=fU)=>{let t=Object.create(vB);return t.strategy=e,t.state={_tag:"Open",finalizers:new Map},t},vH=(e=fU)=>u6(()=>vJ(e)),vK=D(2,(e,t)=>h3(e,s4(sQ(vL,t)))),vV=D(2,(e,t)=>J(e,vK(t),uQ(e=>t.close(e)))),vW=D(2,(e,t)=>um(vd(uM(cE(e),r=>ud(cF(e,t),r)),t=>cF(e,t)))),vG=D(2,(e,t)=>cI(e,r=>vW(e,t(r)))),vY=cq(lX,{differ:l5,fork:l5.empty}),vZ=cq(yU,{differ:yZ,fork:yK}),vQ=D(3,(e,t,r)=>v0(e,t,{onSelfWin:(e,t)=>uM(e.await,i=>{switch(i._tag){case te:return uM(e.inheritAll,()=>r.onSelfDone(i,t));case e6:return r.onSelfDone(i,t)}}),onOtherWin:(e,t)=>uM(e.await,i=>{switch(i._tag){case te:return uM(e.inheritAll,()=>r.onOtherDone(i,t));case e6:return r.onOtherDone(i,t)}})})),vX=D(2,(e,t)=>uj(r=>vQ(e,t,{onSelfDone:(e,t)=>hj(e,{onFailure:e=>J(bO(t),gK(t=>se(e,t))),onSuccess:e=>J(t,cm(r),ud(e))}),onOtherDone:(e,t)=>hj(e,{onFailure:e=>J(bO(t),gK(t=>se(t,e))),onSuccess:e=>J(t,cm(r),ud(e))})}))),v0=D(3,(e,t,r)=>up((i,n)=>{let s=n.runtimeFlags,a=aJ(!0),o=vE(e,i,s,r.selfScope),l=vE(t,i,s,r.otherScope);return uy(i=>{o.addObserver(()=>v1(o,l,r.onSelfWin,a,i)),l.addObserver(()=>v1(l,o,r.onOtherWin,a,i)),o.startFork(e),l.startFork(t)},a6(o.id(),l.id()))})),v1=(e,t,r,i,n)=>{aH(!0,!1)(i)&&n(r(e,t))},v2=D(2,(e,t)=>ct(r=>uL(r(e),{onFailure:e=>uL(t,{onFailure:t=>uR(st(e,t)),onSuccess:()=>uR(e)}),onSuccess:e=>ud(t,e)}))),v3=(e,t,r)=>uj(i=>uM(uM(vk(uV(e)),e=>uy(i=>{let n=t.map(e=>e.listeners.count),s=()=>{n.every(e=>0===e)&&t.every(e=>"Pending"===e.result.state.current._tag||!!("Done"===e.result.state.current._tag&&hv(e.result.state.current.effect)&&"Failure"===e.result.state.current.effect._tag&&sa(e.result.state.current.effect.cause)))&&(a.forEach(e=>e()),r?.(),i(cd(e)))};e.addObserver(e=>{a.forEach(e=>e()),i(e)});let a=t.map((e,t)=>{let r=e=>{n[t]=e,s()};return e.listeners.addObserver(r),()=>e.listeners.removeObserver(r)});return s(),u6(()=>{a.forEach(e=>e())})})),()=>u4(()=>uB(t.flatMap(e=>e.state.completed?[]:[e]),e=>yT(e.request,hR(i)))))),v5=function(){let e=Symbol.for("effect/Data/Error/plainArgs");return({BaseEffectError:class extends hi{constructor(t){super(t?.message,t?.cause?{cause:t.cause}:void 0),t&&(Object.assign(this,t),Object.defineProperty(this,e,{value:t,enumerable:!1}))}toJSON(){return{...this[e],...this}}}}).BaseEffectError}(),v4=e=>{let t={BaseEffectError:class extends v5{constructor(...t){super(...t),this._tag=e}}};return t.BaseEffectError.prototype.name=e,t.BaseEffectError},v6=e=>e._tag===nX,v8=e=>sI(e,{onEmpty:tI(n6),onFail:t7(n8),onDie:e=>tI(n7(e)),onInterrupt:e=>tI(n9(e)),onSequential:ra(st),onParallel:ra(se)}),v7=e=>hr($)(e),v9=e=>ea(e,hp),Se=e=>ea(e,hs),St=Symbol.for("effect/DateTime"),Sr=Symbol.for("effect/DateTime/TimeZone"),Si={[St]:St,pipe(){return e3(this,arguments)},[eH](){return this.toString()},toJSON(){return SI(this).toJSON()}},Sn={...Si,_tag:"Utc",[eI](){return e$(this,ej(this.epochMillis))},[eL](e){return Sc(e)&&"Utc"===e._tag&&this.epochMillis===e.epochMillis},toString(){return`DateTime.Utc(${SI(this).toJSON()})`}},Ss={...Si,_tag:"Zoned",[eI](){return J(ej(this.epochMillis),eR(eF(this.zone)),e$(this))},[eL](e){return Sc(e)&&"Zoned"===e._tag&&this.epochMillis===e.epochMillis&&eU(this.zone,e.zone)},toString(){return`DateTime.Zoned(${SV(this)})`}},Sa={[Sr]:Sr,[eH](){return this.toString()}},So={...Sa,_tag:"Named",[eI](){return e$(this,eM(`Named:${this.id}`))},[eL](e){return Sh(e)&&"Named"===e._tag&&this.id===e.id},toString(){return`TimeZone.Named(${this.id})`},toJSON(){return{_id:"TimeZone",_tag:"Named",id:this.id}}},Sl={...Sa,_tag:"Offset",[eI](){return e$(this,eM(`Offset:${this.offset}`))},[eL](e){return Sh(e)&&"Offset"===e._tag&&this.offset===e.offset},toString(){return`TimeZone.Offset(${SR(this.offset)})`},toJSON(){return{_id:"TimeZone",_tag:"Offset",offset:this.offset}}},Su=(e,t,r)=>{let i=Object.create(Ss);return i.epochMillis=e,i.zone=t,Object.defineProperty(i,"partsUtc",{value:r,enumerable:!1,writable:!0}),Object.defineProperty(i,"adjustedEpochMillis",{value:void 0,enumerable:!1,writable:!0}),Object.defineProperty(i,"partsAdjusted",{value:void 0,enumerable:!1,writable:!0}),i},Sc=e=>ea(e,St),Sh=e=>ea(e,Sr),Sp=e=>"Zoned"===e._tag,Sf=tK((e,t)=>e.epochMillis===t.epochMillis),Sd=e=>{let t=Object.create(Sn);return t.epochMillis=e,Object.defineProperty(t,"partsUtc",{value:void 0,enumerable:!1,writable:!0}),t},Sm=e=>{let t=e.getTime();if(Number.isNaN(t))throw new hh("Invalid date");return Sd(t)},Sg=e=>{if(Sc(e))return e;if(e instanceof Date)return Sm(e);if("object"==typeof e){let t=new Date(0);return Sz(t,e),Sm(t)}return Sm(new Date(e))},Sb=(e,t)=>{let r;if(t?.timeZone===void 0&&Sc(e)&&Sp(e))return e;let i=Sg(e);if(i.epochMillis<-86399999568e5||i.epochMillis>86399999496e5)throw new hh(`Epoch millis out of range: ${i.epochMillis}`);if(t?.timeZone===void 0)r=Sk(-6e4*new Date(i.epochMillis).getTimezoneOffset());else if(Sh(t?.timeZone))r=t.timeZone;else if("number"==typeof t?.timeZone)r=Sk(t.timeZone);else{let e=SE(t.timeZone);if(tO(e))throw new hh(`Invalid time zone: ${t.timeZone}`);r=e.value}return t?.adjustForTimeZone!==!0?Su(i.epochMillis,r,i.partsUtc):SP(i.epochMillis,r)},Sx=t4(Sb),Sy=/^(.{17,35})\[(.+)\]$/,Sv=(e=>Sc(e[0]),V("effect/DateTime/validZoneCache",()=>new Map)),SS={day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"longOffset",fractionalSecondDigits:3,hourCycle:"h23"},S_=e=>{let t=e.resolvedOptions().timeZone;if(Sv.has(t))return Sv.get(t);let r=Object.create(So);return r.id=t,r.format=e,Sv.set(t,r),r},Sw=e=>{if(Sv.has(e))return Sv.get(e);try{return S_(new Intl.DateTimeFormat("en-US",{...SS,timeZone:e}))}catch(t){throw new hh(`Invalid time zone: ${e}`)}},Sk=e=>{let t=Object.create(Sl);return t.offset=e,t},SO=t4(Sw),SC=/^(?:GMT|[+-])/,SE=e=>{if(SC.test(e)){let t=S$(e);return null===t?tQ():tI(Sk(t))}return SO(e)},SI=((e,t)=>SA(t)-SA(e),e=>new Date(e.epochMillis)),SF=e=>{if("Utc"===e._tag)return new Date(e.epochMillis);if("Offset"===e.zone._tag)return new Date(e.epochMillis+e.zone.offset);if(void 0!==e.adjustedEpochMillis)return new Date(e.adjustedEpochMillis);let t=e.zone.format.formatToParts(e.epochMillis).filter(e=>"literal"!==e.type),r=new Date(0);return r.setUTCFullYear(Number(t[2].value),Number(t[0].value)-1,Number(t[1].value)),r.setUTCHours(Number(t[3].value),Number(t[4].value),Number(t[5].value),Number(t[6].value)),e.adjustedEpochMillis=r.getTime(),r},ST=e=>SF(e).getTime()-SA(e),SR=e=>{let t=Math.abs(e),r=Math.floor(t/36e5),i=Math.round(t%36e5/6e4);return 60===i&&(r+=1,i=0),`${e<0?"-":"+"}${String(r).padStart(2,"0")}:${String(i).padStart(2,"0")}`},SN=e=>SR(ST(e)),SA=e=>e.epochMillis,Sj=e=>({millis:e.getUTCMilliseconds(),seconds:e.getUTCSeconds(),minutes:e.getUTCMinutes(),hours:e.getUTCHours(),day:e.getUTCDate(),weekDay:e.getUTCDay(),month:e.getUTCMonth()+1,year:e.getUTCFullYear()}),SM=e=>(void 0!==e.partsUtc||(e.partsUtc=SJ(e,Sj)),e.partsUtc),Sz=(e,t)=>{if(void 0!==t.year&&e.setUTCFullYear(t.year),void 0!==t.month&&e.setUTCMonth(t.month-1),void 0!==t.day&&e.setUTCDate(t.day),void 0!==t.weekDay){let r=t.weekDay-e.getUTCDay();e.setUTCDate(e.getUTCDate()+r)}void 0!==t.hours&&e.setUTCHours(t.hours),void 0!==t.minutes&&e.setUTCMinutes(t.minutes),void 0!==t.seconds&&e.setUTCSeconds(t.seconds),void 0!==t.millis&&e.setUTCMilliseconds(t.millis)},SP=(e,t)=>{let r="Offset"===t._tag?t.offset:SL(e,t);return Su(e-r,t)},SD=/([+-])(\d{2}):(\d{2})$/,S$=e=>{let t=SD.exec(e);if(null===t)return null;let[,r,i,n]=t;return("+"===r?1:-1)*(60*Number(i)+Number(n))*6e4},SL=(e,t)=>{let r=t.format.formatToParts(e).find(e=>"timeZoneName"===e.type)?.value??"";if("GMT"===r)return 0;let i=S$(r);return null===i?ST(Su(e,t)):i},SU=D(2,(e,t)=>{if("Utc"===e._tag){let r=SI(e);return t(r),Sd(r.getTime())}let r=new Date(SF(e).getTime());return t(r),SP(r.getTime(),e.zone)}),Sq=((e,t)=>Sq(e,e=>{let r=new Date(e);return t(r),r.getTime()}),D(2,(e,t)=>{let r=t(SA(e));return"Utc"===e._tag?Sd(r):Su(r,e.zone)})),SB=D(2,(e,t)=>t(SF(e))),SJ=D(2,(e,t)=>t(SI(e))),SH=(e,t)=>{e.setTime(e.getTime()+t)},SK=((e,t)=>SU(e,e=>{if(t.millis&&SH(e,t.millis),t.seconds&&SH(e,1e3*t.seconds),t.minutes&&SH(e,6e4*t.minutes),t.hours&&SH(e,36e5*t.hours),t.days&&e.setUTCDate(e.getUTCDate()+t.days),t.weeks&&e.setUTCDate(e.getUTCDate()+7*t.weeks),t.months){let r=e.getUTCDate();e.setUTCMonth(e.getUTCMonth()+t.months+1,0),r<e.getUTCDate()&&e.setUTCDate(r)}if(t.years){let r=e.getUTCDate(),i=e.getUTCMonth();e.setUTCFullYear(e.getUTCFullYear()+t.years,i+1,0),r<e.getUTCDate()&&e.setUTCDate(r)}}),e=>{let t=SF(e);return"Utc"===e._tag?t.toISOString():`${t.toISOString().slice(0,-1)}${SN(e)}`}),SV=e=>"Offset"===e.zone._tag?SK(e):`${SK(e)}[${e.zone.id}]`,SW=e=>e.toUpperCase(),SG=e=>e.toLowerCase();class SY{constructor(e,t=!1){this.s=e,this.stripped=t,this.index=0,this.length=e.length}next(){if(this.done)return{done:!0,value:void 0};let e=this.index;for(;!this.done&&!SZ(this.s[this.index]);)this.index=this.index+1;let t=this.index;if(!this.done){let e=this.s[this.index];this.index=this.index+1,!this.done&&SQ(e,this.s[this.index])&&(this.index=this.index+1),this.stripped||(t=this.index)}return{done:!1,value:this.s.substring(e,t)}}[Symbol.iterator](){return new SY(this.s,this.stripped)}get done(){return this.index>=this.length}}let SZ=e=>{let t=e.charCodeAt(0);return 13===t||10===t},SQ=(e,t)=>13===e.charCodeAt(0)&&10===t.charCodeAt(0);(e,t)=>S0(e.seconds,t.seconds)&&S0(e.minutes,t.minutes)&&S0(e.hours,t.hours)&&S0(e.days,t.days)&&S0(e.months,t.months)&&S0(e.weekdays,t.weekdays);let SX=tY(tV),S0=(e,t)=>SX(rw(e),rw(t)),S1=Symbol.for("effect/ScheduleInterval"),S2={[S1]:S1,startMillis:0,endMillis:0},S3=(e,t)=>e>t?S2:{[S1]:S1,startMillis:e,endMillis:t},S5=D(2,(e,t)=>S4(e,t)===e),S4=D(2,(e,t)=>e.endMillis<=t.startMillis?e:t.endMillis<=e.startMillis?t:e.startMillis<t.startMillis?e:t.startMillis<e.startMillis?t:e.endMillis<=t.endMillis?e:t),S6=D(2,(e,t)=>S3(Math.max(e.startMillis,t.startMillis),Math.min(e.endMillis,t.endMillis))),S8=e=>e.startMillis>=e.endMillis,S7=e=>aS(e.endMillis-e.startMillis),S9=e=>S3(e,Number.POSITIVE_INFINITY),_e=Symbol.for("effect/ScheduleIntervals"),_t=e=>({[_e]:_e,intervals:e}),_r=D(2,(e,t)=>iq(t.intervals)?iq(e.intervals)?iJ(e.intervals).startMillis<iJ(t.intervals).startMillis?_i(iG(e.intervals),t.intervals,iJ(e.intervals),iy()):_i(e.intervals,iG(t.intervals),iJ(t.intervals),iy()):t:e),_i=(e,t,r,i)=>{let n=e,s=t,a=r,o=i;for(;iq(n)||iq(s);)if(!iq(n)&&iq(s))a.endMillis<iJ(s).startMillis?(o=J(o,iR(a)),a=iJ(s)):a=S3(a.startMillis,iJ(s).endMillis),s=iG(s),n=iy();else if(iq(n)&&iU(s))a.endMillis<iJ(n).startMillis?(o=J(o,iR(a)),a=iJ(n)):a=S3(a.startMillis,iJ(n).endMillis),s=iy(),n=iG(n);else if(iq(n)&&iq(s))iJ(n).startMillis<iJ(s).startMillis?(a.endMillis<iJ(n).startMillis?(o=J(o,iR(a)),a=iJ(n)):a=S3(a.startMillis,iJ(n).endMillis),n=iG(n)):(a.endMillis<iJ(s).startMillis?(o=J(o,iR(a)),a=iJ(s)):a=S3(a.startMillis,iJ(s).endMillis),s=iG(s));else throw Error(em("Intervals.unionLoop"));return _t(J(o,iR(a),iO))},_n=D(2,(e,t)=>_s(e.intervals,t.intervals,iy())),_s=(e,t,r)=>{let i=e,n=t,s=r;for(;iq(i)&&iq(n);){let e=J(iJ(i),S6(iJ(n))),t=S8(e)?s:J(s,iR(e));J(iJ(i),S5(iJ(n)))?i=iG(i):n=iG(n),s=t}return _t(iO(s))},_a=e=>J(e.intervals,iB,t0(()=>S2)).startMillis,_o=D(2,(e,t)=>_a(e)<_a(t)),_l=((e,t)=>_o(e,t)?t:e,e=>J(e.intervals,iB,t0(()=>S2)).endMillis),_u=e=>iq(e.intervals),_c="Continue",_h="Done",_p=e=>({_tag:_c,intervals:e}),_f=e=>({_tag:_c,intervals:_t(iS(e))}),_d={_tag:_h},_m=e=>e._tag===_c,_g=e=>e._tag===_h,_b=Symbol.for("effect/Schedule"),_x=e=>ea(e,_b),_y=Symbol.for("effect/ScheduleDriver"),_v={_Out:e=>e,_In:e=>e,_R:e=>e},_S={_Out:e=>e,_In:e=>e,_R:e=>e};class __{constructor(e,t){this[_b]=_v,this.initial=e,this.step=t}pipe(){return e3(this,arguments)}}class _w{constructor(e,t){this[_y]=_S,this.schedule=e,this.ref=t}get state(){return uG(mU(this.ref),e=>e[1])}get last(){return uM(mU(this.ref),([e,t])=>{switch(e._tag){case"None":return uT(()=>new hf);case"Some":return u5(e.value)}})}get reset(){return mq(this.ref,[tQ(),this.schedule.initial])}next(e){return J(uG(mU(this.ref),e=>e[1]),uM(t=>J(fF,uM(r=>J(u4(()=>this.schedule.step(r,e,t)),uM(([e,t,i])=>{let n=mq(this.ref,[tI(t),e]);if(_g(i))return ch(n,uF(tQ()));let s=_a(i.intervals)-r;return s<=0?ud(n,t):J(n,ch(gd(aS(s))),ud(t))}))))))}}let _k=(e,t)=>new __(e,t),_O=D(2,(e,t)=>_C(e,e=>u6(()=>t(e)))),_C=D(2,(e,t)=>_z(e,(e,r)=>uG(t(e),e=>aj(r,au(e))))),_E=((e,t)=>_k([e.initial,t.initial,!0],(r,i,n)=>n[2]?uM(e.step(r,i,n[0]),([e,s,a])=>_g(a)?uG(t.step(r,i,n[1]),([t,r,i])=>[[e,t,!1],tP(r),i]):u5([[e,n[1],!0],tz(s),a])):uG(t.step(r,i,n[1]),([e,t,r])=>[[n[0],e,!1],tP(t),r])),D(2,(e,t)=>_I(e,(e,r)=>u6(()=>t(e,r))))),_I=D(2,(e,t)=>_k(e.initial,(r,i,n)=>uM(e.step(r,i,n),([e,r,n])=>_g(n)?u5([e,r,_d]):uG(t(i,r),t=>t?[e,r,n]:[e,r,_d])))),_F=((e,t)=>_F(e,e=>u6(()=>t(e))),D(2,(e,t)=>_k(e.initial,(r,i,n)=>uM(t(i),t=>e.step(r,t,n))))),_T=((e,t)=>_z(e,(e,r)=>t(r)),e=>J(mL([tQ(),e.initial]),uG(t=>new _w(e,t)))),_R=D(2,(e,t)=>_N(e,t,_n)),_N=D(3,(e,t,r)=>_k([e.initial,t.initial],(i,n,s)=>J(cp(e.step(i,n,s[0]),t.step(i,n,s[1]),(e,t)=>[e,t]),uM(([[i,s,a],[o,l,u]])=>_m(a)&&_m(u)?_A(e,t,n,i,s,a.intervals,o,l,u.intervals,r):u5([[i,o],[s,l],_d]))))),_A=(e,t,r,i,n,s,a,o,l,u)=>{let c=u(s,l);return _u(c)?u5([[i,a],[n,o],_p(c)]):J(s,_o(l))?uM(e.step(_l(s),r,i),([i,n,s])=>_g(s)?u5([[i,a],[n,o],_d]):_A(e,t,r,i,n,s.intervals,a,o,l,u)):uM(t.step(_l(l),r,a),([a,o,l])=>_g(l)?u5([[i,a],[n,o],_d]):_A(e,t,r,i,n,s,a,o,l.intervals,u))},_j=D(2,(e,t)=>_M(e,e=>u6(()=>t(e)))),_M=D(2,(e,t)=>_k(e.initial,(r,i,n)=>uM(e.step(r,i,n),([e,r,i])=>uG(t(r),t=>[e,t,i])))),_z=D(2,(e,t)=>_k(e.initial,(r,i,n)=>uM(e.step(r,i,n),([e,i,n])=>{if(_g(n))return u5([e,i,n]);let s=n.intervals;return uG(t(i,S7(S3(r,_a(s)))),t=>{let n=au(t),a=_a(s),o=r+aE(n),l=o-a;return[e,i,_f(S3(o,Math.max(0,_l(s)+l)))]})}))),_P=e=>_k(e.initial,(t,r,i)=>J(e.step(t,r,i),uG(([e,t,i])=>[e,r,i]))),_D=e=>_q(_5,t=>t<e),_$=((e,t,r)=>_k([e.initial,t],(t,i,[n,s])=>uM(e.step(t,i,n),([e,t,i])=>_g(i)?u5([[e,s],s,i]):uG(r(s,t),t=>[[e,t],s,i]))),D(3,(e,t,r)=>_k([e.initial,t.initial],(i,n,s)=>cp(e.step(i,n,s[0]),t.step(i,n,s[1]),([e,t,i],[n,s,a])=>{if(_g(i)&&_g(a))return[[e,n],[t,s],_d];if(_g(i)&&_m(a))return[[e,n],[t,s],_p(a.intervals)];if(_m(i)&&_g(a))return[[e,n],[t,s],_p(i.intervals)];if(_m(i)&&_m(a))return[[e,n],[t,s],_p(r(i.intervals,a.intervals))];throw Error("BUG: Schedule.unionWith - please report an issue at https://github.com/Effect-TS/effect/issues")})))),_L=D(2,(e,t)=>_I(e,(e,r)=>gV(t(e)))),_U=D(2,(e,t)=>_I(e,(e,r)=>t(e))),_q=D(2,(e,t)=>_E(e,(e,r)=>t(r))),_B=((e,t)=>_j(_R(e,t),e=>e[0]),Symbol.for("effect/Schedule/ScheduleDefect"));class _J{constructor(e){this.error=e,this[_B]=_B}}let _H=e=>ea(e,_B),_K=e=>uS(e,e=>uO(new _J(e))),_V=e=>uv(e,e=>tX(sw(e,e=>sn(e)&&_H(e.defect)?tI(e.defect):tQ()),{onNone:()=>uR(e),onSome:e=>uF(e.error)})),_W=D(2,(e,t)=>_Y(e,t,(e,t)=>uF(e))),_G=D(2,(e,t)=>{if(_x(t))return _W(e,t);let r=t.schedule??_P(_5),i=t.while?_U(r,e=>{let r=t.while(e);return"boolean"==typeof r?u5(r):_K(r)}):r,n=t.until?_L(i,e=>{let r=t.until(e);return"boolean"==typeof r?u5(r):_K(r)}):i;return _V(_W(e,t.times?_R(n,_D(t.times)).pipe(_j(e=>e[0])):n))}),_Y=D(3,(e,t,r)=>uM(_T(t),t=>uU(e,{onFailure:e=>r(e,tQ()),onSuccess:i=>_Z(e,t,r,i)}))),_Z=(e,t,r,i)=>uU(t.next(i),{onFailure:()=>u1(t.last),onSuccess:i=>uU(e,{onFailure:e=>r(e,tI(i)),onSuccess:i=>_Z(e,t,r,i)})}),_Q=D(2,(e,t)=>_X(e,t,(e,t)=>uF(e))),_X=((e,t)=>{if(_x(t))return _Q(e,t);let r=t.schedule??_5,i=t.while?_U(r,e=>{let r=t.while(e);return"boolean"==typeof r?u5(r):_K(r)}):r,n=t.until?_L(i,e=>{let r=t.until(e);return"boolean"==typeof r?u5(r):_K(r)}):i;return _V(_Q(e,t.times?_R(n,_D(t.times)):n))},D(3,(e,t,r)=>uM(_T(t),t=>_0(e,t,r)))),_0=(e,t,r)=>uS(e,i=>uU(t.next(i),{onFailure:()=>J(t.last,u1,uM(e=>r(i,e))),onSuccess:()=>_0(e,t,r)})),_1=((e,t)=>_1(e,void 0,t),D(3,(e,t,r)=>uM(_T(r),r=>_2(e,t,r)))),_2=(e,t,r)=>uU(r.next(t),{onFailure:()=>u1(r.last),onSuccess:()=>uM(e,t=>_2(e,t,r))}),_3=_k(tQ(),(e,t,r)=>{switch(r._tag){case"None":return u5([tI(e),ab,_f(S9(e))]);case"Some":return u5([tI(r.value),aS(e-r.value),_f(S9(e))])}}),_5=(S=e=>e+1,_k(0,(e,t,r)=>u6(()=>[S(r),r,_f(S9(e))])));class _4{constructor(e){this.waiters=new Set,this.taken=0,this.take=e=>ux(t=>{if(this.free<e){let r=()=>{!(this.free<e)&&(this.waiters.delete(r),this.taken+=e,t(u5(e)))};return this.waiters.add(r),u6(()=>{this.waiters.delete(r)})}return this.taken+=e,t(u5(e))}),this.updateTaken=e=>up(t=>(this.taken=e(this.taken),this.waiters.size>0&&t.getFiberRef(mY).scheduleTask(()=>{let e=this.waiters.values(),t=e.next();for(;!1===t.done&&this.free>0;)t.value(),t=e.next()},t.getFiberRef(cJ)),u5(this.free))),this.release=e=>this.updateTaken(t=>t-e),this.releaseAll=this.updateTaken(e=>0),this.withPermits=e=>t=>ct(r=>uM(r(this.take(e)),e=>v2(r(t),this.release(e)))),this.withPermitsIfAvailable=e=>t=>ct(r=>u4(()=>this.free<e?gX:(this.taken+=e,v2(r(gO(t)),this.release(e))))),this.permits=e}get free(){return this.permits-this.taken}}let _6=e=>new _4(e);class _8 extends fP{constructor(e){super(),this.waiters=[],this.scheduled=!1,this.flushWaiters=()=>{this.scheduled=!1;let e=this.waiters;this.waiters=[];for(let t=0;t<e.length;t++)e[t](hz)},this.open=up(e=>this.isOpen?cr:(this.isOpen=!0,this.unsafeSchedule(e))),this.release=up(e=>this.isOpen?cr:this.unsafeSchedule(e)),this.await=ux(e=>this.isOpen?e(cr):(this.waiters.push(e),u6(()=>{let t=this.waiters.indexOf(e);-1!==t&&this.waiters.splice(t,1)}))),this.close=u6(()=>{this.isOpen=!1}),this.whenOpen=e=>ch(this.await,e),this.isOpen=e}commit(){return this.await}unsafeSchedule(e){return this.scheduled||0===this.waiters.length||(this.scheduled=!0,e.currentScheduler.scheduleTask(this.flushWaiters,e.getFiberRef(cJ))),cr}unsafeOpen(){this.isOpen||(this.isOpen=!0,this.flushWaiters())}unsafeClose(){this.isOpen=!1}}(e,t)=>{let r=au(t);return uM(hX(),t=>uG(wc(tQ()),i=>[h1(_9(e,r,i),t),we(i)]))};let _7=(e,t,r)=>{let i=aE(au(t));return J(hq(),u8(t=>uW(e,t)),uG(e=>tI([r+i,e])))},_9=(e,t,r)=>ct(i=>J(fI(e=>e.currentTimeMillis),uM(i=>wp(r,r=>{switch(r._tag){case"None":return tI(_7(e,t,i));case"Some":{let[n]=r.value;return n-i<=0?tI(_7(e,t,i)):tQ()}}})),uM(e=>tO(e)?uC("BUG: Effect.cachedInvalidate - please report an issue at https://github.com/Effect-TS/effect/issues"):i(hJ(e.value[1]))))),we=e=>mq(e,tQ()),wt=((e,t)=>uM(y$,r=>J(wn(e,r),v2(uM(r.value,t)))),D(2,(e,t)=>up((r,i)=>{let n=vO(e,r,i.runtimeFlags,bx);if("Open"===t.state._tag){let e={};t.state.finalizers.set(e,()=>uj(e=>eU(e,n.id())?cr:um(cd(n)))),n.addObserver(()=>{"Closed"!==t.state._tag&&t.state.finalizers.delete(e)})}else n.unsafeInterruptAsFork(r.id());return u5(n)}))),wr=e=>vN(t=>wt(e,t)),wi=D(2,(e,t)=>J(uI(e),vX(uI(t)),e=>uD(e))),wn=D(2,(e,t)=>cP(vZ,e=>e.zip(t))(e)),ws=((e,{duration:t,onTimeout:r})=>uD(wa(e,{onTimeout:()=>uT(r),onSuccess:u5,duration:t})),D(2,(e,{duration:t,onTimeout:r})=>uD(wa(e,{onTimeout:()=>uN(r),onSuccess:u5,duration:t})))),wa=D(2,(e,{duration:t,onSuccess:r,onTimeout:i})=>uj(n=>ct(s=>v0(s(e),uV(gd(t)),{onSelfWin:(e,t)=>uM(e.await,i=>"Success"===i._tag?uM(e.inheritAll,()=>ud(cm(t,n),r(i.value))):uM(cm(t,n),()=>hI(i.cause))),onOtherWin:(e,t)=>uM(e.await,r=>"Success"===r._tag?uM(e.inheritAll,()=>ud(cm(t,n),i())):uM(cm(t,n),()=>hI(r.cause))),otherScope:bx})))),wo=Symbol.for("effect/Ref/SynchronizedRef"),wl={_A:e=>e};class wu extends fP{static{o=mz,l=mM}constructor(e,t){super(),this[wo]=wl,this[o]=mP,this[l]=mM,this.ref=e,this.withLock=t,this.get=mU(this.ref)}commit(){return this.get}modify(e){return this.modifyEffect(t=>u5(e(t)))}modifyEffect(e){return this.withLock(J(uM(mU(this.ref),e),uM(([e,t])=>ud(mq(this.ref,t),e))))}}let wc=e=>u6(()=>wh(e)),wh=e=>new wu(m$(e),_6(1).withPermits(1)),wp=D(2,(e,t)=>e.modifyEffect(e=>{let r=t(e);switch(r._tag){case"None":return u5([e,e]);case"Some":return uG(r.value,e=>[e,e])}})),wf=((e,t,r)=>({...tm,commit(){return bO(this)},[by]:bv,id:()=>J(e.id(),a8(t.id())),await:J(e.await,uD,v$(uD(t.await),r,{concurrent:!0}),uI),children:e.children,inheritAll:ch(t.inheritAll,e.inheritAll),poll:cp(e.poll,t.poll,(e,t)=>J(e,t9(e=>J(t,t7(t=>h$(e,t,{onSuccess:r,onFailure:se})))))),interruptAsFork:r=>ch(e.interruptAsFork(r),t.interruptAsFork(r)),pipe(){return e3(this,arguments)}}),Symbol.for("effect/ManagedRuntime")),wd="Fresh",wm="ProvideMerge",wg=e=>e.inheritAll,wb=e=>e.poll,wx=e=>function(){if(1==arguments.length){let t=arguments[0];return(r,...i)=>e(t,r,...i)}return e.apply(this,arguments)},wy=wx((e,t,r)=>{let i=oo(),n=[[cB,[[i,e.context]]]];r?.scheduler&&n.push([mY,[[i,r.scheduler]]]);let s=f5(e.fiberRefs,{entries:n,forkAs:i});r?.updateRefs&&(s=r.updateRefs(s,i));let a=new vi(i,s,e.runtimeFlags),o=t;r?.scope&&(o=uM(ht(r.scope,fU),e=>ch(c7(e,uj(e=>eU(e,a.id())?cr:cm(a,e))),uQ(t,t=>he(e,t)))));let l=a.currentSupervisor;return l!==yU&&(l.onStart(e.context,o,tQ(),a),a.addObserver(e=>l.onEnd(e,a))),bx.add(e.runtimeFlags,a),r?.immediate===!1?a.resume(o):a.start(o),a}),wv=wx((e,t,r={})=>{let i=wy(e,t,r);return r.onExit&&i.addObserver(e=>{r.onExit(e)}),(t,r)=>wv(e)(J(i,cm(t??or)),{...r,onExit:r?.onExit?e=>r.onExit(bs(e)):void 0})}),wS=wx((e,t)=>{let r=wF(e)(t);if("Failure"===r._tag)throw wE(r.effect_instruction_i0);return r.effect_instruction_i0});class w_ extends Error{constructor(e){super(`Fiber #${e.id().id} cannot be resolved synchronously. This is caused by using runSync on an effect that performs async work`),this._tag="AsyncFiberException",this.fiber=e,this.name=this._tag,this.stack=this.message}}let ww=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=0;let r=new w_(e);return Error.stackTraceLimit=t,r},wk=Symbol.for("effect/Runtime/FiberFailure"),wO=Symbol.for("effect/Runtime/FiberFailure/Cause");class wC extends Error{constructor(e){let t=s$(e)[0];super(t?.message||"An error has occurred"),this[wk]=wk,this[wO]=e,this.name=t?`(FiberFailure) ${t.name}`:"FiberFailure",t?.stack&&(this.stack=t.stack)}toJSON(){return{_id:"FiberFailure",cause:this[wO].toJSON()}}toString(){return"(FiberFailure) "+sR(this[wO],{renderErrorCause:!0})}[eH](){return this.toString()}}let wE=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=0;let r=new wC(e);return Error.stackTraceLimit=t,r},wI=e=>{switch(e._op){case"Failure":case"Success":return e;case"Left":return hE(e.left);case"Right":return hM(e.right);case"Some":return hM(e.value);case"None":return hE(hf())}},wF=wx((e,t)=>{let r=wI(t);if(r)return r;let i=new mG,n=wy(e)(t,{scheduler:i});i.flush();let s=n.unsafePoll();return s||hC(uk(ww(n),h5(n)))}),wT=wx((e,t,r)=>wR(e,t,r).then(e=>{switch(e._tag){case te:return e.effect_instruction_i0;case e6:throw wE(e.effect_instruction_i0)}})),wR=wx((e,t,r)=>new Promise(i=>{let n=wI(t);n&&i(n);let s=wy(e)(t);s.addObserver(e=>{i(e)}),r?.signal!==void 0&&(r.signal.aborted?s.unsafeInterruptAsFork(s.id()):r.signal.addEventListener("abort",()=>{s.unsafeInterruptAsFork(s.id())},{once:!0}))}));class wN{constructor(e,t,r){this.context=e,this.runtimeFlags=t,this.fiberRefs=r}pipe(){return e3(this,arguments)}}let wA=e=>new wN(e.context,e.runtimeFlags,e.fiberRefs),wj=()=>up((e,t)=>u5(new wN(e.getFiberRef(cB),t.runtimeFlags,e.getFiberRefs()))),wM=lQ(1,32,4),wz=wA({context:s7(),runtimeFlags:wM,fiberRefs:fK()}),wP=((e,t)=>wA({context:e.context,runtimeFlags:t(e.runtimeFlags),fiberRefs:e.fiberRefs}),wy(wz)),wD=wT(wz),w$=wR(wz),wL=wS(wz),wU=wF(wz),wq=D(2,(e,t)=>e.modifyEffect(t)),wB=Symbol.for("effect/Layer"),wJ={[wB]:{_RIn:e=>e,_E:e=>e,_ROut:e=>e},pipe(){return e3(this,arguments)}},wH=Symbol.for("effect/Layer/MemoMap"),wK=ae()("effect/Layer/CurrentMemoMap",{defaultValue:()=>wZ()}),wV=e=>ea(e,wB),wW=e=>e._op_layer===wd;class wG{constructor(e){this.ref=e,this[wH]=wH}getOrElseMemoize(e,t){return J(wq(this.ref,r=>{let i=r.get(e);if(void 0!==i){let[e,n]=i;return u5([J(e,uM(([e,t])=>J(gG(e),ud(t))),uQ(hA({onFailure:()=>cr,onSuccess:()=>c9(t,n)}))),r])}return J(mL(0),uM(i=>J(hq(),uM(n=>J(mL(()=>cr),uG(s=>{let a=ct(a=>J(vH(),uM(o=>J(a(uM(w0(e,o,!0),e=>gT(e(this)))),uI,uM(a=>{switch(a._tag){case e6:return J(hV(n,a.effect_instruction_i0),ch(he(o,a)),ch(uR(a.effect_instruction_i0)));case te:return J(mq(s,e=>J(he(o,e),cn(mJ(i,e=>[1===e,e-1])),um)),ch(mH(i,e=>e+1)),ch(c9(t,t=>J(u6(()=>r.delete(e)),ch(mU(s)),uM(e=>e(t))))),ch(hG(n,a.effect_instruction_i0)),ud(a.effect_instruction_i0[1]))}}))))),o=[J(hJ(n),uQ(hj({onFailure:()=>cr,onSuccess:()=>mH(i,e=>e+1)}))),e=>J(mU(s),uM(t=>t(e)))];return[a,wW(e)?r:r.set(e,o)]}))))))}),uD)}}let wY=u4(()=>uG(wc(new Map),e=>new wG(e))),wZ=()=>new wG(wh(new Map)),wQ=D(2,(e,t)=>uM(wY,r=>wX(e,r,t))),wX=D(3,(e,t,r)=>uM(w0(e,r),e=>gY(e(t),wK,t))),w0=(e,t,r=!1)=>{switch(e._op_layer){case"Locally":return u6(()=>r=>e.f(r.getOrElseMemoize(e.self,t)));case"ExtendScope":return u6(()=>t=>vN(r=>t.getOrElseMemoize(e.layer,r)));case"Fold":return u6(()=>r=>J(r.getOrElseMemoize(e.layer,t),uL({onFailure:i=>r.getOrElseMemoize(e.failureK(i),t),onSuccess:i=>r.getOrElseMemoize(e.successK(i),t)})));case"Fresh":return u6(()=>r=>J(e.layer,wQ(t)));case"FromEffect":return r?u6(()=>t=>e.effect):u6(()=>r=>r.getOrElseMemoize(e,t));case"Provide":return u6(()=>r=>J(r.getOrElseMemoize(e.first,t),uM(i=>J(r.getOrElseMemoize(e.second,t),h1(i)))));case"Scoped":return r?u6(()=>r=>vK(e.effect,t)):u6(()=>r=>r.getOrElseMemoize(e,t));case"Suspend":return u6(()=>r=>r.getOrElseMemoize(e.evaluate(),t));case"ProvideMerge":return u6(()=>r=>J(r.getOrElseMemoize(e.first,t),cp(r.getOrElseMemoize(e.second,t),e.zipK)));case"ZipWith":return u6(()=>r=>J(r.getOrElseMemoize(e.first,t),v$(r.getOrElseMemoize(e.second,t),e.zipK,{concurrent:!0})))}},w1=D(2,(e,t)=>kr(e,{onFailure:t,onSuccess:kc})),w2=((e,t)=>kt(e,{onFailure:t,onSuccess:kc}),()=>w9(hX())),w3=e=>w5(n8(e)),w5=e=>w9(uR(e)),w4=e=>w9(uN(e)),w6=D(2,(e,t)=>kr(e,{onFailure:w3,onSuccess:t})),w8=e=>{let t=Object.create(wJ);return t._op_layer=wd,t.layer=e,t},w7=D(2,(e,t)=>{let r=s8(e),i=r?e:t;return w9(uG(r?t:e,e=>sQ(i,e)))});function w9(e){let t=Object.create(wJ);return t._op_layer="FromEffect",t.effect=e,t}let ke=D(2,(e,t)=>{let r=Object.create(wJ);return r._op_layer="Locally",r.self=e,r.f=t,r});(e,t)=>w6(e,e=>kc(t(e)));let kt=D(2,(e,{onFailure:t,onSuccess:r})=>{let i=Object.create(wJ);return i._op_layer="Fold",i.layer=e,i.failureK=t,i.successK=r,i}),kr=D(2,(e,{onFailure:t,onSuccess:r})=>kt(e,{onFailure:e=>{let r=sp(e);switch(r._tag){case"Left":return t(r.left);case"Right":return w5(r.right)}},onSuccess:r})),ki=D(2,(e,t)=>km(e,t,(e,t)=>s4(e,t))),kn=(...e)=>{let t=e[0];for(let r=1;r<e.length;r++)t=ki(t,e[r]);return t},ks=(e,t,r,i)=>w7(t,J(fF,uM(t=>J(e.step(t,r,i),uM(([e,i,n])=>_g(n)?uF(r):J(gd(aS(_a(n.intervals)-t)),ud({state:e}))))))),ka=D(2,(e,t)=>{let r=s8(e),i=r?e:t;return kl(uG(r?t:e,e=>sQ(i,e)))}),ko=e=>kl(J(e,ud(s7()))),kl=e=>{let t=Object.create(wJ);return t._op_layer="Scoped",t.effect=e,t},ku=D(2,(e,t)=>{let r=s8(e);return w9(u5(sQ(r?e:t,r?t:e)))}),kc=e=>w9(u5(e)),kh=e=>{let t=Object.create(wJ);return t._op_layer="Suspend",t.evaluate=e,t},kp=D(2,(e,t)=>uM(vN(r=>wX(e,t,r)),e=>J(wj(),h1(e)))),kf=D(2,(e,t)=>kh(()=>{let r=Object.create(wJ);return r._op_layer="Provide",r.first=Object.create(wJ,{_op_layer:{value:wm,enumerable:!0},first:{value:w2(),enumerable:!0},second:{value:Array.isArray(t)?kn(...t):t},zipK:{value:(e,t)=>J(e,s4(t))}}),r.second=e,r})),kd=D(2,(e,t)=>{let r=Object.create(wJ);return r._op_layer=wm,r.first=t,r.second=kf(e,t),r.zipK=(e,t)=>J(e,s4(t)),r}),km=D(3,(e,t,r)=>kh(()=>{let i=Object.create(wJ);return i._op_layer="ZipWith",i.first=e,i.second=t,i.zipK=r,i})),kg=e=>{let t=s6("effect/Layer/unwrapEffect/Layer.Layer<R1, E1, A>");return w6(w7(t,e),e=>s3(e,t))},kb=e=>{let t=s6("effect/Layer/unwrapScoped/Layer.Layer<R1, E1, A>");return w6(ka(t,e),e=>s3(e,t))},kx=D(2,(e,t)=>vA(r=>uM(wQ(t,r),t=>h2(e,t)))),ky=D(2,(e,t)=>{let r=de(wz.fiberRefs,t.fiberRefs),i=l2(wz.runtimeFlags,t.runtimeFlags);return ct(n=>up(s=>{let a=s.getFiberRef(cB),o=s.getFiberRefs(),l=dr(s.id(),o)(r),u=s.currentRuntimeFlags,c=l3(i)(u),h=de(l,o),p=l2(c,u);return s.setFiberRefs(l),s.currentRuntimeFlags=c,v2(h2(n(e),s4(a,t.context)),up(e=>(e.setFiberRefs(dr(e.id(),e.getFiberRefs())(h)),e.currentRuntimeFlags=l3(p)(e.currentRuntimeFlags),cr)))}))}),kv=D(2,(e,t)=>Array.isArray(t)?kx(e,kn(...t)):wV(t)?kx(e,t):sG(t)?h2(e,t):wf in t?uM(t.runtimeEffect,t=>ky(e,t)):ky(e,t)),kS=Symbol.for("effect/MutableList"),k_={[kS]:kS,[Symbol.iterator](){let e=!1,t=this.head;return{next(){if(e)return this.return();if(null==t)return e=!0,this.return();let r=t.value;return t=t.next,{done:e,value:r}},return:t=>(e||(e=!0),{done:!0,value:t})}},toString(){return eV(this.toJSON())},toJSON(){return{_id:"MutableList",values:Array.from(this).map(eK)}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},kw=e=>({value:e,removed:!1,prev:void 0,next:void 0}),kk=()=>{let e=Object.create(k_);return e.head=void 0,e.tail=void 0,e._length=0,e},kO=e=>0===kC(e),kC=e=>e._length,kE=D(2,(e,t)=>{let r=kw(t);return void 0===e.head&&(e.head=r),void 0===e.tail||(e.tail.next=r,r.prev=e.tail),e.tail=r,e._length+=1,e}),kI=e=>{let t=e.head;if(void 0!==t)return kF(e,t),t.value},kF=(e,t)=>{!t.removed&&(t.removed=!0,void 0!==t.prev&&void 0!==t.next?(t.prev.next=t.next,t.next.prev=t.prev):void 0!==t.prev?(e.tail=t.prev,t.prev.next=void 0):void 0!==t.next?(e.head=t.next,t.next.prev=void 0):(e.tail=void 0,e.head=void 0),e._length>0&&(e._length-=1))},kT=Symbol.for("effect/MutableQueue"),kR=Symbol.for("effect/mutable/MutableQueue/Empty"),kN={[kT]:kT,[Symbol.iterator](){return Array.from(this.queue)[Symbol.iterator]()},toString(){return eV(this.toJSON())},toJSON(){return{_id:"MutableQueue",values:Array.from(this).map(eK)}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},kA=e=>{let t=Object.create(kN);return t.queue=kk(),t.capacity=e,t},kj=e=>kA(e),kM=()=>kA(void 0),kz=e=>kC(e.queue),kP=e=>kO(e.queue),kD=e=>void 0===e.capacity?1/0:e.capacity,k$=D(2,(e,t)=>{let r=kC(e.queue);return(void 0===e.capacity||r!==e.capacity)&&(kE(t)(e.queue),!0)}),kL=D(2,(e,t)=>{let r;let i=t[Symbol.iterator](),n=iy(),s=!0;for(;s&&(r=i.next())&&!r.done;)s=k$(r.value)(e);for(;null!=r&&!r.done;)n=iR(r.value)(n),r=i.next();return iO(n)}),kU=D(2,(e,t)=>kO(e.queue)?t:kI(e.queue)),kq=D(2,(e,t)=>{let r=iy(),i=0;for(;i<t;){let t=kU(kR)(e);if(t===kR)break;r=iR(t)(r),i+=1}return iO(r)}),kB=(e,t,r,i)=>oF({_tag:"Complete",key:e,exit:t,entryStats:r,timeToLiveMillis:i}),kJ=(e,t)=>oF({_tag:"Pending",key:e,deferred:t}),kH=(e,t)=>oF({_tag:"Refreshing",deferred:e,complete:t}),kK=Symbol.for("effect/Cache/MapKey");class kV{constructor(e){this[kK]=kK,this.previous=void 0,this.next=void 0,this.current=e}[eI](){return J(eF(this.current),eR(eF(this.previous)),eR(eF(this.next)),e$(this))}[eL](e){return this===e||kG(e)&&eU(this.current,e.current)&&eU(this.previous,e.previous)&&eU(this.next,e.next)}}let kW=e=>new kV(e),kG=e=>ea(e,kK);class kY{add(e){if(e!==this.tail){if(void 0===this.tail)this.head=e,this.tail=e;else{let t=e.previous,r=e.next;void 0!==r&&(e.next=void 0,void 0!==t?(t.next=r,r.previous=t):(this.head=r,this.head.previous=void 0)),this.tail.next=e,e.previous=this.tail,this.tail=e}}}remove(){let e=this.head;if(void 0!==e){let t=e.next;void 0!==t?(e.next=void 0,this.head=t,this.head.previous=void 0):(this.head=void 0,this.tail=void 0)}return e}constructor(){this.head=void 0,this.tail=void 0}}let kZ=()=>new kY,kQ=(e,t,r,i,n,s)=>({map:e,keys:t,accesses:r,updating:i,hits:n,misses:s}),kX=()=>kQ(xC(),kZ(),kM(),aJ(!1),0,0),k0=Symbol.for("effect/Cache"),k1={_Key:e=>e,_Error:e=>e,_Value:e=>e},k2=Symbol.for("effect/ConsumerCache"),k3={_Key:e=>e,_Error:e=>e,_Value:e=>e},k5=e=>e,k4=e=>({loadedMillis:e});class k6{constructor(e,t,r,i,n){this[k0]=k1,this[k2]=k3,this.capacity=e,this.context=t,this.fiberId=r,this.lookup=i,this.timeToLive=n,this.cacheState=kX()}get(e){return uG(this.getEither(e),tB)}get cacheStats(){return u6(()=>k5({hits:this.cacheState.hits,misses:this.cacheState.misses,size:xA(this.cacheState.map)}))}getOption(e){return u4(()=>tX(xE(this.cacheState.map,e),{onNone:()=>{let t=kW(e);return this.trackAccess(t),this.trackMiss(),u5(tQ())},onSome:e=>this.resolveMapValue(e)}))}getOptionComplete(e){return u4(()=>tX(xE(this.cacheState.map,e),{onNone:()=>{let t=kW(e);return this.trackAccess(t),this.trackMiss(),u5(tQ())},onSome:e=>this.resolveMapValue(e,!0)}))}contains(e){return u6(()=>xF(this.cacheState.map,e))}entryStats(e){return u6(()=>{let t=xE(this.cacheState.map,e);if(tC(t))switch(t.value._tag){case"Complete":return tI(k4(t.value.entryStats.loadedMillis));case"Pending":break;case"Refreshing":return tI(k4(t.value.complete.entryStats.loadedMillis))}return tQ()})}getEither(e){return u4(()=>{let t,r;let i=t5(xE(this.cacheState.map,e));return(void 0===i&&(r=hU(this.fiberId),t=kW(e),xF(this.cacheState.map,e)?i=t5(xE(this.cacheState.map,e)):xT(this.cacheState.map,e,kJ(t,r))),void 0===i)?(this.trackAccess(t),this.trackMiss(),uG(this.lookupValueOf(e,r),tP)):uM(this.resolveMapValue(i),tX({onNone:()=>this.getEither(e),onSome:e=>u5(tz(e))}))})}invalidate(e){return u6(()=>{xN(this.cacheState.map,e)})}invalidateWhen(e,t){return u6(()=>{let r=xE(this.cacheState.map,e);tC(r)&&"Complete"===r.value._tag&&"Success"===r.value.exit._tag&&t(r.value.exit.value)&&xN(this.cacheState.map,e)})}get invalidateAll(){return u6(()=>{this.cacheState.map=xC()})}refresh(e){return fI(t=>u4(()=>{let r=hU(this.fiberId),i=t5(xE(this.cacheState.map,e));if(void 0===i&&(xF(this.cacheState.map,e)?i=t5(xE(this.cacheState.map,e)):xT(this.cacheState.map,e,kJ(kW(e),r))),void 0===i)return um(this.lookupValueOf(e,r));switch(i._tag){case"Complete":if(this.hasExpired(t,i.timeToLiveMillis))return eU(t5(xE(this.cacheState.map,e)),i)&&xN(this.cacheState.map,e),um(this.get(e));return J(this.lookupValueOf(e,r),g8(()=>{if(eU(t5(xE(this.cacheState.map,e)),i)){let t=kH(r,i);return xT(this.cacheState.map,e,t),!0}return!1}),um);case"Pending":case"Refreshing":return hJ(i.deferred)}}))}set(e,t){return fI(r=>u6(()=>{let i=r.unsafeCurrentTimeMillis(),n=hM(t),s=kB(kW(e),n,k4(i),i+aE(au(this.timeToLive(n))));xT(this.cacheState.map,e,s)}))}get size(){return u6(()=>xA(this.cacheState.map))}get values(){return u6(()=>{let e=[];for(let t of this.cacheState.map)"Complete"===t[1]._tag&&"Success"===t[1].exit._tag&&e.push(t[1].exit.value);return e})}get entries(){return u6(()=>{let e=[];for(let t of this.cacheState.map)"Complete"===t[1]._tag&&"Success"===t[1].exit._tag&&e.push([t[0],t[1].exit.value]);return e})}get keys(){return u6(()=>{let e=[];for(let t of this.cacheState.map)"Complete"===t[1]._tag&&"Success"===t[1].exit._tag&&e.push(t[0]);return e})}resolveMapValue(e,t=!1){return fI(r=>{switch(e._tag){case"Complete":if(this.trackAccess(e.key),this.hasExpired(r,e.timeToLiveMillis))return xN(this.cacheState.map,e.key.current),u5(tQ());return this.trackHit(),uG(e.exit,tI);case"Pending":if(this.trackAccess(e.key),this.trackHit(),t)return u5(tQ());return uG(hJ(e.deferred),tI);case"Refreshing":if(this.trackAccess(e.complete.key),this.trackHit(),this.hasExpired(r,e.complete.timeToLiveMillis)){if(t)return u5(tQ());return uG(hJ(e.deferred),tI)}return uG(e.complete.exit,tI)}})}trackHit(){this.cacheState.hits=this.cacheState.hits+1}trackMiss(){this.cacheState.misses=this.cacheState.misses+1}trackAccess(e){if(k$(this.cacheState.accesses,e),aH(this.cacheState.updating,!1,!0)){let e=!0;for(;e;){let t=kU(this.cacheState.accesses,kR);t===kR?e=!1:this.cacheState.keys.add(t)}let t=xA(this.cacheState.map);for(e=t>this.capacity;e;){let r=this.cacheState.keys.remove();void 0!==r?xF(this.cacheState.map,r.current)&&(xN(this.cacheState.map,r.current),t-=1,e=t>this.capacity):e=!1}aV(this.cacheState.updating,!1)}}hasExpired(e,t){return e.unsafeCurrentTimeMillis()>t}lookupValueOf(e,t){return fI(r=>u4(()=>J(this.lookup(e),h1(this.context),uI,uM(i=>{let n=r.unsafeCurrentTimeMillis(),s=k4(n),a=kB(kW(e),i,s,n+aE(au(this.timeToLive(i))));return xT(this.cacheState.map,e,a),ch(hK(t,i),i)}),uX(()=>ch(fA(t),u6(()=>{xN(this.cacheState.map,e)}))))))}}let k8=(e,t,r)=>new k6(e,s7(),a3,t,e=>au(r(e))),k7=(()=>cD(k8(65536,()=>uG(hq(),e=>({listeners:new yR,handle:e})),()=>a_(60))),e=>J(hq(),uM(t=>J(gR(e),uW(t),gW,uG(e=>ch(e,J(hJ(t),uM(([e,t])=>ud(cu(gG(e[0]),ci(e[1])),t))))))))),k9=function(){let e=1==arguments.length?arguments[0]:arguments[1].bind(arguments[0]);return ca(()=>e(J))},Oe=e=>e.length>=1?uy((t,r)=>{try{e(r).then(e=>t(hM(e)),e=>t(hC(e)))}catch(e){t(hC(e))}}):uy(t=>{try{e().then(e=>t(hM(e)),e=>t(hC(e)))}catch(e){t(hC(e))}}),Ot=e=>gz(e,{onFailure:B,onSuccess:B}),Or=e=>uL(e,{onFailure:e=>gU(e,"An error was silently ignored because it is not anticipated to be useful"),onSuccess:()=>cr}),Oi=e=>uM(vH(),t=>vV(e,t)),On=(e,...t)=>g$(e)(...t),Os=e=>u6(()=>_6(e)),Oa=e=>new _8(e??!1),Oo=D(2,(e,t)=>v$(e,t,(e,t)=>e(t))),Ol=function(){let e="string"!=typeof arguments[0],t=e?arguments[1]:arguments[0],r=fw(e?arguments[2]:arguments[1]);if(e){let e=arguments[0];return br(t,r,t=>bi(e,t))}return e=>br(t,r,t=>bi(e,t))},Ou=function(e,...t){let r=Error.stackTraceLimit;Error.stackTraceLimit=2;let i=Error();if(Error.stackTraceLimit=r,"string"!=typeof e)return Oc(e.length,function(...r){let n=Error.stackTraceLimit;Error.stackTraceLimit=2;let s=Error();return Error.stackTraceLimit=n,Oh({self:this,body:e,args:r,pipeables:t,spanName:"<anonymous>",spanOptions:{context:fk.context(!0)},errorDef:i,errorCall:s})});let n=t[0];return(t,...r)=>Oc(t.length,function(...s){let a=Error.stackTraceLimit;Error.stackTraceLimit=2;let o=Error();return Error.stackTraceLimit=a,Oh({self:this,body:t,args:s,pipeables:r,spanName:e,spanOptions:n,errorDef:i,errorCall:o})})};function Oc(e,t){return Object.defineProperty(t,"length",{value:e,configurable:!0})}function Oh(e){let t,r;if(eC(e.body))t=ca(()=>e.body.apply(e.self,e.args));else try{t=e.body.apply(e.self,e.args)}catch(e){r=e,t=uO(e)}if(e.pipeables.length>0)try{for(let r of e.pipeables)t=r(t,...e.args)}catch(e){t=r?uR(st(n7(r),n7(e))):uO(e)}let i=!1,n=e.spanOptions&&"captureStackTrace"in e.spanOptions?e.spanOptions:{captureStackTrace:()=>{if(!1!==i)return i;if(e.errorCall.stack){let t=e.errorDef.stack.trim().split("\n"),r=e.errorCall.stack.trim().split("\n"),n=t.slice(2).join("\n").trim();n.includes("(")||(n=n.replace(/at (.*)/,"at ($1)"));let s=r.slice(2).join("\n").trim();return s.includes("(")||(s=s.replace(/at (.*)/,"at ($1)")),i=`${n}
${s}`}},...e.spanOptions};return Ol(t,e.spanName,n)}let Op=Symbol.for("@effect/platform/Error/PlatformErrorTypeId"),Of=e=>t=>oF({[Op]:Op,_tag:e,...t}),Od=Of("BadArgument"),Om=Of("SystemError"),Og=(e,t)=>{class r extends v5{constructor(...e){super(...e),this._tag=t}}return r.prototype[e]=e,r.prototype.name=t,r},Ob=(e,t)=>{switch(t._tag){case"StringKeyword":case"TemplateLiteral":return Object.keys(e);case"SymbolKeyword":return Object.getOwnPropertySymbols(e);case"Refinement":return Ob(e,t.from)}},Ox=e=>Object.keys(e).concat(Object.getOwnPropertySymbols(e)),Oy=e=>{let t,r=!1;return()=>(r||(t=e(),r=!0),t)},Ov=e=>{try{return e.toISOString()}catch(t){return String(e)}},OS=(e,t=!0)=>{if(Array.isArray(e))return`[${e.map(e=>OS(e,t)).join(",")}]`;if(eh(e))return Ov(e);if(ea(e,"toString")&&X(e.toString)&&e.toString!==Object.prototype.toString)return e.toString();if(W(e))return JSON.stringify(e);if(G(e)||null==e||Y(e)||Q(e))return String(e);if(Z(e))return String(e)+"n";if(ep(e))return`${e.constructor.name}(${OS(Array.from(e),t)})`;try{t&&JSON.stringify(e);let r=`{${Ox(e).map(t=>`${W(t)?JSON.stringify(t):String(t)}:${OS(e[t],!1)}`).join(",")}}`,i=e.constructor.name;return e.constructor!==Object.prototype.constructor?`${i}(${r})`:r}catch(e){return"<circular structure>"}},O_=e=>"string"==typeof e?JSON.stringify(e):String(e),Ow=e=>Array.isArray(e),Ok=e=>!Array.isArray(e),OO=e=>`[${O_(e)}]`,OC=e=>Ow(e)?e.map(OO).join(""):OO(e),OE=(e,t,r,i)=>{let n=e;return r&&rN(r)&&(n+=`
at path: ${OC(r)}`),void 0!==t&&(n+=`
details: ${t}`),i&&(n+=`
schema (${i._tag}): ${i}`),n},OI=(e,t,r)=>OE("Unsupported schema or overlapping types",`cannot extend ${e} with ${t}`,r),OF=e=>OE("Unsupported key schema",void 0,void 0,e),OT=e=>OE("Unsupported literal",`literal value: ${OS(e)}`),OR=e=>OE("Duplicate index signature",`${e} index signature`),ON=OE("Unsupported index signature parameter","An index signature parameter type must be `string`, `symbol`, a template literal type or a refinement of the previous types"),OA=OE("Invalid element","A required element cannot follow an optional element. ts(1257)"),Oj=e=>OE("Duplicate property signature transformation",`Duplicate key ${OS(e)}`),OM=e=>OE("Duplicate property signature",`Duplicate key ${OS(e)}`),Oz=Symbol.for("effect/annotation/Brand"),OP=Symbol.for("effect/annotation/SchemaId"),OD=Symbol.for("effect/annotation/Message"),O$=Symbol.for("effect/annotation/MissingMessage"),OL=Symbol.for("effect/annotation/Identifier"),OU=Symbol.for("effect/annotation/Title"),Oq=Symbol.for("effect/annotation/AutoTitle"),OB=Symbol.for("effect/annotation/Description"),OJ=Symbol.for("effect/annotation/Examples"),OH=Symbol.for("effect/annotation/Default"),OK=Symbol.for("effect/annotation/JSONSchema"),OV=Symbol.for("effect/annotation/Arbitrary"),OW=Symbol.for("effect/annotation/Pretty"),OG=Symbol.for("effect/annotation/Equivalence"),OY=Symbol.for("effect/annotation/Documentation"),OZ=Symbol.for("effect/annotation/Concurrency"),OQ=Symbol.for("effect/annotation/Batching"),OX=Symbol.for("effect/annotation/ParseIssueTitle"),O0=Symbol.for("effect/annotation/ParseOptions"),O1=Symbol.for("effect/annotation/DecodingFallback"),O2=Symbol.for("effect/annotation/Surrogate"),O3=Symbol.for("effect/annotation/StableFilter"),O5=D(2,(e,t)=>Object.prototype.hasOwnProperty.call(e.annotations,t)?tI(e.annotations[t]):tQ()),O4=O5(Oz),O6=O5(OD),O8=O5(O$),O7=O5(OU),O9=O5(Oq),Ce=O5(OL),Ct=O5(OB),Cr=O5(OZ),Ci=O5(OQ),Cn=O5(OX),Cs=O5(O0),Ca=O5(O1),Co=O5(O2),Cl=O5(O3),Cu=e=>rs(Cl(e),e=>!0===e),Cc=Symbol.for("effect/annotation/JSONIdentifier"),Ch=O5(Cc),Cp=e=>t1(Ch(e),()=>Ce(e)),Cf=Symbol.for("effect/schema/ParseJson");class Cd{constructor(e,t,r,i={}){this._tag="Declaration",this.typeParameters=e,this.decodeUnknown=t,this.encodeUnknown=r,this.annotations=i}toString(){return t0(EP(this),()=>"<declaration schema>")}toJSON(){return{_tag:this._tag,typeParameters:this.typeParameters.map(e=>e.toJSON()),annotations:EN(this.annotations)}}}let Cm=e=>t=>t._tag===e;class Cg{constructor(e,t={}){this._tag="Literal",this.literal=e,this.annotations=t}toString(){return t0(EP(this),()=>OS(this.literal))}toJSON(){return{_tag:this._tag,literal:Z(this.literal)?String(this.literal):this.literal,annotations:EN(this.annotations)}}}let Cb=Cm("Literal"),Cx=new Cg(null);class Cy{constructor(e,t={}){this._tag="UniqueSymbol",this.symbol=e,this.annotations=t}toString(){return t0(EP(this),()=>OS(this.symbol))}toJSON(){return{_tag:this._tag,symbol:String(this.symbol),annotations:EN(this.annotations)}}}class Cv{constructor(e={}){this._tag="UndefinedKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CS=new Cv({[OU]:"undefined"});class C_{constructor(e={}){this._tag="VoidKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}class Cw{constructor(e={}){this._tag="NeverKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let Ck=new Cw({[OU]:"never"});class CO{constructor(e={}){this._tag="UnknownKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CC=new CO({[OU]:"unknown"});class CE{constructor(e={}){this._tag="AnyKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CI=new CE({[OU]:"any"});class CF{constructor(e={}){this._tag="StringKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CT=new CF({[OU]:"string",[OB]:"a string"}),CR=Cm("StringKeyword");class CN{constructor(e={}){this._tag="NumberKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CA=new CN({[OU]:"number",[OB]:"a number"}),Cj=Cm("NumberKeyword");class CM{constructor(e={}){this._tag="BooleanKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let Cz=new CM({[OU]:"boolean",[OB]:"a boolean"}),CP=Cm("BooleanKeyword");class CD{constructor(e={}){this._tag="BigIntKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let C$=new CD({[OU]:"bigint",[OB]:"a bigint"});class CL{constructor(e={}){this._tag="SymbolKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CU=new CL({[OU]:"symbol",[OB]:"a symbol"}),Cq=Cm("SymbolKeyword");class CB{constructor(e={}){this._tag="ObjectKeyword",this.annotations=e}toString(){return EM(this)}toJSON(){return{_tag:this._tag,annotations:EN(this.annotations)}}}let CJ=e=>{switch(e._tag){case"Literal":case"NumberKeyword":case"StringKeyword":case"TemplateLiteral":return!0;case"Union":return e.types.every(CJ)}return!1},CH=e=>{switch(e._tag){case"Literal":return JSON.stringify(String(e.literal));case"StringKeyword":return"string";case"NumberKeyword":return"number";case"TemplateLiteral":return String(e);case"Union":return e.types.map(CH).join(" | ")}};class CK{constructor(e,t={}){this.type=e,this.annotations=t}toJSON(){return{type:this.type.toJSON(),annotations:EN(this.annotations)}}toString(){return String(this.type)}}class CV extends CK{constructor(e,t,r={}){super(e,r),this.isOptional=t}toJSON(){return{type:this.type.toJSON(),isOptional:this.isOptional,annotations:EN(this.annotations)}}toString(){return String(this.type)+(this.isOptional?"?":"")}}let CW=e=>e.map(e=>e.type);class CG{constructor(e,t,r,i={}){this._tag="TupleType",this.elements=e,this.rest=t,this.isReadonly=r,this.annotations=i;let n=!1,s=!1;for(let t of e)if(t.isOptional)n=!0;else if(n){s=!0;break}if(s||n&&t.length>1)throw Error(OA)}toString(){return t0(EP(this),()=>CY(this))}toJSON(){return{_tag:this._tag,elements:this.elements.map(e=>e.toJSON()),rest:this.rest.map(e=>e.toJSON()),isReadonly:this.isReadonly,annotations:EN(this.annotations)}}}let CY=e=>{let t=e.elements.map(String).join(", ");return rC(e.rest,{onEmpty:()=>`readonly [${t}]`,onNonEmpty:(r,i)=>{let n=String(r),s=n.includes(" | ")?`(${n})`:n;if(i.length>0){let r=i.map(String).join(", ");return e.elements.length>0?`readonly [${t}, ...${s}[], ${r}]`:`readonly [...${s}[], ${r}]`}return e.elements.length>0?`readonly [${t}, ...${s}[]]`:`ReadonlyArray<${n}>`}})};class CZ extends CV{constructor(e,t,r,i,n){super(t,r,n),this.name=e,this.isReadonly=i}toString(){return(this.isReadonly?"readonly ":"")+String(this.name)+(this.isOptional?"?":"")+": "+this.type}toJSON(){return{name:String(this.name),type:this.type.toJSON(),isOptional:this.isOptional,isReadonly:this.isReadonly,annotations:EN(this.annotations)}}}let CQ=e=>{switch(e._tag){case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":return!0;case"Refinement":return CQ(e.from)}return!1};class CX{constructor(e,t,r){if(this.type=t,this.isReadonly=r,CQ(e))this.parameter=e;else throw Error(ON)}toString(){return(this.isReadonly?"readonly ":"")+`[x: ${this.parameter}]: ${this.type}`}toJSON(){return{parameter:this.parameter.toJSON(),type:this.type.toJSON(),isReadonly:this.isReadonly}}}class C0{constructor(e,t,r={}){this._tag="TypeLiteral",this.annotations=r;let i={};for(let t=0;t<e.length;t++){let r=e[t].name;if(Object.prototype.hasOwnProperty.call(i,r))throw Error(OM(r));i[r]=null}let n={string:!1,symbol:!1};for(let e=0;e<t.length;e++){let r=EA(t[e].parameter);if(CR(r)){if(n.string)throw Error(OR("string"));n.string=!0}else if(Cq(r)){if(n.symbol)throw Error(OR("symbol"));n.symbol=!0}}this.propertySignatures=e,this.indexSignatures=t}toString(){return t0(EP(this),()=>C2(this))}toJSON(){return{_tag:this._tag,propertySignatures:this.propertySignatures.map(e=>e.toJSON()),indexSignatures:this.indexSignatures.map(e=>e.toJSON()),annotations:EN(this.annotations)}}}let C1=e=>e.map(String).join("; "),C2=e=>{if(e.propertySignatures.length>0){let t=e.propertySignatures.map(String).join("; ");return e.indexSignatures.length>0?`{ ${t}; ${C1(e.indexSignatures)} }`:`{ ${t} }`}return e.indexSignatures.length>0?`{ ${C1(e.indexSignatures)} }`:"{}"},C3=Cm("TypeLiteral"),C5=rK(rh(rc,e=>{switch(e._tag){case"AnyKeyword":return 0;case"UnknownKeyword":return 1;case"ObjectKeyword":return 2;case"StringKeyword":case"NumberKeyword":case"BooleanKeyword":case"BigIntKeyword":case"SymbolKeyword":return 3}return 4})),C4={string:"StringKeyword",number:"NumberKeyword",boolean:"BooleanKeyword",bigint:"BigIntKeyword"},C6=e=>r5(e,e=>Et(e)?C6(e.types):[e]),C8=e=>{let t=C5(e),r=[],i={},n=[];for(let e of t)switch(e._tag){case"NeverKeyword":break;case"AnyKeyword":return[CI];case"UnknownKeyword":return[CC];case"ObjectKeyword":case"UndefinedKeyword":case"VoidKeyword":case"StringKeyword":case"NumberKeyword":case"BooleanKeyword":case"BigIntKeyword":case"SymbolKeyword":i[e._tag]||(i[e._tag]=e,r.push(e));break;case"Literal":{let t=typeof e.literal;switch(t){case"string":case"number":case"bigint":case"boolean":i[C4[t]]||n.includes(e.literal)||(n.push(e.literal),r.push(e));break;case"object":n.includes(e.literal)||(n.push(e.literal),r.push(e))}break}case"UniqueSymbol":i.SymbolKeyword||n.includes(e.symbol)||(n.push(e.symbol),r.push(e));break;case"TupleType":i.ObjectKeyword||r.push(e);break;case"TypeLiteral":0===e.propertySignatures.length&&0===e.indexSignatures.length?i["{}"]||(i["{}"]=e,r.push(e)):i.ObjectKeyword||r.push(e);break;default:r.push(e)}return r};class C7{static{this.make=(e,t)=>Ee(e)?new C7(e,t):1===e.length?e[0]:Ck}static{this.unify=(e,t)=>C7.make(C8(C6(e)),t)}constructor(e,t={}){this._tag="Union",this.types=e,this.annotations=t}toString(){return t0(EP(this),()=>this.types.map(String).join(" | "))}toJSON(){return{_tag:this._tag,types:this.types.map(e=>e.toJSON()),annotations:EN(this.annotations)}}}let C9=(e,t)=>e.map(t),Ee=e=>e.length>1,Et=Cm("Union"),Er=V(Symbol.for("effect/Schema/AST/toJSONMemoMap"),()=>new WeakMap);class Ei{constructor(e,t={}){this._tag="Suspend",this.f=e,this.annotations=t,this.f=Oy(e)}toString(){return EP(this).pipe(t1(()=>t9(t4(this.f)(),e=>EP(e))),t0(()=>"<suspended schema>"))}toJSON(){let e=this.f(),t=Er.get(e);return t||(Er.set(e,{_tag:this._tag}),t={_tag:this._tag,ast:e.toJSON(),annotations:EN(this.annotations)},Er.set(e,t)),t}}class En{constructor(e,t,r={}){this._tag="Refinement",this.from=e,this.filter=t,this.annotations=r}toString(){return Ce(this).pipe(t0(()=>tX(Ez(this),{onNone:()=>`{ ${this.from} | filter }`,onSome:e=>Es(this.from)?String(this.from)+" & "+e:e})))}toJSON(){return{_tag:this._tag,from:this.from.toJSON(),annotations:EN(this.annotations)}}}let Es=Cm("Refinement"),Ea={};class Eo{constructor(e,t,r,i={}){this._tag="Transformation",this.from=e,this.to=t,this.transformation=r,this.annotations=i}toString(){return t0(EP(this),()=>`(${String(this.from)} <-> ${String(this.to)})`)}toJSON(){return{_tag:this._tag,from:this.from.toJSON(),to:this.to.toJSON(),annotations:EN(this.annotations)}}}let El=Cm("Transformation");class Eu{constructor(e,t){this._tag="FinalTransformation",this.decode=e,this.encode=t}}class Ec{constructor(){this._tag="ComposeTransformation"}}let Eh=new Ec;class Ep{constructor(e,t,r,i){this.from=e,this.to=t,this.decode=r,this.encode=i}}class Ef{constructor(e){this._tag="TypeLiteralTransformation",this.propertySignatureTransformations=e;let t={},r={};for(let i of e){let e=i.from;if(t[e])throw Error(Oj(e));t[e]=!0;let n=i.to;if(r[n])throw Error(Oj(n));r[n]=!0}}}let Ed=e=>"TypeLiteralTransformation"===e._tag,Em=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),i={...e.annotations,...t},n=Co(e);return tC(n)&&(i[O2]=Em(n.value,t)),r.annotations.value=i,Object.create(Object.getPrototypeOf(e),r)},Eg=(e,t)=>{switch(e._tag){case"Literal":return pa(String(e.literal));case"StringKeyword":return"[\\s\\S]*";case"NumberKeyword":return"[+-]?\\d*\\.?\\d+(?:[Ee][+-]?\\d+)?";case"TemplateLiteral":return Ex(e,t,!1);case"Union":return e.types.map(e=>Eg(e,t)).join("|")}},Eb=(e,t,r,i)=>{if(Et(e)){if(r&&!i)return`(?:${t})`}else if(!r||!i)return t;return`(${t})`},Ex=(e,t,r)=>{let i="";if(""!==e.head){let n=pa(e.head);i+=t&&r?`(${n})`:n}for(let n of e.spans){let e=Eg(n.type,t);if(i+=Eb(n.type,e,t,r),""!==n.literal){let e=pa(n.literal);i+=t&&r?`(${e})`:e}}return i},Ey=e=>RegExp(`^${Ex(e,!1,!0)}$`),Ev=e=>{switch(e._tag){case"TupleType":{let t=!1,r=[];for(let i of e.elements)i.isOptional&&(t=!0),r.push(i.type);return t&&r.push(CS),r=r.concat(CW(e.rest)),C7.make(r)}case"Refinement":return Ev(e.from);case"Union":return C7.make(e.types.map(Ev));case"Suspend":return Ev(e.f())}throw Error(errors_.getASTUnsupportedSchemaErrorMessage(e))},ES=(e,t)=>{let r=Arr.findFirst(e.propertySignatures,e=>e.name===t);if(Option.isSome(r))return r.value;if(Predicate.isString(t)){let r;for(let i of e.indexSignatures){let e=EA(i.parameter);switch(e._tag){case"TemplateLiteral":if(Ey(e).test(t))return new CZ(t,i.type,!1,!0);break;case"StringKeyword":void 0===r&&(r=new CZ(t,i.type,!1,!0))}}if(r)return r}else if(Predicate.isSymbol(t)){for(let r of e.indexSignatures)if(Cq(EA(r.parameter)))return new CZ(t,r.type,!1,!0)}},E_=(e,t)=>{let r=[],i=[],n=e=>{switch(e._tag){case"NeverKeyword":break;case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":case"Refinement":i.push(new CX(e,t,!0));break;case"Literal":if(W(e.literal)||G(e.literal))r.push(new CZ(e.literal,t,!1,!0));else throw Error(OT(e.literal));break;case"Enums":for(let[i,n]of e.enums)r.push(new CZ(n,t,!1,!0));break;case"UniqueSymbol":r.push(new CZ(e.symbol,t,!1,!0));break;case"Union":e.types.forEach(n);break;default:throw Error(OF(e))}};return n(e),{propertySignatures:r,indexSignatures:i}},Ew=e=>{switch(e._tag){case"TupleType":return!1===e.isReadonly?e:new CG(e.elements,e.rest,!1,e.annotations);case"TypeLiteral":{let t=EI(e.propertySignatures,e=>!1===e.isReadonly?e:new CZ(e.name,e.type,e.isOptional,!1,e.annotations)),r=EI(e.indexSignatures,e=>!1===e.isReadonly?e:new CX(e.parameter,e.type,!1));return t===e.propertySignatures&&r===e.indexSignatures?e:new C0(t,r,e.annotations)}case"Union":{let t=EI(e.types,Ew);return t===e.types?e:C7.make(t,e.annotations)}case"Suspend":return new Ei(()=>Ew(e.f()),e.annotations);case"Refinement":{let t=Ew(e.from);return t===e.from?e:new En(t,e.filter,e.annotations)}case"Transformation":{let t=Ew(e.from),r=Ew(e.to);return t===e.from&&r===e.to?e:new Eo(t,r,e.transformation,e.annotations)}}return e},Ek=e=>t=>{let r;for(let i of e)Object.prototype.hasOwnProperty.call(t.annotations,i)&&(void 0===r&&(r={}),r[i]=t.annotations[i]);return r},EO=Ek([OJ,OH,OK,OV,OW,OG]),EC=e=>{switch(e._tag){case"Declaration":{let t=EI(e.typeParameters,EC);return t===e.typeParameters?e:new Cd(t,e.decodeUnknown,e.encodeUnknown,e.annotations)}case"TupleType":{let t=EI(e.elements,e=>{let t=EC(e.type);return t===e.type?e:new CV(t,e.isOptional)}),r=CW(e.rest),i=EI(r,EC);return t===e.elements&&i===r?e:new CG(t,i.map(e=>new CK(e)),e.isReadonly,e.annotations)}case"TypeLiteral":{let t=EI(e.propertySignatures,e=>{let t=EC(e.type);return t===e.type?e:new CZ(e.name,t,e.isOptional,e.isReadonly)}),r=EI(e.indexSignatures,e=>{let t=EC(e.type);return t===e.type?e:new CX(e.parameter,t,e.isReadonly)});return t===e.propertySignatures&&r===e.indexSignatures?e:new C0(t,r,e.annotations)}case"Union":{let t=EI(e.types,EC);return t===e.types?e:C7.make(t,e.annotations)}case"Suspend":return new Ei(()=>EC(e.f()),e.annotations);case"Refinement":{let t=EC(e.from);return t===e.from?e:new En(t,e.filter,e.annotations)}case"Transformation":{let t=EO(e);return EC(void 0!==t?Em(e.to,t):e.to)}}return e},EE=e=>tX(Cp(e),{onNone:()=>void 0,onSome:e=>({[Cc]:e})});function EI(e,t){let r=!1,i=rS(e.length);for(let n=0;n<e.length;n++){let s=e[n],a=t(s);a!==s&&(r=!0),i[n]=a}return r?i:e}let EF=e=>{switch(e._tag){case"Transformation":return e.from;case"Refinement":return EF(e.from);case"Suspend":return EF(e.f())}},ET=(e,t)=>{switch(e._tag){case"Declaration":{let r=EI(e.typeParameters,e=>ET(e,t));return r===e.typeParameters?e:new Cd(r,e.decodeUnknown,e.encodeUnknown,e.annotations)}case"TupleType":{let r=EI(e.elements,e=>{let r=ET(e.type,t);return r===e.type?e:new CV(r,e.isOptional)}),i=CW(e.rest),n=EI(i,e=>ET(e,t));return r===e.elements&&n===i?e:new CG(r,n.map(e=>new CK(e)),e.isReadonly,EE(e))}case"TypeLiteral":{let r=EI(e.propertySignatures,e=>{let r=ET(e.type,t);return r===e.type?e:new CZ(e.name,r,e.isOptional,e.isReadonly)}),i=EI(e.indexSignatures,e=>{let r=ET(e.type,t);return r===e.type?e:new CX(e.parameter,r,e.isReadonly)});return r===e.propertySignatures&&i===e.indexSignatures?e:new C0(r,i,EE(e))}case"Union":{let r=EI(e.types,e=>ET(e,t));return r===e.types?e:C7.make(r,EE(e))}case"Suspend":return new Ei(()=>ET(e.f(),t),EE(e));case"Refinement":{let r=ET(e.from,t);if(t){if(r===e.from)return e;if(void 0===EF(e.from)&&Cu(e))return new En(r,e.filter,e.annotations)}let i=EE(e);return i?Em(r,i):r}case"Transformation":{let r=EE(e);return ET(r?Em(e.from,r):e.from,t)}}return e},ER=e=>ET(e,!1),EN=e=>{let t={};for(let r of Object.getOwnPropertySymbols(e))t[String(r)]=e[r];return t},EA=e=>{switch(e._tag){case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":return e;case"Refinement":return EA(e.from)}},Ej=(e,t)=>new Eo(e,t,Eh),EM=e=>t0(EP(e),()=>e._tag),Ez=e=>O7(e).pipe(t1(()=>Ct(e)),t1(()=>O9(e)),t7(t=>t+tX(O4(e),{onNone:()=>"",onSome:e=>e.map(e=>` & Brand<${OS(e)}>`).join("")}))),EP=e=>t1(Ce(e),()=>Ez(e));class ED{constructor(e,t,r){this._tag="Pointer",this.path=e,this.actual=t,this.issue=r}}class E${constructor(e,t){this._tag="Unexpected",this.actual=e,this.message=t}}class EL{constructor(e,t){this._tag="Missing",this.actual=void 0,this.ast=e,this.message=t}}class EU{constructor(e,t,r,i){this._tag="Composite",this.ast=e,this.actual=t,this.issues=r,this.output=i}}class Eq{constructor(e,t,r,i){this._tag="Refinement",this.ast=e,this.actual=t,this.kind=r,this.issue=i}}class EB{constructor(e,t,r,i){this._tag="Transformation",this.ast=e,this.actual=t,this.kind=r,this.issue=i}}class EJ{constructor(e,t,r){this._tag="Type",this.ast=e,this.actual=t,this.message=r}}class EH{constructor(e,t,r){this._tag="Forbidden",this.ast=e,this.actual=t,this.message=r}}let EK=Symbol.for("effect/Schema/ParseErrorTypeId"),EV=e=>ea(e,EK);class EW extends v4("ParseError"){get message(){return this.toString()}toString(){return Ix.formatIssueSync(this.issue)}toJSON(){return{_id:"ParseError",message:this.toString()}}[eH](){return this.toJSON()}constructor(...e){super(...e),this[EK]=EK}}let EG=e=>new EW({issue:e}),EY=tP,EZ=e=>{if(X(e))try{return tP(e())}catch(e){return tz(e)}else try{return tP(e.try())}catch(t){return tz(e.catch(t))}},EQ=tD,EX=D(2,(e,t)=>tA(e)?tq(e,{onLeft:tz,onRight:t}):uM(e,t)),E0=D(2,(e,t)=>tA(e)?tU(e,t):uG(e,t)),E1=D(2,(e,t)=>tA(e)?tL(e,t):uZ(e,t)),E2=D(2,(e,t)=>tA(e)?t$(e,{onLeft:t.onFailure,onRight:t.onSuccess}):uY(e,t)),E3=D(2,(e,t)=>tA(e)?tq(e,{onLeft:t,onRight:tP}):uS(e,t)),E5=(e,t)=>void 0===t||G(t)?e:void 0===e?t:{...e,...t},E4=(e,t,r)=>{let i=Is(e,t);return(e,t)=>i(e,E5(r,t))},E6=(e,t,r)=>{let i=E4(e,t,r);return(e,t)=>tJ(i(e,t),EG)},E8=(e,t,r)=>{let i=Is(e,t);return(e,t)=>i(e,{...E5(r,t),isEffectAllowed:!0})},E7=(e,t)=>E4(e.ast,!0,t),E9=(e,t)=>E8(e.ast,!0,t),Ie=(e,t)=>E8(e.ast,!1,t),It=(e,t)=>E6(EC(e.ast),!0,t),Ir=(e,t)=>{let r=Is(EC(e.ast),!0);return(e,i)=>tM(r(e,{exact:!0,...E5(t,i)}))},Ii=V(Symbol.for("effect/ParseResult/decodeMemoMap"),()=>new WeakMap),In=V(Symbol.for("effect/ParseResult/encodeMemoMap"),()=>new WeakMap),Is=(e,t)=>{let r=t?Ii:In,i=r.get(e);if(i)return i;let n=Il(e,t),s=Cs(e),a=tC(s)?(e,t)=>n(e,E5(t,s.value)):n,o=Ca(e),l=t&&tC(o)?(t,r)=>If(E3(a(t,r),o.value),e,t,r):a;return r.set(e,l),l},Ia=e=>t5(Cr(e)),Io=e=>t5(Ci(e)),Il=(e,t)=>{switch(e._tag){case"Refinement":if(t){let t=Is(e.from,!0);return(r,i)=>{i=i??Ea;let n=i?.errors==="all";return If(EX(E3(t(r,i),t=>{let s=new Eq(e,r,"From",t);return n&&Cu(e)&&IE(t)?tX(e.filter(r,i,e),{onNone:()=>tz(s),onSome:t=>tz(new EU(e,r,[s,new Eq(e,r,"Predicate",t)]))}):tz(s)}),t=>tX(e.filter(t,i,e),{onNone:()=>tP(t),onSome:t=>tz(new Eq(e,r,"Predicate",t))})),e,r,i)}}{let t=Is(EC(e),!0),r=Is(Ip(e.from),!1);return(i,n)=>If(EX(t(i,n),e=>r(e,n)),e,i,n)}case"Transformation":{let r=Ig(e.transformation,t),i=t?Is(e.from,!0):Is(e.to,!1),n=t?Is(e.to,!0):Is(e.from,!1);return(s,a)=>If(EX(E1(i(s,a),r=>new EB(e,s,t?"Encoded":"Type",r)),i=>EX(E1(r(i,a??Ea,e,s),t=>new EB(e,s,"Transformation",t)),r=>E1(n(r,a),r=>new EB(e,s,t?"Type":"Encoded",r)))),e,s,a)}case"Declaration":{let r=t?e.decodeUnknown(...e.typeParameters):e.encodeUnknown(...e.typeParameters);return(t,i)=>If(r(t,i??Ea,e),e,t,i)}case"Literal":return Iu(e,t=>t===e.literal);case"UniqueSymbol":return Iu(e,t=>t===e.symbol);case"UndefinedKeyword":return Iu(e,ee);case"NeverKeyword":return Iu(e,ei);case"UnknownKeyword":case"AnyKeyword":case"VoidKeyword":return tP;case"StringKeyword":return Iu(e,W);case"NumberKeyword":return Iu(e,G);case"BooleanKeyword":return Iu(e,Y);case"BigIntKeyword":return Iu(e,Z);case"SymbolKeyword":return Iu(e,Q);case"ObjectKeyword":return Iu(e,es);case"Enums":return Iu(e,t=>e.enums.some(([e,r])=>r===t));case"TemplateLiteral":{let t=Ey(e);return Iu(e,e=>W(e)&&t.test(e))}case"TupleType":{let r=e.elements.map(e=>Is(e.type,t)),i=e.rest.map(e=>Is(e.type,t)),n=e.elements.filter(e=>!e.isOptional);e.rest.length>0&&(n=n.concat(e.rest.slice(1)));let s=n.length,a=e.elements.length>0?e.elements.map((e,t)=>t).join(" | "):"never",o=Ia(e),l=Io(e);return(t,u)=>{let c;if(!rT(t))return tz(new EJ(e,t));let h=u?.errors==="all",p=[],f=0,d=[],m=t.length;for(let r=m;r<=s-1;r++){let i=new ED(r,t,new EL(n[r-m]));if(!h)return tz(new EU(e,t,i,d));p.push([f++,i])}if(0===e.rest.length)for(let r=e.elements.length;r<=m-1;r++){let i=new ED(r,t,new E$(t[r],`is unexpected, expected: ${a}`));if(!h)return tz(new EU(e,t,i,d));p.push([f++,i])}let g=0;for(;g<r.length;g++)if(m<g+1){if(e.elements[g].isOptional)continue}else{let i=(0,r[g])(t[g],u);if(tA(i)){if(tj(i)){let r=new ED(g,t,i.left);if(!h)return tz(new EU(e,t,r,Im(d)));p.push([f++,r]);continue}d.push([f++,i.right])}else{let r=f++,n=g;c||(c=[]),c.push(({es:s,output:a})=>uM(uE(i),i=>{if(tj(i)){let o=new ED(n,t,i.left);return h?(s.push([r,o]),cr):tz(new EU(e,t,o,Im(a)))}return a.push([r,i.right]),cr}))}}if(rN(i)){let[r,...n]=i;for(;g<m-n.length;g++){let i=r(t[g],u);if(tA(i)){if(tj(i)){let r=new ED(g,t,i.left);if(!h)return tz(new EU(e,t,r,Im(d)));p.push([f++,r]);continue}d.push([f++,i.right])}else{let r=f++,n=g;c||(c=[]),c.push(({es:s,output:a})=>uM(uE(i),i=>{if(!tj(i))return a.push([r,i.right]),cr;{let o=new ED(n,t,i.left);return h?(s.push([r,o]),cr):tz(new EU(e,t,o,Im(a)))}}))}}for(let r=0;r<n.length;r++)if(!(m<(g+=r)+1)){let i=n[r](t[g],u);if(tA(i)){if(tj(i)){let r=new ED(g,t,i.left);if(!h)return tz(new EU(e,t,r,Im(d)));p.push([f++,r]);continue}d.push([f++,i.right])}else{let r=f++,n=g;c||(c=[]),c.push(({es:s,output:a})=>uM(uE(i),i=>{if(tj(i)){let o=new ED(n,t,i.left);return h?(s.push([r,o]),cr):tz(new EU(e,t,o,Im(a)))}return a.push([r,i.right]),cr}))}}}let b=({es:r,output:i})=>tZ(r)?tz(new EU(e,t,Im(r),Im(i))):tP(Im(i));if(c&&c.length>0){let e=c;return u4(()=>{let t={es:rQ(p),output:rQ(d)};return uM(vv(e,e=>e(t),{concurrency:o,batching:l,discard:!0}),()=>b(t))})}return b({output:d,es:p})}}case"TypeLiteral":{if(0===e.propertySignatures.length&&0===e.indexSignatures.length)return Iu(e,eu);let r=[],i={},n=[];for(let s of e.propertySignatures)r.push([Is(s.type,t),s]),i[s.name]=null,n.push(s.name);let s=e.indexSignatures.map(e=>[Is(e.parameter,t),Is(e.type,t),e.parameter]),a=C7.make(e.indexSignatures.map(e=>e.parameter).concat(n.map(e=>Q(e)?new Cy(e):new Cg(e)))),o=Is(a,t),l=Ia(e),u=Io(e);return(t,c)=>{let h,p;if(!ef(t))return tz(new EJ(e,t));let f=c?.errors==="all",d=[],m=0,g=c?.onExcessProperty==="error",b=c?.onExcessProperty==="preserve",x={};if(g||b)for(let r of h=Ox(t)){let i=o(r,c);if(tA(i)&&tj(i)){if(g){let i=new ED(r,t,new E$(t[r],`is unexpected, expected: ${String(a)}`));if(!f)return tz(new EU(e,t,i,x));d.push([m++,i]);continue}x[r]=t[r]}}let y=c?.exact===!0;for(let i=0;i<r.length;i++){let n=r[i][1],s=n.name,a=Object.prototype.hasOwnProperty.call(t,s);if(!a){if(n.isOptional)continue;if(y){let r=new ED(s,t,new EL(n));if(!f)return tz(new EU(e,t,r,x));d.push([m++,r]);continue}}let o=(0,r[i][0])(t[s],c);if(tA(o)){if(tj(o)){let r=new ED(s,t,a?o.left:new EL(n));if(!f)return tz(new EU(e,t,r,x));d.push([m++,r]);continue}x[s]=o.right}else{let r=m++;p||(p=[]),p.push(({es:i,output:l})=>uM(uE(o),o=>{if(tj(o)){let u=new ED(s,t,a?o.left:new EL(n));return f?(i.push([r,u]),cr):tz(new EU(e,t,u,l))}return l[s]=o.right,cr}))}}for(let r=0;r<s.length;r++){let n=s[r],a=n[0],o=n[1];for(let r of Ob(t,n[2])){let n=a(r,c);if(tA(n)&&tM(n)){let n=o(t[r],c);if(tA(n)){if(tj(n)){let i=new ED(r,t,n.left);if(!f)return tz(new EU(e,t,i,x));d.push([m++,i]);continue}Object.prototype.hasOwnProperty.call(i,r)||(x[r]=n.right)}else{let s=m++,a=r;p||(p=[]),p.push(({es:o,output:l})=>uM(uE(n),n=>{if(!tj(n))return Object.prototype.hasOwnProperty.call(i,r)||(l[r]=n.right),cr;{let r=new ED(a,t,n.left);return f?(o.push([s,r]),cr):tz(new EU(e,t,r,l))}}))}}}}let v=({es:r,output:i})=>{if(tZ(r))return tz(new EU(e,t,Im(r),i));if(c?.propertyOrder==="original"){let e=h||Ox(t);for(let t of n)-1===e.indexOf(t)&&e.push(t);let r={};for(let t of e)Object.prototype.hasOwnProperty.call(i,t)&&(r[t]=i[t]);return tP(r)}return tP(i)};if(p&&p.length>0){let e=p;return u4(()=>{let t={es:rQ(d),output:Object.assign({},x)};return uM(vv(e,e=>e(t),{concurrency:l,batching:u,discard:!0}),()=>v(t))})}return v({es:d,output:x})}}case"Union":{let r=Ih(e.types,t),i=Ox(r.keys),n=i.length,s=e.types.length,a=new Map;for(let r=0;r<s;r++)a.set(e.types[r],Is(e.types[r],t));let o=Ia(e)??1,l=Io(e);return(t,u)=>{let c;let h=[],p=0,f=[];if(n>0){if(en(t))for(let e=0;e<n;e++){let n=i[e],a=r.keys[n].buckets;if(Object.prototype.hasOwnProperty.call(t,n)){let e=String(t[n]);if(Object.prototype.hasOwnProperty.call(a,e))f=f.concat(a[e]);else{let{candidates:e,literals:i}=r.keys[n],a=C7.make(i),o=e.length===s?new C0([new CZ(n,a,!1,!0)],[]):C7.make(e);h.push([p++,new EU(o,t,new ED(n,t,new EJ(a,t[n])))])}}else{let{candidates:e,literals:i}=r.keys[n],a=new CZ(n,C7.make(i),!1,!0),o=e.length===s?new C0([a],[]):C7.make(e);h.push([p++,new EU(o,t,new ED(n,t,new EL(a)))])}}else{let i=r.candidates.length===s?e:C7.make(r.candidates);h.push([p++,new EJ(i,t)])}}r.otherwise.length>0&&(f=f.concat(r.otherwise));for(let e=0;e<f.length;e++){let r=f[e],i=a.get(r)(t,u);if(tA(i)&&(!c||0===c.length)){if(tM(i))return i;h.push([p++,i.left])}else{let e=p++;c||(c=[]),c.push(t=>u4(()=>"finalResult"in t?cr:uM(uE(i),r=>(tM(r)?t.finalResult=r:t.es.push([e,r.left]),cr))))}}let d=r=>tZ(r)?1===r.length&&"Type"===r[0][1]._tag?tz(r[0][1]):tz(new EU(e,t,Im(r))):tz(new EJ(e,t));if(c&&c.length>0){let e=c;return u4(()=>{let t={es:rQ(h)};return uM(vv(e,e=>e(t),{concurrency:o,batching:l,discard:!0}),()=>"finalResult"in t?t.finalResult:d(t.es))})}return d(h)}}case"Suspend":{let r=Oy(()=>Is(Em(e.f(),e.annotations),t));return(e,t)=>r()(e,t)}}},Iu=(e,t)=>r=>t(r)?tP(r):tz(new EJ(e,r)),Ic=(e,t)=>{switch(e._tag){case"Declaration":{let r=Co(e);if(tC(r))return Ic(r.value,t);break}case"TypeLiteral":{let r=[];for(let i=0;i<e.propertySignatures.length;i++){let n=e.propertySignatures[i],s=t?ER(n.type):EC(n.type);Cb(s)&&!n.isOptional&&r.push([n.name,s])}return r}case"TupleType":{let r=[];for(let i=0;i<e.elements.length;i++){let n=e.elements[i],s=t?ER(n.type):EC(n.type);Cb(s)&&!n.isOptional&&r.push([i,s])}return r}case"Refinement":return Ic(e.from,t);case"Suspend":return Ic(e.f(),t);case"Transformation":return Ic(t?e.from:e.to,t)}return[]},Ih=(e,t)=>{let r={},i=[],n=[];for(let s=0;s<e.length;s++){let a=e[s],o=Ic(a,t);if(o.length>0){n.push(a);for(let e=0;e<o.length;e++){let[t,i]=o[e],n=String(i.literal);r[t]=r[t]||{buckets:{},literals:[],candidates:[]};let s=r[t].buckets;if(Object.prototype.hasOwnProperty.call(s,n)){if(e<o.length-1)continue;s[n].push(a),r[t].literals.push(i),r[t].candidates.push(a)}else{s[n]=[a],r[t].literals.push(i),r[t].candidates.push(a);break}}}else i.push(a)}return{keys:r,otherwise:i,candidates:n}},Ip=e=>Es(e)?Ip(e.from):e,If=(e,t,r,i)=>{if(i?.isEffectAllowed===!0||tA(e))return e;let n=new mG,s=wP(e,{scheduler:n});n.flush();let a=s.unsafePoll();if(a){if(h_(a))return tP(a.value);let e=a.cause;return v6(e)?tz(e.error):tz(new EH(t,r,sR(e)))}return tz(new EH(t,r,"cannot be be resolved synchronously, this is caused by using runSync on an effect that performs async work"))},Id=([e],[t])=>e>t?1:e<t?-1:0;function Im(e){return e.sort(Id).map(e=>e[1])}let Ig=(e,t)=>{switch(e._tag){case"FinalTransformation":return t?e.decode:e.encode;case"ComposeTransformation":return tP;case"TypeLiteralTransformation":return r=>{let i=tP(r);for(let r of e.propertySignatureTransformations){let[e,n]=t?[r.from,r.to]:[r.to,r.from],s=t?r.decode:r.encode;i=E0(i,t=>{let r=s(Object.prototype.hasOwnProperty.call(t,e)?tI(t[e]):tQ());return delete t[e],tC(r)&&(t[n]=r.value),t})}return i}}},Ib=(e,t=[])=>({value:e,forest:t}),Ix={formatIssue:e=>E0(IP(e),Iy),formatIssueSync:e=>{let t=Ix.formatIssue(e);return tA(t)?tH(t):wL(t)},formatError:e=>Ix.formatIssue(e.issue),formatErrorSync:e=>Ix.formatIssueSync(e.issue)},Iy=e=>e.value+Iv("\n",e.forest),Iv=(e,t)=>{let r,i="",n=t.length;for(let s=0;s<n;s++){r=t[s];let a=s===n-1;i+=e+(a?"└":"├")+"─ "+r.value,i+=Iv(e+(n>1&&!a?"│  ":"   "),r.forest)}return i},IS=e=>{switch(e){case"Encoded":return"Encoded side transformation failure";case"Transformation":return"Transformation process failure";case"Type":return"Type side transformation failure"}},I_=e=>{switch(e){case"From":return"From side refinement failure";case"Predicate":return"Predicate refinement failure"}},Iw=e=>"ast"in e?tI(e.ast):tQ(),Ik=tP(void 0),IO=e=>Iw(e).pipe(t9(O6),tX({onNone:()=>Ik,onSome:t=>{let r=t(e);return W(r)?tP({message:r,override:!1}):uh(r)?uG(r,e=>({message:e,override:!1})):W(r.message)?tP({message:r.message,override:r.override}):uG(r.message,e=>({message:e,override:r.override}))}})),IC=e=>t=>t._tag===e,IE=IC("Composite"),II=IC("Refinement"),IF=IC("Transformation"),IT=e=>EX(IO(e),t=>void 0!==t?!t.override&&(IE(e)||II(e)&&"From"===e.kind||IF(e)&&"Transformation"!==e.kind)?IF(e)||II(e)?IT(e.issue):Ik:tP(t.message):Ik),IR=e=>Iw(e).pipe(t9(Cn),re(t=>t(e)),t5),IN=e=>E0(IT(e),t=>t??IR(e)??function(e){var t;if(void 0!==e.message)return e.message;let r=Es(e.ast)?Ct(t=e.ast).pipe(t1(()=>O7(t)),t1(()=>O9(t)),t1(()=>Ce(t)),t0(()=>`{ ${t.from} | filter }`)):String(e.ast);return`Expected ${r}, actual ${OS(e.actual)}`}(e)),IA=e=>IR(e)??String(e.ast),Ij=e=>e.message??"is forbidden",IM=e=>e.message??"is unexpected",Iz=e=>{let t=O8(e.ast);if(tC(t)){let e=t.value();return W(e)?tP(e):e}return tP(e.message??"is missing")},IP=e=>{switch(e._tag){case"Type":return E0(IN(e),Ib);case"Forbidden":return tP(Ib(IA(e),[Ib(Ij(e))]));case"Unexpected":return tP(Ib(IM(e)));case"Missing":return E0(Iz(e),Ib);case"Transformation":return EX(IT(e),t=>void 0!==t?tP(Ib(t)):E0(IP(e.issue),t=>Ib(IA(e),[Ib(IS(e.kind),[t])])));case"Refinement":return EX(IT(e),t=>void 0!==t?tP(Ib(t)):E0(IP(e.issue),t=>Ib(IA(e),[Ib(I_(e.kind),[t])])));case"Pointer":return E0(IP(e.issue),t=>Ib(OC(e.path),[t]));case"Composite":return EX(IT(e),t=>{if(void 0!==t)return tP(Ib(t));let r=IA(e);return Ow(e.issues)?E0(vv(e.issues,IP),e=>Ib(r,e)):E0(IP(e.issues),e=>Ib(r,[e]))})}},ID=(e,t,r)=>({_tag:e,path:t,message:r}),I$=Symbol.for("effect/QueueEnqueue"),IL=Symbol.for("effect/QueueDequeue"),IU=Symbol.for("effect/QueueStrategy"),Iq=Symbol.for("effect/BackingQueue"),IB={_A:e=>e},IJ={_A:e=>e},IH={_In:e=>e},IK={_Out:e=>e};class IV extends fP{constructor(e,t,r,i,n){super(),this[I$]=IH,this[IL]=IK,this.queue=e,this.takers=t,this.shutdownHook=r,this.shutdownFlag=i,this.strategy=n}pipe(){return e3(this,arguments)}commit(){return this.take}capacity(){return this.queue.capacity()}get size(){return u4(()=>uS(this.unsafeSize(),()=>uH))}unsafeSize(){return aK(this.shutdownFlag)?tQ():tI(this.queue.length()-kz(this.takers)+this.strategy.surplusSize())}get isEmpty(){return uG(this.size,e=>e<=0)}get isFull(){return uG(this.size,e=>e>=this.capacity())}get shutdown(){return ce(up(e=>(J(this.shutdownFlag,aV(!0)),J(v_(Fi(this.takers),t=>hW(t,e.id()),!1,!1),ch(this.strategy.shutdown),cn(hG(this.shutdownHook,void 0)),um))))}get isShutdown(){return u6(()=>aK(this.shutdownFlag))}get awaitShutdown(){return hJ(this.shutdownHook)}isActive(){return!aK(this.shutdownFlag)}unsafeOffer(e){let t;if(aK(this.shutdownFlag))return!1;if(0===this.queue.length()){let r=J(this.takers,kU(kR));r!==kR?(Ft(r,e),t=!0):t=!1}else t=!1;if(t)return!0;let r=this.queue.offer(e);return Fa(this.strategy,this.queue,this.takers),r}offer(e){return u4(()=>{let t;if(aK(this.shutdownFlag))return uH;if(0===this.queue.length()){let r=J(this.takers,kU(kR));r!==kR?(Ft(r,e),t=!0):t=!1}else t=!1;if(t)return u5(!0);let r=this.queue.offer(e);return Fa(this.strategy,this.queue,this.takers),r?u5(!0):this.strategy.handleSurplus([e],this.queue,this.takers,this.shutdownFlag)})}offerAll(e){return u4(()=>{if(aK(this.shutdownFlag))return uH;let t=rw(e),r=0===this.queue.length()?rw(Fn(this.takers,t.length)):r1,[i,n]=J(t,rY(r.length));for(let e=0;e<r.length;e++)Ft(r[e],i[e]);if(0===n.length)return u5(!0);let s=this.queue.offerAll(n);return Fa(this.strategy,this.queue,this.takers),iU(s)?u5(!0):this.strategy.handleSurplus(s,this.queue,this.takers,this.shutdownFlag)})}get take(){return up(e=>{if(aK(this.shutdownFlag))return uH;let t=this.queue.poll(kR);if(t!==kR)return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),u5(t);{let t=hU(e.id());return J(u4(()=>(J(this.takers,k$(t)),Fa(this.strategy,this.queue,this.takers),aK(this.shutdownFlag)?uH:hJ(t))),uX(()=>u6(()=>Fs(this.takers,t))))}})}get takeAll(){return u4(()=>aK(this.shutdownFlag)?uH:u6(()=>{let e=this.queue.pollUpTo(Number.POSITIVE_INFINITY);return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),i_(e)}))}takeUpTo(e){return u4(()=>aK(this.shutdownFlag)?uH:u6(()=>{let t=this.queue.pollUpTo(e);return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),i_(t)}))}takeBetween(e,t){return u4(()=>IW(this,e,t,iy()))}}let IW=(e,t,r,i)=>r<t?u5(i):J(I2(e,r),uM(n=>{let s=t-n.length;return 1===s?J(I1(e),uG(e=>J(i,iM(n),iT(e)))):s>1?J(I1(e),uM(t=>IW(e,s-1,r-n.length-1,J(i,iM(n),iT(t))))):u5(J(i,iM(n)))})),IG=(e,t,r,i,n)=>new IV(e,t,r,i,n),IY=(e,t)=>J(hq(),uG(r=>IG(e,kM(),r,aJ(!1),t)));class IZ{constructor(e){this[Iq]=IJ,this.mutable=e}poll(e){return kU(this.mutable,e)}pollUpTo(e){return kq(this.mutable,e)}offerAll(e){return kL(this.mutable,e)}offer(e){return k$(this.mutable,e)}capacity(){return kD(this.mutable)}length(){return kz(this.mutable)}}let IQ=e=>new IZ(e),IX=D(2,(e,t)=>e.offer(t)),I0=D(2,(e,t)=>e.offerAll(t)),I1=e=>e.take,I2=D(2,(e,t)=>e.takeUpTo(t)),I3=D(3,(e,t,r)=>e.takeBetween(t,r)),I5=D(2,(e,t)=>e.takeBetween(t,t)),I4=()=>new I7,I6=()=>new I9,I8=()=>new Fe;class I7{surplusSize(){return kz(this.putters)}onCompleteTakersWithEmptyQueue(e){for(;!kP(this.putters)&&!kP(e);){let t=kU(e,void 0),r=kU(this.putters,void 0);r[2]&&Ft(r[1],!0),Ft(t,r[0])}}get shutdown(){return J(uA,uM(e=>J(u6(()=>Fi(this.putters)),uM(t=>v_(t,([t,r,i])=>i?J(hW(r,e),um):cr,!1,!1)))))}handleSurplus(e,t,r,i){return up(n=>{let s=hU(n.id());return J(u4(()=>(this.unsafeOffer(e,s),this.unsafeOnQueueEmptySpace(t,r),Fa(this,t,r),aK(i)?uH:hJ(s))),uX(()=>u6(()=>this.unsafeRemove(s))))})}unsafeOnQueueEmptySpace(e,t){let r=!0;for(;r&&(e.capacity()===Number.POSITIVE_INFINITY||e.length()<e.capacity());){let i=J(this.putters,kU(kR));if(i===kR)r=!1;else{let r=e.offer(i[0]);r&&i[2]?Ft(i[1],!0):r||Fr(this.putters,J(Fi(this.putters),iR(i))),Fa(this,e,t)}}}unsafeOffer(e,t){let r=rw(e);for(let e=0;e<r.length;e++){let i=r[e];e===r.length-1?J(this.putters,k$([i,t,!0])):J(this.putters,k$([i,t,!1]))}}unsafeRemove(e){Fr(this.putters,J(Fi(this.putters),iP(([,t])=>t!==e)))}constructor(){this[IU]=IB,this.putters=kM()}}class I9{surplusSize(){return 0}get shutdown(){return cr}onCompleteTakersWithEmptyQueue(){}handleSurplus(e,t,r,i){return u5(!1)}unsafeOnQueueEmptySpace(e,t){}constructor(){this[IU]=IB}}class Fe{surplusSize(){return 0}get shutdown(){return cr}onCompleteTakersWithEmptyQueue(){}handleSurplus(e,t,r,i){return u6(()=>(this.unsafeOffer(t,e),Fa(this,t,r),!0))}unsafeOnQueueEmptySpace(e,t){}unsafeOffer(e,t){let r;let i=t[Symbol.iterator](),n=!0;for(;!(r=i.next()).done&&n;){if(0===e.capacity())return;e.poll(kR),n=e.offer(r.value)}}constructor(){this[IU]=IB}}let Ft=(e,t)=>hY(e,u5(t)),Fr=(e,t)=>J(e,kL(t)),Fi=e=>J(e,kq(Number.POSITIVE_INFINITY)),Fn=(e,t)=>J(e,kq(t)),Fs=(e,t)=>{Fr(e,J(Fi(e),iP(e=>t!==e)))},Fa=(e,t,r)=>{let i=!0;for(;i&&0!==t.length();){let n=J(r,kU(kR));if(n!==kR){let s=t.poll(kR);s!==kR?(Ft(n,s),e.unsafeOnQueueEmptySpace(t,r)):Fr(r,J(Fi(r),iR(n))),i=!0}else i=!1}i&&0===t.length()&&!kP(r)&&e.onCompleteTakersWithEmptyQueue(r)},Fo=e=>J(u6(()=>kj(e)),uM(e=>IY(IQ(e),I4()))),Fl=e=>J(u6(()=>kj(e)),uM(e=>IY(IQ(e),I6()))),Fu=e=>J(u6(()=>kj(e)),uM(e=>IY(IQ(e),I8()))),Fc=()=>J(u6(()=>kM()),uM(e=>IY(IQ(e),I6()))),Fh=e=>e.capacity(),Fp=e=>e.size,Ff=e=>e.isEmpty,Fd=e=>e.isFull,Fm=e=>e.isShutdown,Fg=e=>e.awaitShutdown,Fb=e=>e.shutdown,Fx=e=>uG(e.takeUpTo(1),iB),Fy=e=>e.takeAll,Fv=D(2,(e,t)=>cz(vn,t)(e)),FS=e=>ko(vG(vp,nB(e))),F_=e=>kg(uG(e,FS)),Fw=e=>kb(uG(e,FS)),Fk=e=>ko(vG(vp,nJ(e))),FO=D(2,(e,t)=>w6(Fk(e),()=>FS(t))),FC=D(2,(e,t)=>w6(Fk(e),()=>F_(t))),FE=D(2,(e,t)=>w6(Fk(e),()=>Fw(t))),FI=e=>w9(uG(e,()=>s7())),FF=e=>ko(vM(e)),FT=Symbol.for("effect/PubSub/AbsentValue"),FR=(e,t)=>r=>{r.has(e)||r.set(e,new Set),r.get(e).add(t)},FN=(e,t)=>r=>{if(!r.has(e))return;let i=r.get(e);i.delete(t),0===i.size&&r.delete(e)},FA=e=>{let t="number"==typeof e?{capacity:e}:e;FY(t.capacity);let r=t.replay&&t.replay>0?new F9(Math.ceil(t.replay)):void 0;return 1===t.capacity?new FU(r):pn(t.capacity)===t.capacity?new F$(t.capacity,r):new FP(t.capacity,r)},Fj=e=>new FB(e?.replay?new F9(e.replay):void 0),FM=(e,t,r)=>uG(hq(),i=>Fz(e,t,e.subscribe(),kM(),i,aJ(!1),r)),Fz=(e,t,r,i,n,s,a)=>new FH(e,t,r,i,n,s,a,e.replayWindow());class FP{constructor(e,t){this.publisherIndex=0,this.subscriberCount=0,this.subscribersIndex=0,this.capacity=e,this.replayBuffer=t,this.array=Array.from({length:e}),this.subscribers=Array.from({length:e})}replayWindow(){return this.replayBuffer?new Te(this.replayBuffer):Tt}isEmpty(){return this.publisherIndex===this.subscribersIndex}isFull(){return this.publisherIndex===this.subscribersIndex+this.capacity}size(){return this.publisherIndex-this.subscribersIndex}publish(e){if(this.isFull())return!1;if(0!==this.subscriberCount){let t=this.publisherIndex%this.capacity;this.array[t]=e,this.subscribers[t]=this.subscriberCount,this.publisherIndex+=1}return this.replayBuffer&&this.replayBuffer.offer(e),!0}publishAll(e){if(0===this.subscriberCount)return this.replayBuffer&&this.replayBuffer.offerAll(e),iy();let t=i_(e),r=t.length,i=this.publisherIndex-this.subscribersIndex,n=Math.min(r,this.capacity-i);if(0===n)return t;let s=0,a=this.publisherIndex+n;for(;this.publisherIndex!==a;){let e=iF(t,s++),r=this.publisherIndex%this.capacity;this.array[r]=e,this.subscribers[r]=this.subscriberCount,this.publisherIndex+=1,this.replayBuffer&&this.replayBuffer.offer(e)}return iA(t,s)}slide(){if(this.subscribersIndex!==this.publisherIndex){let e=this.subscribersIndex%this.capacity;this.array[e]=FT,this.subscribers[e]=0,this.subscribersIndex+=1}this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.subscriberCount+=1,new FD(this,this.publisherIndex,!1)}}class FD{constructor(e,t,r){this.self=e,this.subscriberIndex=t,this.unsubscribed=r}isEmpty(){return this.unsubscribed||this.self.publisherIndex===this.subscriberIndex||this.self.publisherIndex===this.self.subscribersIndex}size(){return this.unsubscribed?0:this.self.publisherIndex-Math.max(this.subscriberIndex,this.self.subscribersIndex)}poll(e){if(this.unsubscribed)return e;if(this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex),this.subscriberIndex!==this.self.publisherIndex){let e=this.subscriberIndex%this.self.capacity,t=this.self.array[e];return this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=FT,this.self.subscribersIndex+=1),this.subscriberIndex+=1,t}return e}pollUpTo(e){if(this.unsubscribed)return iy();this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);let t=Math.min(e,this.self.publisherIndex-this.subscriberIndex);if(t<=0)return iy();let r=[],i=this.subscriberIndex+t;for(;this.subscriberIndex!==i;){let e=this.subscriberIndex%this.self.capacity,t=this.self.array[e];this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=FT,this.self.subscribersIndex+=1),r.push(t),this.subscriberIndex+=1}return i_(r)}unsubscribe(){if(!this.unsubscribed)for(this.unsubscribed=!0,this.self.subscriberCount-=1,this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);this.subscriberIndex!==this.self.publisherIndex;){let e=this.subscriberIndex%this.self.capacity;this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=FT,this.self.subscribersIndex+=1),this.subscriberIndex+=1}}}class F${constructor(e,t){this.publisherIndex=0,this.subscriberCount=0,this.subscribersIndex=0,this.capacity=e,this.replayBuffer=t,this.array=Array.from({length:e}),this.mask=e-1,this.subscribers=Array.from({length:e})}replayWindow(){return this.replayBuffer?new Te(this.replayBuffer):Tt}isEmpty(){return this.publisherIndex===this.subscribersIndex}isFull(){return this.publisherIndex===this.subscribersIndex+this.capacity}size(){return this.publisherIndex-this.subscribersIndex}publish(e){if(this.isFull())return!1;if(0!==this.subscriberCount){let t=this.publisherIndex&this.mask;this.array[t]=e,this.subscribers[t]=this.subscriberCount,this.publisherIndex+=1}return this.replayBuffer&&this.replayBuffer.offer(e),!0}publishAll(e){if(0===this.subscriberCount)return this.replayBuffer&&this.replayBuffer.offerAll(e),iy();let t=i_(e),r=t.length,i=this.publisherIndex-this.subscribersIndex,n=Math.min(r,this.capacity-i);if(0===n)return t;let s=0,a=this.publisherIndex+n;for(;this.publisherIndex!==a;){let e=iF(t,s++),r=this.publisherIndex&this.mask;this.array[r]=e,this.subscribers[r]=this.subscriberCount,this.publisherIndex+=1,this.replayBuffer&&this.replayBuffer.offer(e)}return iA(t,s)}slide(){if(this.subscribersIndex!==this.publisherIndex){let e=this.subscribersIndex&this.mask;this.array[e]=FT,this.subscribers[e]=0,this.subscribersIndex+=1}this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.subscriberCount+=1,new FL(this,this.publisherIndex,!1)}}class FL{constructor(e,t,r){this.self=e,this.subscriberIndex=t,this.unsubscribed=r}isEmpty(){return this.unsubscribed||this.self.publisherIndex===this.subscriberIndex||this.self.publisherIndex===this.self.subscribersIndex}size(){return this.unsubscribed?0:this.self.publisherIndex-Math.max(this.subscriberIndex,this.self.subscribersIndex)}poll(e){if(this.unsubscribed)return e;if(this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex),this.subscriberIndex!==this.self.publisherIndex){let e=this.subscriberIndex&this.self.mask,t=this.self.array[e];return this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=FT,this.self.subscribersIndex+=1),this.subscriberIndex+=1,t}return e}pollUpTo(e){if(this.unsubscribed)return iy();this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);let t=Math.min(e,this.self.publisherIndex-this.subscriberIndex);if(t<=0)return iy();let r=[],i=this.subscriberIndex+t;for(;this.subscriberIndex!==i;){let e=this.subscriberIndex&this.self.mask,t=this.self.array[e];this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=FT,this.self.subscribersIndex+=1),r.push(t),this.subscriberIndex+=1}return i_(r)}unsubscribe(){if(!this.unsubscribed)for(this.unsubscribed=!0,this.self.subscriberCount-=1,this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);this.subscriberIndex!==this.self.publisherIndex;){let e=this.subscriberIndex&this.self.mask;this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=FT,this.self.subscribersIndex+=1),this.subscriberIndex+=1}}}class FU{constructor(e){this.publisherIndex=0,this.subscriberCount=0,this.subscribers=0,this.value=FT,this.capacity=1,this.replayBuffer=e}replayWindow(){return this.replayBuffer?new Te(this.replayBuffer):Tt}pipe(){return e3(this,arguments)}isEmpty(){return 0===this.subscribers}isFull(){return!this.isEmpty()}size(){return+!this.isEmpty()}publish(e){return!this.isFull()&&(0!==this.subscriberCount&&(this.value=e,this.subscribers=this.subscriberCount,this.publisherIndex+=1),this.replayBuffer&&this.replayBuffer.offer(e),!0)}publishAll(e){if(0===this.subscriberCount)return this.replayBuffer&&this.replayBuffer.offerAll(e),iy();let t=i_(e);return iU(t)?t:this.publish(iJ(t))?iA(t,1):t}slide(){this.isFull()&&(this.subscribers=0,this.value=FT),this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.subscriberCount+=1,new Fq(this,this.publisherIndex,!1)}}class Fq{constructor(e,t,r){this.self=e,this.subscriberIndex=t,this.unsubscribed=r}isEmpty(){return this.unsubscribed||0===this.self.subscribers||this.subscriberIndex===this.self.publisherIndex}size(){return+!this.isEmpty()}poll(e){if(this.isEmpty())return e;let t=this.self.value;return this.self.subscribers-=1,0===this.self.subscribers&&(this.self.value=FT),this.subscriberIndex+=1,t}pollUpTo(e){if(this.isEmpty()||e<1)return iy();let t=this.self.value;return this.self.subscribers-=1,0===this.self.subscribers&&(this.self.value=FT),this.subscriberIndex+=1,iS(t)}unsubscribe(){this.unsubscribed||(this.unsubscribed=!0,this.self.subscriberCount-=1,this.subscriberIndex===this.self.publisherIndex||(this.self.subscribers-=1,0!==this.self.subscribers||(this.self.value=FT)))}}class FB{constructor(e){this.publisherHead={value:FT,subscribers:0,next:null},this.publisherTail=this.publisherHead,this.publisherIndex=0,this.subscribersIndex=0,this.capacity=Number.MAX_SAFE_INTEGER,this.replayBuffer=e}replayWindow(){return this.replayBuffer?new Te(this.replayBuffer):Tt}isEmpty(){return this.publisherHead===this.publisherTail}isFull(){return!1}size(){return this.publisherIndex-this.subscribersIndex}publish(e){let t=this.publisherTail.subscribers;return 0!==t&&(this.publisherTail.next={value:e,subscribers:t,next:null},this.publisherTail=this.publisherTail.next,this.publisherIndex+=1),this.replayBuffer&&this.replayBuffer.offer(e),!0}publishAll(e){if(0!==this.publisherTail.subscribers)for(let t of e)this.publish(t);else this.replayBuffer&&this.replayBuffer.offerAll(e);return iy()}slide(){this.publisherHead!==this.publisherTail&&(this.publisherHead=this.publisherHead.next,this.publisherHead.value=FT,this.subscribersIndex+=1),this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.publisherTail.subscribers+=1,new FJ(this,this.publisherTail,this.publisherIndex,!1)}}class FJ{constructor(e,t,r,i){this.self=e,this.subscriberHead=t,this.subscriberIndex=r,this.unsubscribed=i}isEmpty(){if(this.unsubscribed)return!0;let e=!0,t=!0;for(;t;)this.subscriberHead===this.self.publisherTail?t=!1:this.subscriberHead.next.value!==FT?(e=!1,t=!1):(this.subscriberHead=this.subscriberHead.next,this.subscriberIndex+=1);return e}size(){return this.unsubscribed?0:this.self.publisherIndex-Math.max(this.subscriberIndex,this.self.subscribersIndex)}poll(e){if(this.unsubscribed)return e;let t=!0,r=e;for(;t;)if(this.subscriberHead===this.self.publisherTail)t=!1;else{let e=this.subscriberHead.next.value;e!==FT&&(r=e,this.subscriberHead.subscribers-=1,0===this.subscriberHead.subscribers&&(this.self.publisherHead=this.self.publisherHead.next,this.self.publisherHead.value=FT,this.self.subscribersIndex+=1),t=!1),this.subscriberHead=this.subscriberHead.next,this.subscriberIndex+=1}return r}pollUpTo(e){let t=[],r=0;for(;r!==e;){let i=this.poll(FT);i===FT?r=e:(t.push(i),r+=1)}return i_(t)}unsubscribe(){if(!this.unsubscribed)for(this.unsubscribed=!0,this.self.publisherTail.subscribers-=1;this.subscriberHead!==this.self.publisherTail;)this.subscriberHead.next.value!==FT&&(this.subscriberHead.subscribers-=1,0===this.subscriberHead.subscribers&&(this.self.publisherHead=this.self.publisherHead.next,this.self.publisherHead.value=FT,this.self.subscribersIndex+=1)),this.subscriberHead=this.subscriberHead.next}}class FH extends fP{static{u=IL}constructor(e,t,r,i,n,s,a,o){super(),this[u]=IK,this.pubsub=e,this.subscribers=t,this.subscription=r,this.pollers=i,this.shutdownHook=n,this.shutdownFlag=s,this.strategy=a,this.replayWindow=o}commit(){return this.take}pipe(){return e3(this,arguments)}capacity(){return this.pubsub.capacity}isActive(){return!aK(this.shutdownFlag)}get size(){return u4(()=>aK(this.shutdownFlag)?uH:u5(this.subscription.size()+this.replayWindow.remaining))}unsafeSize(){return aK(this.shutdownFlag)?tQ():tI(this.subscription.size()+this.replayWindow.remaining)}get isFull(){return u4(()=>aK(this.shutdownFlag)?uH:u5(this.subscription.size()===this.capacity()))}get isEmpty(){return uG(this.size,e=>0===e)}get shutdown(){return ce(up(e=>(aV(this.shutdownFlag,!0),J(vS(FX(this.pollers),t=>hW(t,e.id()),!1),ch(u6(()=>{this.subscribers.delete(this.subscription),this.subscription.unsubscribe(),this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers)})),cn(hG(this.shutdownHook,void 0)),um))))}get isShutdown(){return u6(()=>aK(this.shutdownFlag))}get awaitShutdown(){return hJ(this.shutdownHook)}get take(){return up(e=>{if(aK(this.shutdownFlag))return uH;if(this.replayWindow.remaining>0)return u5(this.replayWindow.take());let t=kP(this.pollers)?this.subscription.poll(kR):kR;if(t!==kR)return this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers),u5(t);{let t=hU(e.id());return J(u4(()=>(J(this.pollers,k$(t)),J(this.subscribers,FR(this.subscription,this.pollers)),this.strategy.unsafeCompletePollers(this.pubsub,this.subscribers,this.subscription,this.pollers),aK(this.shutdownFlag)?uH:hJ(t))),uX(()=>u6(()=>F3(this.pollers,t))))}})}get takeAll(){return u4(()=>{if(aK(this.shutdownFlag))return uH;let e=kP(this.pollers)?F0(this.subscription):iy();return(this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers),this.replayWindow.remaining>0)?u5(iM(this.replayWindow.takeAll(),e)):u5(e)})}takeUpTo(e){return u4(()=>{let t;if(aK(this.shutdownFlag))return uH;if(this.replayWindow.remaining>=e)return u5(this.replayWindow.takeN(e));this.replayWindow.remaining>0&&(t=this.replayWindow.takeAll(),e-=t.length);let r=kP(this.pollers)?F1(this.subscription,e):iy();return this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers),t?u5(iM(t,r)):u5(r)})}takeBetween(e,t){return u4(()=>FK(this,e,t,iy()))}}let FK=(e,t,r,i)=>r<t?u5(i):J(e.takeUpTo(r),uM(n=>{let s=t-n.length;return 1===s?J(e.take,uG(e=>J(i,iM(n),iT(e)))):s>1?J(e.take,uM(t=>FK(e,s-1,r-n.length-1,J(i,iM(n),iT(t))))):u5(J(i,iM(n)))}));class FV{static{c=I$,h=IL}constructor(e,t,r,i,n,s){this[c]=IH,this[h]=IK,this.pubsub=e,this.subscribers=t,this.scope=r,this.shutdownHook=i,this.shutdownFlag=n,this.strategy=s}capacity(){return this.pubsub.capacity}get size(){return u4(()=>aK(this.shutdownFlag)?uH:u6(()=>this.pubsub.size()))}unsafeSize(){return aK(this.shutdownFlag)?tQ():tI(this.pubsub.size())}get isFull(){return uG(this.size,e=>e===this.capacity())}get isEmpty(){return uG(this.size,e=>0===e)}get awaitShutdown(){return hJ(this.shutdownHook)}get isShutdown(){return u6(()=>aK(this.shutdownFlag))}get shutdown(){return ce(up(e=>(J(this.shutdownFlag,aV(!0)),J(this.scope.close(hR(e.id())),ch(this.strategy.shutdown),cn(hG(this.shutdownHook,void 0)),um))))}publish(e){return u4(()=>aK(this.shutdownFlag)?uH:this.pubsub.publish(e)?(this.strategy.unsafeCompleteSubscribers(this.pubsub,this.subscribers),u5(!0)):this.strategy.handleSurplus(this.pubsub,this.subscribers,iS(e),this.shutdownFlag))}isActive(){return!aK(this.shutdownFlag)}unsafeOffer(e){return!aK(this.shutdownFlag)&&!!this.pubsub.publish(e)&&(this.strategy.unsafeCompleteSubscribers(this.pubsub,this.subscribers),!0)}publishAll(e){return u4(()=>{if(aK(this.shutdownFlag))return uH;let t=F2(this.pubsub,e);return(this.strategy.unsafeCompleteSubscribers(this.pubsub,this.subscribers),iU(t))?u5(!0):this.strategy.handleSurplus(this.pubsub,this.subscribers,t,this.shutdownFlag)})}get subscribe(){return uG(vd(u8(vy([this.scope.fork(fU),FM(this.pubsub,this.subscribers,this.strategy)]),e=>e[0].addFinalizer(()=>e[1].shutdown)),(e,t)=>e[0].close(t)),e=>e[1])}offer(e){return this.publish(e)}offerAll(e){return this.publishAll(e)}pipe(){return e3(this,arguments)}}let FW=(e,t)=>uM(vH(),r=>uG(hq(),i=>FG(e,new Map,r,i,aJ(!1),t))),FG=(e,t,r,i,n,s)=>new FV(e,t,r,i,n,s),FY=e=>{if(e<=0)throw new hm(`Cannot construct PubSub with capacity of ${e}`)},FZ=(e,t)=>{hY(e,u5(t))},FQ=(e,t)=>J(e,kL(t)),FX=e=>J(e,kq(Number.POSITIVE_INFINITY)),F0=e=>e.pollUpTo(Number.POSITIVE_INFINITY),F1=(e,t)=>e.pollUpTo(t),F2=(e,t)=>e.publishAll(t),F3=(e,t)=>{FQ(e,J(FX(e),iP(e=>e!==t)))};class F5{get shutdown(){return uM(uA,e=>uM(u6(()=>FX(this.publishers)),t=>v_(t,([t,r,i])=>i?J(hW(r,e),um):cr,!1,!1)))}handleSurplus(e,t,r,i){return up(n=>{let s=hU(n.id());return J(u4(()=>(this.unsafeOffer(r,s),this.unsafeOnPubSubEmptySpace(e,t),this.unsafeCompleteSubscribers(e,t),aK(i)?uH:hJ(s))),uX(()=>u6(()=>this.unsafeRemove(s))))})}unsafeOnPubSubEmptySpace(e,t){let r=!0;for(;r&&!e.isFull();){let i=J(this.publishers,kU(kR));if(i===kR)r=!1;else{let r=e.publish(i[0]);r&&i[2]?FZ(i[1],!0):r||FQ(this.publishers,J(FX(this.publishers),iR(i))),this.unsafeCompleteSubscribers(e,t)}}}unsafeCompletePollers(e,t,r,i){return F8(this,e,t,r,i)}unsafeCompleteSubscribers(e,t){return F7(this,e,t)}unsafeOffer(e,t){let r=e[Symbol.iterator](),i=r.next();if(!i.done)for(;;){let e=i.value;if((i=r.next()).done){J(this.publishers,k$([e,t,!0]));break}J(this.publishers,k$([e,t,!1]))}}unsafeRemove(e){FQ(this.publishers,J(FX(this.publishers),iP(([t,r])=>r!==e)))}constructor(){this.publishers=kM()}}class F4{get shutdown(){return cr}handleSurplus(e,t,r,i){return u5(!1)}unsafeOnPubSubEmptySpace(e,t){}unsafeCompletePollers(e,t,r,i){return F8(this,e,t,r,i)}unsafeCompleteSubscribers(e,t){return F7(this,e,t)}}class F6{get shutdown(){return cr}handleSurplus(e,t,r,i){return u6(()=>(this.unsafeSlidingPublish(e,r),this.unsafeCompleteSubscribers(e,t),!0))}unsafeOnPubSubEmptySpace(e,t){}unsafeCompletePollers(e,t,r,i){return F8(this,e,t,r,i)}unsafeCompleteSubscribers(e,t){return F7(this,e,t)}unsafeSlidingPublish(e,t){let r=t[Symbol.iterator](),i=r.next();if(!i.done&&e.capacity>0){let t=i.value,n=!0;for(;n;){e.slide();let s=e.publish(t);s&&(i=r.next())&&!i.done?t=i.value:s&&(n=!1)}}}}let F8=(e,t,r,i,n)=>{let s=!0;for(;s&&!i.isEmpty();){let a=J(n,kU(kR));if(a===kR)J(r,FN(i,n)),kP(n)?s=!1:J(r,FR(i,n));else{let s=i.poll(kR);s===kR?FQ(n,J(FX(n),iR(a))):(FZ(a,s),e.unsafeOnPubSubEmptySpace(t,r))}}},F7=(e,t,r)=>{for(let[i,n]of r)for(let s of n)e.unsafeCompletePollers(t,r,i,s)};class F9{constructor(e){this.head={value:FT,next:null},this.tail=this.head,this.size=0,this.index=0,this.capacity=e}slide(){this.index++}offer(e){this.tail.value=e,this.tail.next={value:FT,next:null},this.tail=this.tail.next,this.size===this.capacity?this.head=this.head.next:this.size+=1}offerAll(e){for(let t of e)this.offer(t)}}class Te{constructor(e){this.buffer=e,this.index=e.index,this.remaining=e.size,this.head=e.head}fastForward(){for(;this.index<this.buffer.index;)this.head=this.head.next,this.index++}take(){if(0===this.remaining)return;this.index<this.buffer.index&&this.fastForward(),this.remaining--;let e=this.head.value;return this.head=this.head.next,e}takeN(e){if(0===this.remaining)return iy();this.index<this.buffer.index&&this.fastForward();let t=Math.min(e,this.remaining),r=Array(t);for(let e=0;e<t;e++){let t=this.head.value;this.head=this.head.next,r[e]=t}return this.remaining-=t,iE(r)}takeAll(){return this.takeN(this.remaining)}}let Tt={remaining:0,take:()=>void 0,takeN:()=>iy(),takeAll:()=>iy()},Tr=e=>u4(()=>FW(FA(e),new F5)),Ti=e=>u4(()=>FW(FA(e),new F4)),Tn=e=>u4(()=>FW(FA(e),new F6)),Ts=e=>u4(()=>FW(Fj(e),new F4)),Ta=e=>e.shutdown,To=e=>e.subscribe,Tl="Continue",Tu=Symbol.for("effect/ChannelChildExecutorDecision"),Tc={[Tu]:Tu},Th=e=>{let t=Object.create(Tc);return t._tag=Tl,t},Tp="ContinuationK",Tf=Symbol.for("effect/ChannelContinuation"),Td={_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutDone:e=>e,_OutErr2:e=>e,_OutElem:e=>e,_OutDone2:e=>e};class Tm{constructor(e,t){this._tag=Tp,this[Tf]=Td,this.onSuccess=e,this.onHalt=t}onExit(e){return hS(e)?this.onHalt(e.cause):this.onSuccess(e.value)}}class Tg{constructor(e){this._tag="ContinuationFinalizer",this[Tf]=Td,this.finalizer=e}}let Tb="PullAfterNext",Tx={[Symbol.for("effect/ChannelUpstreamPullStrategy")]:{_A:e=>e}},Ty=e=>{let t=Object.create(Tx);return t._tag=Tb,t.emitSeparator=e,t},Tv="BracketOut",TS="Bridge",T_="ConcatAll",Tw="Emit",Tk="Ensuring",TO="Fail",TC="Fold",TE="FromEffect",TI="PipeTo",TF="Provide",TT="Read",TR="Succeed",TN="SucceedNow",TA="Suspend",Tj=Symbol.for("effect/Channel"),TM={[Tj]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},pipe(){return e3(this,arguments)}},Tz=e=>ea(e,Tj)||uh(e),TP=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=Tv,r.acquire=()=>e,r.finalizer=t,r}),TD=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=TC,r.channel=e,r.k=new Tm(T1,t),r}),T$=e=>T3(()=>{let t=[];return TW(TZ(e,TL(t)),e=>T5(()=>[i_(t),e]))}),TL=e=>TX({onInput:t=>TW(T5(()=>{e.push(t)}),()=>TL(e)),onFailure:TH,onDone:T2}),TU=(e,t,r)=>{let i=Object.create(TM);return i._tag=T_,i.combineInners=t,i.combineAll=r,i.onPull=()=>Ty(tQ()),i.onEmit=()=>Th,i.value=()=>e,i.k=$,i},Tq=D(4,(e,t,r,i)=>{let n=Object.create(TM);return n._tag=T_,n.combineInners=r,n.combineAll=i,n.onPull=()=>Ty(tQ()),n.onEmit=()=>Th,n.value=()=>e,n.k=t,n}),TB=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=TS,r.input=t,r.channel=e,r}),TJ=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=Tk,r.channel=e,r.finalizer=t,r}),TH=e=>TK(n8(e)),TK=e=>TV(()=>e),TV=e=>{let t=Object.create(TM);return t._tag=TO,t.error=e,t},TW=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=TC,r.channel=e,r.k=new Tm(t,TK),r}),TG=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=TC,r.channel=e,r.k=new Tm(t.onSuccess,t.onFailure),r}),TY=e=>{let t=Object.create(TM);return t._tag=TE,t.effect=()=>e,t},TZ=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=TI,r.left=()=>e,r.right=()=>t,r}),TQ=D(2,(e,t)=>{let r=Object.create(TM);return r._tag=TF,r.context=()=>t,r.inner=e,r}),TX=e=>T0({onInput:e.onInput,onFailure:t=>tq(sp(t),{onLeft:e.onFailure,onRight:TK}),onDone:e.onDone}),T0=e=>{let t=Object.create(TM);return t._tag=TT,t.more=e.onInput,t.done=new Tm(e.onDone,e.onFailure),t},T1=e=>T5(()=>e),T2=e=>{let t=Object.create(TM);return t._tag=TN,t.terminal=e,t},T3=e=>{let t=Object.create(TM);return t._tag=TA,t.channel=e,t},T5=e=>{let t=Object.create(TM);return t._tag=TR,t.evaluate=e,t},T4=T2(void 0),T6=e=>{let t=Object.create(TM);return t._tag=Tw,t.out=e,t},T8="Done",T7="Emit",T9="FromEffect",Re="Read",Rt={[Symbol.for("effect/ChannelState")]:{_E:e=>e,_R:e=>e}},Rr=()=>{let e=Object.create(Rt);return e._tag=T8,e},Ri=()=>{let e=Object.create(Rt);return e._tag=T7,e},Rn=e=>{let t=Object.create(Rt);return t._tag=T9,t.effect=e,t},Rs=(e,t,r,i)=>{let n=Object.create(Rt);return n._tag=Re,n.upstream=e,n.onEffect=t,n.onEmit=r,n.onDone=i,n},Ra=e=>e._tag===T9,Ro=e=>Ra(e)?e.effect:cr,Rl=e=>Ra(e)?Ot(e.effect):void 0,Ru="PullFromChild",Rc="PullFromUpstream",Rh="DrainChildExecutors",Rp="Emit";class Rf{constructor(e,t,r){this._tag=Ru,this.childExecutor=e,this.parentSubexecutor=t,this.onEmit=r}close(e){let t=this.childExecutor.close(e),r=this.parentSubexecutor.close(e);return void 0!==t&&void 0!==r?v$(uI(t),uI(r),(e,t)=>J(e,hD(t))):void 0!==t?t:void 0!==r?r:void 0}enqueuePullFromChild(e){return this}}class Rd{constructor(e,t,r,i,n,s,a,o){this._tag=Rc,this.upstreamExecutor=e,this.createChild=t,this.lastDone=r,this.activeChildExecutors=i,this.combineChildResults=n,this.combineWithChildResult=s,this.onPull=a,this.onEmit=o}close(e){let t=this.upstreamExecutor.close(e);return[...this.activeChildExecutors.map(t=>void 0!==t?t.childExecutor.close(e):void 0),t].reduce((e,t)=>void 0!==e&&void 0!==t?v$(e,uI(t),(e,t)=>hD(e,t)):void 0!==e?e:void 0!==t?uI(t):void 0,void 0)}enqueuePullFromChild(e){return new Rd(this.upstreamExecutor,this.createChild,this.lastDone,[...this.activeChildExecutors,e],this.combineChildResults,this.combineWithChildResult,this.onPull,this.onEmit)}}class Rm{constructor(e,t,r,i,n,s,a){this._tag=Rh,this.upstreamExecutor=e,this.lastDone=t,this.activeChildExecutors=r,this.upstreamDone=i,this.combineChildResults=n,this.combineWithChildResult=s,this.onPull=a}close(e){let t=this.upstreamExecutor.close(e);return[...this.activeChildExecutors.map(t=>void 0!==t?t.childExecutor.close(e):void 0),t].reduce((e,t)=>void 0!==e&&void 0!==t?v$(e,uI(t),(e,t)=>hD(e,t)):void 0!==e?e:void 0!==t?uI(t):void 0,void 0)}enqueuePullFromChild(e){return new Rm(this.upstreamExecutor,this.lastDone,[...this.activeChildExecutors,e],this.upstreamDone,this.combineChildResults,this.combineWithChildResult,this.onPull)}}class Rg{constructor(e,t){this._tag=Rp,this.value=e,this.next=t}close(e){return this.next.close(e)}enqueuePullFromChild(e){return this}}let Rb={[Symbol.for("effect/ChannelUpstreamPullRequest")]:{_A:e=>e}},Rx=e=>{let t=Object.create(Rb);return t._tag="Pulled",t.value=e,t},Ry=e=>{let t=Object.create(Rb);return t._tag="NoUpstream",t.activeDownstreamCount=e,t};class Rv{constructor(e,t,r){this._activeSubexecutor=void 0,this._cancelled=void 0,this._closeLastSubstream=void 0,this._done=void 0,this._doneStack=[],this._emitted=void 0,this._input=void 0,this._inProgressFinalizer=void 0,this._currentChannel=e,this._executeCloseLastSubstream=r,this._providedEnv=t}run(){let e;for(;void 0===e;)if(void 0!==this._cancelled)e=this.processCancellation();else if(void 0!==this._activeSubexecutor)e=this.runSubexecutor();else try{if(void 0===this._currentChannel)e=Rr();else switch(uh(this._currentChannel)&&(this._currentChannel=TY(this._currentChannel)),this._currentChannel._tag){case Tv:e=this.runBracketOut(this._currentChannel);break;case TS:{let t=this._currentChannel.input;if(this._currentChannel=this._currentChannel.channel,void 0!==this._input){let r=this._input;this._input=void 0;let i=()=>uM(t.awaitRead(),()=>u4(()=>{let e=r.run();switch(e._tag){case T8:return hA(r.getDone(),{onFailure:e=>t.error(e),onSuccess:e=>t.done(e)});case T7:return uM(t.emit(r.getEmit()),()=>i());case T9:return uL(e.effect,{onFailure:e=>t.error(e),onSuccess:()=>i()});case Re:return Rw(e,()=>i(),e=>t.error(e))}}));e=Rn(uM(vk(uV(i())),e=>u6(()=>this.addFinalizer(t=>uM(cd(e),()=>u4(()=>{let e=this.restorePipe(t,r);return void 0!==e?e:cr}))))))}break}case T_:{let e=new Rv(this._currentChannel.value(),this._providedEnv,e=>u6(()=>{let t=void 0===this._closeLastSubstream?cr:this._closeLastSubstream;this._closeLastSubstream=J(t,vD(e))}));e._input=this._input;let t=this._currentChannel;this._activeSubexecutor=new Rd(e,e=>t.k(e),void 0,[],(e,r)=>t.combineInners(e,r),(e,r)=>t.combineAll(e,r),e=>t.onPull(e),e=>t.onEmit(e)),this._closeLastSubstream=void 0,this._currentChannel=void 0;break}case Tw:this._emitted=this._currentChannel.out,this._currentChannel=void 0!==this._activeSubexecutor?void 0:T4,e=Ri();break;case Tk:this.runEnsuring(this._currentChannel);break;case TO:e=this.doneHalt(this._currentChannel.error());break;case TC:this._doneStack.push(this._currentChannel.k),this._currentChannel=this._currentChannel.channel;break;case TE:{let t=void 0===this._providedEnv?this._currentChannel.effect():J(this._currentChannel.effect(),kv(this._providedEnv));e=Rn(uL(t,{onFailure:e=>{let t=this.doneHalt(e);return void 0!==t&&Ra(t)?t.effect:cr},onSuccess:e=>{let t=this.doneSucceed(e);return void 0!==t&&Ra(t)?t.effect:cr}}));break}case TI:{let e=this._input,t=new Rv(this._currentChannel.left(),this._providedEnv,e=>this._executeCloseLastSubstream(e));t._input=e,this._input=t,this.addFinalizer(t=>{let r=this.restorePipe(t,e);return void 0!==r?r:cr}),this._currentChannel=this._currentChannel.right();break}case TF:{let e=this._providedEnv;this._providedEnv=this._currentChannel.context(),this._currentChannel=this._currentChannel.inner,this.addFinalizer(()=>u6(()=>{this._providedEnv=e}));break}case TT:{let t=this._currentChannel;e=Rs(this._input,$,e=>{try{this._currentChannel=t.more(e)}catch(e){this._currentChannel=t.done.onExit(hC(e))}},e=>{let r=e=>t.done.onExit(e);this._currentChannel=r(e)});break}case TR:e=this.doneSucceed(this._currentChannel.evaluate());break;case TN:e=this.doneSucceed(this._currentChannel.terminal);break;case TA:this._currentChannel=this._currentChannel.channel();break;default:this._currentChannel._tag}}catch(e){this._currentChannel=TK(n7(e))}return e}getDone(){return this._done}getEmit(){return this._emitted}cancelWith(e){this._cancelled=e}clearInProgressFinalizer(){this._inProgressFinalizer=void 0}storeInProgressFinalizer(e){this._inProgressFinalizer=e}popAllFinalizers(e){let t=[],r=this._doneStack.pop();for(;r;)"ContinuationFinalizer"===r._tag&&t.push(r.finalizer),r=this._doneStack.pop();let i=0===t.length?cr:R_(t,e);return this.storeInProgressFinalizer(i),i}popNextFinalizers(){let e=[];for(;0!==this._doneStack.length;){let t=this._doneStack[this._doneStack.length-1];if(t._tag===Tp)break;e.push(t),this._doneStack.pop()}return e}restorePipe(e,t){let r=this._input;return(this._input=t,void 0!==r)?r.close(e):cr}close(e){let t,r;let i=this._inProgressFinalizer;void 0!==i&&(t=J(i,v2(u6(()=>this.clearInProgressFinalizer()))));let n=this.popAllFinalizers(e);void 0!==n&&(r=J(n,v2(u6(()=>this.clearInProgressFinalizer()))));let s=void 0===this._activeSubexecutor?void 0:this._activeSubexecutor.close(e);if(void 0!==s||void 0!==t||void 0!==r)return J(uI(RS(s)),vz(uI(RS(t))),vz(uI(RS(r))),uG(([[e,t],r])=>J(e,hD(t),hD(r))),ce,uM(e=>u4(()=>e)))}doneSucceed(e){if(0===this._doneStack.length)return this._done=hM(e),this._currentChannel=void 0,Rr();let t=this._doneStack[this._doneStack.length-1];if(t._tag===Tp){this._doneStack.pop(),this._currentChannel=t.onSuccess(e);return}let r=this.popNextFinalizers();if(0===this._doneStack.length)return this._doneStack=r.reverse(),this._done=hM(e),this._currentChannel=void 0,Rr();let i=R_(r.map(e=>e.finalizer),hM(e));return this.storeInProgressFinalizer(i),Rn(J(i,v2(u6(()=>this.clearInProgressFinalizer())),ce,uM(()=>u6(()=>this.doneSucceed(e)))))}doneHalt(e){if(0===this._doneStack.length)return this._done=hI(e),this._currentChannel=void 0,Rr();let t=this._doneStack[this._doneStack.length-1];if(t._tag===Tp){this._doneStack.pop();try{this._currentChannel=t.onHalt(e)}catch(e){this._currentChannel=TK(n7(e))}return}let r=this.popNextFinalizers();if(0===this._doneStack.length)return this._doneStack=r.reverse(),this._done=hI(e),this._currentChannel=void 0,Rr();let i=R_(r.map(e=>e.finalizer),hI(e));return this.storeInProgressFinalizer(i),Rn(J(i,v2(u6(()=>this.clearInProgressFinalizer())),ce,uM(()=>u6(()=>this.doneHalt(e)))))}processCancellation(){return this._currentChannel=void 0,this._done=this._cancelled,this._cancelled=void 0,Rr()}runBracketOut(e){return Rn(ce(uL(this.provide(e.acquire()),{onFailure:e=>u6(()=>{this._currentChannel=TK(e)}),onSuccess:t=>u6(()=>{this.addFinalizer(r=>this.provide(e.finalizer(t,r))),this._currentChannel=T6(t)})})))}provide(e){return void 0===this._providedEnv?e:J(e,kv(this._providedEnv))}runEnsuring(e){this.addFinalizer(e.finalizer),this._currentChannel=e.channel}addFinalizer(e){this._doneStack.push(new Tg(e))}runSubexecutor(){let e=this._activeSubexecutor;switch(e._tag){case Ru:return this.pullFromChild(e.childExecutor,e.parentSubexecutor,e.onEmit,e);case Rc:return this.pullFromUpstream(e);case Rh:return this.drainChildExecutors(e);case Rp:return this._emitted=e.value,this._activeSubexecutor=e.next,Ri()}}replaceSubexecutor(e){this._currentChannel=void 0,this._activeSubexecutor=e}finishWithExit(e){let t=hA(e,{onFailure:e=>this.doneHalt(e),onSuccess:e=>this.doneSucceed(e)});return this._activeSubexecutor=void 0,void 0===t?cr:Ro(t)}finishSubexecutorWithCloseEffect(e,...t){this.addFinalizer(()=>J(t,vv(t=>J(u6(()=>t(e)),uM(e=>void 0!==e?e:cr)),{discard:!0})));let r=J(e,hA({onFailure:e=>this.doneHalt(e),onSuccess:e=>this.doneSucceed(e)}));return this._activeSubexecutor=void 0,r}applyUpstreamPullStrategy(e,t,r){switch(r._tag){case Tb:{let i=!e||t.some(e=>void 0!==e);return[r.emitSeparator,i?[void 0,...t]:t]}case"PullAfterAllEnqueued":{let i=!e||t.some(e=>void 0!==e);return[r.emitSeparator,i?[...t,void 0]:t]}}}pullFromChild(e,t,r,i){return Rs(e,$,n=>{let s=r(n);switch(s._tag){case Tl:break;case"Close":this.finishWithDoneValue(e,t,s.value);break;case"Yield":{let e=t.enqueuePullFromChild(i);this.replaceSubexecutor(e)}}this._activeSubexecutor=new Rg(n,this._activeSubexecutor)},hA({onFailure:r=>{let i=this.handleSubexecutorFailure(e,t,r);return void 0===i?void 0:Rl(i)},onSuccess:r=>{this.finishWithDoneValue(e,t,r)}}))}finishWithDoneValue(e,t,r){switch(t._tag){case Rc:{let i=new Rd(t.upstreamExecutor,t.createChild,void 0!==t.lastDone?t.combineChildResults(t.lastDone,r):r,t.activeChildExecutors,t.combineChildResults,t.combineWithChildResult,t.onPull,t.onEmit);this._closeLastSubstream=e.close(hM(r)),this.replaceSubexecutor(i);break}case Rh:{let i=new Rm(t.upstreamExecutor,void 0!==t.lastDone?t.combineChildResults(t.lastDone,r):r,t.activeChildExecutors,t.upstreamDone,t.combineChildResults,t.combineWithChildResult,t.onPull);this._closeLastSubstream=e.close(hM(r)),this.replaceSubexecutor(i)}}}handleSubexecutorFailure(e,t,r){return this.finishSubexecutorWithCloseEffect(hI(r),e=>t.close(e),t=>e.close(t))}pullFromUpstream(e){if(0===e.activeChildExecutors.length)return this.performPullFromUpstream(e);let t=e.activeChildExecutors[0],r=new Rd(e.upstreamExecutor,e.createChild,e.lastDone,e.activeChildExecutors.slice(1),e.combineChildResults,e.combineWithChildResult,e.onPull,e.onEmit);if(void 0===t)return this.performPullFromUpstream(r);this.replaceSubexecutor(new Rf(t.childExecutor,r,t.onEmit))}performPullFromUpstream(e){return Rs(e.upstreamExecutor,e=>{let t=void 0===this._closeLastSubstream?cr:this._closeLastSubstream;return this._closeLastSubstream=void 0,J(this._executeCloseLastSubstream(t),vD(e))},t=>{if(void 0!==this._closeLastSubstream){let r=this._closeLastSubstream;return this._closeLastSubstream=void 0,J(this._executeCloseLastSubstream(r),uG(()=>{let r=new Rv(e.createChild(t),this._providedEnv,this._executeCloseLastSubstream);r._input=this._input;let[i,n]=this.applyUpstreamPullStrategy(!1,e.activeChildExecutors,e.onPull(Rx(t)));this._activeSubexecutor=new Rf(r,new Rd(e.upstreamExecutor,e.createChild,e.lastDone,n,e.combineChildResults,e.combineWithChildResult,e.onPull,e.onEmit),e.onEmit),tC(i)&&(this._activeSubexecutor=new Rg(i.value,this._activeSubexecutor))}))}let r=new Rv(e.createChild(t),this._providedEnv,this._executeCloseLastSubstream);r._input=this._input;let[i,n]=this.applyUpstreamPullStrategy(!1,e.activeChildExecutors,e.onPull(Rx(t)));this._activeSubexecutor=new Rf(r,new Rd(e.upstreamExecutor,e.createChild,e.lastDone,n,e.combineChildResults,e.combineWithChildResult,e.onPull,e.onEmit),e.onEmit),tC(i)&&(this._activeSubexecutor=new Rg(i.value,this._activeSubexecutor))},t=>{if(e.activeChildExecutors.some(e=>void 0!==e)){let t=new Rm(e.upstreamExecutor,e.lastDone,[void 0,...e.activeChildExecutors],e.upstreamExecutor.getDone(),e.combineChildResults,e.combineWithChildResult,e.onPull);if(void 0!==this._closeLastSubstream){let e=this._closeLastSubstream;return this._closeLastSubstream=void 0,J(this._executeCloseLastSubstream(e),uG(()=>this.replaceSubexecutor(t)))}return void this.replaceSubexecutor(t)}let r=this._closeLastSubstream,i=this.finishSubexecutorWithCloseEffect(J(t,hN(t=>e.combineWithChildResult(e.lastDone,t))),()=>r,t=>e.upstreamExecutor.close(t));return void 0===i?void 0:Rl(i)})}drainChildExecutors(e){if(0===e.activeChildExecutors.length){let t=this._closeLastSubstream;return void 0!==t&&this.addFinalizer(()=>u5(t)),this.finishSubexecutorWithCloseEffect(e.upstreamDone,()=>t,t=>e.upstreamExecutor.close(t))}let t=e.activeChildExecutors[0],r=e.activeChildExecutors.slice(1);if(void 0===t){let[t,i]=this.applyUpstreamPullStrategy(!0,r,e.onPull(Ry(r.reduce((e,t)=>void 0!==t?e+1:e,0))));return(this.replaceSubexecutor(new Rm(e.upstreamExecutor,e.lastDone,i,e.upstreamDone,e.combineChildResults,e.combineWithChildResult,e.onPull)),tC(t))?(this._emitted=t.value,Ri()):void 0}let i=new Rm(e.upstreamExecutor,e.lastDone,r,e.upstreamDone,e.combineChildResults,e.combineWithChildResult,e.onPull);this.replaceSubexecutor(new Rf(t.childExecutor,i,t.onEmit))}}let RS=e=>void 0!==e?e:cr,R_=(e,t)=>J(vv(e,e=>uI(e(t))),uG(e=>J(hO(e),t0(()=>hz))),uM(e=>u4(()=>e))),Rw=(e,t,r)=>{let i=[e],n=()=>{let e=i.pop();if(void 0===e||void 0===e.upstream)return uC("Unexpected end of input for channel execution");let s=e.upstream.run();switch(s._tag){case T7:{let s=e.onEmit(e.upstream.getEmit());if(0===i.length){if(void 0===s)return u4(t);return J(s,uL({onFailure:r,onSuccess:t}))}if(void 0===s)return u4(()=>n());return J(s,uL({onFailure:r,onSuccess:()=>n()}))}case T8:{let s=e.onDone(e.upstream.getDone());if(0===i.length){if(void 0===s)return u4(t);return J(s,uL({onFailure:r,onSuccess:t}))}if(void 0===s)return u4(()=>n());return J(s,uL({onFailure:r,onSuccess:()=>n()}))}case T9:return i.push(e),J(e.onEffect(s.effect),uv(t=>u4(()=>{let r=e.onDone(hI(t));return void 0===r?cr:r})),uL({onFailure:r,onSuccess:()=>n()}));case Re:return i.push(e),i.push(s),u4(()=>n())}};return n()},Rk=D(2,(e,t)=>{let r=(t,r,i)=>uf(u6(()=>new Rv(e,void 0,$)),e=>u4(()=>RO(e.run(),e).pipe(uW(t),vD(hJ(t)),vP(hJ(r)))),(e,t)=>{let r=e.close(t);return void 0===r?cr:g3(r,e=>c7(i,uR(e)))});return ct(e=>vy([ht(t,fU),hq(),hq()]).pipe(uM(([i,n,s])=>e(r(n,s,i)).pipe(wt(t),uM(r=>t.addFinalizer(e=>{let t=hS(e)?sc(e.cause):void 0;return fj(n).pipe(uM(e=>e?hG(s,void 0).pipe(vD(bw(r)),vD(wg(r))):hG(s,void 0).pipe(vD(t&&nZ(t)>0?cm(r,oa(t)):cd(r)),vD(wg(r)))))}).pipe(vD(e(hJ(n)))))))))}),RO=(e,t)=>{switch(e._tag){case T9:return J(e.effect,uM(()=>RO(t.run(),t)));case T7:return RO(t.run(),t);case T8:return u4(()=>t.getDone());case Re:return Rw(e,()=>RO(t.run(),t),uR)}},RC="Done",RE={[Symbol.for("effect/ChannelMergeDecision")]:{_R:e=>e,_E0:e=>e,_Z0:e=>e,_E:e=>e,_Z:e=>e}},RI=e=>{let t=Object.create(RE);return t._tag=RC,t.effect=e,t},RF=e=>{let t=Object.create(RE);return t._tag="Await",t.f=e,t},RT="BothRunning",RR="LeftDone",RN="RightDone",RA=Symbol.for("effect/ChannelMergeState"),Rj={[RA]:RA},RM=(e,t)=>{let r=Object.create(Rj);return r._tag=RT,r.left=e,r.right=t,r},Rz=e=>{let t=Object.create(Rj);return t._tag=RR,t.f=e,t},RP=e=>{let t=Object.create(Rj);return t._tag=RN,t.f=e,t},RD="BackPressure",R$="BufferSliding",RL=Symbol.for("effect/ChannelMergeStrategy"),RU={[RL]:RL},Rq=e=>{let t=Object.create(RU);return t._tag=RD,t},RB=e=>{let t=Object.create(RU);return t._tag=R$,t},RJ=D(2,(e,{onBackPressure:t,onBufferSliding:r})=>{switch(e._tag){case RD:return t();case R$:return r()}}),RH="Empty",RK="Emit",RV="Error",RW="Done",RG=e=>({_tag:RH,notifyProducer:e}),RY=e=>({_tag:RK,notifyConsumers:e}),RZ=e=>({_tag:RV,cause:e}),RQ=e=>({_tag:RW,done:e});class RX{constructor(e){this.ref=e}awaitRead(){return uD(mJ(this.ref,e=>e._tag===RH?[hJ(e.notifyProducer),e]:[cr,e]))}get close(){return uj(e=>this.error(n9(e)))}done(e){return uD(mJ(this.ref,t=>{switch(t._tag){case RH:return[hJ(t.notifyProducer),t];case RK:return[vv(t.notifyConsumers,t=>hG(t,tz(e)),{discard:!0}),RQ(e)];case RV:case RW:return[uH,t]}}))}emit(e){return uM(hq(),t=>uD(mJ(this.ref,r=>{switch(r._tag){case RH:return[hJ(r.notifyProducer),r];case RK:{let i=r.notifyConsumers[0],n=r.notifyConsumers.slice(1);if(void 0!==i)return[hG(i,tP(e)),0===n.length?RG(t):RY(n)];throw Error("Bug: Channel.SingleProducerAsyncInput.emit - Queue was empty! please report an issue at https://github.com/Effect-TS/effect/issues")}case RV:case RW:return[uH,r]}})))}error(e){return uD(mJ(this.ref,t=>{switch(t._tag){case RH:return[hJ(t.notifyProducer),t];case RK:return[vv(t.notifyConsumers,t=>hV(t,e),{discard:!0}),RZ(e)];case RV:case RW:return[uH,t]}}))}get take(){return this.takeWith(e=>hI(sx(e,tz)),e=>hM(e),e=>hE(tP(e)))}takeWith(e,t,r){return uM(hq(),i=>uD(mJ(this.ref,n=>{switch(n._tag){case RH:return[vD(hG(n.notifyProducer,void 0),u$(hJ(i),{onFailure:e,onSuccess:tq({onLeft:r,onRight:t})})),RY([i])];case RK:return[u$(hJ(i),{onFailure:e,onSuccess:tq({onLeft:r,onRight:t})}),RY([...n.notifyConsumers,i])];case RV:return[u5(e(n.cause)),n];case RW:return[u5(r(n.done)),n]}})))}}let R0=()=>J(hq(),uM(e=>mL(RG(e))),uG(e=>new RX(e))),R1=D(2,(e,t)=>Nu(e,()=>t)),R2=e=>T3(()=>{let t=(e,r,i)=>Nj(mJ(i,n=>r(n)?[TX({onInput:n=>TW(T6(n),()=>t(e,r,i)),onFailure:e=>TH(e),onDone:e=>T2(e)}),n]:[TW(T6(n),()=>t(e,r,i)),e]));return t(e.empty,e.isEmpty,e.ref)}),R3=D(2,(e,t)=>TD(e,e=>tq(sp(e),{onLeft:t,onRight:TK}))),R5=D(2,(e,t)=>Tq(e,t,()=>void 0,()=>void 0)),R4=e=>T3(()=>{let t=[];return J(TZ(e,R6(t)),TW(e=>T1([iE(t),e])))}),R6=e=>TX({onInput:t=>TW(T5(()=>{e.push(t)}),()=>R6(e)),onFailure:TH,onDone:T1}),R8=e=>{let t=T0({onInput:()=>t,onFailure:TK,onDone:T1});return TZ(e,t)},R7=D(2,(e,t)=>TJ(e,()=>t)),R9=()=>TY(hX()),Ne=e=>TW(R9(),e),Nt=e=>TW(e,$),Nr=D(2,(e,t)=>TG(e,{onFailure:e=>{let r=sp(e);switch(r._tag){case"Left":return t.onFailure(r.left);case"Right":return TK(r.right)}},onSuccess:t.onSuccess})),Ni=e=>Nj(e.takeWith(TK,t=>TW(T6(t),()=>Ni(e)),T1)),Nn=e=>T3(()=>Ns(e)),Ns=e=>J(TY(I1(e)),TW(tq({onLeft:hA({onFailure:TK,onSuccess:T2}),onRight:t=>TW(T6(t),()=>Ns(e))}))),Na=()=>TX({onInput:e=>TW(T6(e),()=>Na()),onFailure:TH,onDone:T2}),No=D(2,(e,t)=>Ny(e,{other:TY(t),onSelfDone:e=>RI(u4(()=>e)),onOtherDone:e=>RI(u4(()=>e))})),Nl=D(2,(e,t)=>No(e,hJ(t))),Nu=D(2,(e,t)=>TW(e,e=>T5(()=>t(e)))),Nc=D(2,(e,t)=>TW(e,e=>TY(t(e)))),Nh=D(2,(e,t)=>Np(e,sx(t))),Np=D(2,(e,t)=>TD(e,e=>TK(t(e)))),Nf=D(2,(e,t)=>{let r=TX({onInput:e=>TW(T6(t(e)),()=>r),onFailure:TH,onDone:T2});return TZ(e,r)}),Nd=D(2,(e,t)=>{let r=T0({onInput:e=>J(TY(t(e)),TW(T6),TW(()=>r)),onFailure:TK,onDone:T2});return TZ(e,r)}),Nm=D(3,(e,t,r)=>Nz(i=>k9(function*(){let n=yield*R0(),s=Ni(n),a=yield*Fo(r);yield*c7(i,Fb(a));let o=yield*hq(),l=r===Number.POSITIVE_INFINITY?e=>$:(yield*Os(r)).withPermits,u=yield*s.pipe(TZ(e),NT(i));yield*u.pipe(uL({onFailure:e=>IX(a,uR(e)),onSuccess:tq({onLeft:e=>vD(uV(l(r)(cr)),um(IX(a,u5(tz(e))))),onRight:e=>k9(function*(){let r=yield*hq(),n=yield*hq();yield*IX(a,uG(hJ(r),tP)),yield*hG(n,void 0).pipe(vD(ct(r=>uI(r(hJ(o))).pipe(wi(uI(r(t(e)))),uM($))).pipe(g3(e=>hV(o,e)),uW(r))),l(1),wt(i)),yield*hJ(n)})})}),gP,uV,wt(i));let c=Nj(u$(uD(I1(a)),{onFailure:TK,onSuccess:tq({onLeft:T2,onRight:e=>TW(T6(e),()=>c)})}));return TB(c,n)}))),Ng=e=>t=>Nb(e)(t,B),Nb=({bufferSize:e=16,concurrency:t,mergeStrategy:r=Rq()})=>(i,n)=>Nz(s=>k9(function*(){let a="unbounded"===t?Number.MAX_SAFE_INTEGER:t,o=yield*R0(),l=Ni(o),u=yield*Fo(e);yield*c7(s,Fb(u));let c=yield*Fc();yield*c7(s,Fb(c));let h=yield*mL(tQ()),p=yield*hq(),f=(yield*Os(a)).withPermits,d=yield*NT(TZ(l,i),s);function m(e){return e.pipe(uM(tq({onLeft:e=>u5(tI(e)),onRight:e=>ud(IX(u,u5(tP(e))),tQ())})),_G({until:e=>tC(e)}),uM(e=>mH(h,tX({onNone:()=>tI(e.value),onSome:t=>tI(n(t,e.value))}))),uv(e=>sa(e)?uR(e):IX(u,uR(e)).pipe(vD(hG(p,void 0)),um)))}yield*d.pipe(uL({onFailure:e=>IX(u,uR(e)).pipe(vD(u5(!1))),onSuccess:tq({onLeft:e=>vQ(uV(hJ(p)),uV(f(a)(cr)),{onSelfDone:(e,t)=>ud(cd(t),!1),onOtherDone:(t,r)=>vD(cd(r),mU(h).pipe(uM(tX({onNone:()=>IX(u,u5(tz(e))),onSome:t=>IX(u,u5(tz(n(t,e))))})),ud(!1)))}),onRight:e=>RJ(r,{onBackPressure:()=>k9(function*(){let t=yield*hq(),r=vA(t=>NT(TZ(l,e),t).pipe(uM(e=>vX(uI(m(e)),uI(uV(hJ(p))))),uM($)));return yield*hG(t,void 0).pipe(vD(r),f(1),wt(s)),yield*hJ(t),!(yield*fj(p))}),onBufferSliding:()=>k9(function*(){let t=yield*hq(),r=yield*hq(),i=yield*Fp(c);yield*I1(c).pipe(uM(e=>hG(e,void 0)),g8(()=>i>=a)),yield*IX(c,t);let n=vA(r=>NT(TZ(l,e),r).pipe(uM(e=>uI(m(e)).pipe(vX(uI(uV(hJ(p)))),vX(uI(uV(hJ(t)))))),uM($)));return yield*hG(r,void 0).pipe(vD(n),f(1),wt(s)),yield*hJ(r),!(yield*fj(p))})})})}),_G({while:e=>e}),wt(s));let g=J(I1(u),uD,u$({onFailure:TK,onSuccess:tq({onLeft:T2,onRight:e=>TW(T6(e),()=>g)})}),Nj);return TB(g,o)})),Nx=D(3,(e,t,r)=>Ng(r)(Nf(e,t))),Ny=D(2,(e,t)=>Nz(function(r){return k9(function*(){let i=yield*R0(),n=Ni(i),s=yield*NT(TZ(n,e),r),a=yield*NT(TZ(n,t.other),r);function o(e,t,i){return(n,s,a)=>{function o(e){return e._tag===RC?u5(TY(vD(cd(t),e.effect))):uG(bw(t),hA({onFailure:t=>TY(e.f(hI(t))),onSuccess:tq({onLeft:t=>TY(e.f(hM(t))),onRight:t=>Nq(T6(t),l(a(e.f)))})}))}return hA(e,{onFailure:e=>o(n(hI(e))),onSuccess:tq({onLeft:e=>o(n(hM(e))),onRight:e=>u5(TW(T6(e),()=>TW(TY(wt(uV(i),r)),e=>l(s(e,t)))))})})}}function l(e){switch(e._tag){case RT:return Nj(vQ(uV(bO(e.left)),uV(bO(e.right)),{onSelfDone:(r,i)=>vD(cd(i),o(r,e.right,s)(t.onSelfDone,RM,e=>Rz(e))),onOtherDone:(r,i)=>vD(cd(i),o(r,e.left,a)(t.onOtherDone,(e,t)=>RM(t,e),e=>RP(e)))}));case RR:return Nj(uG(uI(a),hA({onFailure:t=>TY(e.f(hI(t))),onSuccess:tq({onLeft:t=>TY(e.f(hM(t))),onRight:t=>TW(T6(t),()=>l(Rz(e.f)))})})));case RN:return Nj(uG(uI(s),hA({onFailure:t=>TY(e.f(hI(t))),onSuccess:tq({onLeft:t=>TY(e.f(hM(t))),onRight:t=>TW(T6(t),()=>l(RP(e.f)))})})))}}return TY(up(e=>{let t=up(t=>(t.transferChildren(e.scope()),cr));return v$(uV(s).pipe(v2(t),wt(r)),uV(a).pipe(v2(t),wt(r)),(e,t)=>RM(e,t))})).pipe(TW(l),TB(i))})})),Nv=((e,t)=>R3(e,e=>TV(()=>n7(t(e)))),D(2,(e,t)=>R3(e,t))),NS=D(2,(e,t)=>T3(()=>{let r;let i=TX({onInput:e=>TW(T6(e),()=>i),onFailure:e=>TK(n7(r=NJ(e))),onDone:T2}),n=T0({onInput:e=>J(T6(e),TW(()=>n)),onFailure:e=>sn(e)&&NH(e.defect)&&eU(e.defect,r)?TH(e.defect.error):TK(e),onDone:T2});return TZ(TZ(TZ(e,i),t),n)})),N_=((e,t)=>Nz(r=>uG(wQ(t,r),t=>TQ(e,t))),e=>TW(e,()=>N_(e))),Nw=e=>vA(t=>Rk(e,t)),Nk=e=>Nw(R8(e)),NO=e=>vN(t=>Rk(e,t)),NC=e=>Nj(ct(t=>uG(vH(),r=>TP(g3(t(vK(e,r)),e=>he(r,hI(e))),(e,t)=>he(r,t))))),NE=e=>NM(uG(vU,t=>TW(TY(e(t)),T6))),NI=e=>NN(e),NF=e=>uM(vU,t=>NT(e,t)),NT=D(2,(e,t)=>vz(u6(()=>new Rv(e,void 0,$)),wj()).pipe(u8(([e,r])=>c9(t,t=>{let i=e.close(t);return void 0!==i?kv(i,r):cr})),ce,uG(([e])=>u4(()=>NR(e.run(),e))))),NR=(e,t)=>{switch(e._tag){case T8:return hA(t.getDone(),{onFailure:uR,onSuccess:e=>u5(tz(e))});case T7:return u5(tP(t.getEmit()));case T9:return J(e.effect,uM(()=>NR(t.run(),t)));case Re:return Rw(e,()=>NR(t.run(),t),e=>uR(e))}},NN=e=>T3(()=>NA(e)),NA=e=>T0({onInput:t=>TW(TY(IX(e,tP(t))),()=>NA(e)),onFailure:t=>TY(J(IX(e,tz(hI(t))))),onDone:t=>TY(J(IX(e,tz(hM(t)))))}),Nj=e=>Nt(TY(e)),NM=e=>TU(NC(e),(e,t)=>e,(e,t)=>e),Nz=e=>TU(NE(e),(e,t)=>e,(e,t)=>e),NP=(...e)=>ND(i_(e)),ND=e=>N$(0,e.length,e),N$=(e,t,r)=>e===t?T4:J(T6(J(r,iF(e))),TW(()=>N$(e+1,t,r))),NL=D(e=>Tz(e[1]),(e,t,r)=>r?.concurrent?Ny(e,{other:t,onSelfDone:e=>RF(t=>u4(()=>hP(e,t))),onOtherDone:e=>RF(t=>u4(()=>hP(t,e)))}):TW(e,e=>Nu(t,t=>[e,t]))),NU=D(e=>Tz(e[1]),(e,t,r)=>r?.concurrent?Nu(NL(e,t,{concurrent:!0}),e=>e[0]):TW(e,e=>R1(t,e))),Nq=D(e=>Tz(e[1]),(e,t,r)=>r?.concurrent?Nu(NL(e,t,{concurrent:!0}),e=>e[1]):TW(e,()=>t)),NB=Symbol.for("effect/Channel/ChannelException"),NJ=e=>({_tag:"ChannelException",[NB]:NB,error:e}),NH=e=>ea(e,NB),NK=Symbol.for("effect/RcRef"),NV={_tag:"Empty"},NW={_tag:"Closed"},NG={_A:$,_E:$};class NY extends fP{static{p=mM}constructor(e,t,r,i){super(),this[NK]=NG,this[p]=mM,this.state=NV,this.semaphore=_6(1),this.acquire=e,this.context=t,this.scope=r,this.idleTimeToLive=i,this.get=NZ(this)}commit(){return this.get}}let NZ=e=>ct(t=>u4(()=>{switch(e.state._tag){case"Closed":return uH;case"Acquired":return e.state.refCount++,e.state.fiber?ud(cd(e.state.fiber),e.state):u5(e.state);case"Empty":return vH().pipe(gA("scope"),gN("value",({scope:r})=>t(cz(e.acquire,cB,sX(e.context,vL,r)))),uG(({scope:t,value:r})=>{let i={_tag:"Acquired",value:r,scope:t,fiber:void 0,refCount:1};return e.state=i,i}))}})).pipe(e.semaphore.withPermits(1),gA("state"),gN("scope",()=>vL),u8(({scope:t,state:r})=>t.addFinalizer(()=>u4(()=>(r.refCount--,r.refCount>0)?cr:void 0===e.idleTimeToLive?(e.state=NV,he(r.scope,hz)):gd(e.idleTimeToLive).pipe(uV,ch(u4(()=>"Acquired"===e.state._tag&&0===e.state.refCount?(e.state=NV,he(r.scope,hz)):cr)),v2(u6(()=>{r.fiber=void 0})),wt(e.scope),u8(e=>{r.fiber=e}),e.semaphore.withPermits(1))))),uG(({state:e})=>e.value)),NQ={_tag:"Left"},NX={_tag:"Right"},N0={_tag:"Both"},N1={_tag:"Either"},N2=e=>{switch(e){case"left":return NQ;case"right":return NX;case"both":return N0;case"either":return N1;default:return e}},N3=Symbol.for("effect/Sink"),N5={_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e};class N4{constructor(e){this[N3]=N5,this.channel=e}pipe(){return e3(this,arguments)}}let N6=e=>ea(e,N3),N8=e=>new N4(T3(()=>Aw(e()))),N7=(e,t)=>T0({onInput:r=>{let[i,n]=iV(r,e);return i.length<e?N7(e-i.length,iM(t,i)):iU(n)?T1(iM(t,i)):TW(T6(n),()=>T1(iM(t,i)))},onFailure:TK,onDone:()=>T1(t)}),N9=((e,t)=>J(e,N9(iK(t))),D(2,(e,t)=>{let r=TX({onInput:e=>J(T6(t(e)),TW(()=>r)),onFailure:TH,onDone:T1});return new N4(J(r,TZ(Aw(e))))})),Ae=D(2,(e,t)=>{let r=TX({onInput:e=>J(TY(t(e)),TW(T6),TW(()=>r)),onFailure:TH,onDone:T1});return new N4(J(r,NS(Aw(e))))}),At=new N4(R8(Na())),Ar=e=>new N4(TH(e)),Ai=(e,t,r)=>N8(()=>new N4(An(e,t,r))),An=(e,t,r)=>t(e)?TX({onInput:i=>{let[n,s]=As(e,i,t,r,0,i.length);return iq(s)?J(T6(s),R1(n)):An(n,t,r)},onFailure:TH,onDone:()=>T2(e)}):T2(e),As=(e,t,r,i,n,s)=>{if(n===s)return[e,iy()];let a=i(e,J(t,iF(n)));return r(a)?As(a,t,r,i,n+1,s):[a,J(t,iA(n+1))]},Aa=D(2,(e,t)=>new N4(J(Aw(e),T$,Nr({onFailure:e=>Aw(t.onFailure(e)),onSuccess:([e,r])=>T3(()=>{let i={ref:J(e,iP(iq))},n=J(T5(()=>{let e=i.ref;return i.ref=iy(),e}),TW(e=>ND(e)));return TW(T$(J(n,Nq(Na()),TZ(Aw(t.onSuccess(r))))),([e,t])=>J(T1(i.ref),TW(ND),Nq(ND(e)),R1(t)))})})))),Ao=(e,t,r)=>N8(()=>new N4(Al(e,t,r))),Al=(e,t,r)=>t(e)?TX({onInput:i=>Al(r(e,i),t,r),onFailure:TH,onDone:()=>T2(e)}):T2(e),Au=(e,t,r)=>N8(()=>new N4(Ac(e,t,r))),Ac=(e,t,r)=>t(e)?TX({onInput:i=>J(TY(Ah(e,i,t,r)),TW(([e,i])=>J(i,tX({onNone:()=>Ac(e,t,r),onSome:t=>J(T6(t),R1(e))})))),onFailure:TH,onDone:()=>T2(e)}):T2(e),Ah=(e,t,r,i)=>Ap(e,t,0,t.length,r,i),Ap=(e,t,r,i,n,s)=>r===i?u5([e,tQ()]):J(s(e,J(t,iF(r))),uM(e=>n(e)?Ap(e,t,r+1,i,n,s):u5([e,tI(J(t,iA(r+1)))]))),Af=D(2,(e,t)=>Aa(e,{onFailure:Ar,onSuccess:t})),Ad=e=>{let t=T0({onInput:r=>J(TY(vv(r,t=>e(t),{discard:!0})),TW(()=>t)),onFailure:TK,onDone:()=>T4});return new N4(t)},Am=e=>{let t=T0({onInput:r=>J(TY(e(r)),TW(()=>t)),onFailure:TK,onDone:()=>T4});return new N4(t)},Ag=(e,t,r,i,n)=>r===i?n:J(TY(e(J(t,iF(r)))),TW(s=>s?Ag(e,t,r+1,i,n):T6(J(t,iA(r)))),R3(e=>J(T6(J(t,iA(r))),Nq(TH(e))))),Ab=e=>new N4(e),Ax=e=>new N4(TY(e)),Ay=(e,t)=>t?.shutdown?Ak(uG(vd(u5(e),Fb),Ay)):Am(t=>J(I0(e,t))),Av=D(2,(e,t)=>new N4(J(Aw(e),Nu(t)))),AS=((e,t)=>new N4(J(Aw(e),Nc(t))),D(2,(e,t)=>AO(function(r){return k9(function*(){let i=yield*Tr(t?.capacity??16),n=yield*vK(To(i),r),s=yield*vK(To(i),r),a=NI(i),o=Nn(n).pipe(TZ(Aw(e)),NU(TY(Fb(n))),Ny({other:Nn(s).pipe(TZ(Aw(t.other)),NU(TY(Fb(s)))),onSelfDone:t.onSelfDone,onOtherDone:t.onOtherDone}));return new N4(Ny(a,{other:o,onSelfDone:()=>RF($),onOtherDone:e=>RI(e)}))})}))),A_=((e,t,r)=>new N4(J(e,Aw,R3(e=>tX(t(e),{onNone:()=>TV(()=>n7(r(e))),onSome:TH})))),(e,t,r=0)=>{let i;let n=e[Symbol.iterator](),s=0,a=-1;for(;a<0&&(i=n.next())&&!i.done;){let e=i.value;s>=r&&t(e)&&(a=s),s+=1}return a}),Aw=((e,t,r)=>new N4(J(TY(t),TW(i=>J(e,Aw,TW(e=>J(TY(t),Nu(t=>[e,r(i,t)]))))))),e=>uh(e)?Aw(Ax(e)):e.channel),Ak=e=>new N4(NM(e.pipe(uG(e=>Aw(e))))),AO=e=>new N4(Nz(t=>e(t).pipe(uG(e=>Aw(e))))),AC=(e=>N6(e[1]),D(e=>N6(e[1]),(e,t,r,i)=>i?.concurrent?AS(e,{other:t,onSelfDone:hA({onFailure:e=>RI(uR(e)),onSuccess:e=>RF(hA({onFailure:uR,onSuccess:t=>u5(r(e,t))}))}),onOtherDone:hA({onFailure:e=>RI(uR(e)),onSuccess:e=>RF(hA({onFailure:uR,onSuccess:t=>u5(r(t,e))}))})}):Af(e,e=>Av(t,t=>r(e,t)))));class AE{constructor(e){this.value=e}}let AI=(e,t)=>({ref:e,isNew:t,isChanged:!1,expected:e.versioned,newValue:e.versioned.value}),AF=e=>e.newValue,AT=(e,t)=>{e.isChanged=!0,e.newValue=t},AR=e=>{e.ref.versioned=new AE(e.newValue)},AN=e=>e.ref.versioned!==e.expected,AA=e=>e.isChanged,Aj="Invalid",AM="ReadWrite",Az=e=>{for(let t of e)AR(t[1])},AP=e=>{let t="ReadOnly";for(let[,r]of e)if((t=AN(r)?Aj:AA(r)?AM:t)===Aj)break;return t},AD=e=>{let t=new Map;for(let[,r]of e){for(let e of r.ref.todos)t.set(e[0],e[1]);r.ref.todos=new Map}return t},A$=e=>{for(let[t,r]of Array.from(e.entries()).sort((e,t)=>e[0]-t[0]))r()},AL=(e,t,r)=>{let i=!1;for(let[,n]of t)n.ref.todos.has(e)||(n.ref.todos.set(e,r),i=!0);return i},AU="WithSTMRuntime",Aq="OnFailure",AB="OnRetry",AJ="OnSuccess",AH="Sync",AK="Succeed",AV="Retry",AW="Fail",AG="Interrupt",AY="Fail",AZ="Interrupt",AQ="Succeed",AX="Retry",A0="Done",A1="Suspend",A2="Done",A3="Interrupted",A5="Running",A4="effect/STM/State",A6=Symbol.for(A4),A8=e=>ea(e,A6),A7=e=>e._tag===A5,A9=e=>e._tag===A2,je=e=>({[A6]:A6,_tag:A2,exit:e,[eI](){return J(eF(A4),eR(eF(A2)),eR(eF(e)),e$(this))},[eL]:t=>A8(t)&&t._tag===A2&&eU(e,t.exit)}),jt=J(eF(A4),eR(eF(A3)),eR(eF("interrupted"))),jr={[A6]:A6,_tag:A3,[eI]:()=>jt,[eL]:e=>A8(e)&&e._tag===A3},ji=J(eF(A4),eR(eF(A5)),eR(eF("running"))),jn={[A6]:A6,_tag:A5,[eI]:()=>ji,[eL]:e=>A8(e)&&e._tag===A5},js=e=>{switch(e._tag){case AY:return je(hE(e.error));case"Die":return je(hC(e.defect));case AZ:return je(hR(e.fiberId));case AQ:return je(hM(e.value));case AX:throw Error("BUG: STM.STMState.fromTExit - please report an issue at https://github.com/Effect-TS/effect/issues")}},ja="effect/TExit",jo=Symbol.for(ja),jl={_A:e=>e,_E:e=>e},ju=e=>ea(e,jo),jc=e=>e._tag===AQ,jh=e=>e._tag===AX,jp=e=>({[jo]:jl,_tag:AY,error:e,[eI](){return J(eF(ja),eR(eF(AY)),eR(eF(e)),e$(this))},[eL]:t=>ju(t)&&t._tag===AY&&eU(e,t.error)}),jf=e=>({[jo]:jl,_tag:"Die",defect:e,[eI](){return J(eF(ja),eR(eF("Die")),eR(eF(e)),e$(this))},[eL]:t=>ju(t)&&"Die"===t._tag&&eU(e,t.defect)}),jd=e=>({[jo]:jl,_tag:AZ,fiberId:e,[eI](){return J(eF(ja),eR(eF(AZ)),eR(eF(e)),e$(this))},[eL]:t=>ju(t)&&t._tag===AZ&&eU(e,t.fiberId)}),jm=e=>({[jo]:jl,_tag:AQ,value:e,[eI](){return J(eF(ja),eR(eF(AQ)),eR(eF(e)),e$(this))},[eL]:t=>ju(t)&&t._tag===AQ&&eU(e,t.value)}),jg=J(eF(ja),eR(eF(AX)),eR(eF("retry"))),jb={[jo]:jl,_tag:AX,[eI]:()=>jg,[eL]:e=>ju(e)&&jh(e)},jx=e=>({_tag:A0,exit:e}),jy=e=>({_tag:A1,journal:e}),jv={ref:0},jS=()=>{let e=jv.ref+1;return jv.ref=e,e},j_=Symbol.for("effect/STM"),jw={_R:e=>e,_E:e=>e,_A:e=>e};class jk{get[j_](){return jw}constructor(e){this._op=e4,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.effect_instruction_i0=e,this[ua]=tp,this[tu]=jw,this[N3]=jw,this[Tj]=jw}[eL](e){return this===e}[eI](){return e$(this,eT(this))}[Symbol.iterator](){return new ui(new ev(this))}commit(){return jO(this,B,B)}pipe(){return e3(this,arguments)}}let jO=(e,t,r)=>up(i=>{let n=i.id(),s=i.getFiberRef(cB),a=i.getFiberRef(mY),o=i.getFiberRef(cJ),l=jE(n,e,s,a,o);switch(l._tag){case A0:return t(l.exit),l.exit;case A1:{let i=jS(),l={value:jn},u=uy(t=>jI(n,e,i,l,s,a,o,t));return ct(e=>J(e(u),uv(e=>{let i=l.value;return(A7(i)&&(l.value=jr),A9(i=l.value))?(t(i.exit),i.exit):(r(),uR(e))})))}}}),jC=(e,t,r,i,n,s)=>{let a=new Map,o=new jR(t,a,e,i).run(),l=AP(a);if(l===AM)Az(a);else if(l===Aj)throw Error("BUG: STM.TryCommit.tryCommit - please report an issue at https://github.com/Effect-TS/effect/issues");switch(o._tag){case AQ:return r.value=js(o),jF(hM(o.value),a,n,s);case AY:return r.value=js(o),jF(hI(n8(o.error)),a,n,s);case"Die":return r.value=js(o),jF(hI(n7(o.defect)),a,n,s);case AZ:return r.value=js(o),jF(hI(n9(e)),a,n,s);case AX:return jy(a)}},jE=(e,t,r,i,n)=>{let s=new Map,a=new jR(t,s,e,r).run(),o=AP(s);if(o===AM&&jc(a))Az(s);else if(o===Aj)throw Error("BUG: STM.TryCommit.tryCommitSync - please report an issue at https://github.com/Effect-TS/effect/issues");switch(a._tag){case AQ:return jF(hM(a.value),s,i,n);case AY:return jF(hI(n8(a.error)),s,i,n);case"Die":return jF(hI(n7(a.defect)),s,i,n);case AZ:return jF(hI(n9(e)),s,i,n);case AX:return jy(s)}},jI=(e,t,r,i,n,s,a,o)=>{if(A7(i.value)){let l=jC(e,t,i,n,s,a);switch(l._tag){case A0:jT(l.exit,o);break;case A1:AL(r,l.journal,()=>jI(e,t,r,i,n,s,a,o))}}},jF=(e,t,r,i)=>{let n=AD(t);return n.size>0&&r.scheduleTask(()=>A$(n),i),jx(e)},jT=(e,t)=>{t(e)};class jR{constructor(e,t,r,i){this.contStack=[],this.self=e,this.journal=t,this.fiberId=r,this.env=i}getEnv(){return this.env}pushStack(e){this.contStack.push(e)}popStack(){return this.contStack.pop()}nextSuccess(){let e=this.popStack();for(;void 0!==e&&e.effect_instruction_i0!==AJ;)e=this.popStack();return e}nextFailure(){let e=this.popStack();for(;void 0!==e&&e.effect_instruction_i0!==Aq;)e=this.popStack();return e}nextRetry(){let e=this.popStack();for(;void 0!==e&&e.effect_instruction_i0!==AB;)e=this.popStack();return e}run(){let e,t=this.self;for(;void 0===e&&void 0!==t;)try{let r=t;if(r)switch(r._op){case"Tag":t=jM((e,t,i)=>s3(i,r));break;case"Left":t=jP(r.left);break;case"None":t=jP(new hf);break;case"Right":t=jH(r.right);break;case"Some":t=jH(r.value);break;case"Commit":switch(r.effect_instruction_i0){case"Die":e=jf(ek(()=>r.effect_instruction_i1()));break;case AW:{let i=this.nextFailure();void 0===i?e=jp(ek(()=>r.effect_instruction_i1())):t=ek(()=>i.effect_instruction_i2(ek(()=>r.effect_instruction_i1())));break}case AV:{let r=this.nextRetry();void 0===r?e=jb:t=ek(()=>r.effect_instruction_i2());break}case AG:e=jd(this.fiberId);break;case AU:t=ek(()=>r.effect_instruction_i1(this));break;case AJ:case Aq:case AB:this.pushStack(r),t=r.effect_instruction_i1;break;case"Provide":{let e=this.env;this.env=ek(()=>r.effect_instruction_i2(e)),t=J(r.effect_instruction_i1,jz(jK(()=>this.env=e)));break}case AK:{let i=r.effect_instruction_i1,n=this.nextSuccess();void 0===n?e=jm(i):t=ek(()=>n.effect_instruction_i2(i));break}case AH:{let i=ek(()=>r.effect_instruction_i1()),n=this.nextSuccess();void 0===n?e=jm(i):t=ek(()=>n.effect_instruction_i2(i))}}}}catch(e){t=jA(e)}return e}}let jN=D(2,(e,t)=>{let r=new jk(Aq);return r.effect_instruction_i1=e,r.effect_instruction_i2=t,r}),jA=e=>jj(()=>e),jj=e=>{let t=new jk("Die");return t.effect_instruction_i1=e,t},jM=e=>jU(t=>jH(e(t.journal,t.fiberId,t.getEnv()))),jz=D(2,(e,t)=>jL(e,{onFailure:e=>jV(t,jP(e)),onSuccess:e=>jV(t,jH(e))})),jP=e=>jD(()=>e),jD=e=>{let t=new jk(AW);return t.effect_instruction_i1=e,t},j$=D(2,(e,t)=>{let r=new jk(AJ);return r.effect_instruction_i1=e,r.effect_instruction_i2=t,r}),jL=D(2,(e,{onFailure:t,onSuccess:r})=>J(e,jB(tP),jN(e=>J(t(e),jB(tz))),j$(e=>{switch(e._tag){case"Left":return jH(e.left);case"Right":return r(e.right)}}))),jU=e=>{let t=new jk(AU);return t.effect_instruction_i1=e,t},jq=e=>{let t=new jk(AG);return t.effect_instruction_i1=e,t},jB=D(2,(e,t)=>J(e,j$(e=>jK(()=>t(e))))),jJ=new jk(AV),jH=e=>{let t=new jk(AK);return t.effect_instruction_i1=e,t},jK=e=>{let t=new jk(AH);return t.effect_instruction_i1=e,t},jV=D(2,(e,t)=>J(e,j$(()=>t))),jW=D(3,(e,t,r)=>J(e,j$(e=>J(t,jB(t=>r(e,t)))))),jG=((e,t)=>J(e,jB(()=>t)),e=>j$(e,$)),jY=D(e=>ep(e[0]),(e,t,r)=>r?.discard?J(jK(()=>e[Symbol.iterator]()),j$(e=>{let r=jZ(()=>{let i=e.next();return i.done?jQ:J(t(i.value),j$(()=>r))});return r})):jZ(()=>rw(e).reduce((e,r)=>jW(e,t(r),(e,t)=>(e.push(t),e)),jH([])))),jZ=e=>jG(jK(e)),jQ=jH(void 0),jX=Symbol.for("effect/TRef"),j0={_A:e=>e};class j1{constructor(e){this[jX]=j0,this.versioned=new AE(e),this.todos=new Map}modify(e){return jM(t=>{let r=j2(this,t),[i,n]=e(AF(r));return AT(r,n),i})}pipe(){return e3(this,arguments)}}let j2=(e,t)=>{if(t.has(e))return t.get(e);let r=AI(e,!1);return t.set(e,r),r},j3=D(2,(e,t)=>AF(j2(e,t))),j5=D(3,(e,t,r)=>{AT(j2(e,r),t)}),j4=Symbol.for("effect/TQueue/TEnqueue"),j6=Symbol.for("effect/TQueue/TDequeue"),j8={_Out:e=>e},j7={_In:e=>e},j9=((e,t,r)=>jZ(()=>{let i=(t,r,n)=>r<t?jH(n):J(e.takeUpTo(r),j$(s=>{let a=t-s.length;return 1===a?J(e.take,jB(e=>J(n,iM(iE(s)),iT(e)))):a>1?J(e.take,j$(e=>i(a-1,r-s.length-1,J(n,iM(iE(s)),iT(e))))):jH(J(n,iM(iE(s))))}));return jB(i(t,r,iy()),e=>Array.from(e))}),Symbol.for("effect/TPubSub")),Me=Symbol.for("effect/TPubSub/AbsentValue"),Mt=(e,t,r)=>({head:e,subscribers:t,tail:r});class Mr{static{f=j4}constructor(e,t,r,i,n,s,a){this[j9]={_A:e=>e},this[f]=j7,this.isShutdown=jM(e=>void 0===j3(this.publisherTail,e)),this.awaitShutdown=j$(this.isShutdown,e=>e?jQ:jJ),this.size=jU(e=>void 0===j3(this.publisherTail,e.journal)?jq(e.fiberId):jH(j3(this.pubsubSize,e.journal))),this.isEmpty=jB(this.size,e=>0===e),this.isFull=jB(this.size,e=>e===this.capacity()),this.shutdown=jM(e=>{void 0!==j3(this.publisherTail,e)&&(j5(this.publisherTail,void 0,e),nV(j3(this.subscribers,e),t=>{j5(t,void 0,e)}),j5(this.subscribers,nz(),e))}),this.pubsubSize=e,this.publisherHead=t,this.publisherTail=r,this.requestedCapacity=i,this.strategy=n,this.subscriberCount=s,this.subscribers=a}capacity(){return this.requestedCapacity}offer(e){return jU(t=>{let r=j3(this.publisherTail,t.journal);if(void 0===r)return jq(t.fiberId);let i=j3(this.subscriberCount,t.journal);if(0===i)return jH(!0);let n=j3(this.pubsubSize,t.journal);if(n<this.requestedCapacity){let s=new j1(void 0);return j5(r,Mt(e,i,s),t.journal),j5(this.publisherTail,s,t.journal),j5(this.pubsubSize,n+1,t.journal),jH(!0)}switch(this.strategy._tag){case"BackPressure":return jJ;case"Dropping":return jH(!1);case"Sliding":{if(this.requestedCapacity>0){let e=j3(this.publisherHead,t.journal),r=!0;for(;r;){let i=j3(e,t.journal);if(void 0===i)return jJ;let n=i.head,s=i.tail;n!==Me?(j5(e,Mt(Me,i.subscribers,i.tail),t.journal),j5(this.publisherHead,s,t.journal),r=!1):e=s}}let n=new j1(void 0);return j5(r,Mt(e,i,n),t.journal),j5(this.publisherTail,n,t.journal),jH(!0)}}})}offerAll(e){return jB(jY(e,e=>this.offer(e)),ii($))}}class Mi{static{d=j6}constructor(e,t,r,i,n,s){this[j9]=j9,this[d]=j8,this.isShutdown=jM(e=>void 0===j3(this.subscriberHead,e)),this.awaitShutdown=j$(this.isShutdown,e=>e?jQ:jJ),this.size=jU(e=>{let t=j3(this.subscriberHead,e.journal);if(void 0===t)return jq(e.fiberId);let r=!0,i=0;for(;r;){let n=j3(t,e.journal);if(void 0===n)r=!1;else{let e=n.head;e!==Me&&(i+=1)>=Number.MAX_SAFE_INTEGER&&(r=!1),t=n.tail}}return jH(i)}),this.isEmpty=jB(this.size,e=>0===e),this.isFull=jB(this.size,e=>e===this.capacity()),this.peek=jU(e=>{let t=j3(this.subscriberHead,e.journal);if(void 0===t)return jq(e.fiberId);let r=Me,i=!0;for(;i;){let n=j3(t,e.journal);if(void 0===n)return jJ;let s=n.head,a=n.tail;s!==Me?(r=s,i=!1):t=a}return jH(r)}),this.peekOption=jU(e=>{let t=j3(this.subscriberHead,e.journal);if(void 0===t)return jq(e.fiberId);let r=tQ(),i=!0;for(;i;){let n=j3(t,e.journal);if(void 0===n)r=tQ(),i=!1;else{let e=n.head,s=n.tail;e!==Me?(r=tI(e),i=!1):t=s}}return jH(r)}),this.shutdown=jM(e=>{let t=j3(this.subscriberHead,e);if(void 0!==t){j5(this.subscriberHead,void 0,e);let r=!0;for(;r;){let i=j3(t,e);if(void 0===i)r=!1;else{let r=i.head,n=i.tail;if(r!==Me){let s=i.subscribers;if(1===s){let r=j3(this.pubsubSize,e);j5(t,Mt(Me,0,n),e),j5(this.publisherHead,n,e),j5(this.pubsubSize,r-1,e)}else j5(t,Mt(r,s-1,n),e)}t=n}}let i=j3(this.subscriberCount,e);j5(this.subscriberCount,i-1,e),j5(this.subscribers,nJ(j3(this.subscribers,e),this.subscriberHead),e)}}),this.take=jU(e=>{let t=j3(this.subscriberHead,e.journal);if(void 0===t)return jq(e.fiberId);let r=Me,i=!0;for(;i;){let n=j3(t,e.journal);if(void 0===n)return jJ;let s=n.head,a=n.tail;if(s!==Me){let o=n.subscribers;if(1===o){let r=j3(this.pubsubSize,e.journal);j5(t,Mt(Me,0,a),e.journal),j5(this.publisherHead,a,e.journal),j5(this.pubsubSize,r-1,e.journal)}else j5(t,Mt(s,o-1,a),e.journal);j5(this.subscriberHead,a,e.journal),r=s,i=!1}else t=a}return jH(r)}),this.takeAll=this.takeUpTo(Number.POSITIVE_INFINITY),this.pubsubSize=e,this.publisherHead=t,this.requestedCapacity=r,this.subscriberHead=i,this.subscriberCount=n,this.subscribers=s}capacity(){return this.requestedCapacity}takeUpTo(e){return jU(t=>{let r=j3(this.subscriberHead,t.journal);if(void 0===r)return jq(t.fiberId);let i=[],n=0;for(;n!==e;){let s=j3(r,t.journal);if(void 0===s)n=e;else{let e=s.head,a=s.tail;if(e!==Me){let o=s.subscribers;if(1===o){let e=j3(this.pubsubSize,t.journal);j5(r,Mt(Me,0,a),t.journal),j5(this.publisherHead,a,t.journal),j5(this.pubsubSize,e-1,t.journal)}else j5(r,Mt(e,o-1,a),t.journal);i.push(e),n+=1}r=a}}return j5(this.subscriberHead,r,t.journal),jH(i)})}}class Mn{constructor(e){this.size=0,this.current=0,this.capacity=e,this.array=Array.from({length:e},B)}head(){return t3(this.array[this.current])}lastOrNull(){if(0===this.size)return;let e=0===this.current?this.array.length-1:this.current-1;return this.array[e]??void 0}put(e){this.array[this.current]=e,this.increment()}dropLast(){this.size>0&&(this.decrement(),this.array[this.current]=void 0)}toChunk(){let e=this.current-this.size;return i_(e<0?[...this.array.slice(this.capacity+e,this.capacity),...this.array.slice(0,this.current)]:this.array.slice(e,this.current))}increment(){this.size<this.capacity&&(this.size+=1),this.current=(this.current+1)%this.capacity}decrement(){this.size-=1,this.current>0?this.current-=1:this.current=this.capacity-1}}let Ms=Symbol.for("effect/Stream/Handoff"),Ma="Empty",Mo="Full",Ml=e=>({_tag:Ma,notifyConsumer:e}),Mu=(e,t)=>({_tag:Mo,value:e,notifyProducer:t}),Mc=(e,t)=>r=>{switch(r._tag){case Ma:return e(r.notifyConsumer);case Mo:return t(r.value,r.notifyProducer)}},Mh={_A:e=>e},Mp=()=>J(hq(),uM(e=>mL(Ml(e))),uG(e=>({[Ms]:Mh,ref:e}))),Mf=D(2,(e,t)=>uM(hq(),r=>uD(mJ(e.ref,i=>J(i,Mc(e=>[vD(hG(e,void 0),hJ(r)),Mu(t,r)],(r,n)=>[uM(hJ(n),()=>J(e,Mf(t))),i])))))),Md=e=>uM(hq(),t=>uD(mJ(e.ref,r=>J(r,Mc(t=>[uM(hJ(t),()=>Md(e)),r],(e,r)=>[ud(hG(r,void 0),e),Ml(t)]))))),Mm="Emit",Mg="Halt",Mb=e=>({_tag:Mm,elements:e}),Mx=e=>({_tag:Mg,cause:e}),My=e=>({_tag:"End",reason:e}),Mv=()=>uF(tQ()),MS=e=>uZ(uR(e),tI),M_="ScheduleEnd",Mw="UpstreamEnd",Mk={_tag:M_},MO={_tag:Mw},MC="DrainLeft",ME="DrainRight",MI="PullBoth",MF="PullLeft",MT="PullRight",MR={_tag:MC},MN={_tag:ME},MA={_tag:MI},Mj=e=>({_tag:MF,rightChunk:e}),MM=e=>({_tag:MT,leftChunk:e}),Mz="PullBoth",MP="PullLet",MD="PullRight",M$={_tag:Mz},ML=e=>({_tag:MP,rightChunk:e}),MU=e=>({_tag:MD,leftChunk:e}),Mq=Symbol.for("effect/Take"),MB={_A:e=>e,_E:e=>e};class MJ{constructor(e){this[Mq]=MB,this.exit=e}pipe(){return e3(this,arguments)}}let MH=e=>new MJ(hM(e)),MK=e=>u4(()=>e.exit),MV=new MJ(hE(tQ())),MW=e=>new MJ(hI(J(e,sx(tI)))),MG=D(2,(e,{onEnd:t,onFailure:r,onSuccess:i})=>hA(e.exit,{onFailure:e=>tX(v8(e),{onNone:t,onSome:r}),onSuccess:i})),MY=e=>new MJ(hM(iS(e))),MZ=Symbol.for("effect/Stream"),MQ={_R:e=>e,_E:e=>e,_A:e=>e};class MX{constructor(e){this[MZ]=MQ,this.channel=e}pipe(){return e3(this,arguments)}}let M0=e=>ea(e,MZ)||uh(e),M1=4096,M2=((e,t,r)=>zd(M2(e,t,r),e=>tq(e,{onLeft:tQ,onRight:tI})),D(3,(e,t,r)=>zO(vy([Mp(),mL(Mk),mL(iy()),_T(r),mL(!1),mL(!1)])).pipe(zm(([r,i,n,s,a,o])=>{let l=T0({onInput:e=>TW(TY(J(r,Mf(Mb(e)),g8(()=>iq(e)))),()=>l),onFailure:e=>TY(Mf(r,Mx(e))),onDone:()=>TY(Mf(r,My(MO)))}),u=J(mB(n,iy()),uM(e=>iq(e)?J(mq(a,!0),vD(u5(J(T6(e),TW(()=>u))))):J(Md(r),uG(e=>{switch(e._tag){case Mm:return J(TY(mq(a,!0)),Nq(T6(e.elements)),Nq(TY(mU(o))),TW(e=>e?T4:u));case Mg:return TK(e.cause);case"End":if(e.reason._tag===M_)return J(mU(a),uG(e=>e?TY(J(mq(i,Mk),vD(mq(o,!0)))):J(TY(J(mq(i,Mk),vD(mq(o,!0)))),TW(()=>u))),Nj);return J(mq(i,e.reason),vD(mq(o,!0)),TY)}}))),Nj),c=e=>s.next(e),h=(e,s,l)=>{let p=J(mq(a,!1),vD(mq(o,!1)),vD(J(u,NS(Aw(t)),T$,Nw,wt(l)))),f=(e,t,r)=>J(mq(n,iL(e)),vD(uG(mU(i),e=>{switch(e._tag){case M_:return J(vy([mU(a),p,J(c(tI(t)),wt(l))]),uG(([e,i,n])=>{let s=J(r,tX({onNone:()=>iS(tP(t)),onSome:e=>iv(tP(t),tz(e))}));return e?J(T6(s),TW(()=>h(i,n,l))):h(i,n,l)}),Nj);case Mw:return J(mU(a),uG(e=>e?T6(iS(tP(t))):T4),Nj)}})),Nj);return Nj(vQ(bO(e),bO(s),{onSelfDone:(e,t)=>J(cd(s),vD(J(u4(()=>e),uG(([e,t])=>f(e,t,tQ()))))),onOtherDone:(t,i)=>uL(u4(()=>t),{onFailure:t=>tq(sp(t),{onLeft:()=>J(r,Mf(My(Mk)),vk,vD(J(bO(e),uG(([e,t])=>f(e,t,tQ()))))),onRight:t=>J(r,Mf(Mx(t)),vk,vD(J(bO(e),uG(([e,t])=>f(e,t,tQ())))))}),onSuccess:t=>J(r,Mf(My(Mk)),vk,vD(J(bO(e),uG(([e,r])=>f(e,r,tI(t))))))})}))};return Pp(r=>TZ(zw(e),l).pipe(Nw,wt(r),vD(NS(u,Aw(t)).pipe(T$,Nw,wt(r),uM(e=>c(tQ()).pipe(wt(r),uG(t=>new MX(h(e,t,r)))))))))})))),M3=((e,t)=>uG(Pn(e,t),e=>zS(zE(e))),(e,t)=>{let r=(e,t)=>{let i=r=>J(mU(t),u8(hJ),vD(hq()),uM(i=>J(IX(e,[r,i]),vD(mq(t,i)),vD(hJ(i)))),um,TY);return T0({onInput:i=>J(hq(),uM(r=>J(IX(e,[MH(i),r]),uM(e=>J(mq(t,r),g8(()=>e))))),um,TY,TW(()=>r(e,t))),onFailure:e=>i(MW(e)),onDone:()=>i(MV)})},i=e=>{let t=J(TY(I1(e)),TW(([e,r])=>Nq(TY(hG(r,void 0)),MG(e,{onEnd:()=>T4,onFailure:TK,onSuccess:e=>J(T6(e),TW(()=>t))}))));return t};return NM(J(e,uM(e=>J(hq(),u8(e=>hG(e,void 0)),uM(n=>J(mL(n),uM(i=>J(t,TZ(r(e,i)),NO,wr)),ud(i(e))))))))}),M5=D(2,(e,t)=>M4(e,e=>tq(sp(e),{onLeft:t,onRight:zf}))),M4=D(2,(e,t)=>new MX(J(zw(e),TD(e=>zw(t(e)))))),M6=D(2,(e,t)=>J(e,M4(e=>J(t(e),t0(()=>zf(e)))))),M8=e=>uS(gO(e),e=>"None"===e._tag?gX:uF(e.value)),M7=D(4,(e,t,r,i)=>{let n=(e,t)=>Nq(TY(Md(t)),T0({onInput:r=>TW(TY(J(e,Mf(MH(r)))),()=>n(e,t)),onFailure:t=>TY(Mf(e,MW(t))),onDone:()=>TY(Mf(e,MV))}));return new MX(Nz(s=>vy([Mp(),Mp(),Mp(),Mp()]).pipe(u8(([t,r,i])=>TZ(zw(e),n(t,i)).pipe(Rk(s),wt(s))),u8(([e,r,i,a])=>TZ(zw(t),n(r,a)).pipe(Rk(s),wt(s))),uG(([e,t,n,s])=>{let a=Mf(n,void 0).pipe(vD(Md(e).pipe(uM(MK)))),o=Mf(s,void 0).pipe(vD(Md(t).pipe(uM(MK))));return zw(Pu(r,e=>uM(i(e,a,o),M8)))}))))}),M9=D(2,(e,t)=>new MX(J(zw(e),Nq(zw(t))))),ze=((e,t)=>zm(e,()=>t),e=>zO(uO(e))),zt=D(2,(e,t)=>J(hq(),uM(r=>J(e,zn({maximumLag:t.maximumLag,decide:e=>uM(hJ(r),t=>t(e))}),uM(e=>J(vy(iK(i0(0,t.size-1),t=>uG(e,([e,r])=>[[e,t],r]))),uG(iE),uM(e=>{let[i,n]=ir(e,[new Map,iy()],([e,t],[r,i])=>[e.set(r[0],r[1]),J(t,iR(i))]);return J(hG(r,e=>uG(t.decide(e),e=>t=>J(e(i.get(t))))),ud(Array.from(n)))}))))))),zr={ref:0},zi=()=>{let e=zr.ref;return zr.ref=e+1,e},zn=D(2,(e,t)=>zs(e,t.maximumLag,t.decide,()=>cr)),zs=D(4,(e,t,r,i)=>J(vd(mL(new Map),(e,t)=>J(mU(e),uM(e=>J(e.values(),vv(Fb))))),uM(n=>k9(function*(){let s=yield*Os(1),a=yield*mL(J(Fo(t),uM(e=>{let t=zi();return J(mH(n,r=>r.set(t,e)),ud([t,e]))}))),o=e=>s.withPermits(1)(J(mq(a,J(Fo(1),u8(t=>IX(t,e)),uM(e=>{let t=zi();return J(mH(n,r=>r.set(t,e)),ud(ry(t,e)))}))),vD(J(mU(n),uM(t=>J(i_(t.values()),vv(t=>J(IX(t,e),gE(e=>sa(e)?tI(cr):tQ()))))))),vD(i(e)),um));return yield*J(e,z1(e=>J(r(e),uM(t=>J(mU(n),uM(r=>J(r.entries(),gQ(iy(),(r,[i,n])=>t(i)?J(IX(n,hM(e)),uL({onFailure:e=>sa(e)?u5(J(r,iR(i))):uR(e),onSuccess:()=>u5(r)})):u5(r)),uM(e=>iq(e)?J(mH(n,t=>{for(let r of e)t.delete(r);return t})):cr))))),um)),uL({onFailure:e=>o(hI(J(e,sx(tI)))),onSuccess:()=>o(hE(tQ()))}),wr),s.withPermits(1)(uD(mU(a)))})))),za=e=>new MX(R8(zw(e))),zo=((e,t)=>{let r=e=>TX({onInput:t=>{let i=J(t,iA(e)),n=Math.max(0,e-t.length);return iU(t)||n>0?r(n):J(T6(i),Nq(Na()))},onFailure:TH,onDone:()=>T4});return new MX(J(zw(e),NS(r(t))))},new MX(T4)),zl=D(2,(e,t)=>new MX(J(zw(e),R7(t)))),zu=D(2,(e,t)=>new MX(TJ(zw(e),t))),zc=()=>zO(hX()),zh=e=>J(zc(),zm(e)),zp=e=>zC(uF(tI(e))),zf=e=>zO(uR(e)),zd=D(2,(e,t)=>zM(e,iz(t))),zm=D(e=>M0(e[0]),(e,t,r)=>{let i=r?.bufferSize??16;return r?.switch?zg(r?.concurrency,()=>zb(e,1,i,t),r=>zb(e,r,i,t)):zg(r?.concurrency,()=>new MX(R5(zw(e),e=>J(e,iK(e=>zw(t(e))),it(T4,(e,t)=>J(e,Nq(t)))))),i=>new MX(J(zw(e),R5(ND),Nx(e=>zw(t(e)),r))))}),zg=(e,t,r)=>{switch(e){case void 0:return t();case"unbounded":return r(Number.MAX_SAFE_INTEGER);default:return e>1?r(e):t()}},zb=D(4,(e,t,r,i)=>new MX(J(zw(e),R5(ND),Nx(e=>zw(i(e)),{concurrency:t,mergeStrategy:RB(),bufferSize:r})))),zx=D(e=>M0(e[0]),(e,t)=>zm(e,$,t)),zy=e=>{let t=T0({onInput:e=>TW(ND(e),()=>t),onFailure:TK,onDone:()=>T4});return new MX(J(zw(e),TZ(t)))},zv=e=>{let t=(e,t)=>{let[r,i]=J(e,iW(e=>!h_(e))),n=J(iB(i),tX({onNone:()=>t,onSome:hA({onFailure:e=>tX(v8(e),{onNone:()=>T4,onSome:TK}),onSuccess:()=>T4})}));return J(T6(J(r,iz(e=>h_(e)?tI(e.value):tQ()))),TW(()=>n))},r=T0({onInput:e=>t(e,r),onFailure:e=>TK(e),onDone:()=>T4});return new MX(J(zw(e),TZ(r)))},zS=e=>zy(zv(J(e,zA(e=>e.exit)))),z_=e=>new MX(e),zw=e=>{if("channel"in e)return e.channel;if(uh(e))return zw(zO(e));throw TypeError("Expected a Stream.")},zk=e=>new MX(iU(e)?T4:T6(e)),zO=e=>J(e,uZ(tI),zC),zC=e=>new MX(Nj(gz(e,{onFailure:tX({onNone:()=>T4,onSome:TH}),onSuccess:e=>T6(iS(e))}))),zE=(e,t)=>{let r=t?.maxChunkSize??M1;if(t?.scoped){let i=uG(To(e),e=>zR(e,{maxChunkSize:r,shutdown:!0}));return t.shutdown?uG(i,zl(Ta(e))):i}let i=zm(z6(To(e)),e=>zR(e,{maxChunkSize:r}));return t?.shutdown?zl(i,Ta(e)):i},zI=e=>z7(()=>ib(e)?zk(e):zF(e[Symbol.iterator]())),zF=(e,t=M1)=>J(u6(()=>{let r=[],i=e=>J(u6(()=>{let n=e.next();if(1===t)return n.done?T4:J(T6(iS(n.value)),TW(()=>i(e)));r=[];let s=0;for(;!1===n.done&&(r.push(n.value),!((s+=1)>=t));)n=e.next();return s>0?J(T6(iE(r)),TW(()=>i(e))):T4}),Nj);return new MX(i(e))}),Pc),zT=e=>J(e,uG(zG),Ph),zR=(e,t)=>J(I3(e,1,t?.maxChunkSize??M1),uv(t=>J(Fm(e),uM(e=>e&&sa(t)?Mv():MS(t)))),zG,t?.shutdown?zl(Fb(e)):$),zN=((e,t)=>{let r=e=>J(wb(e),uG(tX({onNone:()=>TX({onInput:t=>TW(T6(t),()=>r(e)),onFailure:TH,onDone:()=>T4}),onSome:hA({onFailure:TK,onSuccess:()=>T4})})),Nj);return new MX(Nz(i=>t.pipe(wt(i),uG(t=>zw(e).pipe(TZ(r(t)))))))},D(2,(e,t)=>new MX(J(zw(e),No(t))))),zA=((e,t)=>new MX(J(zw(e),Nl(t))),D(2,(e,t)=>new MX(J(zw(e),Nf(iK(t)))))),zj=D(3,(e,t,r)=>z7(()=>{let i=e=>TX({onInput:t=>J(u4(()=>{let n=[],s=e=>u6(()=>{n.push(e)});return J(t,gQ(e,(e,t)=>J(r(e,t),uM(([e,t])=>J(s(t),ud(e))))),gz({onFailure:e=>0!==n.length?Nq(T6(iE(n)),TH(e)):TH(e),onSuccess:e=>TW(T6(iE(n)),()=>i(e))}))}),Nj),onFailure:TH,onDone:()=>T4});return new MX(J(zw(e),NS(i(t))))})),zM=D(2,(e,t)=>new MX(J(zw(e),Nf(t)))),zz=((e,t)=>J(e,zM(iD(t))),D(2,(e,t)=>{let r=e=>{let i=e.next();return i.done?T0({onInput:e=>r(e[Symbol.iterator]()),onFailure:TK,onDone:T1}):Nj(uG(t(i.value),t=>TW(T6(iS(t)),()=>r(e))))};return new MX(J(zw(e),TZ(T3(()=>r(iy()[Symbol.iterator]())))))})),zP=D(3,(e,t,r)=>new MX(J(zw(e),R5(ND),Nm(r,t),Nf(iS)))),zD=D(2,(e,t)=>new MX(J(zw(e),Nh(t)))),z$=(e=>M0(e[1]),D(e=>Symbol.iterator in e[0],(e,t)=>zx(zI(e),t))),zL=D(2,(e,t)=>zU(e,t,{onSelf:tz,onOther:tP})),zU=D(3,(e,t,r)=>{let i=r.haltStrategy?N2(r.haltStrategy):N0,n=e=>t=>e||!h_(t)?RI(u4(()=>t)):RF(e=>u4(()=>e));return new MX(Ny(zw(zA(e,r.onSelf)),{other:zw(zA(t,r.onOther)),onSelfDone:n("Either"===i._tag||"Left"===i._tag),onOtherDone:n("Either"===i._tag||"Right"===i._tag)}))}),zq=((e,t)=>new MX(J(zw(e),Nv(()=>zw(t())))),D(2,(e,t)=>{let r=TX({onInput:e=>iU(e)?T3(()=>r):J(T6(e),Nq(Na())),onFailure:TH,onDone:()=>T3(()=>zw(t()))});return new MX(J(zw(e),TZ(r)))})),zB=(e=>"function"==typeof e[1],D(2,(e,t)=>new MX(TZ(zw(e),t)))),zJ=D(2,(e,t)=>new MX(J(zw(e),TQ(t)))),zH=((e,t)=>new MX(Nz(r=>wQ(t,r).pipe(uG(t=>J(zw(e),TQ(t)))))),D(3,(e,t,r)=>zh(i=>zm(r,r=>J(e,zJ(sX(i,t,r))))))),zK=((e,t)=>zh(r=>J(e,zJ(t(r)))),D(2,(e,t)=>z7(()=>{let r=Math.max(t,1),i=zV(new zW(r),r);return new MX(J(zw(e),TZ(i)))}))),zV=(e,t)=>T0({onInput:r=>{if(r.length===t&&e.isEmpty())return TW(T6(r),()=>zV(e,t));if(r.length>0){let i;let n=[],s=0;for(;s<r.length;){for(;s<r.length&&void 0===i;)i=e.write(J(r,iF(s))),s+=1;void 0!==i&&(n.push(i),i=void 0)}return TW(NP(...n),()=>zV(e,t))}return T3(()=>zV(e,t))},onFailure:t=>Nq(e.emitIfNotEmpty(),TK(t)),onDone:()=>e.emitIfNotEmpty()});class zW{constructor(e){this.builder=[],this.pos=0,this.n=e}isEmpty(){return 0===this.pos}write(e){if(this.builder.push(e),this.pos+=1,this.pos===this.n){let e=iE(this.builder);return this.builder=[],this.pos=0,e}}emitIfNotEmpty(){return 0!==this.pos?T6(iE(this.builder)):T4}}(e,t,r)=>new MX(R3(zw(e),e=>tX(t(e),{onNone:()=>TK(n7(r(e))),onSome:TH})));let zG=e=>Pu(e,e=>J(uG(e,t=>tI([t,e])),uS(tX({onNone:()=>u5(tQ()),onSome:uF})))),zY=e=>zG(J(e,uG(iS))),zZ=((e,t)=>zZ(e,t,{onElement:e=>tP(e),onSchedule:tz}),D(3,(e,t,r)=>J(_T(t),uG(t=>{let i=J(t.last,u1,uG(r.onSchedule)),n=J(e,zA(r.onElement),zw),s=Nj(gz(t.next(void 0),{onFailure:()=>T4,onSuccess:()=>J(n,Nq(J(i,uG(e=>J(T6(iS(e)),TW(()=>s))),Nj)))}));return new MX(J(n,Nq(s)))}),Pc))),zQ=D(2,(e,t)=>zw(e).pipe(NS(Aw(t)),Nk)),zX=((e,t,r,i)=>zQ(e,Ai(t,r,i)),D(2,(e,t)=>zQ(e,Ad(t)))),z0=D(2,(e,t)=>zQ(e,Am(t))),z1=D(2,(e,t)=>J(e,z4(Ad(t)))),z2=D(2,(e,t)=>J(e,z5(t))),z3=((e,t)=>J(e,z5(t),Oi),D(2,(e,t)=>{let r=T0({onInput:e=>TW(TY(I0(t,iK(e,hM))),()=>r),onFailure:e=>TY(IX(t,hI(sx(e,tI)))),onDone:()=>TY(IX(t,hE(tQ())))});return J(TZ(zw(e),r),R8,NO,um)})),z5=D(2,(e,t)=>{let r=T0({onInput:e=>TW(T6(MH(e)),()=>r),onFailure:e=>T6(MW(e)),onDone:()=>T6(MV)});return J(TZ(zw(e),r),Nd(e=>IX(t,e)),R8,NO,um)}),z4=D(2,(e,t)=>J(zw(e),NS(Aw(t)),R8,NO)),z6=((e,t)=>J(e,zj(tQ(),(e,r)=>{switch(e._tag){case"None":return u5([tI(r),r]);case"Some":return J(t(e.value,r),uG(e=>[tI(e),e]))}})),e=>new MX(R7(NC(J(e,uG(iS))),cr))),z8=e=>new MX(NE(t=>e(t).pipe(uG(iS)))),z7=((e,t,r)=>t<=0||r<=0?ze(new hh("Invalid bounds - `chunkSize` and `stepSize` must be greater than zero")):new MX(T3(()=>{let i=new Mn(t),n=(e,n)=>{if(e<t){let e=i.toChunk();return J(T6(iU(e)?iy():iS(e)),TW(()=>n))}let s=e-(e-t)%r;if(s===e)return n;let a=J(i.toChunk(),iY(e-(s-t+r)));return J(T6(iU(a)?iy():iS(a)),TW(()=>n))},s=e=>T0({onInput:n=>TW(T6(iz(n,(n,s)=>{i.put(n);let a=e+s+1;return a<t||(a-t)%r>0?tQ():tI(i.toChunk())})),()=>s(e+n.length)),onFailure:t=>n(e,TK(t)),onDone:()=>n(e,T4)});return J(zw(e),TZ(s(0)))})),e=>new MX(T3(()=>zw(e())))),z9=D(2,(e,t)=>{let r=TX({onInput:e=>{let i=J(e,iZ(t));return i.length===e.length?J(T6(i),TW(()=>r)):T6(i)},onFailure:TH,onDone:T1});return new MX(J(zw(e),NS(r)))}),Pe=((e,t)=>zz(e,e=>ud(t(e),e)),(e,t,r,i,n)=>{let s=(e,a)=>T0({onInput:o=>J(t(o),vz(fF),uG(([t,l])=>{let u=e+(l-a)/aE(i)*r,c=r+n<0?Number.POSITIVE_INFINITY:r+n,h=u<0?c:Math.min(u,c);return t<=h?J(T6(o),TW(()=>s(h-t,l))):s(e,a)}),Nj),onFailure:TK,onDone:()=>T4}),a=J(fF,uG(e=>s(r,e)),Nj);return new MX(J(zw(e),NS(a)))}),Pt=(e,t,r,i,n)=>{let s=(e,a)=>T0({onInput:o=>J(t(o),vz(fF),uG(([t,l])=>{let u=e+(l-a)/aE(i)*r,c=r+n<0?Number.POSITIVE_INFINITY:r+n,h=(u<0?c:Math.min(u,c))-t,p=aS(Math.max(0,(h>=0?0:-h/r)*aE(i)));return aP(p,ab)?J(TY(gd(p)),Nq(T6(o)),TW(()=>s(h,l))):TW(T6(o),()=>s(h,l))}),Nj),onFailure:TK,onDone:()=>T4}),a=J(fF,uG(e=>s(r,e)),Nj);return new MX(J(zw(e),NS(a)))},Pr=D(3,(e,t,r)=>J(Ps(e),uG(ws({onTimeout:()=>sx(t(),tI),duration:r})),zT)),Pi=((e,t,r)=>{let i=new ha("Stream Timeout");return J(e,Pr(()=>n7(i),t),M6(e=>sn(e)&&Se(e.defect)&&void 0!==e.defect.message&&"Stream Timeout"===e.defect.message?tI(r):tQ()))},e=>{if("number"==typeof e)return Tr(e);if("unbounded"===e.capacity)return Ts({replay:e.replay});switch(e.strategy){case"dropping":return Ti(e);case"sliding":return Tn(e);default:return Tr(e)}}),Pn=D(2,(e,t)=>J(vd(Pi(t),e=>Ta(e)),u8(t=>J(e,z2(t),wr)))),Ps=e=>uG(NF(zw(e)),e=>J(e,uZ(tI),uM(tq({onLeft:()=>uF(tQ()),onRight:u5})))),Pa=D(e=>M0(e[0]),(e,t)=>u8(vd(t?.strategy==="unbounded"?Fc():t?.strategy==="dropping"?Fl(t.capacity??2):t?.strategy==="sliding"?Fu(t.capacity??2):Fo(t?.capacity??2),e=>Fb(e)),t=>wr(z5(e,t)))),Po=(e=>M0(e[0]),D(e=>M0(e[0]),(e,t)=>uG(wj(),r=>Pl(e,r,t)))),Pl=D(e=>M0(e[0]),(e,t,r)=>{let i,n;let s=wy(t),a=Oa(!1);return new ReadableStream({start(t){(n=s(z0(e,e=>a.whenOpen(u6(()=>{for(let r of(a.unsafeClose(),e))t.enqueue(r);i(),i=void 0}))))).addObserver(e=>{"Failure"===e._tag?t.error(v7(e.cause)):t.close()})},pull:()=>new Promise(e=>{i=e,wL(a.open)}),cancel(){if(n)return wD(um(cd(n)))}},r?.strategy)}),Pu=(e,t)=>z7(()=>{let r=e=>Nj(uG(t(e),tX({onNone:()=>T4,onSome:([e,t])=>TW(T6(e),()=>r(t))})));return new MX(r(e))}),Pc=e=>zx(zO(e)),Ph=e=>zx(z6(e)),Pp=e=>zx(z8(t=>e(t))),Pf=((e,t)=>J(zO(t),zm(t=>t?e:zo)),D(3,(e,t,r)=>{let i=(e,t,r)=>{switch(e._tag){case Mz:return J(M8(t),vz(M8(r),{concurrent:!0}),uU({onFailure:e=>u5(hE(tI(e))),onSuccess:([e,s])=>tC(e)&&tC(s)?iU(e.value)&&iU(s.value)?i(M$,t,r):iU(e.value)?i(ML(s.value),t,r):iU(s.value)?i(MU(e.value),t,r):u5(hM(n(e.value,s.value))):u5(hE(tQ()))}));case MP:return uU(t,{onFailure:e=>u5(hE(e)),onSuccess:s=>iU(s)?i(ML(e.rightChunk),t,r):iU(e.rightChunk)?i(MU(s),t,r):u5(hM(n(s,e.rightChunk)))});case MD:return uU(r,{onFailure:e=>u5(hE(e)),onSuccess:s=>iU(s)?i(MU(e.leftChunk),t,r):iU(e.leftChunk)?i(ML(s),t,r):u5(hM(n(e.leftChunk,s)))})}},n=(e,t)=>{let[i,n]=r(e,t);switch(n._tag){case"Left":if(iU(n.left))return[i,M$];return[i,MU(n.left)];case"Right":if(iU(n.right))return[i,M$];return[i,ML(n.right)]}};return J(e,M7(t,M$,i))})),Pd=(e,t,r)=>e.length>t.length?[J(e,iN(t.length),iQ(t,r)),tz(J(e,iA(t.length)))]:[J(e,iQ(J(t,iN(e.length)),r)),tP(J(t,iA(e.length)))],Pm=D(e=>M0(e[0]),(e,t="utf-8")=>z7(()=>{let r=new TextDecoder(t);return zA(e,e=>r.decode(e))})),Pg=Symbol.for("effect/GroupBy"),Pb={_R:e=>e,_E:e=>e,_K:e=>e,_V:e=>e},Px=e=>ea(e,Pg),Py=D(e=>Px(e[0]),(e,t,r)=>zm(e.grouped,([e,r])=>t(e,zS(zR(r,{shutdown:!0}))),{concurrency:"unbounded",bufferSize:r?.bufferSize??16})),Pv=e=>({[Pg]:Pb,pipe(){return e3(this,arguments)},grouped:e}),PS=D(e=>"function"!=typeof e[0],(e,t,r)=>r?.key?Py(Pw(e,r.key,{bufferSize:r.bufferSize}),(e,r)=>zz(r,t)):zg(r?.concurrency,()=>zz(e,t),i=>r?.unordered?zm(e,e=>zO(t(e)),{concurrency:i}):zP(e,i,t)));class P_ extends fP{static{m=IL}constructor(e,t){super(),this[m]={_Out:e=>e},this.dequeue=e,this.f=t}capacity(){return Fh(this.dequeue)}get size(){return Fp(this.dequeue)}unsafeSize(){return this.dequeue.unsafeSize()}get awaitShutdown(){return Fg(this.dequeue)}isActive(){return this.dequeue.isActive()}get isShutdown(){return Fm(this.dequeue)}get shutdown(){return Fb(this.dequeue)}get isFull(){return Fd(this.dequeue)}get isEmpty(){return Ff(this.dequeue)}get take(){return J(I1(this.dequeue),uG(e=>this.f(e)))}get takeAll(){return J(Fy(this.dequeue),uG(iK(e=>this.f(e))))}takeUpTo(e){return J(I2(this.dequeue,e),uG(iK(e=>this.f(e))))}takeBetween(e,t){return J(I3(this.dequeue,e,t),uG(iK(e=>this.f(e))))}takeN(e){return J(I5(this.dequeue,e),uG(iK(e=>this.f(e))))}poll(){return J(Fx(this.dequeue),uG(t7(e=>this.f(e))))}pipe(){return e3(this,arguments)}commit(){return this.take}}let Pw=D(e=>"function"!=typeof e[0],(e,t,r)=>{let i=(e,n)=>T0({onInput:s=>TW(TY(vv(Pk(s,t),([t,i])=>{let s=e.get(t);return void 0===s?J(Fo(r?.bufferSize??16),uM(r=>J(u6(()=>{e.set(t,r)}),vD(IX(n,MY([t,r]))),vD(J(IX(r,MH(i)),gE(e=>so(e)?tI(cr):tQ())))))):gE(IX(s,MH(i)),e=>so(e)?tI(cr):tQ())},{discard:!0})),()=>i(e,n)),onFailure:e=>TY(IX(n,MW(e))),onDone:()=>J(TY(J(vv(e.entries(),([e,t])=>J(IX(t,MV),gE(e=>so(e)?tI(cr):tQ())),{discard:!0}),vD(IX(n,MV)))))});return Pv(Pp(t=>k9(function*(){let r=new Map,n=yield*Fc();return yield*c7(t,Fb(n)),yield*zw(e).pipe(TZ(i(r,n)),R8,Rk(t),wt(t),ud(zS(zR(n,{shutdown:!0}))))})))}),Pk=D(2,(e,t)=>{let r;let i=[],n=e[Symbol.iterator](),s=new Map;for(;(r=n.next())&&!r.done;){let e=r.value,n=t(e);if(s.has(n))s.get(n).push(e);else{let t=[e];i.push([n,t]),s.set(n,t)}}return iE(i.map(e=>[e[0],iE(e[1])]))}),PO=(...e)=>{let t=1===e.length?e[0].evaluate:e[0],r=1===e.length?e[0].onError:e[1],i=1===e.length&&!0===e[0].releaseLockOnEnd;return Ph(uG(vd(u6(()=>t().getReader()),e=>i?u6(()=>e.releaseLock()):Oe(()=>e.cancel())),e=>zY(uM(g5({try:()=>e.read(),catch:e=>tI(r(e))}),({done:e,value:t})=>e?uF(tQ()):u5(t)))))},PC=e=>zQ(e,At),PE=Symbol.for("@effect/platform/Cookies"),PI=e=>ea(e,PE),PF=Symbol.for("@effect/platform/Cookies/Cookie"),PT=Symbol.for("@effect/platform/Cookies/CookieError");class PR extends Og(PT,"CookieError"){get message(){return this.reason}}let PN={[PE]:PE,...eW,toJSON(){return{_id:"@effect/platform/Cookies",cookies:rm(this.cookies,e=>e.toJSON())}},pipe(){return e3(this,arguments)}},PA=e=>{let t=Object.create(PN);return t.cookies=e,t},Pj=e=>{let t={};for(let r of e)t[r.name]=r;return PA(t)},PM=e=>{let t=[];for(let r of"string"==typeof e?[e]:e){let e=function(e){let t=e.split(";").map(e=>e.trim()).filter(e=>""!==e);if(0===t.length)return tQ();let r=t[0].indexOf("=");if(-1===r)return tQ();let i=t[0].slice(0,r);if(!PD.test(i))return tQ();let n=t[0].slice(r+1),s=PH(n);if(1===t.length)return tI(Object.assign(Object.create(P$),{name:i,value:s,valueEncoded:n}));let a={};for(let e=1;e<t.length;e++){let r=t[e],i=r.indexOf("="),n=-1===i?r:r.slice(0,i).trim(),s=-1===i?void 0:r.slice(i+1).trim();switch(n.toLowerCase()){case"domain":{if(void 0===s)break;let e=s.trim().replace(/^\./,"");e&&(a.domain=e);break}case"expires":{if(void 0===s)break;let e=new Date(s);isNaN(e.getTime())||(a.expires=e);break}case"max-age":{if(void 0===s)break;let e=parseInt(s,10);isNaN(e)||(a.maxAge=a_(e));break}case"path":if(void 0===s)break;"/"===s[0]&&(a.path=s);break;case"priority":if(void 0===s)break;switch(s.toLowerCase()){case"low":a.priority="low";break;case"medium":a.priority="medium";break;case"high":a.priority="high"}break;case"httponly":a.httpOnly=!0;break;case"secure":a.secure=!0;break;case"partitioned":a.partitioned=!0;break;case"samesite":if(void 0===s)break;switch(s.toLowerCase()){case"lax":a.sameSite="lax";break;case"strict":a.sameSite="strict";break;case"none":a.sameSite="none"}}}return tI(Object.assign(Object.create(P$),{name:i,value:s,valueEncoded:n,options:Object.keys(a).length>0?a:void 0}))}(r.trim());tC(e)&&t.push(e.value)}return Pj(t)},Pz=Pj([]),PP=e=>rp(e.cookies),PD=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,P$={[PF]:PF,...eW,toJSON(){return{_id:"@effect/platform/Cookies/Cookie",name:this.name,value:this.value,options:this.options}}};function PL(e,t,r){if(!PD.test(e))return tz(new PR({reason:"InvalidName"}));let i=encodeURIComponent(t);if(i&&!PD.test(i))return tz(new PR({reason:"InvalidValue"}));if(void 0!==r){if(void 0!==r.domain&&!PD.test(r.domain))return tz(new PR({reason:"InvalidDomain"}));if(void 0!==r.path&&!PD.test(r.path))return tz(new PR({reason:"InvalidPath"}));if(void 0!==r.maxAge&&!am(au(r.maxAge)))return tz(new PR({reason:"InfinityMaxAge"}))}return tP(Object.assign(Object.create(P$),{name:e,value:t,valueEncoded:i,options:r}))}let PU=(e,t,r)=>tJ(PL(e,t,r),$),Pq=((e,t)=>PA({...e.cookies,...t.cookies}),D(2,(e,t)=>{let r={...e.cookies};for(let[e,i,n]of t){let t=PL(e,i,n);if(tj(t))return t;r[e]=t.right}return tP(PA(r))}));function PB(e){let t=e.name+"="+e.valueEncoded;if(void 0===e.options)return t;let r=e.options;if(void 0!==r.maxAge&&(t+="; Max-Age="+Math.trunc(aI(r.maxAge))),void 0!==r.domain&&(t+="; Domain="+r.domain),void 0!==r.path&&(t+="; Path="+r.path),void 0!==r.priority)switch(r.priority){case"low":t+="; Priority=Low";break;case"medium":t+="; Priority=Medium";break;case"high":t+="; Priority=High"}if(void 0!==r.expires&&(t+="; Expires="+r.expires.toUTCString()),r.httpOnly&&(t+="; HttpOnly"),r.secure&&(t+="; Secure"),r.partitioned&&(t+="; Partitioned"),void 0!==r.sameSite)switch(r.sameSite){case"lax":t+="; SameSite=Lax";break;case"strict":t+="; SameSite=Strict";break;case"none":t+="; SameSite=None"}return t}(e,t)=>tJ(Pq(e,t),$);let PJ=e=>Object.values(e.cookies).map(PB),PH=e=>{try{return decodeURIComponent(e)}catch(t){return e}},PK="effect/Redacted",PV=V("effect/Redacted/redactedRegistry",()=>new WeakMap),PW=Symbol.for(PK),PG={[PW]:{_A:e=>e},pipe(){return e3(this,arguments)},toString:()=>"<redacted>",toJSON:()=>"<redacted>",[eH]:()=>"<redacted>",[eI](){return J(eF(PK),eR(eF(PV.get(this))),e$(this))},[eL](e){return PY(e)&&eU(PV.get(this),PV.get(e))}},PY=e=>ea(e,PW),PZ=e=>{let t=Object.create(PG);return PV.set(t,e),t},PQ=e=>{if(PV.has(e))return PV.get(e);throw Error("Unable to get redacted value")},PX=e=>tK((t,r)=>e(PQ(t),PQ(r))),P0=/^[+-]?\d+$/,P1=Symbol.for("effect/BigDecimal"),P2={[P1]:P1,[eI](){let e=P9(this);return J(eF(e.value),eR(ej(e.scale)),e$(this))},[eL](e){return P3(e)&&Di(this,e)},toString(){return`BigDecimal(${Da(this)})`},toJSON(){return{_id:"BigDecimal",value:String(this.value),scale:this.scale}},[eH](){return this.toJSON()},pipe(){return e3(this,arguments)}},P3=e=>ea(e,P1),P5=(e,t)=>{let r=Object.create(P2);return r.value=e,r.scale=t,r},P4=(e,t)=>{if(e!==P6&&e%P8===P6)throw RangeError("Value must be normalized");let r=P5(e,t);return r.normalized=r,r},P6=BigInt(0),P8=BigInt(10),P7=P4(P6,0),P9=e=>{if(void 0===e.normalized){if(e.value===P6)e.normalized=P7;else{let t=`${e.value}`,r=0;for(let e=t.length-1;e>=0;e--)if("0"===t[e])r++;else break;0===r&&(e.normalized=e);let i=BigInt(t.substring(0,t.length-r)),n=e.scale-r;e.normalized=P4(i,n)}}return e.normalized},De=D(2,(e,t)=>t>e.scale?P5(e.value*P8**BigInt(t-e.scale),t):t<e.scale?P5(e.value/P8**BigInt(e.scale-t),t):e),Dt=e=>e.value<P6?P5(-e.value,e.scale):e,Dr=tK((e,t)=>e.scale>t.scale?De(t,e.scale).value===e.value:e.scale<t.scale?De(e,t.scale).value===t.value:e.value===t.value),Di=D(2,(e,t)=>Dr(e,t)),Dn=e=>{if(!Number.isFinite(e))return tQ();let t=`${e}`;if(t.includes("e"))return Ds(t);let[r,i=""]=t.split(".");return tI(P5(BigInt(`${r}${i}`),i.length))},Ds=e=>{let t,r,i,n;if(""===e)return tI(P7);let s=e.search(/[eE]/);if(-1!==s){let i=e.slice(s+1);if(t=e.slice(0,s),r=Number(i),""===t||!Number.isSafeInteger(r)||!P0.test(i))return tQ()}else t=e,r=0;let a=t.search(/\./);if(-1!==a){let e=t.slice(0,a),r=t.slice(a+1);i=`${e}${r}`,n=r.length}else i=t,n=0;if(!P0.test(i))return tQ();let o=n-r;return Number.isSafeInteger(o)?tI(P5(BigInt(i),o)):tQ()},Da=e=>{let t,r;let i=P9(e);if(Math.abs(i.scale)>=16)return Do(i);let n=i.value<P6,s=n?`${i.value}`.substring(1):`${i.value}`;if(i.scale>=s.length)t="0",r="0".repeat(i.scale-s.length)+s;else{let e=s.length-i.scale;if(e>s.length){let i=e-s.length;t=`${s}${"0".repeat(i)}`,r=""}else r=s.slice(e),t=s.slice(0,e)}let a=""===r?t:`${t}.${r}`;return n?`-${a}`:a},Do=e=>{if(Dl(e))return"0e+0";let t=P9(e),r=`${Dt(t).value}`,i=r.slice(0,1),n=r.slice(1),s=`${Du(t)?"-":""}${i}`;""!==n&&(s+=`.${n}`);let a=n.length-t.scale;return`${s}e${a>=0?"+":""}${a}`},Dl=e=>e.value===P6,Du=e=>e.value<P6,Dc=e=>{try{return""===e.trim()?tQ():tI(BigInt(e))}catch(e){return tQ()}},Dh=e=>pS(e,void 0,pv);({...PG});let Dp=Symbol.for("effect/Config"),Df={...tm,[Dp]:{_A:e=>e},commit(){return fR(this)}},Dd=D(2,(e,t)=>Dg(e,e=>tP(t(e)))),Dm=D(2,(e,t)=>Dg(e,e=>{try{return tP(t(e))}catch(e){return tz(pm([],e instanceof Error?e.message:`${e}`))}})),Dg=D(2,(e,t)=>{let r=Object.create(Df);return r._tag=pR,r.original=e,r.mapOrFail=t,r}),Db=D(2,(e,t)=>{let r=Object.create(Df);return r._tag=pN,r.name=t,r.config=e,r}),Dx=D(2,(e,t)=>{let r=Object.create(Df);return r._tag=pF,r.first=e,r.second=D_(t),r.condition=U,r}),Dy=D(2,(e,t)=>{let r=Object.create(Df);return r._tag=pF,r.first=e,r.second=D_(t.orElse),r.condition=t.if,r}),Dv=(e,t)=>{let r=Object.create(Df);return r._tag=pA,r.description=e,r.parse=t,r},DS=e=>{let t=Object.create(Df);return t._tag=pI,t.value=e,t.parse=()=>tP(e),t},D_=e=>{let t=Object.create(Df);return t._tag=pT,t.config=e,t},Dw=D(2,(e,t)=>Dy(e,{orElse:()=>DS(t),if:Dh})),Dk=D(3,(e,t,r)=>{let i=Object.create(Df);return i._tag=pj,i.left=e,i.right=t,i.zip=r,i}),DO=e=>{let t=Dv("a text property",tP);return void 0===e?t:Db(t,e)},DC=Sc,DE=e=>Sh(e)&&"Offset"===e._tag,DI=e=>Sh(e)&&"Named"===e._tag,DF=e=>"Utc"===e._tag,DT=Sp,DR=Sf,DN=Sm,DA=Sb,Dj=Symbol.for("effect/Encoding/errors/Decode"),DM=(e,t)=>{let r={_tag:"DecodeException",[Dj]:Dj,input:e};return W(t)&&(r.message=t),r},Dz=Symbol.for("effect/Encoding/errors/Encode"),DP=new TextEncoder,DD=e=>{let t;let r=e.length,i="";for(t=2;t<r;t+=3)i+=Dq[e[t-2]>>2],i+=Dq[(3&e[t-2])<<4|e[t-1]>>4],i+=Dq[(15&e[t-1])<<2|e[t]>>6],i+=Dq[63&e[t]];return t===r+1&&(i+=Dq[e[t-2]>>2],i+=Dq[(3&e[t-2])<<4],i+="=="),t===r&&(i+=Dq[e[t-2]>>2],i+=Dq[(3&e[t-2])<<4|e[t-1]>>4],i+=Dq[(15&e[t-1])<<2],i+="="),i},D$=e=>{let t=DL(e),r=t.length;if(r%4!=0)return tz(DM(t,`Length must be a multiple of 4, but is ${r}`));let i=t.indexOf("=");if(-1!==i&&(i<r-2||i===r-2&&"="!==t[r-1]))return tz(DM(t,"Found a '=' character, but it is not at the end"));try{let e=t.endsWith("==")?2:+!!t.endsWith("="),i=new Uint8Array(r/4*3-e);for(let e=0,n=0;e<r;e+=4,n+=3){let r=DU(t.charCodeAt(e))<<18|DU(t.charCodeAt(e+1))<<12|DU(t.charCodeAt(e+2))<<6|DU(t.charCodeAt(e+3));i[n]=r>>16,i[n+1]=r>>8&255,i[n+2]=255&r}return tP(i)}catch(e){return tz(DM(t,e instanceof Error?e.message:"Invalid input"))}},DL=e=>e.replace(/[\n\r]/g,"");function DU(e){if(e>=DB.length)throw TypeError(`Invalid character ${String.fromCharCode(e)}`);let t=DB[e];if(255===t)throw TypeError(`Invalid character ${String.fromCharCode(e)}`);return t}let Dq=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/"],DB=[255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,62,255,255,255,63,52,53,54,55,56,57,58,59,60,61,255,255,255,0,255,255,255,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,255,255,255,255,255,255,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],DJ=e=>{let t=DL(e),r=t.length;if(r%4==1)return tz(DM(t,`Length should be a multiple of 4, but is ${r}`));if(!/^[-_A-Z0-9]*?={0,2}$/i.test(t))return tz(DM(t,"Invalid input"));let i=r%4==2?`${t}==`:r%4==3?`${t}=`:t;return D$(i=i.replace(/-/g,"+").replace(/_/g,"/"))},DH=e=>{let t="";for(let r=0;r<e.length;++r)t+=DV[e[r]];return t},DK=e=>{let t=new TextEncoder().encode(e);if(t.length%2!=0)return tz(DM(e,`Length must be a multiple of 2, but is ${t.length}`));try{let e=t.length/2,r=new Uint8Array(e);for(let i=0;i<e;i++){let e=DW(t[2*i]),n=DW(t[2*i+1]);r[i]=e<<4|n}return tP(r)}catch(t){return tz(DM(e,t instanceof Error?t.message:"Invalid input"))}},DV=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],DW=e=>{if(48<=e&&e<=57)return e-48;if(97<=e&&e<=102)return e-97+10;if(65<=e&&e<=70)return e-65+10;throw TypeError("Invalid input")},DG=e=>"string"==typeof e?DH(DP.encode(e)):DH(e),DY=e=>DK(e),DZ=Symbol.for("effect/SchemaId/DateFromSelf"),DQ=Symbol.for("effect/SchemaId/GreaterThan"),DX=Symbol.for("effect/SchemaId/GreaterThanOrEqualTo"),D0=Symbol.for("effect/SchemaId/LessThan"),D1=Symbol.for("effect/SchemaId/LessThanOrEqualTo"),D2=Symbol.for("effect/SchemaId/Int"),D3=Symbol.for("effect/SchemaId/NonNaN"),D5=Symbol.for("effect/SchemaId/Finite"),D4=Symbol.for("effect/SchemaId/JsonNumber"),D6=Symbol.for("effect/SchemaId/Between"),D8=Symbol.for("effect/SchemaId/GreaterThanBigint"),D7=Symbol.for("effect/SchemaId/GreaterThanOrEqualToBigint"),D9=Symbol.for("effect/SchemaId/LessThanBigint"),$e=Symbol.for("effect/SchemaId/LessThanOrEqualToBigint"),$t=Symbol.for("effect/SchemaId/BetweenBigint"),$r=Symbol.for("effect/SchemaId/MinLength"),$i=Symbol.for("effect/SchemaId/Length"),$n=D(e=>es(e[0]),(e,...t)=>{let r={};for(let i of t)i in e&&(r[i]=e[i]);return r}),$s=D(e=>es(e[0]),(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r}),$a=Symbol.for("effect/Schema");function $o(e){return class{static{this.ast=e}static annotations(e){return $o($h(this.ast,e))}static pipe(){return e3(this,arguments)}static toString(){return String(e)}static{this[$a]=$l}constructor(){this[$a]=$l}}}let $l={_A:e=>e,_I:e=>e,_R:e=>e},$u={schemaId:OP,message:OD,missingMessage:O$,identifier:OL,title:OU,description:OB,examples:OJ,default:OH,documentation:OY,jsonSchema:OK,arbitrary:OV,pretty:OW,equivalence:OG,concurrency:OZ,batching:OQ,parseIssueTitle:OX,parseOptions:O0,decodingFallback:O1},$c=e=>{if(!e)return{};let t={...e};for(let r in $u)r in e&&(t[$u[r]]=e[r],delete t[r]);return t},$h=(e,t)=>Em(e,$c(t)),$p=e=>String(e.ast),$f=e=>$o(ER(e.ast)),$d=e=>$o(EC(e.ast)),$m=(e,t)=>{let r=E9(e,t);return(e,t)=>E1(r(e,t),EG)},$g=(e,t)=>{let r=$m(e,t);return(e,t)=>wD(r(e,t))},$b=e=>ea(e,$a)&&es(e[$a]);function $x(...e){return rN(e)?function e(t,r=function(e){return Ee(e)?C7.make(C9(e,e=>new Cg(e))):new Cg(e[0])}(t)){return class extends $o(r){static annotations(t){return e(this.literals,$h(this.ast,t))}static{this.literals=[...t]}}}(e):$E}let $y=(e,t,r)=>$S(e,new Cd(e.map(e=>e.ast),(...e)=>t.decode(...e.map($o)),(...e)=>t.encode(...e.map($o)),$c(r))),$v=(e,t)=>{let r=()=>(t,r,i)=>e(t)?EY(t):tz(new EJ(i,t));return $S([],new Cd([],r,r,$c(t)))};function $S(e,t){return class extends $o(t){static annotations(e){return $S(this.typeParameters,$h(this.ast,e))}static{this.typeParameters=[...e]}}}let $_=function(){if(Array.isArray(arguments[0])){let e=arguments[0],t=arguments[1],r=arguments[2];return $y(e,t,r)}let e=arguments[0],t=arguments[1];return $v(e,t)},$w=Symbol.for("effect/SchemaId/InstanceOf"),$k=(e,t)=>$_(t=>t instanceof e,{title:e.name,description:`an instance of ${e.name}`,pretty:()=>String,schemaId:$w,[$w]:{constructor:e},...t});class $O extends $o(CS){}class $C extends $o(Cx){}class $E extends $o(Ck){}class $I extends $o(CC){}class $F extends $o(C$){}class $T extends $o(CU){}class $R extends $o(CT){}class $N extends $o(CA){}class $A extends $o(Cz){}let $j=e=>C7.make(e.map(e=>e.ast));function $M(...e){return Ee(e)?function e(t,r=$j(t)){return class extends $o(r){static annotations(t){return e(this.members,$h(this.ast,t))}static{this.members=[...t]}}}(e):rN(e)?e[0]:$E}let $z=e=>$M(e,$C),$P=e=>$M(e,$O),$D=e=>$M(e,$C,$O),$$=e=>new $L(new CV(e.ast,!1),e);class $L{constructor(e,t){this.ast=e,this.from=t}annotations(e){return new $L(new CV(this.ast.type,this.ast.isOptional,{...this.ast.annotations,...$c(e)}),this.from)}toString(){return`${this.ast.type}${this.ast.isOptional?"?":""}`}}let $U=(e,t)=>new CG(e.map(e=>$b(e)?new CV(e.ast,!1):e.ast),t.map(e=>$b(e)?new CK(e.ast):e.ast),!0);function $q(e,t,r=$U(e,t)){return class extends $o(r){static annotations(e){return $q(this.elements,this.rest,$h(this.ast,e))}static{this.elements=[...e]}static{this.rest=[...t]}}}function $B(...e){return Array.isArray(e[0])?$q(e[0],e.slice(1)):$q(e,[])}let $J=e=>(function e(t,r){return class extends $q([],[t],r){static annotations(t){return e(this.value,$h(this.ast,t))}static{this.value=t}}})(e),$H=e=>(function e(t,r){return class extends $q([t],[t],r){static annotations(t){return e(this.value,$h(this.ast,t))}static{this.value=t}}})(e),$K=e=>e?'"?:"':'":"';class $V extends CV{constructor(e,t,r,i,n){super(e,t,i),this._tag="PropertySignatureDeclaration",this.isReadonly=r,this.defaultValue=n}toString(){let e=$K(this.isOptional),t=String(this.type);return`PropertySignature<${e}, ${t}, never, ${e}, ${t}>`}}class $W extends CV{constructor(e,t,r,i,n){super(e,t,i),this.isReadonly=r,this.fromKey=n}}class $G extends CV{constructor(e,t,r,i,n){super(e,t,i),this.isReadonly=r,this.defaultValue=n}}let $Y=e=>void 0===e?"never":W(e)?JSON.stringify(e):String(e);class $Z{constructor(e,t,r,i){this._tag="PropertySignatureTransformation",this.from=e,this.to=t,this.decode=r,this.encode=i}toString(){return`PropertySignature<${$K(this.to.isOptional)}, ${this.to.type}, ${$Y(this.from.fromKey)}, ${$K(this.from.isOptional)}, ${this.from.type}>`}}let $Q=(e,t)=>{switch(e._tag){case"PropertySignatureDeclaration":return new $V(e.type,e.isOptional,e.isReadonly,{...e.annotations,...t},e.defaultValue);case"PropertySignatureTransformation":return new $Z(e.from,new $G(e.to.type,e.to.isOptional,e.to.isReadonly,{...e.to.annotations,...t},e.to.defaultValue),e.decode,e.encode)}},$X=Symbol.for("effect/PropertySignature"),$0=e=>ea(e,$X);class $1{constructor(e){this[$X]=null,this.ast=e}pipe(){return e3(this,arguments)}annotations(e){return new $1($Q(this.ast,$c(e)))}toString(){return String(this.ast)}}let $2=e=>new $1(e);class $3 extends $1{constructor(e,t){super(e),this.from=t}annotations(e){return new $3($Q(this.ast,$c(e)),this.from)}}let $5=e=>new $3(new $V(e.ast,!1,!0,{},void 0),e),$4=D(2,(e,t)=>{let r=e.ast;switch(r._tag){case"PropertySignatureDeclaration":return $2(new $V(r.type,r.isOptional,r.isReadonly,r.annotations,t));case"PropertySignatureTransformation":return $2(new $Z(r.from,new $G(r.to.type,r.to.isOptional,r.to.isReadonly,r.to.annotations,t),r.decode,r.encode))}}),$6=e=>AST.pruneUndefined(e,$6,e=>{let t=$6(e.to);if(t)return new AST.Transformation(e.from,t,e.transformation)}),$8=(e,t,r)=>$2(new $Z(new $W(e.ast,!0,!0,{},void 0),new $G(t.ast,!1,!0,{},void 0),e=>tI(r.decode(e)),t9(r.encode))),$7=(e,t,r)=>$2(new $Z(new $W(e.ast,!0,!0,{},void 0),new $G(t.ast,!0,!0,{},void 0),r.decode,r.encode)),$9=(e,t)=>{let r=t?.exact,i=t?.default,n=t?.nullable,s=t?.as=="Option",a=t?.onNoneEncoding?t1(t.onNoneEncoding):$;if(r)return i?n?$4($8($z(e),$d(e),{decode:tX({onNone:i,onSome:e=>null===e?i():e}),encode:tI}),i).ast:$4($8(e,$d(e),{decode:tX({onNone:i,onSome:$}),encode:tI}),i).ast:s?n?$8($z(e),Ux($d(e)),{decode:rr(er),encode:a}).ast:$8(e,Ux($d(e)),{decode:$,encode:$}).ast:n?$7($z(e),$d(e),{decode:rr(er),encode:$}).ast:new $V(e.ast,!0,!0,{},void 0);return i?n?$4($8($D(e),$d(e),{decode:tX({onNone:i,onSome:e=>null==e?i():e}),encode:tI}),i).ast:$4($8($P(e),$d(e),{decode:tX({onNone:i,onSome:e=>void 0===e?i():e}),encode:tI}),i).ast:s?n?$8($D(e),Ux($d(e)),{decode:rr(e=>null!=e),encode:a}).ast:$8($P(e),Ux($d(e)),{decode:rr(et),encode:a}).ast:n?$7($D(e),$P($d(e)),{decode:rr(er),encode:$}).ast:new $V($P(e).ast,!0,!0,{},void 0)},Le=e=>new $3(new $V(e.ast===CS||e.ast===Ck?CS:$P(e).ast,!0,!0,{},void 0),e),Lt=D(e=>$b(e[0]),(e,t)=>new $3($9(e,t),e)),Lr=Ek([O$]),Li=(e,t)=>{let r=Ox(e),i=[];if(r.length>0){let n=[],s=[],a=[];for(let t=0;t<r.length;t++){let o=r[t],l=e[o];if($0(l)){let e=l.ast;switch(e._tag){case"PropertySignatureDeclaration":{let t=e.type,r=e.isOptional,a=e.annotations;n.push(new CZ(o,t,r,!0,Lr(e))),s.push(new CZ(o,EC(t),r,!0,a)),i.push(new CZ(o,t,r,!0,a));break}case"PropertySignatureTransformation":{let t=e.from.fromKey??o;n.push(new CZ(t,e.from.type,e.from.isOptional,!0,e.from.annotations)),s.push(new CZ(o,e.to.type,e.to.isOptional,!0,e.to.annotations)),a.push(new Ep(t,o,e.decode,e.encode))}}}else n.push(new CZ(o,l.ast,!1,!0)),s.push(new CZ(o,EC(l.ast),!1,!0)),i.push(new CZ(o,l.ast,!1,!0))}if(rN(a)){let e=[],r=[];for(let i of t){let{indexSignatures:t,propertySignatures:a}=E_(i.key.ast,i.value.ast);a.forEach(e=>{n.push(e),s.push(new CZ(e.name,EC(e.type),e.isOptional,e.isReadonly,e.annotations))}),t.forEach(t=>{e.push(t),r.push(new CX(t.parameter,EC(t.type),t.isReadonly))})}return new Eo(new C0(n,e,{[Oq]:"Struct (Encoded side)"}),new C0(s,r,{[Oq]:"Struct (Type side)"}),new Ef(a))}}let n=[];for(let e of t){let{indexSignatures:t,propertySignatures:r}=E_(e.key.ast,e.value.ast);r.forEach(e=>i.push(e)),t.forEach(e=>n.push(e))}return new C0(i,n)},Ln=(e,t)=>{for(let r of Ox(e)){let i=e[r];if(void 0===t[r]&&$0(i)){let e=i.ast,n="PropertySignatureDeclaration"===e._tag?e.defaultValue:e.to.defaultValue;void 0!==n&&(t[r]=n())}}return t};function Ls(e,t,r=Li(e,t)){return class extends $o(r){static annotations(e){return Ls(this.fields,this.records,$h(this.ast,e))}static{this.fields={...e}}static{this.records=[...t]}static{this.make=(t,r)=>{let i=Ln(e,{...t});return UJ(r)?i:It(this)(i)}}static pick(...t){return La($n(e,...t))}static omit(...t){return La($s(e,...t))}}}function La(e,...t){return Ls(e,t)}let Lo=e=>$x(e).pipe($5,$4(()=>e)),Ll=(e,t)=>La({_tag:Lo(e),...t}),Lu=e=>(function e(t,r,i){return class extends Ls({},[{key:t,value:r}],i){static annotations(i){return e(t,r,$h(this.ast,i))}static{this.key=t}static{this.value=r}}})(e.key,e.value),Lc=(e,t,r)=>{if(C3(e)&&C3(t)){let i=[...e.propertySignatures];for(let e of t.propertySignatures){let t=e.name,n=i.findIndex(e=>e.name===t);if(-1===n)i.push(e);else{let{isOptional:s,type:a}=i[n];i[n]=new CZ(t,Lf(a,e.type,r.concat(t)),s,!0)}}return new C0(i,e.indexSignatures.concat(t.indexSignatures))}throw Error(OI(e,t,r))},Lh=(_=[OL],e=>{let t={...e.annotations};for(let e of _)delete t[e];return t}),Lp=(e,t)=>t.map(t=>new En(t,e.filter,Lh(e))),Lf=(e,t,r)=>C7.make(Lm([e],[t],r)),Ld=e=>Et(e)?e.types:[e],Lm=(e,t,r)=>r5(e,e=>r5(t,t=>{switch(t._tag){case"Literal":if(W(t.literal)&&CR(e)||G(t.literal)&&Cj(e)||Y(t.literal)&&CP(e))return[t];break;case"StringKeyword":if(t===CT){if(CR(e)||Cb(e)&&W(e.literal))return[e];if(Es(e))return Lp(e,Lm(Ld(e.from),[t],r))}else if(e===CT)return[t];break;case"NumberKeyword":if(t===CA){if(Cj(e)||Cb(e)&&G(e.literal))return[e];if(Es(e))return Lp(e,Lm(Ld(e.from),[t],r))}else if(e===CA)return[t];break;case"BooleanKeyword":if(t===Cz){if(CP(e)||Cb(e)&&Y(e.literal))return[e];if(Es(e))return Lp(e,Lm(Ld(e.from),[t],r))}else if(e===Cz)return[t];break;case"Union":return Lm(Ld(e),t.types,r);case"Suspend":return[new Ei(()=>Lf(e,t.f(),r))];case"Refinement":return Lp(t,Lm(Ld(e),Ld(t.from),r));case"TypeLiteral":switch(e._tag){case"Union":return Lm(e.types,[t],r);case"Suspend":return[new Ei(()=>Lf(e.f(),t,r))];case"Refinement":return Lp(e,Lm(Ld(e.from),[t],r));case"TypeLiteral":return[Lc(e,t,r)];case"Transformation":{let i=e.transformation,n=Lc(e.from,t,r),s=Lc(e.to,EC(t),r);switch(i._tag){case"TypeLiteralTransformation":return[new Eo(n,s,new Ef(i.propertySignatureTransformations))];case"ComposeTransformation":return[new Eo(n,s,Eh)];case"FinalTransformation":return[new Eo(n,s,new Eu((e,t,r,n)=>E0(i.decode(e,t,r,n),t=>({...e,...t})),(e,t,r,n)=>E0(i.encode(e,t,r,n),t=>({...e,...t}))))]}}}break;case"Transformation":if(!El(e))return Lm([t],[e],r);if(Ed(t.transformation)&&Ed(e.transformation))return[new Eo(Lc(e.from,t.from,r),Lc(e.to,t.to,r),new Ef(t.transformation.propertySignatureTransformations.concat(e.transformation.propertySignatureTransformations)))]}throw Error(OI(e,t,r))})),Lg=D(2,(e,t)=>$o(Lf(e.ast,t.ast,[]))),Lb=D(e=>$b(e[1]),(e,t)=>Lw(e,t,Ej(e.ast,t.ast))),Lx=e=>$o(new Ei(()=>e().ast)),Ly=Symbol.for("effect/SchemaId/Refine"),Lv=(e,t,r)=>{if(Y(e))return e?tQ():tI(new EJ(t,r));if(W(e))return tI(new EJ(t,r,e));if(void 0!==e){if("_tag"in e)return tI(e);let i=new EJ(t,r,e.message);return tI(rN(e.path)?new ED(e.path,r,i):i)}return tQ()},LS=(e,t,r)=>{if(Ok(e))return Lv(e,t,r);if(rN(e)){let i=r6(e,e=>Lv(e,t,r));if(rN(i))return tI(1===i.length?i[0]:new EU(t,r,i))}return tQ()};function L_(e,t){return r=>{function i(t,r,i){return LS(e(t,r,i),i,t)}let n=new En(r.ast,i,$c(t));return function e(t,r,i){return class extends $o(i){static annotations(t){return e(this.from,this.filter,$h(this.ast,t))}static{this[Ly]=t}static{this.from=t}static{this.filter=r}static{this.make=(e,t)=>UJ(t)?e:It(this)(e)}}}(r,i,n)}}function Lw(e,t,r){return class extends $o(r){static annotations(e){return Lw(this.from,this.to,$h(this.ast,e))}static{this.from=e}static{this.to=t}}}let Lk=D(e=>$b(e[0])&&$b(e[1]),(e,t,r)=>Lw(e,t,new Eo(e.ast,t.ast,new Eu(r.decode,r.encode)))),LO=D(e=>$b(e[0])&&$b(e[1]),(e,t,r)=>Lk(e,t,{strict:!0,decode:(e,t,i,n)=>EY(r.decode(e,n)),encode:(e,t,i,n)=>EY(r.encode(e,n))})),LC=Symbol.for("effect/SchemaId/Trimmed"),LE=e=>t=>t.pipe(L_(e=>e===e.trim(),{schemaId:LC,title:"trimmed",description:"a string with no leading or trailing whitespace",jsonSchema:{pattern:"^\\S[\\s\\S]*\\S$|^\\S$|^$"},...e})),LI=(e,t)=>r=>r.pipe(L_(t=>t.length>=e,{schemaId:$r,title:`minLength(${e})`,description:`a string at least ${e} character(s) long`,jsonSchema:{minLength:e},...t})),LF=Symbol.for("effect/SchemaId/Pattern"),LT=Symbol.for("effect/SchemaId/StartsWith"),LR=Symbol.for("effect/SchemaId/Lowercased"),LN=e=>t=>t.pipe(L_(e=>e===e.toLowerCase(),{schemaId:LR,title:"lowercased",description:"a lowercase string",jsonSchema:{pattern:"^[^A-Z]*$"},...e})),LA=Symbol.for("effect/SchemaId/Uppercased"),Lj=e=>t=>t.pipe(L_(e=>e===e.toUpperCase(),{schemaId:LA,title:"uppercased",description:"an uppercase string",jsonSchema:{pattern:"^[^a-z]*$"},...e})),LM=Symbol.for("effect/SchemaId/Capitalized"),Lz=e=>t=>t.pipe(L_(e=>e[0]?.toUpperCase()===e[0],{schemaId:LM,title:"capitalized",description:"a capitalized string",jsonSchema:{pattern:"^[^a-z]?.*$"},...e})),LP=Symbol.for("effect/SchemaId/Uncapitalized"),LD=e=>t=>t.pipe(L_(e=>e[0]?.toLowerCase()===e[0],{schemaId:LP,title:"uncapitalized",description:"a uncapitalized string",jsonSchema:{pattern:"^[^A-Z]?.*$"},...e})),L$=e=>LI(1,{title:"nonEmptyString",description:"a non empty string",...e});class LL extends $R.pipe(LE({identifier:"Trimmed"})){}let LU=e=>e instanceof Error?e.message:String(e),Lq=e=>Lk($R.annotations({description:"a string to be decoded into JSON"}),$I,{strict:!0,decode:(t,r,i)=>EZ({try:()=>JSON.parse(t,e?.reviver),catch:e=>new EJ(i,t,LU(e))}),encode:(t,r,i)=>EZ({try:()=>JSON.stringify(t,e?.replacer,e?.space),catch:e=>new EJ(i,t,LU(e))})}).annotations({title:"parseJson",schemaId:Cf}),LB=(e,t)=>$b(e)?Lb(LB(t),e):Lq(e);/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i.source,URL;let LJ=(e,t)=>r=>r.pipe(L_(t=>t>e,{schemaId:DQ,title:`greaterThan(${e})`,description:0===e?"a positive number":`a number greater than ${e}`,jsonSchema:{exclusiveMinimum:e},...t})),LH=(e,t)=>r=>r.pipe(L_(t=>t>=e,{schemaId:DX,title:`greaterThanOrEqualTo(${e})`,description:0===e?"a non-negative number":`a number greater than or equal to ${e}`,jsonSchema:{minimum:e},...t})),LK=e=>t=>t.pipe(L_(e=>Number.isSafeInteger(e),{schemaId:D2,title:"int",description:"an integer",jsonSchema:{type:"integer"},...e})),LV=(e,t)=>r=>r.pipe(L_(t=>t<e,{schemaId:D0,title:`lessThan(${e})`,description:0===e?"a negative number":`a number less than ${e}`,jsonSchema:{exclusiveMaximum:e},...t})),LW=(e,t)=>r=>r.pipe(L_(t=>t<=e,{schemaId:D1,title:`lessThanOrEqualTo(${e})`,description:0===e?"a non-positive number":`a number less than or equal to ${e}`,jsonSchema:{maximum:e},...t})),LG=(e,t,r)=>i=>i.pipe(L_(r=>r>=e&&r<=t,{schemaId:D6,title:`between(${e}, ${t})`,description:`a number between ${e} and ${t}`,jsonSchema:{minimum:e,maximum:t},...r})),LY=e=>LH(0,{title:"nonNegative",...e});function LZ(e){return Lk(e,$N,{strict:!1,decode:(e,t,r)=>EQ(ps(e),()=>new EJ(r,e,`Unable to decode ${JSON.stringify(e)} into a number`)),encode:e=>EY(String(e))})}class LQ extends $N.pipe(LK({identifier:"Int"})){}class LX extends $N.pipe(LY({identifier:"NonNegative"})){}let L0=(e,t)=>{let r=Symbol.keyFor(e);return void 0===r?tz(new EJ(t,e,`Unable to encode a unique symbol ${String(e)} into a string`)):EY(r)},L1=e=>EY(Symbol.for(e));class L2 extends Lk($R.annotations({description:"a string to be decoded into a bigint"}),$F,{strict:!0,decode:(e,t,r)=>EQ(Dc(e),()=>new EJ(r,e,`Unable to decode ${JSON.stringify(e)} into a bigint`)),encode:e=>EY(String(e))}).annotations({identifier:"BigInt"}){}((e,t)=>r=>r.pipe(L_(t=>t>=e,{schemaId:D7,[D7]:{min:e},title:`greaterThanOrEqualToBigInt(${e})`,description:0n===e?"a non-negative bigint":`a bigint greater than or equal to ${e}n`,...t})))(0n,{title:"nonNegativeBigInt",identifier:"NonNegativeBigintFromSelf"});let L3=e=>t=>e(t).map(PZ),L5=(e,t,r,i)=>E2(e,{onFailure:e=>new EU(r,i,e),onSuccess:t}),L4=e=>(t,r,i)=>PY(t)?L5(e(PQ(t),r),PZ,i,t):tz(new EJ(i,t)),L6=e=>$_([e],{decode:e=>L4(E9(e)),encode:e=>L4(Ie(e))},{description:"Redacted(<redacted>)",pretty:()=>()=>"Redacted(<redacted>)",arbitrary:L3,equivalence:PX}),L8=LX.pipe(LK()).annotations({identifier:"NonNegativeInt"}),L7=Ll("Millis",{millis:L8}),L9=Ll("Nanos",{nanos:L2}),Ue=Ll("Infinity",{}),Ut=$B($$(L8).annotations({title:"seconds"}),$$(L8).annotations({title:"nanos"})).annotations({identifier:"FiniteHRTime"});class Ur extends $_(ec,{identifier:"Uint8ArrayFromSelf",pretty:()=>e=>`new Uint8Array(${JSON.stringify(Array.from(e))})`,arbitrary:()=>e=>e.uint8Array(),equivalence:()=>ia(eU)}){}let Ui=(w=e=>D$(e),k=e=>"string"==typeof e?DD(DP.encode(e)):DD(e),Lk($R.annotations({description:"a string to be decoded into a Uint8Array"}),Ur,{strict:!0,decode:(e,t,r)=>tL(w(e),t=>new EJ(r,e,t.message)),encode:e=>EY(k(e))}).annotations({identifier:"Uint8ArrayFromBase64"})),Un=Symbol.for("effect/SchemaId/ValidDate"),Us=DZ;class Ua extends $_(eh,{identifier:"DateFromSelf",schemaId:Us,[Us]:{noInvalidDate:!1},description:"a potentially invalid Date instance",pretty:()=>e=>`new Date(${JSON.stringify(e)})`,arbitrary:()=>e=>e.date({noInvalidDate:!1}),equivalence:()=>tG}){}e=>DC(e)&&DF(e);let Uo=()=>e=>e.integer({min:-432e5,max:504e5}).map(Sk);class Ul extends $_(DE,{identifier:"TimeZoneOffsetFromSelf",description:"a TimeZone.Offset instance",pretty:()=>e=>e.toString(),arbitrary:Uo}){}let Uu=()=>e=>e.constantFrom(...Intl.supportedValuesOf("timeZone")).map(Sw);class Uc extends $_(DI,{identifier:"TimeZoneNamedFromSelf",description:"a TimeZone.Named instance",pretty:()=>e=>e.toString(),arbitrary:Uu}){}let Uh=e=>e.oneof(Uo()(e),Uu()(e));e=>DC(e)&&DT(e);let Up=La({_tag:$x("None")}).annotations({description:"NoneEncoded"}),Uf=e=>La({_tag:$x("Some"),value:e}).annotations({description:`SomeEncoded<${$p(e)}>`}),Ud=e=>"None"===e._tag?tQ():tI(e.value),Um=(e,t)=>r=>r.oneof(t,r.record({_tag:r.constant("None")}),r.record({_tag:r.constant("Some"),value:e(r)})).map(Ud),Ug=e=>tX({onNone:()=>"none()",onSome:t=>`some(${e(t)})`}),Ub=e=>(t,r,i)=>tk(t)?tO(t)?EY(tQ()):L5(e(t.value,r),tI,i,t):tz(new EJ(i,t)),Ux=e=>$_([e],{decode:e=>Ub(E9(e)),encode:e=>Ub(Ie(e))},{description:`Option<${$p(e)}>`,pretty:Ug,arbitrary:Um,equivalence:ri}),Uy=(e,t,r)=>i=>{let n=i.array(i.tuple(e(i),t(i)));return(void 0!==r.depthIdentifier?i.oneof(r,i.constant([]),n):n).map(e=>new Map(e))},Uv=(e,t)=>r=>`new Map([${Array.from(r.entries()).map(([r,i])=>`[${e(r)}, ${t(i)}]`).join(", ")}])`,US=(e,t)=>{let r=array_.getEquivalence(Equivalence.make(([r,i],[n,s])=>e(r,n)&&t(i,s)));return Equivalence.make((e,t)=>r(Array.from(e.entries()),Array.from(t.entries())))},U_=e=>(t,r,i)=>Predicate.isMap(t)?L5(e(Array.from(t.entries()),r),e=>new Map(e),i,t):ParseResult.fail(new ParseResult.Type(i,t)),Uw=(e,t,r)=>$_([e,t],{decode:(e,t)=>U_(ParseResult.decodeUnknown($J($B(e,t)))),encode:(e,t)=>U_(ParseResult.encodeUnknown($J($B(e,t))))},{description:r,pretty:Uv,arbitrary:Uy,equivalence:US}),Uk=(e,t)=>r=>{let i=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),i):i).map(e=>new Set(e))},UO=e=>t=>`new Set([${Array.from(t.values()).map(t=>e(t)).join(", ")}])`,UC=e=>{let t=array_.getEquivalence(e);return Equivalence.make((e,r)=>t(Array.from(e.values()),Array.from(r.values())))},UE=e=>(t,r,i)=>Predicate.isSet(t)?L5(e(Array.from(t.values()),r),e=>new Set(e),i,t):ParseResult.fail(new ParseResult.Type(i,t)),UI=(e,t)=>$_([e],{decode:e=>UE(ParseResult.decodeUnknown($J(e))),encode:e=>UE(ParseResult.encodeUnknown($J(e)))},{description:t,pretty:UO,arbitrary:Uk,equivalence:UC}),UF=()=>e=>`BigDecimal(${Da(P9(e))})`,UT=()=>e=>e.tuple(e.bigInt(),e.integer({min:0,max:18})).map(([e,t])=>P5(e,t)),UR=(e,t)=>r=>{let i=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),i):i).map(chunk_.fromIterable)},UN=e=>t=>`Chunk(${chunk_.toReadonlyArray(t).map(e).join(", ")})`,UA=e=>(t,r,i)=>chunk_.isChunk(t)?chunk_.isEmpty(t)?ParseResult.succeed(chunk_.empty()):L5(e(chunk_.toReadonlyArray(t),r),chunk_.fromIterable,i,t):ParseResult.fail(new ParseResult.Type(i,t)),Uj=e=>t=>fastCheck_.array(e(t),{minLength:1}).map(e=>chunk_.unsafeFromNonEmptyArray(e)),UM=e=>t=>`NonEmptyChunk(${chunk_.toReadonlyArray(t).map(e).join(", ")})`,Uz=e=>(t,r,i)=>chunk_.isChunk(t)&&chunk_.isNonEmpty(t)?L5(e(chunk_.toReadonlyArray(t),r),chunk_.unsafeFromNonEmptyArray,i,t):ParseResult.fail(new ParseResult.Type(i,t)),UP=e=>$b(e)||$0(e),UD=e=>Ox(e).every(t=>UP(e[t])),U$=e=>"fields"in e?e.fields:U$(e[Ly]),UL=e=>UD(e)?La(e):$b(e)?e:La(U$(e)),UU=e=>UD(e)?e:U$(e),Uq=e=>(t,r)=>UV({kind:"Class",identifier:e,schema:UL(t),fields:UU(t),Base:oI,annotations:r}),UB=(e,t)=>{let r={...e};for(let i of Ox(t)){if(i in e)throw Error(OM(i));r[i]=t[i]}return r};function UJ(e){return Y(e)?e:e?.disableValidation??!1}let UH=V("effect/Schema/astCache",()=>new WeakMap),UK=e=>void 0===e?[]:Array.isArray(e)?e:[e],UV=({Base:e,annotations:t,disableToString:r,fields:i,identifier:n,kind:s,schema:a})=>{let o=Symbol.for(`effect/Schema/${s}/${n}`),[l,u,c]=UK(t),h=$d(a),p=h.annotations({identifier:n,...l}),f=h.annotations({[Oq]:`${n} (Type side)`,...l}),d=a.annotations({[Oq]:`${n} (Constructor)`,...l}),m=a.annotations({[Oq]:`${n} (Encoded side)`,...c}),g=a.annotations({[Cc]:n,...c,...l,...u}),b=e=>ea(e,o)&&Ir(f)(e),x=class extends e{constructor(e={},t=!1){e={...e},"Class"!==s&&delete e._tag,e=Ln(i,e),UJ(t)||(e=It(d)(e)),super(e,!0)}static{this[$a]=$l}static get ast(){let e=UH.get(this);return e||(e=LO(m,$_([a],{decode:()=>(e,t,r)=>e instanceof this||b(e)?EY(e):tz(new EJ(r,e)),encode:()=>(e,t)=>e instanceof this?EY(e):E0(Ie(f)(e,t),e=>new this(e,!0))},{identifier:n,pretty:e=>t=>`${n}(${e(t)})`,arbitrary:e=>t=>e(t).map(e=>new this(e)),equivalence:$,[O2]:p.ast,...l}),{strict:!0,decode:e=>new this(e,!0),encode:$}).annotations({[O2]:g.ast,...u}).ast,UH.set(this,e)),e}static pipe(){return e3(this,arguments)}static annotations(e){return $o(this.ast).annotations(e)}static toString(){return`(${String(m)} <-> ${n})`}static make(...e){return new this(...e)}static{this.fields={...i}}static{this.identifier=n}static extend(e){return(t,r)=>{let n=UU(t),o=UL(t),l=UB(i,n);return UV({kind:s,identifier:e,schema:Lg(a,o),fields:l,Base:this,annotations:r})}}static transformOrFail(e){return(t,r,n)=>{let o=UB(i,t);return UV({kind:s,identifier:e,schema:Lk(a,$d(La(o)),r),fields:o,Base:this,annotations:n})}}static transformOrFailFrom(e){return(t,r,n)=>{let o=UB(i,t);return UV({kind:s,identifier:e,schema:Lk($f(a),La(o),r),fields:o,Base:this,annotations:n})}}get[o](){return o}};return!0!==r&&Object.defineProperty(x.prototype,"toString",{value(){return`${n}({ ${Ox(i).map(e=>`${O_(e)}: ${OS(this[e])}`).join(", ")} })`},configurable:!0,writable:!0}),x},UW=La({_tag:$x("None")}).annotations({identifier:"FiberIdNoneEncoded"}),UG=$M(UW,La({_tag:$x("Runtime"),id:LQ,startTimeMillis:LQ}).annotations({identifier:"FiberIdRuntimeEncoded"}),La({_tag:$x("Composite"),left:Lx(()=>UG),right:Lx(()=>UG)}).annotations({identifier:"FiberIdCompositeEncoded"})).annotations({identifier:"FiberIdEncoded"}),UY=e=>e.letrec(t=>({None:e.record({_tag:e.constant("None")}),Runtime:e.record({_tag:e.constant("Runtime"),id:e.integer(),startTimeMillis:e.integer()}),Composite:e.record({_tag:e.constant("Composite"),left:t("FiberId"),right:t("FiberId")}),FiberId:e.oneof(t("None"),t("Runtime"),t("Composite"))})).FiberId.map(UQ),UZ=e=>{switch(e._tag){case"None":return"FiberId.none";case"Runtime":return`FiberId.runtime(${e.id}, ${e.startTimeMillis})`;case"Composite":return`FiberId.composite(${UZ(e.right)}, ${UZ(e.left)})`}},UQ=e=>{switch(e._tag){case"None":return or;case"Runtime":return oi(e.id,e.startTimeMillis);case"Composite":return on(UQ(e.left),UQ(e.right))}},UX=(e,t)=>r=>{let i=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),i):i).map(hashSet_.fromIterable)},U0=e=>t=>`HashSet(${Array.from(t).map(t=>e(t)).join(", ")})`,U1=e=>{let t=array_.getEquivalence(e);return Equivalence.make((e,r)=>t(Array.from(e),Array.from(r)))},U2=e=>(t,r,i)=>hashSet_.isHashSet(t)?L5(e(Array.from(t),r),hashSet_.fromIterable,i,t):ParseResult.fail(new ParseResult.Type(i,t)),U3=(e,t)=>r=>{let i=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),i):i).map(list_.fromIterable)},U5=e=>t=>`List(${Array.from(t).map(t=>e(t)).join(", ")})`,U4=e=>{let t=array_.getEquivalence(e);return Equivalence.make((e,r)=>t(Array.from(e),Array.from(r)))},U6=e=>(t,r,i)=>list_.isList(t)?L5(e(Array.from(t),r),list_.fromIterable,i,t):ParseResult.fail(new ParseResult.Type(i,t)),U8=(e,t,r)=>i=>{let n=i.array(e(i));return(void 0!==r.depthIdentifier?i.oneof(r,i.constant([]),n):n).map(e=>sortedSet_.fromIterable(e,t))},U7=e=>t=>`new SortedSet([${Array.from(sortedSet_.values(t)).map(t=>e(t)).join(", ")}])`,U9=(e,t)=>(r,i,n)=>sortedSet_.isSortedSet(r)?L5(e(Array.from(sortedSet_.values(r)),i),e=>sortedSet_.fromIterable(e,t),n,r):ParseResult.fail(new ParseResult.Type(n,r)),qe=Ll("symbol",{key:$R}).annotations({description:"an object to be decoded into a globally shared symbol"}),qt=Lk(qe,$T,{strict:!0,decode:e=>L1(e.key),encode:(e,t,r)=>E0(L0(e,r),e=>qe.make({key:e}))}),qr=Symbol.for("@effect/platform/Headers"),qi=Object.assign(Object.create(null),{[qr]:qr,[eQ](e){return qp(this,f1(e,qf))}}),qn=e=>Object.assign(Object.create(qi),e),qs=Object.create(qi),qa=e=>{if(void 0===e)return qs;if(Symbol.iterator in e){let t=Object.create(qi);for(let[r,i]of e)t[r.toLowerCase()]=i;return t}let t=Object.create(qi);for(let[r,i]of Object.entries(e))Array.isArray(i)?t[r.toLowerCase()]=i.join(", "):void 0!==i&&(t[r.toLowerCase()]=i);return t},qo=e=>Object.setPrototypeOf(e,qi),ql=D(3,(e,t,r)=>{let i=qn(e);return i[t.toLowerCase()]=r,i}),qu=D(2,(e,t)=>qn({...e,...qa(t)})),qc=D(2,(e,t)=>{let r=qn(e);return Object.assign(r,t),r}),qh=D(2,(e,t)=>{let r=qn(e);return delete r[t.toLowerCase()],r}),qp=D(2,(e,t)=>{let r={...e},i=t=>{if("string"==typeof t){let i=t.toLowerCase();i in e&&(r[i]=PZ(e[i]))}else for(let i in e)t.test(i)&&(r[i]=PZ(e[i]))};if(Array.isArray(t))for(let e=0;e<t.length;e++)i(t[e]);else i(t);return r}),qf=V("@effect/platform/Headers/currentRedactedNames",()=>cD(["authorization","cookie","set-cookie","x-api-key"]));function qd(e){if(Array.isArray(e))return e.map(qd).join("");switch(typeof e){case"string":return e;case"number":case"bigint":return e.toString();case"boolean":return e?"true":"false";default:return""}}let qm=e=>{let t=qg(e),r=[];for(let e=0;e<t.length;e++)if(Array.isArray(t[e][0])){let[i,n]=t[e];r.push([`${i[0]}[${i.slice(1).join("][")}]`,n])}else r.push(t[e]);return r},qg=e=>{let t=Symbol.iterator in e?rw(e):Object.entries(e),r=[];for(let[e,i]of t)if(Array.isArray(i))for(let t=0;t<i.length;t++)void 0!==i[t]&&r.push([e,String(i[t])]);else if("object"==typeof i)for(let[t,n]of qg(i))r.push([[e,..."string"==typeof t?[t]:t],n]);else void 0!==i&&r.push([e,String(i)]);return r},qb=((e,t,r)=>rI(ie(e,([e])=>e!==t),[t,String(r)]),D(2,(e,t)=>{let r=qm(t),i=r.map(([e])=>e);return rF(ie(e,([e])=>i.includes(e)),r)})),qx=D(3,(e,t,r)=>rI(e,[t,String(r)])),qy=((e,t)=>rF(e,qm(t)),(e,t,r)=>{try{let i=new URL(e,qv());for(let e=0;e<t.length;e++){let[r,n]=t[e];void 0!==n&&i.searchParams.append(r,n)}return"Some"===r._tag&&(i.hash=r.value),tP(i)}catch(e){return tz(e)}}),qv=()=>{if("location"in globalThis&&void 0!==globalThis.location&&void 0!==globalThis.location.origin&&void 0!==globalThis.location.pathname)return location.origin+location.pathname},qS=(e,t,r)=>TW(TY(mL(()=>cr)),i=>J(TY(ce(u8(e,e=>mq(i,t=>r(e,t))))),TW(t),TJ(e=>uM(mU(i),t=>t(e))))),q_=s6("@effect/platform/FileSystem"),qw=(e,t)=>Om({module:"FileSystem",method:e,reason:"NotFound",message:"No such file or directory",pathOrDescriptor:t}),qk=e=>"bigint"==typeof e?e:BigInt(e),qO=Symbol.for("@effect/platform/HttpBody"),qC=Symbol.for("@effect/platform/HttpBody/HttpBodyError"),qE=e=>{let t=void 0===e?Object.create(td):oF(e);return t._tag="HttpBodyError",t},qI=e=>qE({[qC]:qC,reason:e});class qF{constructor(){this[qO]=qO}[eH](){return this.toJSON()}toString(){return eV(this)}}class qT extends qF{toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Empty"}}constructor(...e){super(...e),this._tag="Empty"}}let qR=new qT;class qN extends qF{constructor(e,t){super(),this._tag="Uint8Array",this.body=e,this.contentType=t}get contentLength(){return this.body.length}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Uint8Array",body:this.contentType.startsWith("text/")||this.contentType.endsWith("json")?new TextDecoder().decode(this.body):`Uint8Array(${this.body.length})`,contentType:this.contentType,contentLength:this.contentLength}}}let qA=(e,t)=>new qN(e,t??"application/octet-stream"),qj=new TextEncoder,qM=(e,t)=>qA(qj.encode(e),t??"text/plain"),qz=e=>qM(JSON.stringify(e),"application/json"),qP=e=>gC({try:()=>qz(e),catch:e=>qI({_tag:"JsonError",error:e})});class qD extends qF{constructor(e){super(),this._tag="FormData",this.formData=e}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"FormData",formData:this.formData}}}class q$ extends qF{constructor(e,t,r){super(),this._tag="Stream",this.stream=e,this.contentType=t,this.contentLength=r}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Stream",contentType:this.contentType,contentLength:this.contentLength}}}let qL=(e,t,r)=>new q$(e,t??"application/octet-stream",r),qU=Symbol.for("@effect/platform/HttpServerResponse"),qq=Symbol.for("@effect/platform/HttpServerRespondable");class qB extends fD{constructor(e,t,r,i,n){if(super(),this.status=e,this.statusText=t,this.cookies=i,this.body=n,this[qU]=qU,n.contentType||n.contentLength){let e={...r};n.contentType&&(e["content-type"]=n.contentType),n.contentLength&&(e["content-length"]=n.contentLength.toString()),this.headers=e}else this.headers=r}commit(){return u5(this)}[qq](){return u5(this)}[eH](){return this.toJSON()}toString(){return eV(this)}toJSON(){return{_id:"@effect/platform/HttpServerResponse",status:this.status,statusText:this.statusText,headers:e2(this.headers),cookies:this.cookies.toJSON(),body:this.body.toJSON()}}}let qJ=e=>"object"==typeof e&&null!==e&&qU in e,qH=e=>new qB(e?.status??204,e?.statusText,e?.headers?qa(e.headers):qs,e?.cookies??Pz,qR),qK=(e,t)=>e?.contentType?e.contentType:e?.headers?t["content-type"]:void 0,qV=D(3,(e,t,r)=>new qB(e.status,e.statusText,ql(e.headers,t,r),e.cookies,e.body)),qW=(e,t)=>uG(qP(e),e=>new qB(t?.status??200,t?.statusText,t?.headers?qa(t.headers):qs,t?.cookies??Pz,e)),qG=(e,t)=>{let r=new globalThis.Headers(e.headers);if(!PP(e.cookies))for(let t of PJ(e.cookies))r.append("set-cookie",t);if(t?.withoutBody)return new Response(void 0,{status:e.status,statusText:e.statusText,headers:r});let i=e.body;switch(i._tag){case"Empty":return new Response(void 0,{status:e.status,statusText:e.statusText,headers:r});case"Uint8Array":case"Raw":return new Response(i.body,{status:e.status,statusText:e.statusText,headers:r});case"FormData":return new Response(i.formData,{status:e.status,statusText:e.statusText,headers:r});case"Stream":return new Response(Pl(i.stream,t?.runtime??wz),{status:e.status,statusText:e.statusText,headers:r})}},qY=Symbol.for("@effect/platform/HttpServerRespondable"),qZ=e=>ea(e,qY),qQ=qH({status:400}),qX=qH({status:404}),q0=e=>qJ(e)?u5(e):u1(e[qY]()),q1=(e,t)=>qJ(e)?u5(e):qZ(e)?uv(e[qY](),()=>u5(t)):EV(e)?u5(qQ):v9(e)?u5(qX):u5(t),q2=(e,t)=>qJ(e)?u5(e):u5(t),q3=Symbol.for("@effect/platform/HttpServerError"),q5=V("@effect/platform/HttpServerError/clientAbortFiberId",()=>oi(-499,0)),q4=e=>{let t;let r=sb(e,e=>qJ(e)?(t=e,tI(n6)):tQ());return[t??q6,r]},q6=qH({status:500}),q8=qH({status:499}),q7=qH({status:503}),q9=q3;class Be extends Og(q9,"RequestError"){[qY](){return qH({status:400})}get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){return this.description?`${this.reason}: ${this.description} (${this.methodAndUrl})`:`${this.reason} error (${this.methodAndUrl})`}}class Bt extends Og(q9,"RouteNotFound"){constructor(e){super(e),this.stack=`${this.name}: ${this.message}`}[qY](){return qH({status:404})}get message(){return`${this.request.method} ${this.request.url} not found`}}let Br=e=>{let[t,r]=sF(e,[u5(q6),n6],(e,t)=>{switch(t._tag){case"Empty":return tI(e);case"Fail":return tI([q1(t.error,q6),t]);case"Die":return tI([q2(t.defect,q6),t]);case"Interrupt":if("Empty"!==e[1]._tag)return tQ();return tI([u5(t.fiberId===q5?q8:q7),t]);default:return tQ()}});return uG(t,e=>si(r)?[e,n7(e)]:[e,st(r,n7(e))])},Bi=e=>"Success"===e._tag?e.value:q4(e.cause)[0],Bn=Symbol.for("@effect/platform/HttpIncomingMessage"),Bs=(e,t)=>{let r=$m(e,t);return e=>uM(e.json,r)},Ba=(e,t)=>{let r=$m(e,t);return e=>r(e.headers)},Bo=V("@effect/platform/HttpIncomingMessage/maxBodySize",()=>cD(tQ())),Bl=(e,t)=>{let r;let i=e.headers["content-type"]??"";if(i.includes("application/json"))try{r=wL(e.json)}catch(e){}else if(i.includes("text/")||i.includes("urlencoded"))try{r=wL(e.text)}catch(e){}let n={...t,headers:e2(e.headers),remoteAddress:e.remoteAddress.toJSON()};return void 0!==r&&(n.body=r),n},Bu=Symbol.for("effect/Mailbox"),Bc=Symbol.for("effect/Mailbox/ReadonlyMailbox"),Bh=iy(),Bp=hM(Bh),Bf=hM(!1),Bd=hM(!0),Bm=[Bh,!0];class Bg extends fP{constructor(e,t,r){super(),this[Bu]=Bu,this[Bc]=Bc,this.state={_tag:"Open",takers:new Set,offers:new Set,awaiters:new Set},this.messages=[],this.messagesChunk=iy(),this.shutdown=u6(()=>{if("Done"===this.state._tag)return!0;this.messages=[],this.messagesChunk=Bh;let e=this.state.offers;if(this.finalize("Open"===this.state._tag?hz:this.state.exit),e.size>0){for(let t of e)"Single"===t._tag?t.resume(Bf):t.resume(hM(iE(t.remaining.slice(t.offset))));e.clear()}return!0}),this.end=this.done(hz),this.clear=u4(()=>{if("Done"===this.state._tag)return hw(this.state.exit,Bh);let e=this.unsafeTakeAll();return this.releaseCapacity(),u5(e)}),this.takeAll=u4(()=>{if("Done"===this.state._tag)return hw(this.state.exit,Bm);let e=this.unsafeTakeAll();return 0===e.length?ch(this.awaitTake,this.takeAll):u5([e,this.releaseCapacity()])}),this.take=u4(()=>this.unsafeTake()??ch(this.awaitTake,this.take)),this.await=ux(e=>"Done"===this.state._tag?e(this.state.exit):(this.state.awaiters.add(e),u6(()=>{"Done"!==this.state._tag&&this.state.awaiters.delete(e)}))),this.size=u6(()=>this.unsafeSize()),this.awaitTake=ux(e=>"Done"===this.state._tag?e(this.state.exit):(this.state.takers.add(e),u6(()=>{"Done"!==this.state._tag&&this.state.takers.delete(e)}))),this.scheduleRunning=!1,this.releaseTaker=()=>{if(this.scheduleRunning=!1,"Done"===this.state._tag||0===this.state.takers.size)return;let e=ro(this.state.takers);this.state.takers.delete(e),e(hz)},this.scheduler=e,this.capacity=t,this.strategy=r}offer(e){return u4(()=>{if("Open"!==this.state._tag)return Bf;if(this.messages.length+this.messagesChunk.length>=this.capacity)switch(this.strategy){case"dropping":return Bf;case"suspend":if(this.capacity<=0&&this.state.takers.size>0)return this.messages.push(e),this.releaseTaker(),Bd;return this.offerRemainingSingle(e);case"sliding":return this.unsafeTake(),this.messages.push(e),Bd}return this.messages.push(e),this.scheduleReleaseTaker(),Bd})}unsafeOffer(e){return"Open"===this.state._tag&&(this.messages.length+this.messagesChunk.length>=this.capacity?"sliding"===this.strategy?(this.unsafeTake(),this.messages.push(e),!0):this.capacity<=0&&this.state.takers.size>0&&(this.messages.push(e),this.releaseTaker(),!0):(this.messages.push(e),this.scheduleReleaseTaker(),!0))}offerAll(e){return u4(()=>{if("Open"!==this.state._tag)return u5(i_(e));let t=this.unsafeOfferAllArray(e);return 0===t.length?Bp:"dropping"===this.strategy?u5(iE(t)):this.offerRemainingArray(t)})}unsafeOfferAll(e){return iE(this.unsafeOfferAllArray(e))}unsafeOfferAllArray(e){if("Open"!==this.state._tag)return rw(e);if(this.capacity===Number.POSITIVE_INFINITY||"sliding"===this.strategy)return this.messages.length>0&&(this.messagesChunk=iM(this.messagesChunk,iE(this.messages))),"sliding"===this.strategy?this.messagesChunk=this.messagesChunk.pipe(iM(i_(e)),iY(this.capacity)):ib(e)?this.messagesChunk=iM(this.messagesChunk,e):this.messages=rw(e),this.scheduleReleaseTaker(),[];let t=this.capacity<=0?this.state.takers.size:this.capacity-this.messages.length-this.messagesChunk.length;if(0===t)return rw(e);let r=[],i=0;for(let n of e)i<t?this.messages.push(n):r.push(n),i++;return this.scheduleReleaseTaker(),r}fail(e){return this.done(hE(e))}failCause(e){return this.done(hI(e))}unsafeDone(e){return"Open"===this.state._tag&&(0===this.state.offers.size&&0===this.messages.length&&0===this.messagesChunk.length?this.finalize(e):this.state={...this.state,_tag:"Closing",exit:e},!0)}done(e){return u6(()=>this.unsafeDone(e))}takeN(e){return u4(()=>{let t;if("Done"===this.state._tag)return hw(this.state.exit,Bm);if(e<=0)return u5([Bh,!1]);if((e=Math.min(e,this.capacity))<=this.messagesChunk.length)t=iN(this.messagesChunk,e),this.messagesChunk=iA(this.messagesChunk,e);else{if(!(e<=this.messages.length+this.messagesChunk.length))return ch(this.awaitTake,this.takeN(e));this.messagesChunk=iM(this.messagesChunk,iE(this.messages)),this.messages=[],t=iN(this.messagesChunk,e),this.messagesChunk=iA(this.messagesChunk,e)}return u5([t,this.releaseCapacity()])})}unsafeTake(){let e;if("Done"===this.state._tag)return hD(this.state.exit,hE(new hf));if(this.messagesChunk.length>0)e=iJ(this.messagesChunk),this.messagesChunk=iA(this.messagesChunk,1);else if(this.messages.length>0)e=this.messages[0],this.messagesChunk=iA(iE(this.messages),1),this.messages=[];else if(this.capacity<=0&&this.state.offers.size>0)return this.capacity=1,this.releaseCapacity(),this.capacity=0,this.messages.length>0?hM(this.messages.pop()):void 0;else return;return this.releaseCapacity(),hM(e)}unsafeSize(){let e=this.messages.length+this.messagesChunk.length;return"Done"===this.state._tag?tQ():tI(e)}commit(){return this.takeAll}pipe(){return e3(this,arguments)}toJSON(){return{_id:"effect/Mailbox",state:this.state._tag,size:this.unsafeSize().toJSON()}}toString(){return eV(this)}[eH](){return eV(this)}offerRemainingSingle(e){return ux(t=>{if("Open"!==this.state._tag)return t(Bf);let r={_tag:"Single",message:e,resume:t};return this.state.offers.add(r),u6(()=>{"Open"===this.state._tag&&this.state.offers.delete(r)})})}offerRemainingArray(e){return ux(t=>{if("Open"!==this.state._tag)return t(hM(iE(e)));let r={_tag:"Array",remaining:e,offset:0,resume:t};return this.state.offers.add(r),u6(()=>{"Open"===this.state._tag&&this.state.offers.delete(r)})})}releaseCapacity(){if("Done"===this.state._tag)return"Success"===this.state.exit._tag;if(0===this.state.offers.size)return"Closing"===this.state._tag&&0===this.messages.length&&0===this.messagesChunk.length&&(this.finalize(this.state.exit),"Success"===this.state.exit._tag);let e=this.capacity-this.messages.length-this.messagesChunk.length;for(let t of this.state.offers){if(0===e)break;if("Single"===t._tag)this.messages.push(t.message),e--,t.resume(Bd),this.state.offers.delete(t);else{for(;t.offset<t.remaining.length;t.offset++){if(0===e)return!1;this.messages.push(t.remaining[t.offset]),e--}t.resume(Bp),this.state.offers.delete(t)}}return!1}scheduleReleaseTaker(){!this.scheduleRunning&&(this.scheduleRunning=!0,this.scheduler.scheduleTask(this.releaseTaker,0))}unsafeTakeAll(){if(this.messagesChunk.length>0){let e=this.messages.length>0?iM(this.messagesChunk,iE(this.messages)):this.messagesChunk;return this.messagesChunk=Bh,this.messages=[],e}if(this.messages.length>0){let e=iE(this.messages);return this.messages=[],e}return"Done"!==this.state._tag&&this.state.offers.size>0?(this.capacity=1,this.releaseCapacity(),this.capacity=0,iS(this.messages.pop())):Bh}finalize(e){if("Done"===this.state._tag)return;let t=this.state;for(let r of(this.state={_tag:"Done",exit:e},t.takers))r(e);for(let r of(t.takers.clear(),t.awaiters))r(e);t.awaiters.clear()}}let Bb=e=>up(t=>u5(new Bg(t.currentScheduler,"number"==typeof e?e:e?.capacity??Number.POSITIVE_INFINITY,"number"==typeof e?"suspend":e?.strategy??"suspend"))),Bx=/; *([!#$%&'*+.^\w`|~-]+)=("(?:[\v\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\v\u0020-\u00ff])*"|[!#$%&'*+.^\w`|~-]+) */gu,By=/\\([\v\u0020-\u00ff])/gu,Bv=/^[!#$%&'*+.^\w|~-]+\/[!#$%&'*+.^\w|~-]+$/u,BS=/^[!#$%&'*+.^\w|~-]+$/u,B_={value:"",parameters:Object.create(null)};function Bw(e,t=!1){let r,i,n;if("string"!=typeof e)return B_;let s=e.indexOf(";"),a=-1!==s?e.slice(0,s).trim():e.trim();if(!1===(t?BS:Bv).test(a))return B_;let o={value:a.toLowerCase(),parameters:Object.create(null)};if(-1===s)return o;for(Bx.lastIndex=s;i=Bx.exec(e);){if(i.index!==s)return B_;s+=i[0].length,r=i[1].toLowerCase(),'"'===(n=i[2])[0]&&(n=n.slice(1,n.length-1),!t&&By.test(n)&&(n=n.replace(By,"$1"))),o.parameters[r]=n}return s!==e.length?B_:o}!function(e){e[e.key=0]="key",e[e.whitespace=1]="whitespace",e[e.value=2]="value"}(R||(R={}));let Bk={_tag:"Continue"},BO=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1],BC=[0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1];!function(e){e[e.headers=0]="headers",e[e.body=1]="body"}(N||(N={}));let BE={_tag:"InvalidDisposition"},BI={_tag:"EndNotReached"},BF={_tag:"ReachedLimit",limit:"MaxParts"},BT={_tag:"ReachedLimit",limit:"MaxTotalSize"},BR={_tag:"ReachedLimit",limit:"MaxPartSize"},BN={_tag:"ReachedLimit",limit:"MaxFieldSize"},BA=new TextEncoder().encode("\r\n");function Bj(e){return void 0!==e.filename||"application/octet-stream"===e.contentType}function BM(e){}let Bz=new TextDecoder("utf-8"),BP=function({headers:e,onFile:t,onField:r,onError:i,onDone:n,isFile:s=Bj,maxParts:a=1/0,maxTotalSize:o=1/0,maxPartSize:l=1/0,maxFieldSize:u=1048576}){let c=Bw(e["content-type"]).parameters.boundary;if(void 0===c)return i({_tag:"InvalidBoundary"}),{write:BM,end(){}};let h={state:N.headers,index:0,parts:0,onChunk:BM,info:void 0,headerSkip:0,partSize:0,totalSize:0,isFile:!1,fieldChunks:[],fieldSize:0};function p(){h.state=N.body,h.isFile=!0,h.onChunk=BM}let f=function(){let e=new TextDecoder,t={state:R.key,headers:Object.create(null),key:"",value:void 0,crlf:0,previousChunk:void 0,pairs:0,size:0};function r(e){return t.state=R.key,t.headers=Object.create(null),t.key="",t.value=void 0,t.crlf=0,t.previousChunk=void 0,t.pairs=0,t.size=0,e}function i(e,t){let r=new Uint8Array(e.length+t.length);return r.set(e),r.set(t,e.length),r}function n(e){return r({_tag:"Failure",reason:e,headers:t.headers})}return function(s,a){let o,l=0;if(void 0!==t.previousChunk){o=l=t.previousChunk.length;let e=new Uint8Array(s.length+l);e.set(t.previousChunk),e.set(s,l),t.previousChunk=void 0,s=e}let u=s.length;e:for(;a<u;){if(t.state===R.key){let r=a;for(;r<u;r++){if(t.size++>16384)return n("HeaderTooLarge");if(58===s[r]){if(t.key+=e.decode(s.subarray(a,r)).toLowerCase(),0===t.key.length)return n("InvalidHeaderName");32===s[r+1]&&32!==s[r+2]&&9!==s[r+2]?(a=r+2,t.state=R.value,t.size++):32!==s[r+1]&&9!==s[r+1]?(a=r+1,t.state=R.value):(a=r+1,t.state=R.whitespace);break}if(1!==BO[s[r]])return n("InvalidHeaderName")}if(r===u)return t.key+=e.decode(s.subarray(a,u)).toLowerCase(),Bk}if(t.state===R.whitespace){for(;a<u;a++){if(t.size++>16384)return n("HeaderTooLarge");if(32!==s[a]&&9!==s[a]){t.state=R.value;break}}if(a===u)return Bk}if(t.state===R.value){let c=a;for(void 0!==o&&(c=o,o=void 0);c<u;c++){if(t.size++>16384)return n("HeaderTooLarge");if(13===s[c]||t.crlf>0){let o=s[c];if(13===o&&0===t.crlf&&(t.crlf=1,c++,t.size++,o=s[c]),10===o&&1===t.crlf&&(t.crlf=2,c++,t.size++,o=s[c]),13===o&&2===t.crlf&&(t.crlf=3,c++,t.size++,o=s[c]),10===o&&3===t.crlf&&(t.crlf=4,c++,t.size++),t.crlf<4&&c>=u)return t.previousChunk=s.subarray(a),Bk;if(t.crlf>=2){t.value=void 0===t.value?s.subarray(a,c-t.crlf):i(t.value,s.subarray(a,c-t.crlf));let o=e.decode(t.value);if(void 0===t.headers[t.key]?t.headers[t.key]=o:"string"==typeof t.headers[t.key]?t.headers[t.key]=[t.headers[t.key],o]:t.headers[t.key].push(o),a=c,t.size--,4!==t.crlf&&100===t.pairs)return n("TooManyHeaders");if(3===t.crlf)return n("InvalidHeaderValue");if(4===t.crlf)return r({_tag:"Headers",headers:t.headers,endPosition:a-l});t.pairs++,t.key="",t.value=void 0,t.crlf=0,t.state=R.key;continue e}}else if(1!==BC[s[c]])return n("InvalidHeaderValue")}if(c===u)return t.value=void 0===t.value?s.subarray(a,u):i(t.value,s.subarray(a,u)),Bk}}return a>u&&(t.size+=u-a),Bk}}(),d=function(e,t,r){let i=function(e){let t=new TextEncoder().encode(e),r=t.length,i={};for(let e=0;e<r;e++){let r=t[e];void 0===i[r]&&(i[r]=[]),i[r].push(e)}return{needle:t,needleLength:r,indexes:i,firstByte:t[0],previousChunk:void 0,previousChunkLength:0,matchIndex:0}}(e);void 0!==r&&(i.previousChunk=r,i.previousChunkLength=r.length);let n=function(){if("Buffer"in globalThis&&!("Bun"in globalThis||"Deno"in globalThis))return function(e,t,r){return Buffer.prototype.indexOf.call(e,t,r)};let e=new Uint8Array(256).fill(i.needle.length);for(let t=0,r=i.needle.length-1;t<r;++t)e[i.needle[t]]=r-t;return function(t,r,n){let s=t.length,a=n+i.needleLength-1;for(;a<s;){for(let e=i.needleLength-1,n=a;e>=0&&t[n]===r[e];e--,n--)if(0===e)return n;a+=e[t[a]]}return -1}}();return{write:function(e){let r=e.length;if(void 0!==i.previousChunk){let t=new Uint8Array(i.previousChunkLength+r);t.set(i.previousChunk),t.set(e,i.previousChunkLength),e=t,r=i.previousChunkLength+r,i.previousChunk=void 0}if(r<i.needleLength){i.previousChunk=e,i.previousChunkLength=r;return}let s=0;for(;s<r;){let a=n(e,i.needle,s);if(a>-1){a>s&&t(i.matchIndex,e.subarray(s,a)),i.matchIndex+=1,s=a+i.needleLength;continue}if(e[r-1]in i.indexes){let n=i.indexes[e[r-1]],a=-1;for(let t=0,s=n.length;t<s;t++){let s=n[t];e[r-1-s]===i.firstByte&&t>a&&(a=s)}-1===a?0===s?t(i.matchIndex,e):t(i.matchIndex,e.subarray(s)):(r-1-a>s&&t(i.matchIndex,e.subarray(s,r-1-a)),i.previousChunk=e.subarray(r-1-a),i.previousChunkLength=a+1)}else 0===s?t(i.matchIndex,e):t(i.matchIndex,e.subarray(s));break}},end:function(){void 0!==i.previousChunk&&i.previousChunk!==r&&t(i.matchIndex,i.previousChunk),i.previousChunk=r,i.previousChunkLength=r?.length??0,i.matchIndex=0}}}(`\r
--${c}`,function(e,o){if(0===e){p();return}if(e!==h.index){if(h.index>0){if(h.isFile)h.onChunk(null),h.partSize=0;else{if(1===h.fieldChunks.length)r(h.info,h.fieldChunks[0]);else{let e=new Uint8Array(h.fieldSize),t=0;for(let r=0;r<h.fieldChunks.length;r++){let i=h.fieldChunks[r];e.set(i,t),t+=i.length}r(h.info,e)}h.fieldSize=0,h.fieldChunks=[]}}if(h.state=N.headers,h.index=e,h.headerSkip=2,45===o[0]&&45===o[1])return n();h.parts++,h.parts>a&&i(BF)}if((h.partSize+=o.length)>l&&i(BR),h.state===N.headers){let e;let r=f(o,h.headerSkip);if(h.headerSkip=0,"Continue"===r._tag)return;if("Failure"===r._tag)return p(),i({_tag:"BadHeaders",error:r});let n=Bw(r.headers["content-type"]),a=Bw(r.headers["content-disposition"],!0);if("form-data"===a.value&&!("name"in a.parameters))return p(),i(BE);if("filename*"in a.parameters){let t=a.parameters["filename*"].split("''");2===t.length&&(e=decodeURIComponent(t[1]))}if(h.info={name:a.parameters.name??"",filename:e??a.parameters.filename,contentType:""===n.value?void 0!==a.parameters.filename?"application/octet-stream":"text/plain":n.value,contentTypeParameters:n.parameters,contentDisposition:a.value,contentDispositionParameters:a.parameters,headers:r.headers},h.state=N.body,h.isFile=s(h.info),h.isFile&&(h.onChunk=t(h.info)),r.endPosition<o.length){if(h.isFile)h.onChunk(o.subarray(r.endPosition));else{let e=o.subarray(r.endPosition);(h.fieldSize+=e.length)>u&&i(BN),h.fieldChunks.push(e)}}}else h.isFile?h.onChunk(o):((h.fieldSize+=o.length)>u&&i(BN),h.fieldChunks.push(o))},BA);return{write:e=>(h.totalSize+=e.length)>o?i(BT):d.write(e),end(){d.end(),h.state===N.body&&i(BI),h.state=N.headers,h.index=0,h.parts=0,h.onChunk=BM,h.info=void 0,h.totalSize=0,h.partSize=0,h.fieldChunks=[],h.fieldSize=0}}},BD=Symbol.for("@effect/platform/Path"),B$=s6("@effect/platform/Path");function BL(e,t){let r,i="",n=0,s=-1,a=0;for(let o=0;o<=e.length;++o){if(o<e.length)r=e.charCodeAt(o);else if(47===r)break;else r=47;if(47===r){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(i.length<2||2!==n||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2)){if(i.length>2){let e=i.lastIndexOf("/");if(e!==i.length-1){-1===e?(i="",n=0):n=(i=i.slice(0,e)).length-1-i.lastIndexOf("/"),s=o,a=0;continue}}else if(2===i.length||1===i.length){i="",n=0,s=o,a=0;continue}}t&&(i.length>0?i+="/..":i="..",n=2)}else i.length>0?i+="/"+e.slice(s+1,o):i=e.slice(s+1,o),n=o-s-1;s=o,a=0}else 46===r&&-1!==a?++a:a=-1}return i}let BU=function(){let e,t="",r=!1;for(let i=arguments.length-1;i>=-1&&!r;i--){let n;if(i>=0)n=arguments[i];else{let t=globalThis.process;void 0===e&&"process"in globalThis&&"object"==typeof t&&null!==t&&"function"==typeof t.cwd&&(e=t.cwd()),n=e}0!==n.length&&(t=n+"/"+t,r=47===n.charCodeAt(0))}return(t=BL(t,!r),r)?t.length>0?"/"+t:"/":t.length>0?t:"."},Bq=/%/g,BB=/\\/g,BJ=/\n/g,BH=/\r/g,BK=/\t/g,BV=B$.of({[BD]:BD,resolve:BU,normalize(e){if(0===e.length)return".";let t=47===e.charCodeAt(0),r=47===e.charCodeAt(e.length-1);return(0!==(e=BL(e,!t)).length||t||(e="."),e.length>0&&r&&(e+="/"),t)?"/"+e:e},isAbsolute:e=>e.length>0&&47===e.charCodeAt(0),join(){let e;if(0==arguments.length)return".";for(let t=0;t<arguments.length;++t){let r=arguments[t];r.length>0&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":BV.normalize(e)},relative(e,t){if(e===t||(e=BV.resolve(e),t=BV.resolve(t),e===t))return"";let r=1;for(;r<e.length&&47===e.charCodeAt(r);++r);let i=e.length,n=i-r,s=1;for(;s<t.length&&47===t.charCodeAt(s);++s);let a=t.length-s,o=n<a?n:a,l=-1,u=0;for(;u<=o;++u){if(u===o){if(a>o){if(47===t.charCodeAt(s+u))return t.slice(s+u+1);if(0===u)return t.slice(s+u)}else n>o&&(47===e.charCodeAt(r+u)?l=u:0===u&&(l=0));break}let i=e.charCodeAt(r+u);if(i!==t.charCodeAt(s+u))break;47===i&&(l=u)}let c="";for(u=r+l+1;u<=i;++u)(u===i||47===e.charCodeAt(u))&&(0===c.length?c+="..":c+="/..");return c.length>0?c+t.slice(s+l):(s+=l,47===t.charCodeAt(s)&&++s,t.slice(s))},dirname(e){if(0===e.length)return".";let t=e.charCodeAt(0),r=47===t,i=-1,n=!0;for(let r=e.length-1;r>=1;--r)if(47===(t=e.charCodeAt(r))){if(!n){i=r;break}}else n=!1;return -1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename(e,t){let r,i=0,n=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";let a=t.length-1,o=-1;for(r=e.length-1;r>=0;--r){let l=e.charCodeAt(r);if(47===l){if(!s){i=r+1;break}}else -1===o&&(s=!1,o=r+1),a>=0&&(l===t.charCodeAt(a)?-1==--a&&(n=r):(a=-1,n=o))}return i===n?n=o:-1===n&&(n=e.length),e.slice(i,n)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!s){i=r+1;break}}else -1===n&&(s=!1,n=r+1);return -1===n?"":e.slice(i,n)},extname(e){let t=-1,r=0,i=-1,n=!0,s=0;for(let a=e.length-1;a>=0;--a){let o=e.charCodeAt(a);if(47===o){if(!n){r=a+1;break}continue}-1===i&&(n=!1,i=a+1),46===o?-1===t?t=a:1!==s&&(s=1):-1!==t&&(s=-1)}return -1===t||-1===i||0===s||1===s&&t===i-1&&t===r+1?"":e.slice(t,i)},format:function(e){if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){let r=t.dir||t.root,i=t.base||(t.name||"")+(t.ext||"");return r?r===t.root?r+i:r+"/"+i:i}(0,e)},parse(e){let t;let r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;let i=e.charCodeAt(0),n=47===i;n?(r.root="/",t=1):t=0;let s=-1,a=0,o=-1,l=!0,u=e.length-1,c=0;for(;u>=t;--u){if(47===(i=e.charCodeAt(u))){if(!l){a=u+1;break}continue}-1===o&&(l=!1,o=u+1),46===i?-1===s?s=u:1!==c&&(c=1):-1!==s&&(c=-1)}return -1===s||-1===o||0===c||1===c&&s===o-1&&s===a+1?-1!==o&&(0===a&&n?r.base=r.name=e.slice(1,o):r.base=r.name=e.slice(a,o)):(0===a&&n?(r.name=e.slice(1,s),r.base=e.slice(1,o)):(r.name=e.slice(a,s),r.base=e.slice(a,o)),r.ext=e.slice(s,o)),a>0?r.dir=e.slice(0,a-1):n&&(r.dir="/"),r},sep:"/",fromFileUrl:function(e){if("file:"!==e.protocol)return uF(Od({module:"Path",method:"fromFileUrl",message:"URL must be of scheme file"}));if(""!==e.hostname)return uF(Od({module:"Path",method:"fromFileUrl",message:"Invalid file URL host"}));let t=e.pathname;for(let e=0;e<t.length;e++)if("%"===t[e]){let r=32|t.codePointAt(e+2);if("2"===t[e+1]&&102===r)return uF(Od({module:"Path",method:"fromFileUrl",message:"must not include encoded / characters"}))}return u5(decodeURIComponent(t))},toFileUrl:function(e){var t;let r=new URL("file://"),i=BU(e);return 47===e.charCodeAt(e.length-1)&&"/"!==i[i.length-1]&&(i+="/"),(t=i).includes("%")&&(t=t.replace(Bq,"%25")),t.includes("\\")&&(t=t.replace(BB,"%5C")),t.includes("\n")&&(t=t.replace(BJ,"%0A")),t.includes("\r")&&(t=t.replace(BH,"%0D")),t.includes("	")&&(t=t.replace(BK,"%09")),r.pathname=t,u5(r)},toNamespacedPath:$}),BW=Symbol.for("@effect/platform/Multipart"),BG=Symbol.for("@effect/platform/Multipart/MultipartError");class BY extends Og(BG,"MultipartError"){get message(){return this.reason}}let BZ=V("@effect/platform/Multipart/maxParts",()=>cD(tQ())),BQ=V("@effect/platform/Multipart/maxFieldSize",()=>cD(qk(0xa00000))),BX=V("@effect/platform/Multipart/maxFileSize",()=>cD(tQ())),B0=V("@effect/platform/Multipart/fieldMimeTypes",()=>cD(iv("application/json"))),B1=e=>up(t=>{let r=t.getFiberRef(B0);return u5({headers:e,maxParts:t5(t.getFiberRef(BZ)),maxFieldSize:Number(t.getFiberRef(BQ)),maxPartSize:t.getFiberRef(BX).pipe(t7(Number),t5),maxTotalSize:t.getFiberRef(Bo).pipe(t7(Number),t5),isFile:0===r.length?void 0:e=>!i1(r,t=>e.contentType.includes(t))&&Bj(e)})}),B2=e=>"Success"===e._tag?T4:TK(e.cause);class B3 extends eG{constructor(){super(),this[BW]=BW}}class B5 extends B3{constructor(e,t,r){super(),this._tag="Field",this.key=e,this.contentType=t,this.value=r}toJSON(){return{_id:"@effect/platform/Multipart/Part",_tag:"Field",key:this.key,contentType:this.contentType,value:this.value}}}class B4 extends B3{constructor(e,t){super(),this._tag="File",this.key=e.name,this.name=e.filename??e.name,this.contentType=e.contentType,this.content=z_(t)}toJSON(){return{_id:"@effect/platform/Multipart/Part",_tag:"File",key:this.key,name:this.name,contentType:this.contentType}}}let B6=(e,t)=>uM(q_,r=>uZ(zQ(t.content,r.sink(e)),e=>new BY({reason:"InternalError",cause:e})));class B8 extends B3{constructor(e,t,r,i){super(),this._tag="PersistedFile",this.key=e,this.name=t,this.contentType=r,this.path=i}toJSON(){return{_id:"@effect/platform/Multipart/Part",_tag:"PersistedFile",key:this.key,name:this.name,contentType:this.contentType,path:this.path}}}let B7=(e,t=16)=>qS(vy([B1(e),Bb(t)]),([e,t])=>{let r=[],i=tQ(),n=BP({...e,onField(e,t){r.push(new B5(e.name,e.contentType,(function(e){if("utf-8"===e||"utf8"===e||""===e)return Bz;try{return new TextDecoder(e)}catch(e){return Bz}})(e.contentTypeParameters.charset??"utf-8").decode(t)))},onFile(e){let t=[],i=!1,n=T3(()=>{if(0===t.length)return i?T4:Nq(s,n);let e=iE(t);return t=[],i?T6(e):Nq(T6(e),Nq(s,n))});return r.push(new B4(e,n)),function(e){null===e?i=!0:t.push(e)}},onError(e){i=tI(hE(function(e){if("ReachedLimit"===e._tag)switch(e.limit){case"MaxParts":return new BY({reason:"TooManyParts",cause:e});case"MaxFieldSize":return new BY({reason:"FieldTooLarge",cause:e});case"MaxPartSize":return new BY({reason:"FileTooLarge",cause:e});case"MaxTotalSize":return new BY({reason:"BodyTooLarge",cause:e})}return new BY({reason:"Parse",cause:e})}(e)))},onDone(){i=tI(hz)}}),s=TW(t.takeAll,([e,t])=>T5(()=>{i$(e,i$(n.write)),t&&n.end()})),a=TW(s,()=>{if(0===r.length)return"None"===i._tag?a:B2(i.value);let e=iE(r);return r=[],Nq(T6(e),"None"===i._tag?a:B2(i.value))});return TB(a,{awaitRead:()=>cr,emit:e=>t.offer(e),error:e=>(i=tI(hI(e)),t.end),done:e=>t.end})},([,e])=>e.shutdown),B9=(e,t=B6)=>k9(function*(){let r=yield*q_,i=yield*B$,n=yield*r.makeTempDirectoryScoped(),s=Object.create(null);return yield*zX(e,e=>{if("Field"===e._tag)return e.key in s?"string"==typeof s[e.key]?s[e.key]=[s[e.key],e.value]:s[e.key].push(e.value):s[e.key]=e.value,cr;if(""===e.name)return cr;let r=i.join(n,i.basename(e.name).slice(-128)),a=new B8(e.key,e.name,e.contentType,r);return Array.isArray(s[e.key])?s[e.key].push(a):s[e.key]=[a],t(r,e)}),s}).pipe(gF({SystemError:e=>uF(new BY({reason:"InternalError",cause:e})),BadArgument:e=>uF(new BY({reason:"InternalError",cause:e}))})),Je=Symbol.for("@effect/platform/HttpServerRequest"),Jt=s6("@effect/platform/HttpServerRequest"),Jr=s6("@effect/platform/HttpServerRequest/ParsedSearchParams"),Ji=e=>{if("/"===e[0])return e;let t=e.indexOf("/",e.indexOf("//")+2);return -1===t?"/":e.slice(t)};class Jn extends eG{constructor(e,t,r,i){super(),this.source=e,this.url=t,this.headersOverride=r,this.remoteAddressOverride=i,this[Je]=Je,this[Bn]=Bn}toJSON(){return Bl(this,{_id:"@effect/platform/HttpServerRequest",method:this.method,url:this.originalUrl})}modify(e){return new Jn(this.source,e.url??this.url,e.headers??this.headersOverride,e.remoteAddress??this.remoteAddressOverride)}get method(){return this.source.method.toUpperCase()}get originalUrl(){return this.source.url}get remoteAddress(){return this.remoteAddressOverride?tI(this.remoteAddressOverride):tQ()}get headers(){return this.headersOverride??=qa(this.source.headers),this.headersOverride}get cookies(){return this.cachedCookies?this.cachedCookies:this.cachedCookies=function(e){let t={},r=e.length,i=0,n=0;for(;n!==r;){-1===(n=e.indexOf(";",i))&&(n=r);let s=e.indexOf("=",i);if(-1===s)break;if(s>n){i=n+1;continue}let a=e.substring(i,s++).trim();if(void 0===t[a]){let r=34===e.charCodeAt(s)?e.substring(s+1,n-1).trim():e.substring(s,n).trim();t[a]=-1!==r.indexOf("%")?PH(r):r}i=n+1}return t}(this.headers.cookie??"")}get stream(){return this.source.body?PO(()=>this.source.body,e=>new Be({request:this,reason:"Decode",cause:e})):zp(new Be({request:this,reason:"Decode",description:"can not create stream from empty body"}))}get text(){return this.textEffect||(this.textEffect=wL(k7(g5({try:()=>this.source.text(),catch:e=>new Be({request:this,reason:"Decode",cause:e})})))),this.textEffect}get json(){return g4(this.text,{try:e=>JSON.parse(e),catch:e=>new Be({request:this,reason:"Decode",cause:e})})}get urlParamsBody(){return uM(this.text,e=>gC({try:()=>qm(new URLSearchParams(e)),catch:e=>new Be({request:this,reason:"Decode",cause:e})}))}get multipart(){return this.multipartEffect||(this.multipartEffect=wL(k7(B9(this.multipartStream)))),this.multipartEffect}get multipartStream(){return zB(zD(this.stream,e=>new BY({reason:"InternalError",cause:e})),B7(this.headers))}get arrayBuffer(){return this.arrayBufferEffect||(this.arrayBufferEffect=wL(k7(g5({try:()=>this.source.arrayBuffer(),catch:e=>new Be({request:this,reason:"Decode",cause:e})})))),this.arrayBufferEffect}get upgrade(){return uF(new Be({request:this,reason:"Decode",description:"Not an upgradeable ServerRequest"}))}}let Js=(e,t)=>uM(Jt,Ba(e,t)),Ja=(e,t)=>uM(Jt,Bs(e,t)),Jo=e=>new Jn(e,Ji(e.url)),Jl=e=>{let t=e.headers.host??"localhost",r="https"===e.headers["x-forwarded-proto"]?"https":"http";try{return tI(new URL(e.url,`${r}://${t}`))}catch(e){return tQ()}},Ju=V(Symbol.for("@effect/platform/HttpApp/preResponseHandlers"),()=>cD(tQ())),Jc=e=>qo({b3:`${e.traceId}-${e.spanId}-${e.sampled?"1":"0"}${"Some"===e.parent._tag?`-${e.parent.value.spanId}`:""}`,traceparent:`00-${e.traceId}-${e.spanId}-${e.sampled?"01":"00"}`}),Jh=e=>{let t=Jg(e);return"Some"===t._tag||"Some"===(t=Jp(e))._tag?t:Jf(e)},Jp=e=>{if(!("b3"in e))return tQ();let t=e.b3.split("-");return t.length<2?tQ():tI(gx({traceId:t[0],spanId:t[1],sampled:!t[2]||"1"===t[2]}))},Jf=e=>e["x-b3-traceid"]&&e["x-b3-spanid"]?tI(gx({traceId:e["x-b3-traceid"],spanId:e["x-b3-spanid"],sampled:!e["x-b3-sampled"]||"1"===e["x-b3-sampled"]})):tQ(),Jd=/^[0-9a-f]{32}$/i,Jm=/^[0-9a-f]{16}$/i,Jg=e=>{if(!e.traceparent)return tQ();let t=e.traceparent.split("-");if(4!==t.length)return tQ();let[r,i,n,s]=t;return"00"===r?!1===Jd.test(i)||!1===Jm.test(n)?tQ():tI(gx({traceId:i,spanId:n,sampled:(1&parseInt(s,16))==1})):tQ()},Jb=V(Symbol.for("@effect/platform/HttpMiddleware/tracerDisabledWhen"),()=>cD(q)),Jx=ae()("@effect/platform/HttpMiddleware/SpanNameGenerator",{defaultValue:()=>e=>`http.server ${e.method}`}),Jy=e=>up(t=>{let r=s3(t.currentContext,Jt);if(t.getFiberRef(Jb)(r))return e;let i=t5(Jl(r));void 0!==i&&(""!==i.username||""!==i.password)&&(i.username="REDACTED",i.password="REDACTED");let n=t.getFiberRef(qf),s=qp(r.headers,n);return br(s3(t.currentContext,Jx)(r),{parent:t5(Jh(r.headers)),kind:"server",captureStackTrace:!1},t=>{for(let e in t.attribute("http.request.method",r.method),void 0!==i&&(t.attribute("url.full",i.toString()),t.attribute("url.path",i.pathname),""!==i.search.slice(1)&&t.attribute("url.query",i.search.slice(1)),t.attribute("url.scheme",i.protocol.slice(0,-1))),void 0!==r.headers["user-agent"]&&t.attribute("user_agent.original",r.headers["user-agent"]),s)t.attribute(`http.request.header.${e}`,String(s[e]));return"Some"===r.remoteAddress._tag&&t.attribute("client.address",r.remoteAddress.value),uM(uI(bi(e,t)),e=>{let r=Bi(e);t.attribute("http.response.status_code",r.status);let i=qp(r.headers,n);for(let e in i)t.attribute(`http.response.header.${e}`,String(i[e]));return e})})}),Jv=Symbol.for("@effect/platform/HttpApp/handled"),JS=(e,t,r)=>{let i=uv(up(r=>uM(e,e=>{let i=s3(r.currentContext,Jt),n=r.getFiberRef(J_);return"None"===n._tag?(i[Jv]=!0,ud(t(i,e),e)):u8(n.value(i,e),e=>(i[Jv]=!0,t(i,e)))})),e=>up(r=>uM(Br(e),([e,i])=>{let n=s3(r.currentContext,Jt),s=r.getFiberRef(J_);return"None"===s._tag?(n[Jv]=!0,vD(t(n,e),uR(i))):vD(u8(s.value(n,e),e=>(n[Jv]=!0,t(n,e))),uR(i))})));return ce(Oi($(void 0===r?Jy(i):uL(r(Jy(i)),{onFailure:e=>up(r=>{let i=s3(r.currentContext,Jt);return Jv in i?cr:uL(Br(e),{onFailure:e=>t(i,qH({status:500})),onSuccess:([e])=>t(i,e)})}),onSuccess:e=>up(r=>{let i=s3(r.currentContext,Jt);return Jv in i?cr:t(i,e)})}))))},J_=Ju,Jw=e=>{let t=wy(e);return(r,i)=>{let n=Symbol.for("@effect/platform/HttpApp/resolve"),s=JS(r,(t,r)=>(t[n](qG(r,{withoutBody:"HEAD"===t.method,runtime:e})),cr),i);return(r,i)=>new Promise(a=>{let o=new Map(e.context.unsafeMap);if(sG(i))for(let[e,t]of i.unsafeMap)o.set(e,t);let l=Jo(r);o.set(Jt.key,l),l[n]=a;let u=t(cz(s,cB,sV(o)));r.signal?.addEventListener("abort",()=>{u.unsafeInterruptAsFork(q5)},{once:!0})})}},Jk=/\+/g,JO=function(){};JO.prototype=Object.create(null);let JC=Array.from({length:256},(e,t)=>"%"+((t<16?"0":"")+t.toString(16)).toUpperCase()),JE=new Int8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,1,1,1,1,0,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,0]);function JI(e){let t=e.length;if(0===t)return"";let r="",i=0,n=0;e:for(;n<t;n++){let s=e.charCodeAt(n);for(;s<128;){if(1!==JE[s]&&(i<n&&(r+=e.slice(i,n)),i=n+1,r+=JC[s]),++n===t)break e;s=e.charCodeAt(n)}if(i<n&&(r+=e.slice(i,n)),s<2048){i=n+1,r+=JC[192|s>>6]+JC[128|63&s];continue}if(s<55296||s>=57344){i=n+1,r+=JC[224|s>>12]+JC[128|s>>6&63]+JC[128|63&s];continue}if(++n>=t)throw Error("URI malformed");let a=1023&e.charCodeAt(n);i=n+1,r+=JC[240|(s=65536+((1023&s)<<10|a))>>18]+JC[128|s>>12&63]+JC[128|s>>6&63]+JC[128|63&s]}return 0===i?e:i<t?r+e.slice(i):r}let JF=/^https?:\/\/.*?\//,JT=/(\/:[^/()]*?)\?(\/?)/;class JR{constructor(e={}){this.options={ignoreTrailingSlash:!0,ignoreDuplicateSlashes:!0,caseSensitive:!1,maxParamLength:100,...e}}options;routes=[];trees={};on(e,t,r){let i=t.match(JT);if(i&&void 0!==i.index){JD(t.length===i.index+i[0].length,"Optional Parameter needs to be the last parameter of the path");let n=t.replace(JT,"$1$2"),s=t.replace(JT,"$2");this.on(e,n,r),this.on(e,s,r);return}for(let i of(this.options.ignoreDuplicateSlashes&&(t=J$(t)),this.options.ignoreTrailingSlash&&(t=JL(t)),"string"==typeof e?[e]:e))this._on(i,t,r)}all(e,t){this.on(JB,e,t)}_on(e,t,r){void 0===this.trees[e]&&(this.trees[e]=new JM("/"));let i=t;if("*"===i&&0!==this.trees[e].prefix.length){let t=this.trees[e];this.trees[e]=new JM(""),this.trees[e].staticChildren["/"]=t}let n=this.trees[e].prefix.length,s=this.trees[e],a=[];for(let e=0;e<=i.length;e++){if(58===i.charCodeAt(e)&&58===i.charCodeAt(e+1)){e++;continue}let t=58===i.charCodeAt(e)&&58!==i.charCodeAt(e+1),r=42===i.charCodeAt(e);if(t||r||e===i.length&&e!==n){let t=i.slice(n,e);this.options.caseSensitive||(t=t.toLowerCase()),t=(t=t.split("::").join(":")).split("%").join("%25"),s=s.createStaticChild(t)}if(t){let t=!1,r=[],l=e+1;for(let u=l;;u++){let c=i.charCodeAt(u),h=40===c,p=45===c||46===c,f=47===c||u===i.length;if(h||p||f){let c=i.slice(l,u);if(a.push(c),t=t||h||p,h){var o;let e=function(e,t){let r=1;for(;t<e.length;){if("\\"===e[++t]){t++;continue}if(")"===e[t]?r--:"("===e[t]&&r++,!r)return t}throw TypeError('Invalid regexp expression in "'+e+'"')}(i,u),t=i.slice(u,e+1);r.push((94===(o=t).charCodeAt(1)&&(o=o.slice(0,1)+o.slice(2)),36===o.charCodeAt(o.length-2)&&(o=o.slice(0,o.length-2)+o.slice(o.length-1)),o)),u=e+1}else r.push("(.*?)");let d=u;for(;u<i.length;u++){let e=i.charCodeAt(u);if(47===e)break;if(58===e){if(58===i.charCodeAt(u+1))u++;else break}}let m=i.slice(d,u);if(m&&(m=(m=m.split("::").join(":")).split("%").join("%25"),r.push(m.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))),l=u+1,f||47===i.charCodeAt(u)||u===i.length){let a=t?"()"+m:m,o=i.slice(e,u);i=i.slice(0,e+1)+a+i.slice(u),e+=a.length;let l=t?RegExp("^"+r.join("")+"$"):void 0;s=s.createParametricChild(l,m,o),n=e+1;break}}}}else if(r&&(a.push("*"),s=s.createWildcardChild(),n=e+1,e!==i.length-1))throw Error("Wildcard must be the last character in the route")}for(let t of(this.options.caseSensitive||(i=i.toLowerCase()),"*"===i&&(i="/*"),this.routes))if(t.method===e&&t.pattern===i)throw Error(`Method '${e}' already declared for route '${i}'`);let l={method:e,path:t,pattern:i,params:a,handler:r};this.routes.push(l),s.addRoute(l)}has(e,t){let r=this.trees[e];if(void 0===r)return!1;let i=r.getStaticChild(t);return void 0!==i&&i.isLeafNode}find(e,t){let r,i,n,s=this.trees[e];if(void 0===s)return;47!==t.charCodeAt(0)&&(t=t.replace(JF,"/")),this.options.ignoreDuplicateSlashes&&(t=J$(t));try{t=(r=function(e){let t=!1,r=!1,i="";for(let n=1;n<e.length;n++){let s=e.charCodeAt(n);if(37===s){let i=e.charCodeAt(n+1),s=e.charCodeAt(n+2);void 0===JU(i,s)?t=!0:(r=!0,50===i&&53===s&&(t=!0,e=e.slice(0,n+1)+"25"+e.slice(n+1),n+=2),n+=2)}else if(63===s||59===s||35===s){i=e.slice(n+1),e=e.slice(0,n);break}}return{path:t?decodeURI(e):e,querystring:i,shouldDecodeParam:r}}(t)).path,i=r.querystring,n=r.shouldDecodeParam}catch(e){return}this.options.ignoreTrailingSlash&&(t=JL(t));let a=t;!1===this.options.caseSensitive&&(t=t.toLowerCase());let o=this.options.maxParamLength,l=s.prefix.length,u=[],c=t.length,h=[];for(;;){if(l===c&&s.isLeafNode){let e=s.handlerStorage?.find();if(void 0!==e)return{handler:e.handler,params:e.createParams(u),searchParams:function(e){let t=new JO;if("string"!=typeof e)return t;let r=e.length,i="",n="",s=-1,a=-1,o=!1,l=!1,u=!1,c=!1,h=!1,p=0;for(let f=0;f<r+1;f++)if(38===(p=f!==r?e.charCodeAt(f):38)){if((h=a>s)||(a=f),i=e.slice(s+1,a),h||i.length>0){u&&(i=i.replace(Jk," ")),o&&(i=decodeURIComponent(i)||i),h&&(n=e.slice(a+1,f),c&&(n=n.replace(Jk," ")),l&&(n=decodeURIComponent(n)||n));let r=t[i];void 0===r?t[i]=n:r.pop?r.push(n):t[i]=[r,n]}n="",s=f,a=f,o=!1,l=!1,u=!1,c=!1}else 61===p?a<=s?a=f:l=!0:43===p?a>s?c=!0:u=!0:37===p&&(a>s?l=!0:o=!0);return t}(i)}}let e=s.getNextNode(t,l,h,u.length);if(void 0===e){if(0===h.length)return;let t=h.pop();l=t.brotherPathIndex,u.splice(t.paramsCount),e=t.brotherNode}if("StaticNode"===(s=e)._tag){l+=s.prefix.length;continue}if("WildcardNode"===s._tag){let e=a.slice(l);n&&(e=Jq(e)),u.push(e),l=c;continue}if("ParametricNode"===s._tag){let e=a.indexOf("/",l);-1===e&&(e=c);let t=a.slice(l,e);if(n&&(t=Jq(t)),void 0!==s.regex){let e=s.regex.exec(t);if(null===e)continue;for(let t=1;t<e.length;t++){let r=e[t];if(r.length>o)return;u.push(r)}}else{if(t.length>o)return;u.push(t)}l=e}}}}class JN{handlers=[];unconstrainedHandler;find(){return this.unconstrainedHandler}add(e){let t={params:e.params,handler:e.handler,createParams:function(e){let t=e.length;return function(r){let i={};for(let n=0;n<t;n++)i[e[n]]=r[n];return i}}(e.params)};this.handlers.push(t),this.unconstrainedHandler=this.handlers[0]}}class JA{isLeafNode=!1;routes;handlerStorage;addRoute(e){void 0===this.routes?this.routes=[e]:this.routes.push(e),void 0===this.handlerStorage&&(this.handlerStorage=new JN),this.isLeafNode=!0,this.handlerStorage.add(e)}}class Jj extends JA{staticChildren={};findStaticMatchingChild(e,t){let r=this.staticChildren[e.charAt(t)];if(void 0!==r&&r.matchPrefix(e,t))return r}getStaticChild(e,t=0){if(e.length===t)return this;let r=this.findStaticMatchingChild(e,t);if(void 0!==r)return r.getStaticChild(e,t+r.prefix.length)}createStaticChild(e){if(0===e.length)return this;let t=this.staticChildren[e.charAt(0)];if(t){let r=1;for(;r<t.prefix.length;r++)if(e.charCodeAt(r)!==t.prefix.charCodeAt(r)){t=t.split(this,r);break}return t.createStaticChild(e.slice(r))}let r=e.charAt(0);return this.staticChildren[r]=new JM(e),this.staticChildren[r]}}class JM extends Jj{_tag="StaticNode";constructor(e){super(),this.setPrefix(e)}prefix;matchPrefix;parametricChildren=[];wildcardChild;setPrefix(e){if(this.prefix=e,1===e.length)this.matchPrefix=(e,t)=>!0;else{let t=e.length;this.matchPrefix=function(e,r){for(let i=1;i<t;i++)if(e.charCodeAt(r+i)!==this.prefix.charCodeAt(i))return!1;return!0}}}getParametricChild(e){if(void 0===e)return this.parametricChildren.find(e=>!1===e.isRegex);let t=e.source;return this.parametricChildren.find(e=>void 0!==e.regex&&e.regex.source===t)}createParametricChild(e,t,r){let i=this.getParametricChild(e);return void 0!==i?i.nodePaths.add(r):(i=new Jz(e,t,r),this.parametricChildren.push(i),this.parametricChildren.sort((e,t)=>e.isRegex?t.isRegex?void 0===e.staticSuffix?1:void 0===t.staticSuffix?-1:t.staticSuffix.endsWith(e.staticSuffix)?1:e.staticSuffix.endsWith(t.staticSuffix)?-1:0:-1:1)),i}createWildcardChild(){return void 0===this.wildcardChild&&(this.wildcardChild=new JP),this.wildcardChild}split(e,t){let r=this.prefix.slice(0,t),i=this.prefix.slice(t);this.setPrefix(i);let n=new JM(r);return n.staticChildren[i.charAt(0)]=this,e.staticChildren[r.charAt(0)]=n,n}getNextNode(e,t,r,i){let n=this.findStaticMatchingChild(e,t),s=0;if(void 0===n){if(0===this.parametricChildren.length)return this.wildcardChild;n=this.parametricChildren[0],s=1}void 0!==this.wildcardChild&&r.push({paramsCount:i,brotherPathIndex:t,brotherNode:this.wildcardChild});for(let e=this.parametricChildren.length-1;e>=s;e--)r.push({paramsCount:i,brotherPathIndex:t,brotherNode:this.parametricChildren[e]});return n}}class Jz extends Jj{regex;staticSuffix;_tag="ParametricNode";constructor(e,t,r){super(),this.regex=e,this.staticSuffix=t,this.isRegex=!!e,this.nodePaths=new Set([r])}isRegex;nodePaths;getNextNode(e,t){return this.findStaticMatchingChild(e,t)}}class JP extends JA{_tag="WildcardNode";getNextNode(e,t,r,i){}}let JD=(e,t)=>{if(!e)throw Error(t)};function J$(e){return e.replace(/\/\/+/g,"/")}function JL(e){return e.length>1&&47===e.charCodeAt(e.length-1)?e.slice(0,-1):e}function JU(e,t){return 50===e?53===t?"%":51===t?"#":52===t?"$":54===t?"&":66===t||98===t?"+":67===t||99===t?",":70===t||102===t?"/":void 0:51===e?65===t||97===t?":":66===t||98===t?";":68===t||100===t?"=":70===t||102===t?"?":void 0:52===e&&48===t?"@":void 0}function Jq(e){let t=e.indexOf("%");if(-1===t)return e;let r="",i=t;for(let n=t;n<e.length;n++)if(37===e.charCodeAt(n)){let t=JU(e.charCodeAt(n+1),e.charCodeAt(n+2));r+=e.slice(i,n)+t,i=n+3}return e.slice(0,t)+r+e.slice(i)}let JB=["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],JJ=(e={})=>new JR(e),JH=Symbol.for("@effect/platform/HttpClientError"),JK=e=>ea(e,JH);class JV extends Og(JH,"RequestError"){get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){return this.description?`${this.reason}: ${this.description} (${this.methodAndUrl})`:`${this.reason} error (${this.methodAndUrl})`}}class JW extends Og(JH,"ResponseError"){get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){let e=`${this.response.status} ${this.methodAndUrl}`;return this.description?`${this.reason}: ${this.description} (${e})`:`${this.reason} error (${e})`}}let JG=Symbol.for("@effect/platform/HttpClientRequest"),JY={[JG]:JG,...eW,toJSON(){return{_id:"@effect/platform/HttpClientRequest",method:this.method,url:this.url,urlParams:this.urlParams,hash:this.hash,headers:e2(this.headers),body:this.body.toJSON()}},pipe(){return e3(this,arguments)}};function JZ(e,t,r,i,n,s){let a=Object.create(JY);return a.method=e,a.url=t,a.urlParams=r,a.hash=i,a.headers=n,a.body=s,a}let JQ=JZ("GET","",[],tQ(),qs,qR),JX=e=>(t,r)=>J8(JQ,{method:e,url:t,...r??void 0}),J0=JX("GET"),J1=JX("POST"),J2=JX("PUT"),J3=JX("PATCH"),J5=JX("DELETE"),J4=JX("HEAD"),J6=JX("OPTIONS"),J8=D(2,(e,t)=>{let r=e;return t.method&&(r=Hr(r,t.method)),t.url&&(r=Hi(r,t.url)),t.headers&&(r=J9(r,t.headers)),t.urlParams&&(r=Ha(r,t.urlParams)),t.hash&&(r=Ho(r,t.hash)),t.body&&(r=Hl(r,t.body)),t.accept&&(r=He(r,t.accept)),t.acceptJson&&(r=Ht(r)),r}),J7=D(3,(e,t,r)=>JZ(e.method,e.url,e.urlParams,e.hash,ql(e.headers,t,r),e.body)),J9=D(2,(e,t)=>JZ(e.method,e.url,e.urlParams,e.hash,qu(e.headers,t),e.body)),He=D(2,(e,t)=>J7(e,"Accept",t)),Ht=He("application/json"),Hr=D(2,(e,t)=>JZ(t,e.url,e.urlParams,e.hash,e.headers,e.body)),Hi=D(2,(e,t)=>{if("string"==typeof t)return JZ(e.method,t,e.urlParams,e.hash,e.headers,e.body);let r=new URL(t.toString()),i=qm(r.searchParams),n=r.hash?tI(r.hash.slice(1)):tQ();return r.search="",r.hash="",JZ(e.method,r.toString(),i,n,e.headers,e.body)}),Hn=D(2,(e,t)=>JZ(e.method,t.endsWith("/")&&e.url.startsWith("/")?t+e.url.slice(1):t+e.url,e.urlParams,e.hash,e.headers,e.body)),Hs=D(3,(e,t,r)=>JZ(e.method,e.url,qx(e.urlParams,t,r),e.hash,e.headers,e.body)),Ha=D(2,(e,t)=>JZ(e.method,e.url,qb(e.urlParams,t),e.hash,e.headers,e.body)),Ho=D(2,(e,t)=>JZ(e.method,e.url,e.urlParams,tI(t),e.headers,e.body)),Hl=D(2,(e,t)=>{let r=e.headers;if("Empty"===t._tag)r=qh(qh(r,"Content-Type"),"Content-length");else{let e=t.contentType;e&&(r=ql(r,"content-type",e));let i=t.contentLength;i&&(r=ql(r,"content-length",i.toString()))}return JZ(e.method,e.url,e.urlParams,e.hash,r,t)}),Hu=D(2,(e,t)=>uG(qP(t),t=>Hl(e,t))),Hc=Symbol.for("@effect/platform/HttpClientResponse"),Hh=(e,t)=>new Hp(e,t);class Hp extends eG{constructor(e,t){super(),this.request=e,this.source=t,this[Bn]=Bn,this[Hc]=Hc}toJSON(){return Bl(this,{_id:"@effect/platform/HttpClientResponse",request:this.request.toJSON(),status:this.status})}get status(){return this.source.status}get headers(){return qa(this.source.headers)}get cookies(){return this.cachedCookies?this.cachedCookies:this.cachedCookies=PM(this.source.headers.getSetCookie())}get remoteAddress(){return tQ()}get stream(){return this.source.body?PO(()=>this.source.body,e=>new JW({request:this.request,response:this,reason:"Decode",cause:e})):zp(new JW({request:this.request,response:this,reason:"EmptyBody",description:"can not create stream from empty body"}))}get json(){return g4(this.text,{try:e=>""===e?null:JSON.parse(e),catch:e=>new JW({request:this.request,response:this,reason:"Decode",cause:e})})}get text(){return this.textBody??=g5({try:()=>this.source.text(),catch:e=>new JW({request:this.request,response:this,reason:"Decode",cause:e})}).pipe(k7,wL)}get urlParamsBody(){return uM(this.text,e=>gC({try:()=>qm(new URLSearchParams(e)),catch:e=>new JW({request:this.request,response:this,reason:"Decode",cause:e})}))}get formData(){return this.formDataBody??=g5({try:()=>this.source.formData(),catch:e=>new JW({request:this.request,response:this,reason:"Decode",cause:e})}).pipe(k7,wL)}get arrayBuffer(){return this.arrayBufferBody??=g5({try:()=>this.source.arrayBuffer(),catch:e=>new JW({request:this.request,response:this,reason:"Decode",cause:e})}).pipe(k7,wL)}}(e,t)=>u4(()=>t(e.status)?u5(e):uF(new JW({response:e,request:e.request,reason:"StatusCode",description:"invalid status code"})));let Hf=e=>e.status>=200&&e.status<300?u5(e):uF(new JW({response:e,request:e.request,reason:"StatusCode",description:"non 2xx status code"})),Hd=Symbol.for("@effect/platform/HttpClient"),Hm=s6("@effect/platform/HttpClient"),Hg=V(Symbol.for("@effect/platform/HttpClient/tracerDisabledWhen"),()=>cD(q)),Hb=V(Symbol.for("@effect/platform/HttpClient/currentTracerPropagation"),()=>cD(!0)),Hx=ae()("@effect/platform/HttpClient/SpanNameGenerator",{defaultValue:()=>e=>`http.client ${e.method}`}),Hy={[Hd]:Hd,pipe(){return e3(this,arguments)},...eW,toJSON:()=>({_id:"@effect/platform/HttpClient"}),get(e,t){return this.execute(J0(e,t))},head(e,t){return this.execute(J4(e,t))},post(e,t){return this.execute(J1(e,t))},put(e,t){return this.execute(J2(e,t))},patch(e,t){return this.execute(J3(e,t))},del(e,t){return this.execute(J5(e,t))},options(e,t){return this.execute(J6(e,t))}},Hv=(e,t)=>{let r=Object.create(Hy);return r.preprocess=t,r.postprocess=e,r.execute=function(r){return e(t(r))},r},HS=V("@effect/platform/HttpClient/responseRegistry",()=>{if("FinalizationRegistry"in globalThis&&globalThis.FinalizationRegistry){let e=new FinalizationRegistry(e=>{e.abort()});return{register(t,r){e.register(t,r,t)},unregister(t){e.unregister(t)}}}let e=new Map;return{register(t,r){e.set(t,setTimeout(()=>r.abort(),5e3))},unregister(t){let r=e.get(t);void 0!==r&&(clearTimeout(r),e.delete(t))}}});class H_{static{g=Hc,b=Bn}constructor(e,t){this[g]=Hc,this[b]=Bn,this.original=e,this.controller=t}applyInterrupt(e){return u4(()=>(HS.unregister(this.original),uX(e,()=>u6(()=>{this.controller.abort()}))))}get request(){return this.original.request}get status(){return this.original.status}get headers(){return this.original.headers}get cookies(){return this.original.cookies}get remoteAddress(){return this.original.remoteAddress}get formData(){return this.applyInterrupt(this.original.formData)}get text(){return this.applyInterrupt(this.original.text)}get json(){return this.applyInterrupt(this.original.json)}get urlParamsBody(){return this.applyInterrupt(this.original.urlParamsBody)}get arrayBuffer(){return this.applyInterrupt(this.original.arrayBuffer)}get stream(){return z7(()=>(HS.unregister(this.original),zu(this.original.stream,e=>(bn(e)&&this.controller.abort(),cr))))}toJSON(){return this.original.toJSON()}[eH](){return this.original[eH]()}}let{del:Hw,execute:Hk,get:HO,head:HC,options:HE,patch:HI,post:HF,put:HT}=new Proxy({},{get:(e,t,r)=>(...e)=>uM(Hm,r=>r[t](...e))}),HR=D(2,(e,t)=>Hv(r=>t(e.postprocess(r)),e.preprocess)),HN=D(2,(e,t)=>Hv(e.postprocess,r=>uG(e.preprocess(r),t))),HA=e=>JK(e)&&("RequestError"===e._tag&&"Transport"===e.reason||"ResponseError"===e._tag&&e.response.status>=429),Hj=e=>HR(e,uM(Hf)),HM=Symbol.for("@effect/platform/Etag/Generator"),Hz=s6("@effect/platform/Etag/Generator"),HP=e=>{let t="Some"===e.mtime._tag?e.mtime.value.getTime().toString(16):"0";return`${e.size.toString(16)}-${t}`},HD=e=>`${e.size.toString(16)}-${e.lastModified.toString(16)}`,H$=e=>{switch(e._tag){case"Weak":return`W/"${e.value}"`;case"Strong":return`"${e.value}"`}},HL=Symbol.for("@effect/platform/HttpPlatform"),HU=s6("@effect/platform/HttpPlatform"),Hq=s6("@effect/platform/HttpServer");O=e=>uM(Hm,t=>{if("UnixAddress"===e._tag)return uO(Error("HttpServer.layerTestClient: UnixAddress not supported"));let r="0.0.0.0"===e.hostname?"127.0.0.1":e.hostname;return u5(HN(t,Hn(`http://${r}:${e.port}`)))}),uM(Hq,e=>O(e.address));let HB=Symbol.for("@effect/platform/HttpRouter"),HJ=Symbol.for("@effect/platform/HttpRouter/Route"),HH=Symbol.for("@effect/platform/HttpRouter/RouteContext"),HK=s6("@effect/platform/HttpRouter/RouteContext"),HV=e=>ea(e,HB),HW=V("@effect/platform/HttpRouter/currentRouterConfig",()=>cD({}));class HG extends fD{constructor(e,t){super(),this.routes=e,this.mounts=t,this[HB]=HB,this.httpApp=cE(HW).pipe(uM(e=>this.httpApp=HY(this,e)))}commit(){return this.httpApp}toJSON(){return{_id:"Router",routes:this.routes.toJSON(),mounts:this.mounts.toJSON()}}toString(){return eV(this)}[eH](){return this.toJSON()}}let HY=(e,t)=>{let r=JJ(t),i=ik(e.mounts).map(([e,t,r])=>[e,new HX(new HQ("*",r?.includePrefix?`${e}/*`:"/*",t,r?.includePrefix?tQ():tI(e),!1),{}),r]),n=i.length;return i$(e.routes,e=>{"*"===e.method?r.all(e.path,e):r.on(e.method,e.path,e)}),up(e=>{let t=sV(new Map(e.getFiberRef(cB).unsafeMap)),s=s3(t,Jt);if(n>0)for(let e=0;e<n;e++){let[r,n,a]=i[e];if(s.url.startsWith(r))return t.unsafeMap.set(HK.key,n),a?.includePrefix!==!0&&t.unsafeMap.set(Jt.key,HZ(s,r)),cz(uM(n.route.handler,q0),cB,t)}let a=r.find(s.method,s.url);if(void 0===a&&"HEAD"===s.method&&(a=r.find("GET",s.url)),void 0===a)return uF(new Bt({request:s}));let o=a.handler;"Some"===o.prefix._tag&&t.unsafeMap.set(Jt.key,HZ(s,o.prefix.value)),t.unsafeMap.set(Jr.key,a.searchParams),t.unsafeMap.set(HK.key,new HX(o,a.params));let l=s5(t,fv);"Some"===l._tag&&"Span"===l.value._tag&&l.value.attribute("http.route",o.path);let u=uM(o.handler,q0);return cz(o.uninterruptible?u:uV(u),cB,t)})};function HZ(e,t){let r=t.length;return e.modify({url:e.url.length<=r?"/":e.url.slice(r)})}class HQ extends eG{constructor(e,t,r,i=tQ(),n=!1){super(),this.method=e,this.path=t,this.handler=r,this.prefix=i,this.uninterruptible=n,this[HJ]=HJ}toJSON(){return{_id:"@effect/platform/HttpRouter/Route",method:this.method,path:this.path,prefix:this.prefix.toJSON()}}}class HX{constructor(e,t){this.route=e,this.params=t,this[HH]=HH}}let H0=new HG(iy(),iy()),H1=((e,t)=>H1(e,t),(...e)=>new HG(e.reduce((e,t)=>iM(e,t.routes),iy()),e.reduce((e,t)=>iM(e,t.mounts),iy()))),H2=e=>e.endsWith("/")?e.slice(0,-1):e,H3=((e,t)=>(t=H2(t),new HG(iK(e.routes,e=>new HQ(e.method,"/"===e.path?t:t+e.path,e.handler,t1(t7(e.prefix,e=>t+e),()=>tI(t)),e.uninterruptible)),iK(e.mounts,([e,r])=>["/"===e?t:t+e,r]))),e=>D(e=>HV(e[0]),(t,r,i,n)=>new HG(iT(t.routes,new HQ(e,r,i,tQ(),n?.uninterruptible??!1)),t.mounts))),H5=H3("GET"),H4=H3("POST"),H6=D(2,(e,t)=>new HG(iK(e.routes,e=>new HQ(e.method,e.path,t(uM(e.handler,q0)),e.prefix,e.uninterruptible)),iK(e.mounts,([e,r])=>[e,t(r)]))),H8=((e,t)=>new HG(iK(e.routes,e=>new HQ(e.method,e.path,t(e.handler),e.prefix,e.uninterruptible)),iK(e.mounts,([e,r])=>[e,uM(t(r),q0)])),(e,t)=>{let r=$m(e,t);return uM(hX(),e=>{let t=s3(e,Jr),i=s3(e,HK);return r({...t,...i.params})})}),H7=e=>Pc(uG(e,e=>e.stream)),H9=Symbol.for("@effect/matcher/Matcher"),Ke={[H9]:{_input:$,_filters:$,_remaining:$,_result:$,_return:$},_tag:"TypeMatcher",add(e){return function(e){let t=Object.create(Ke);return t.cases=e,t}([...this.cases,e])},pipe(){return e3(this,arguments)}},Kt={[H9]:{_input:$,_filters:$,_result:$,_return:$},_tag:"ValueMatcher",add(e){return"Right"===this.value._tag?this:"When"===e._tag&&!0===e.guard(this.provided)?Kr(this.provided,tP(e.evaluate(this.provided))):"Not"===e._tag&&!1===e.guard(this.provided)?Kr(this.provided,tP(e.evaluate(this.provided))):this},pipe(){return e3(this,arguments)}};function Kr(e,t){let r=Object.create(Kt);return r.provided=e,r.value=t,r}let Ki=(e,t)=>({_tag:"When",guard:e,evaluate:t}),Kn=e=>{if("function"==typeof e)return e;if(Array.isArray(e)){let t=e.map(Kn),r=t.length;return e=>{if(!Array.isArray(e))return!1;for(let i=0;i<r;i++)if(!1===t[i](e[i]))return!1;return!0}}if(null!==e&&"object"==typeof e){let t=Object.entries(e).map(([e,t])=>[e,Kn(t)]),r=t.length;return e=>{if("object"!=typeof e||null===e)return!1;for(let i=0;i<r;i++){let[r,n]=t[i];if(!(r in e)||!1===n(e[r]))return!1}return!0}}return t=>t===e},Ks=e=>t=>{let r=Ki(r=>null!=r&&r[e]in t,r=>t[r[e]](r));return e=>e.add(r)},Ka=e=>{if("ValueMatcher"===e._tag)return e.value;let t=e.cases.length;if(1===t){let t=e.cases[0];return e=>"When"===t._tag&&!0===t.guard(e)?tP(t.evaluate(e)):"Not"===t._tag&&!1===t.guard(e)?tP(t.evaluate(e)):tz(e)}return r=>{for(let i=0;i<t;i++){let t=e.cases[i];if("When"===t._tag&&!0===t.guard(r)||"Not"===t._tag&&!1===t.guard(r))return tP(t.evaluate(r))}return tz(r)}},Ko="effect/Match/exhaustive: absurd",Kl=e=>{let t=Ka(e);if(tA(t)){if("Right"===t._tag)return t.right;throw Error(Ko)}return e=>{let r=t(e);if("Right"===r._tag)return r.right;throw Error(Ko)}},Ku=e=>Kr(e,tz(e)),Kc=(e,t)=>r=>r.add(Ki(Kn(e),t)),Kh=e=>t=>{let r=Ka(t);return tA(r)?"Right"===r._tag?r.right:e(r.left):t=>{let i=r(t);return"Right"===i._tag?i.right:e(i.left)}},Kp={"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applixware":{source:"apache",extensions:["aw"]},"application/atom+xml":{source:"iana",extensions:["atom"]},"application/atomcat+xml":{source:"iana",extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",extensions:["atomdeleted"]},"application/atomsvc+xml":{source:"iana",extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",extensions:["dwd"]},"application/atsc-held+xml":{source:"iana",extensions:["held"]},"application/atsc-rsat+xml":{source:"iana",extensions:["rsat"]},"application/calendar+xml":{source:"iana",extensions:["xcs"]},"application/ccxml+xml":{source:"iana",extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cpl+xml":{source:"iana",extensions:["cpl"]},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/dash+xml":{source:"iana",extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",extensions:["mpp"]},"application/davmount+xml":{source:"iana",extensions:["davmount"]},"application/dicom":{source:"iana",extensions:["dcm"]},"application/docbook+xml":{source:"apache",extensions:["dbk"]},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",extensions:["xdssc"]},"application/ecmascript":{source:"iana",extensions:["es","ecma"]},"application/emma+xml":{source:"iana",extensions:["emma"]},"application/emotionml+xml":{source:"iana",extensions:["emotionml"]},"application/epub+zip":{source:"iana",extensions:["epub"]},"application/exi":{source:"iana",extensions:["exi"]},"application/express":{source:"iana",extensions:["exp"]},"application/fdt+xml":{source:"iana",extensions:["fdt"]},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/geo+json":{source:"iana",extensions:["geojson"]},"application/gml+xml":{source:"iana",extensions:["gml"]},"application/gpx+xml":{source:"apache",extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",extensions:["gz"]},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/inkml+xml":{source:"iana",extensions:["ink","inkml"]},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/its+xml":{source:"iana",extensions:["its"]},"application/java-archive":{source:"apache",extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",extensions:["ser"]},"application/java-vm":{source:"apache",extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",extensions:["js","mjs"]},"application/json":{source:"iana",charset:"UTF-8",extensions:["json","map"]},"application/jsonml+json":{source:"apache",extensions:["jsonml"]},"application/ld+json":{source:"iana",extensions:["jsonld"]},"application/lgr+xml":{source:"iana",extensions:["lgr"]},"application/lost+xml":{source:"iana",extensions:["lostxml"]},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/mads+xml":{source:"iana",extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",extensions:["mathml"]},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",extensions:["mpf"]},"application/mediaservercontrol+xml":{source:"iana",extensions:["mscml"]},"application/metalink+xml":{source:"apache",extensions:["metalink"]},"application/metalink4+xml":{source:"iana",extensions:["meta4"]},"application/mets+xml":{source:"iana",extensions:["mets"]},"application/mmt-aei+xml":{source:"iana",extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",extensions:["musd"]},"application/mods+xml":{source:"iana",extensions:["mods"]},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/msword":{source:"iana",extensions:["doc","dot"]},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/node":{source:"iana",extensions:["cjs"]},"application/octet-stream":{source:"iana",extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/oebps-package+xml":{source:"iana",extensions:["opf"]},"application/ogg":{source:"iana",extensions:["ogx"]},"application/omdoc+xml":{source:"apache",extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p2p-overlay+xml":{source:"iana",extensions:["relo"]},"application/patch-ops-error+xml":{source:"iana",extensions:["xer"]},"application/pdf":{source:"iana",extensions:["pdf"]},"application/pgp-encrypted":{source:"iana",extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",extensions:["pls"]},"application/postscript":{source:"iana",extensions:["ai","eps","ps"]},"application/provenance+xml":{source:"iana",extensions:["provx"]},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/pskc+xml":{source:"iana",extensions:["pskcxml"]},"application/rdf+xml":{source:"iana",extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/resource-lists+xml":{source:"iana",extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",extensions:["rld"]},"application/rls-services+xml":{source:"iana",extensions:["rs"]},"application/route-apd+xml":{source:"iana",extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",extensions:["sls"]},"application/route-usd+xml":{source:"iana",extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rsd+xml":{source:"apache",extensions:["rsd"]},"application/rss+xml":{source:"apache",extensions:["rss"]},"application/rtf":{source:"iana",extensions:["rtf"]},"application/sbml+xml":{source:"iana",extensions:["sbml"]},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/senml+xml":{source:"iana",extensions:["senmlx"]},"application/sensml+xml":{source:"iana",extensions:["sensmlx"]},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/shf+xml":{source:"iana",extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/smil+xml":{source:"iana",extensions:["smi","smil"]},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",extensions:["srx"]},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",extensions:["grxml"]},"application/sru+xml":{source:"iana",extensions:["sru"]},"application/ssdl+xml":{source:"apache",extensions:["ssdl"]},"application/ssml+xml":{source:"iana",extensions:["ssml"]},"application/swid+xml":{source:"iana",extensions:["swidtag"]},"application/tei+xml":{source:"iana",extensions:["tei","teicorpus"]},"application/thraud+xml":{source:"iana",extensions:["tfi"]},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",extensions:["ttml"]},"application/urc-ressheet+xml":{source:"iana",extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",extensions:["td"]},"application/vnd.1000minds.decision-model+xml":{source:"iana",extensions:["1km"]},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",extensions:["air"]},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.xdp+xml":{source:"iana",extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.android.package-archive":{source:"apache",extensions:["apk"]},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apple.installer+xml":{source:"iana",extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.balsamiq.bmml+xml":{source:"iana",extensions:["bmml"]},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.chemdraw+xml":{source:"iana",extensions:["cdxml"]},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.citationstyles.style+xml":{source:"iana",extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",extensions:["wbs"]},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.dart":{source:"iana",extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.eszigno3+xml":{source:"iana",extensions:["es3","et3"]},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-earth.kml+xml":{source:"iana",extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",extensions:["kmz"]},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+xml":{source:"iana",extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las.las+xml":{source:"iana",extensions:["lasxml"]},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",extensions:["lbe"]},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.mozilla.xul+xml":{source:"iana",extensions:["xul"]},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-excel":{source:"iana",extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-powerpoint":{source:"iana",extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",extensions:["xps"]},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma.dd2+xml":{source:"iana",extensions:["dd2"]},"application/vnd.openblox.game+xml":{source:"iana",extensions:["obgx"]},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",extensions:["osm"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",extensions:["musicxml"]},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",extensions:["link66"]},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.software602.filler.form+xml":{source:"iana",extensions:["fo"]},"application/vnd.solent.sdkm+xml":{source:"iana",extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.sun.wadl+xml":{source:"iana",extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",extensions:["xdm"]},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",extensions:["ddf"]},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",extensions:["uoml"]},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",extensions:["osfpvg"]},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",extensions:["zaz"]},"application/voicexml+xml":{source:"iana",extensions:["vxml"]},"application/wasm":{source:"iana",extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",extensions:["wif"]},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wsdl+xml":{source:"iana",extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",extensions:["bz"]},"application/x-bzip2":{source:"apache",extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",extensions:["res"]},"application/x-dvi":{source:"apache",extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",extensions:["jnlp"]},"application/x-latex":{source:"apache",extensions:["latex"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-rar-compressed":{source:"apache",extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",extensions:["xlf"]},"application/x-xpinstall":{source:"apache",extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/xaml+xml":{source:"apache",extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",extensions:["xel"]},"application/xcap-ns+xml":{source:"iana",extensions:["xns"]},"application/xenc+xml":{source:"iana",extensions:["xenc"]},"application/xhtml+xml":{source:"iana",extensions:["xhtml","xht"]},"application/xliff+xml":{source:"iana",extensions:["xlf"]},"application/xml":{source:"iana",extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",extensions:["dtd"]},"application/xop+xml":{source:"iana",extensions:["xop"]},"application/xproc+xml":{source:"apache",extensions:["xpl"]},"application/xslt+xml":{source:"iana",extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",extensions:["xspf"]},"application/xv+xml":{source:"iana",extensions:["mxml","xhvml","xvml","xvm"]},"application/yaml":{source:"iana",extensions:["yaml","yml"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yin+xml":{source:"iana",extensions:["yin"]},"application/zip":{source:"iana",extensions:["zip"]},"audio/3gpp":{source:"iana",extensions:["3gpp"]},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/basic":{source:"iana",extensions:["au","snd"]},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp4":{source:"iana",extensions:["m4a","mp4a"]},"audio/mpeg":{source:"iana",extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/ogg":{source:"iana",extensions:["oga","ogg","spx","opus"]},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/silk":{source:"apache",extensions:["sil"]},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/webm":{source:"apache",extensions:["weba"]},"audio/x-aac":{source:"apache",extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/x-gsm":{source:"apache",extensions:["gsm"]},"audio/xm":{source:"apache",extensions:["xm"]},"image/aces":{source:"iana",extensions:["exr"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",extensions:["avif"]},"image/bmp":{source:"iana",extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",extensions:["jpeg","jpg","jpe","jfif","pjpeg","pjp"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",extensions:["jpm"]},"image/jpx":{source:"iana",extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/png":{source:"iana",extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.microsoft.icon":{source:"iana",extensions:["ico"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"text/cache-manifest":{source:"iana",extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/css":{source:"iana",charset:"UTF-8",extensions:["css"]},"text/csv":{source:"iana",extensions:["csv"]},"text/html":{source:"iana",extensions:["html","htm","shtml"]},"text/markdown":{source:"iana",extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/n3":{source:"iana",charset:"UTF-8",extensions:["n3"]},"text/plain":{source:"iana",extensions:["txt","text","conf","def","list","log","in","ini"]},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/richtext":{source:"iana",extensions:["rtx"]},"text/rtf":{source:"iana",extensions:["rtf"]},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shex":{source:"iana",extensions:["shex"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/tab-separated-values":{source:"iana",extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/uri-list":{source:"iana",extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",extensions:["vcard"]},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",extensions:["xml"]},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h264":{source:"iana",extensions:["h264"]},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",extensions:["mp4","mp4v","mpg4"]},"video/mpeg":{source:"iana",extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/ogg":{source:"iana",extensions:["ogv"]},"video/quicktime":{source:"iana",extensions:["qt","mov"]},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/webm":{source:"apache",extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",extensions:["otf"]},"font/ttf":{source:"iana",extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/rfc822":{source:"iana",extensions:["eml","mime"]},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/gltf+json":{source:"iana",extensions:["gltf"]},"model/gltf-binary":{source:"iana",extensions:["glb"]},"model/iges":{source:"iana",extensions:["igs","iges"]},"model/mesh":{source:"iana",extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana",extensions:[".p21",".stp",".step",".stpnc",".210"]},"model/step+xml":{source:"iana",extensions:["stpx"]},"model/step+zip":{source:"iana",extensions:["stpz"]},"model/step-xml+zip":{source:"iana",extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]}},Kf={},Kd={},Km=!1,Kg={alphabet:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",minLength:0,blocklist:new Set(["0rgasm","1d10t","1d1ot","1di0t","1diot","1eccacu10","1eccacu1o","1eccacul0","1eccaculo","1mbec11e","1mbec1le","1mbeci1e","1mbecile","a11upat0","a11upato","a1lupat0","a1lupato","aand","ah01e","ah0le","aho1e","ahole","al1upat0","al1upato","allupat0","allupato","ana1","ana1e","anal","anale","anus","arrapat0","arrapato","arsch","arse","ass","b00b","b00be","b01ata","b0ceta","b0iata","b0ob","b0obe","b0sta","b1tch","b1te","b1tte","ba1atkar","balatkar","bastard0","bastardo","batt0na","battona","bitch","bite","bitte","bo0b","bo0be","bo1ata","boceta","boiata","boob","boobe","bosta","bran1age","bran1er","bran1ette","bran1eur","bran1euse","branlage","branler","branlette","branleur","branleuse","c0ck","c0g110ne","c0g11one","c0g1i0ne","c0g1ione","c0gl10ne","c0gl1one","c0gli0ne","c0glione","c0na","c0nnard","c0nnasse","c0nne","c0u111es","c0u11les","c0u1l1es","c0u1lles","c0ui11es","c0ui1les","c0uil1es","c0uilles","c11t","c11t0","c11to","c1it","c1it0","c1ito","cabr0n","cabra0","cabrao","cabron","caca","cacca","cacete","cagante","cagar","cagare","cagna","cara1h0","cara1ho","caracu10","caracu1o","caracul0","caraculo","caralh0","caralho","cazz0","cazz1mma","cazzata","cazzimma","cazzo","ch00t1a","ch00t1ya","ch00tia","ch00tiya","ch0d","ch0ot1a","ch0ot1ya","ch0otia","ch0otiya","ch1asse","ch1avata","ch1er","ch1ng0","ch1ngadaz0s","ch1ngadazos","ch1ngader1ta","ch1ngaderita","ch1ngar","ch1ngo","ch1ngues","ch1nk","chatte","chiasse","chiavata","chier","ching0","chingadaz0s","chingadazos","chingader1ta","chingaderita","chingar","chingo","chingues","chink","cho0t1a","cho0t1ya","cho0tia","cho0tiya","chod","choot1a","choot1ya","chootia","chootiya","cl1t","cl1t0","cl1to","clit","clit0","clito","cock","cog110ne","cog11one","cog1i0ne","cog1ione","cogl10ne","cogl1one","cogli0ne","coglione","cona","connard","connasse","conne","cou111es","cou11les","cou1l1es","cou1lles","coui11es","coui1les","couil1es","couilles","cracker","crap","cu10","cu1att0ne","cu1attone","cu1er0","cu1ero","cu1o","cul0","culatt0ne","culattone","culer0","culero","culo","cum","cunt","d11d0","d11do","d1ck","d1ld0","d1ldo","damn","de1ch","deich","depp","di1d0","di1do","dick","dild0","dildo","dyke","encu1e","encule","enema","enf01re","enf0ire","enfo1re","enfoire","estup1d0","estup1do","estupid0","estupido","etr0n","etron","f0da","f0der","f0ttere","f0tters1","f0ttersi","f0tze","f0utre","f1ca","f1cker","f1ga","fag","fica","ficker","figa","foda","foder","fottere","fotters1","fottersi","fotze","foutre","fr0c10","fr0c1o","fr0ci0","fr0cio","fr0sc10","fr0sc1o","fr0sci0","fr0scio","froc10","froc1o","froci0","frocio","frosc10","frosc1o","frosci0","froscio","fuck","g00","g0o","g0u1ne","g0uine","gandu","go0","goo","gou1ne","gouine","gr0gnasse","grognasse","haram1","harami","haramzade","hund1n","hundin","id10t","id1ot","idi0t","idiot","imbec11e","imbec1le","imbeci1e","imbecile","j1zz","jerk","jizz","k1ke","kam1ne","kamine","kike","leccacu10","leccacu1o","leccacul0","leccaculo","m1erda","m1gn0tta","m1gnotta","m1nch1a","m1nchia","m1st","mam0n","mamahuev0","mamahuevo","mamon","masturbat10n","masturbat1on","masturbate","masturbati0n","masturbation","merd0s0","merd0so","merda","merde","merdos0","merdoso","mierda","mign0tta","mignotta","minch1a","minchia","mist","musch1","muschi","n1gger","neger","negr0","negre","negro","nerch1a","nerchia","nigger","orgasm","p00p","p011a","p01la","p0l1a","p0lla","p0mp1n0","p0mp1no","p0mpin0","p0mpino","p0op","p0rca","p0rn","p0rra","p0uff1asse","p0uffiasse","p1p1","p1pi","p1r1a","p1rla","p1sc10","p1sc1o","p1sci0","p1scio","p1sser","pa11e","pa1le","pal1e","palle","pane1e1r0","pane1e1ro","pane1eir0","pane1eiro","panele1r0","panele1ro","paneleir0","paneleiro","patakha","pec0r1na","pec0rina","pecor1na","pecorina","pen1s","pendej0","pendejo","penis","pip1","pipi","pir1a","pirla","pisc10","pisc1o","pisci0","piscio","pisser","po0p","po11a","po1la","pol1a","polla","pomp1n0","pomp1no","pompin0","pompino","poop","porca","porn","porra","pouff1asse","pouffiasse","pr1ck","prick","pussy","put1za","puta","puta1n","putain","pute","putiza","puttana","queca","r0mp1ba11e","r0mp1ba1le","r0mp1bal1e","r0mp1balle","r0mpiba11e","r0mpiba1le","r0mpibal1e","r0mpiballe","rand1","randi","rape","recch10ne","recch1one","recchi0ne","recchione","retard","romp1ba11e","romp1ba1le","romp1bal1e","romp1balle","rompiba11e","rompiba1le","rompibal1e","rompiballe","ruff1an0","ruff1ano","ruffian0","ruffiano","s1ut","sa10pe","sa1aud","sa1ope","sacanagem","sal0pe","salaud","salope","saugnapf","sb0rr0ne","sb0rra","sb0rrone","sbattere","sbatters1","sbattersi","sborr0ne","sborra","sborrone","sc0pare","sc0pata","sch1ampe","sche1se","sche1sse","scheise","scheisse","schlampe","schwachs1nn1g","schwachs1nnig","schwachsinn1g","schwachsinnig","schwanz","scopare","scopata","sexy","sh1t","shit","slut","sp0mp1nare","sp0mpinare","spomp1nare","spompinare","str0nz0","str0nza","str0nzo","stronz0","stronza","stronzo","stup1d","stupid","succh1am1","succh1ami","succhiam1","succhiami","sucker","t0pa","tapette","test1c1e","test1cle","testic1e","testicle","tette","topa","tr01a","tr0ia","tr0mbare","tr1ng1er","tr1ngler","tring1er","tringler","tro1a","troia","trombare","turd","twat","vaffancu10","vaffancu1o","vaffancul0","vaffanculo","vag1na","vagina","verdammt","verga","w1chsen","wank","wichsen","x0ch0ta","x0chota","xana","xoch0ta","xochota","z0cc01a","z0cc0la","z0cco1a","z0ccola","z1z1","z1zi","ziz1","zizi","zocc01a","zocc0la","zocco1a","zoccola"])};class Kb{constructor(e){var t,r,i;let n=null!==(t=null==e?void 0:e.alphabet)&&void 0!==t?t:Kg.alphabet,s=null!==(r=null==e?void 0:e.minLength)&&void 0!==r?r:Kg.minLength,a=null!==(i=null==e?void 0:e.blocklist)&&void 0!==i?i:Kg.blocklist;if(new Blob([n]).size!==n.length)throw Error("Alphabet cannot contain multibyte characters");if(n.length<3)throw Error("Alphabet length must be at least 3");if(new Set(n).size!==n.length)throw Error("Alphabet must contain unique characters");if("number"!=typeof s||s<0||s>255)throw Error("Minimum length has to be between 0 and 255");let o=new Set,l=n.toLowerCase().split("");for(let e of a)if(e.length>=3){let t=e.toLowerCase(),r=t.split("");r.filter(e=>l.includes(e)).length===r.length&&o.add(t)}this.alphabet=this.shuffle(n),this.minLength=s,this.blocklist=o}encode(e){if(0===e.length)return"";if(e.filter(e=>e>=0&&e<=this.maxValue()).length!==e.length)throw Error(`Encoding supports numbers between 0 and ${this.maxValue()}`);return this.encodeNumbers(e)}decode(e){let t=[];if(""===e)return t;let r=this.alphabet.split("");for(let i of e.split(""))if(!r.includes(i))return t;let i=e.charAt(0),n=this.alphabet.indexOf(i),s=this.alphabet.slice(n)+this.alphabet.slice(0,n);s=s.split("").reverse().join("");let a=e.slice(1);for(;a.length>0;){let e=s.slice(0,1),r=a.split(e);if(r.length>0){if(""===r[0])return t;t.push(this.toNumber(r[0],s.slice(1))),r.length>1&&(s=this.shuffle(s))}a=r.slice(1).join(e)}return t}encodeNumbers(e,t=0){if(t>this.alphabet.length)throw Error("Reached max attempts to re-generate the ID");let r=e.reduce((e,t,r)=>this.alphabet[t%this.alphabet.length].codePointAt(0)+r+e,e.length)%this.alphabet.length;r=(r+t)%this.alphabet.length;let i=this.alphabet.slice(r)+this.alphabet.slice(0,r),n=i.charAt(0);i=i.split("").reverse().join("");let s=[n];for(let t=0;t!==e.length;t++){let r=e[t];s.push(this.toId(r,i.slice(1))),t<e.length-1&&(s.push(i.slice(0,1)),i=this.shuffle(i))}let a=s.join("");if(this.minLength>a.length)for(a+=i.slice(0,1);this.minLength-a.length>0;)i=this.shuffle(i),a+=i.slice(0,Math.min(this.minLength-a.length,i.length));return this.isBlockedId(a)&&(a=this.encodeNumbers(e,t+1)),a}shuffle(e){let t=e.split("");for(let e=0,r=t.length-1;r>0;e++,r--){let i=(e*r+t[e].codePointAt(0)+t[r].codePointAt(0))%t.length;[t[e],t[i]]=[t[i],t[e]]}return t.join("")}toId(e,t){let r=[],i=t.split(""),n=e;do r.unshift(i[n%i.length]),n=Math.floor(n/i.length);while(n>0);return r.join("")}toNumber(e,t){let r=t.split("");return e.split("").reduce((e,t)=>e*r.length+r.indexOf(t),0)}isBlockedId(e){let t=e.toLowerCase();for(let e of this.blocklist)if(e.length<=t.length){if(t.length<=3||e.length<=3){if(t===e)return!0}else if(/\d/.test(e)){if(t.startsWith(e)||t.endsWith(e))return!0}else if(t.includes(e))return!0}return!1}maxValue(){return Number.MAX_SAFE_INTEGER}}class Kx extends mj("InvalidRouteConfig"){constructor(e,t){super({reason:t?`Expected route config to have a ${t} for key ${e} but none was found.`:`Encountered an invalid route config during backfilling. ${e} was not found.`})}}class Ky extends mj("UnknownFileType"){constructor(e){super({reason:`Could not determine type for ${e}`})}}class Kv extends mj("InvalidFileType"){constructor(e,t){super({reason:`File type ${e} not allowed for ${t}`})}}class KS extends mj("InvalidFileSize"){constructor(e){super({reason:`Invalid file size: ${e}`})}}function K_(e){return{maxFileSize:"image"===e?"4MB":"video"===e?"16MB":"audio"===e||"blob"===e?"8MB":"pdf"===e?"4MB":"text"===e?"64KB":"4MB",maxFileCount:1,minFileCount:1,contentDisposition:"inline"}}let Kw=e=>{if(Array.isArray(e))return dY(e.reduce((e,t)=>(e[t]=K_(t),e),{}));let t={};for(let r of KI(e)){let i=e[r];if(!i)return dQ(new Kx(r));t[r]={...K_(r),...i}}return dY(JSON.parse(JSON.stringify(t,KF)))},Kk=(e,t)=>{let r=e.type||function(e){if(!e||"string"!=typeof e)return!1;let t=(function(e){let t=e.lastIndexOf(".");return t<0?"":e.substring(t)})("x."+e).toLowerCase().substring(1);return!!t&&((function(e,t){if(Km)return;Km=!0;let r=["nginx","apache",void 0,"iana"];Object.keys(Kp).forEach(i=>{let n=Kp[i],s=n.extensions;if(s.length)for(let a of(e[i]=s,s)){if(a in t){let e=r.indexOf(Kp[t[a]].source),i=r.indexOf(n.source);if("application/octet-stream"!==t[a]&&(e>i||e===i&&t[a].startsWith("application/")))continue}t[a]=i}})}(Kf,Kd),Kd)[t]||!1)}(e.name);if(!r)return t.includes("blob")?dY("blob"):dQ(new Ky(e.name));if(t.some(e=>e.includes("/"))&&t.includes(r))return dY(r);let i="application/pdf"===r.toLowerCase()?"pdf":r.split("/")[0];return t.includes(i)?dY(i):t.includes("blob")?dY("blob"):dQ(new Kv(i,e.name))},KO=["B","KB","MB","GB","TB"],KC=e=>{let t=RegExp(`^(\\d+)(\\.\\d+)?\\s*(${KO.join("|")})$`,"i"),r=e.match(t);if(!r?.[1]||!r[3])return dQ(new KS(e));let i=parseFloat(r[1]),n=r[3].toUpperCase();return dY(Math.floor(i*Math.pow(1024,KO.indexOf(n))))},KE=e=>{if(0===e||-1===e)return"0B";let t=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,t)).toFixed(2)}${KO[t]}`};function KI(e){return Object.keys(e)}let KF=(e,t)=>"number"!=typeof t||Number.isSafeInteger(t)||t<=Number.MAX_SAFE_INTEGER&&t>=Number.MIN_SAFE_INTEGER?t:t===1/0?Number.MAX_SAFE_INTEGER:t===-1/0?Number.MIN_SAFE_INTEGER:Number.isNaN(t)?0:void 0,KT={BAD_REQUEST:400,NOT_FOUND:404,FORBIDDEN:403,INTERNAL_SERVER_ERROR:500,INTERNAL_CLIENT_ERROR:500,TOO_LARGE:413,TOO_SMALL:400,TOO_MANY_FILES:400,KEY_TOO_LONG:400,URL_GENERATION_FAILED:500,UPLOAD_FAILED:500,MISSING_ENV:500,INVALID_SERVER_CONFIG:500,FILE_LIMIT_EXCEEDED:500};class KR extends mA{constructor(e){let t="string"==typeof e?{code:"INTERNAL_SERVER_ERROR",message:e}:e;super({message:t.message??function(e,t){return"string"==typeof e?e:e instanceof Error||e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:t??"An unknown error occurred"}(t.cause,t.code)}),this._tag="UploadThingError",this.name="UploadThingError",this.code=t.code,this.data=t.data,t.cause instanceof Error?this.cause=t.cause:ef(t.cause)&&G(t.cause.status)&&W(t.cause.statusText)?this.cause=Error(`Response ${t.cause.status} ${t.cause.statusText}`):W(t.cause)?this.cause=Error(t.cause):this.cause=t.cause}static toObject(e){return{code:e.code,message:e.message,data:e.data}}static serialize(e){return JSON.stringify(KR.toObject(e))}}let KN="hmac-sha256=",KA={name:"HMAC",hash:"SHA-256"},Kj=new TextEncoder,KM=(e,t)=>me(function*(){let r=yield*d6({try:()=>crypto.subtle.importKey("raw",Kj.encode(PQ(t)),KA,!1,["sign"]),catch:e=>new KR({code:"BAD_REQUEST",message:"Invalid signing secret",cause:e})}),i=yield*mn(d6({try:()=>crypto.subtle.sign(KA,r,Kj.encode(e)),catch:e=>new KR({code:"BAD_REQUEST",cause:e})}),e=>DG(new Uint8Array(e)));return`${KN}${i}`}).pipe(m_("signPayload")),Kz=(e,t,r)=>me(function*(){let i=t?.slice(KN.length);if(!i)return!1;let n=Kj.encode(PQ(r)),s=yield*d4(()=>crypto.subtle.importKey("raw",n,KA,!1,["verify"])),a=yield*d3(DY(i)),o=Kj.encode(e);return yield*d4(()=>crypto.subtle.verify(KA,s,a,o))}).pipe(m_("verifySignature"),mS(()=>!1)),KP=(e,t,r)=>dX(()=>{let i=JSON.stringify(r?.(e)??[e.name,e.size,e.type,e.lastModified,Date.now()]),n=function(e,t){let r,i;let n=e.split(""),s=eM(t);for(let e=0;e<n.length;e++)i=(s%(e+1)+e)%n.length,r=n[e],n[e]=n[i],n[i]=r;return n.join("")}(Kg.alphabet,t),s=new Kb({alphabet:n,minLength:36}).encode([Math.abs(eM(i))]);return new Kb({alphabet:n,minLength:12}).encode([Math.abs(eM(t))])+s}).pipe(m_("generateKey")),KD=(e,t,r)=>me(function*(){let i=new URL(e),n=r.ttlInSeconds?function(e){if("number"==typeof e)return e;let t=e.split(/(\d+)/).filter(Boolean),r=Number(t[0]);return r*({s:1,m:60,h:3600,d:86400})[(t[1]??"s").trim().slice(0,1)]}(r.ttlInSeconds):3600,s=Date.now()+1e3*n;i.searchParams.append("expires",s.toString()),r.data&&Object.entries(r.data).forEach(([e,t])=>{if(null==t)return;let r=encodeURIComponent(t);i.searchParams.append(e,r)});let a=yield*KM(i.toString(),t);return i.searchParams.append("signature",a),i.href}).pipe(m_("generateSignedURL")),K$=e=>pY(new Map(r3(fu(e),([e,t])=>[fl(e).join("\uFEFF"),t])),{pathDelim:"\uFEFF",seqDelim:"\uFEFF"});$x("inline","attachment"),$x("public-read","private");let KL=$x("upload"),KU=$x("callback","error"),Kq=LO(Ur,$R,{decode:e=>new TextDecoder().decode(e),encode:e=>new TextEncoder().encode(e)}),KB=La({apiKey:function(e){return LO(e,L6($d(e)),{strict:!0,decode:e=>PZ(e),encode:e=>PQ(e)})}($R.pipe(((e,t)=>r=>{let i=JSON.stringify(e);return r.pipe(L_(t=>t.startsWith(e),{schemaId:LT,[LT]:{startsWith:e},title:`startsWith(${i})`,description:`a string starting with ${i}`,jsonSchema:{pattern:`^${e}`},...t}))})("sk_"))),appId:$R,regions:$H($R),ingestHost:$R.pipe(Lt({default:()=>"ingest.uploadthing.com"}))}),KJ=Ui.pipe(Lb(Kq),Lb(LB(KB)));class KH extends Uq("FileUploadData")({name:$R,size:$N,type:$R,lastModified:$N.pipe(Le)}){}class KK extends KH.extend("FileUploadDataWithCustomId")({customId:$z($R)}){}class KV extends KK.extend("UploadedFileData")({key:$R,url:$R,appUrl:$R,ufsUrl:$R,fileHash:$R}){}Uq("NewPresignedUrl")({url:$R,key:$R,customId:$z($R),name:$R});class KW extends Uq("MetadataFetchStreamPart")({payload:$R,signature:$R,hook:KU}){}class KG extends Uq("MetadataFetchResponse")({ok:$A}){}class KY extends Uq("CallbackResultResponse")({ok:$A}){}class KZ extends Uq("UploadActionPayload")({files:$J(KH),input:$I}){}let KQ=pG().pipe(p5(()=>pY(new Map(Object.entries(Object.fromEntries(Object.entries({}).filter(e=>null!=e[1])))),{pathDelim:"_"})),p3("uploadthing"),e=>p1(e,pU)),KX=e=>K$(e??{}).pipe(p5(()=>KQ)),K0=(e=>{let t=Dv("a boolean property",e=>{switch(e){case"true":case"yes":case"on":case"1":return tP(!0);case"false":case"no":case"off":case"0":return tP(!1);default:return tz(pm([],`Expected a boolean value but received ${e}`))}});return void 0===e?t:Db(t,e)})("isDev").pipe(Dx(()=>DS("undefined"!=typeof process?"production":void 0).pipe(Dd(e=>"development"===e))),Dw(!1)),K1=((e,t)=>{let r=E7(t);return DO(e).pipe(Dg(e=>r(e).pipe(tL(e=>pm([],Ix.formatIssueSync(e))))))})("token",KJ).pipe(gF({ConfigError:e=>new KR({code:"InvalidData"===e._op?"INVALID_SERVER_CONFIG":"MISSING_ENV",message:"InvalidData"===e._op?"Invalid token. A token is a base64 encoded JSON object matching { apiKey: string, appId: string, regions: string[] }.":"Missing token. Please set the `UPLOADTHING_TOKEN` environment variable or provide a token manually through config.",cause:e})}));DO("apiUrl").pipe(Dw("https://api.uploadthing.com"),Dm(e=>new URL(e)),Dd(e=>e.href.replace(/\/$/,"")));let K2=Ou(function*(e){let{regions:t,ingestHost:r}=yield*K1,i=e?t.find(t=>t===e)??t[0]:t[0];return yield*DO("ingestUrl").pipe(Dw(`https://${i}.${r}`),Dm(e=>new URL(e)),Dd(e=>e.href.replace(/\/$/,"")))});DO("utfsHost").pipe(Dw("utfs.io")),DO("ufsHost").pipe(Dw("ufs.sh"));let K3=e=>{console.warn(`⚠️ [uploadthing][deprecated] ${e}`)};function K5(e){return{message:e.message}}function K4(e,t){let r=Object.keys(t)[0];return(r?t[r]?.errorFormatter??K5:K5)(e)}let K6=(e,t)=>r=>{let i="";return r.pipe(Pm(),PS(e=>k9(function*(){let t=(i+=e).split("\n"),r=[];for(let e of t)try{r.push(JSON.parse(e)),i=i.slice(e.length+1)}catch{}return yield*gU("Received chunks").pipe(gk("chunk",e),gk("parsedChunks",r),gk("buf",i)),r})),PS($m($J(e))),PS(vv(e=>t(e))),PC,gH("handleJsonLineStream"))},K8=D(2,(e,t)=>e.log({fiberId:a3,logLevel:cv,message:t,cause:n6,context:fK(),spans:oS(),annotations:nf(),date:new Date})),K7=bI,K9=bT,Ve=FS,Vt=F_,Vr=Fw,Vi=bR,Vn=bN,Vs=bA,Va=bj,Vo=vf,Vl=vs,Vu=e=>bT(t=>{let r=s3(f1(t.context,fC),fh).unsafe;switch(t.logLevel._tag){case"Debug":return r.debug(e.log(t));case"Info":return r.info(e.log(t));case"Trace":return r.trace(e.log(t));case"Warning":return r.warn(e.log(t));case"Error":case"Fatal":return r.error(e.log(t));default:return r.log(e.log(t))}}),Vc=e=>bT(t=>{s3(f1(t.context,fC),fh).unsafe.error(e.log(t))}),Vh={[bI]:bF,log:B,pipe(){return e3(this,arguments)}},Vp=Fk,Vf=FO,Vd=FC,Vm=FE,Vg=bM,Vb=e=>bM(()=>e),Vx=e=>bM(e),Vy=K8,Vv=Fv,VS=e=>bN(e,e=>{let t=t9(f0(e.context,cB),s5(fv));return"None"===t._tag?e:{...e,annotations:J(e.annotations,nx("effect.traceId",t.value.traceId),nx("effect.spanId",t.value.spanId),"Span"===t.value._tag?nx("effect.spanName",t.value.name):$)}}),V_=bz,Vw=bP,Vk=bD,VO=va,VC=bH,VE=bq,VI=bU,VF=b1,VT=b5,VR=bB,VN=vh,VA=Vf(va,vo),Vj=Vf(va,vl),VM=Vf(va,vu),Vz=Vf(va,vc),VP=e=>ko(vW(vn,e)),VD=e=>"object"==typeof e&&null!=e&&bI in e,V$=(e=>{let t=Dg(DO(),e=>{let t=ck.find(t=>t._tag===e);return void 0===t?tz(pm([],`Expected a log level but received ${e}`)):tP(t)});return void 0===e?t:Db(t,e)})("logLevel").pipe(Dw(cv),uz(e=>VP(e)),g2(e=>gJ("Invalid log level").pipe(gk("error",e))),gI("ConfigError",e=>new KR({code:"INVALID_SERVER_CONFIG",message:"Invalid server configuration",cause:e})),kg),VL=((...e)=>t=>{let r=e.map(String).join(", "),i=Dv(`one of (${r})`,t=>{let i=e.find(e=>String(e)===t);return void 0===i?tz(pm([],`Expected one of (${r}) but received ${t}`)):tP(i)});return void 0===t?i:Db(i,t)})("json","logFmt","structured","pretty")("logFormat"),VU=k9(function*(){let e=yield*K0;return A[yield*VL.pipe(Dw(e?"pretty":"json"))]}).pipe(gI("ConfigError",e=>new KR({code:"INVALID_SERVER_CONFIG",message:"Invalid server configuration",cause:e})),kg),Vq=(e,t)=>{let r=t?.mixin??"json",i=dy(t?.level??"Debug");return t=>uM("None"!==r?t[r]:cr,()=>On(i,`${e} (${t.status})`).pipe(gk("response",t)))},VB=e=>t=>"ResponseError"===t._tag?Vq(e,{level:"Error"})(t.response):gJ(e).pipe(gk("error",t));class VJ extends v4("ParserError"){constructor(...e){super(...e),this.message="Input validation failed. The original error with it's validation issues is in the error cause."}}class VH extends v5{constructor(e,t,r){super({reason:`You uploaded a ${e} file that was ${KE(r)}, but the limit for that type is ${t}`}),this._tag="FileSizeMismatch",this.name="FileSizeMismatchError"}}class VK extends v5{constructor(e,t,r,i){super({reason:`You uploaded ${i} file(s) of type '${e}', but the ${t} for that type is ${r}`}),this._tag="FileCountMismatch",this.name="FileCountMismatchError"}}let VV=(e,t)=>k9(function*(){let r={};for(let i of e){let e=yield*Kk(i,KI(t));r[e]=(r[e]??0)+1;let n=t[e]?.maxFileSize;if(!n)return yield*new Kx(e,"maxFileSize");let s=yield*KC(n);if(i.size>s)return yield*new VH(e,n,i.size)}for(let e in r){let i=t[e];if(!i)return yield*new Kx(e);let n=r[e],s=i.minFileCount,a=i.maxFileCount;if(s>a)return yield*new KR({code:"BAD_REQUEST",message:"Invalid config during file count - minFileCount > maxFileCount",cause:`minFileCount must be less than maxFileCount for key ${e}. got: ${s} > ${a}`});if(null!=n&&n<s)return yield*new VK(e,"minimum",s,n);if(null!=n&&n>a)return yield*new VK(e,"maximum",a,n)}return null}),VW=e=>vv(KI(e),t=>uG(Kw(e[t].routerConfig),e=>({slug:t,config:e}))),VG="@effect/platform/FetchHttpClient/Fetch",VY=(e=>Hv(t=>uM(t,t=>up(r=>{let i=new AbortController,n=qy(t.url,t.urlParams,t.hash);if("Left"===n._tag)return uF(new JV({request:t,reason:"InvalidUrl",cause:n.left}));let s=n.right;return!r.getFiberRef(c2)||r.getFiberRef(Hg)(t)?ct(n=>uL(n(e(t,s,i.signal,r)),{onSuccess:e=>(HS.register(e,i),u5(new H_(e,i))),onFailure:e=>(sa(e)&&i.abort(),uR(e))})):br(s3(r.currentContext,Hx)(t),{kind:"client",captureStackTrace:!1},n=>{n.attribute("http.request.method",t.method),n.attribute("server.address",s.origin),""!==s.port&&n.attribute("server.port",+s.port),n.attribute("url.full",s.toString()),n.attribute("url.path",s.pathname),n.attribute("url.scheme",s.protocol.slice(0,-1));let a=s.search.slice(1);""!==a&&n.attribute("url.query",a);let o=r.getFiberRef(qf),l=qp(t.headers,o);for(let e in l)n.attribute(`http.request.header.${e}`,String(l[e]));return t=r.getFiberRef(Hb)?J9(t,Jc(n)):t,ct(a=>a(e(t,s,i.signal,r)).pipe(bi(n),uL({onSuccess:e=>{n.attribute("http.response.status_code",e.status);let t=qp(e.headers,o);for(let e in t)n.attribute(`http.response.header.${e}`,String(t[e]));return HS.register(e,i),u5(new H_(e,i))},onFailure:e=>(sa(e)&&i.abort(),uR(e))})))})})),u5))((e,t,r,i)=>{let n=i.getFiberRef(cB),s=n.unsafeMap.get(VG)??globalThis.fetch,a=n.unsafeMap.get("@effect/platform/FetchHttpClient/FetchOptions")??{},o=a.headers?qc(qa(a.headers),e.headers):e.headers,l=i=>uG(g5({try:()=>s(t,{...a,method:e.method,headers:o,body:i,duplex:"Stream"===e.body._tag?"half":void 0,signal:r}),catch:t=>new JV({request:e,reason:"Transport",cause:t})}),t=>Hh(e,t));switch(e.body._tag){case"Raw":case"Uint8Array":return l(e.body.body);case"FormData":return l(e.body.formData);case"Stream":return uM(Po(e.body.stream),l)}return l(void 0)}),VZ=(C=u5(VY),w7(Hm,uM(hX(),e=>uG(C,t=>HR(t,h3(t=>s4(e,t)))))));class VQ extends s9(VG)(){}function VX(e,t){return uM(e.runtimeEffect,e=>up(r=>(r.setFiberRefs(e.fiberRefs),r.currentRuntimeFlags=e.runtimeFlags,h1(t,e.context))))}let V0={...tm,[wf]:wf,pipe(){return e3(this,arguments)},commit(){return this.runtimeEffect}},V1=(e,t)=>{let r;t=t??wZ();let i=wL(vH()),n=up(n=>(r||(r=wP(u8(vK(kp(e,t),i),e=>{s.cachedRuntime=e}),{scope:i,scheduler:n.currentScheduler})),uD(r.await))),s=Object.assign(Object.create(V0),{memoMap:t,scope:i,runtimeEffect:n,cachedRuntime:void 0,runtime:()=>void 0===s.cachedRuntime?wD(s.runtimeEffect):Promise.resolve(s.cachedRuntime),dispose:()=>wD(s.disposeEffect),disposeEffect:u4(()=>(s.runtimeEffect=uO("ManagedRuntime disposed"),s.cachedRuntime=void 0,he(s.scope,hz))),runFork:(e,t)=>void 0===s.cachedRuntime?wP(VX(s,e),t):wy(s.cachedRuntime)(e,t),runSyncExit:e=>void 0===s.cachedRuntime?wU(VX(s,e)):wF(s.cachedRuntime)(e),runSync:e=>void 0===s.cachedRuntime?wL(VX(s,e)):wS(s.cachedRuntime)(e),runPromiseExit:(e,t)=>void 0===s.cachedRuntime?w$(VX(s,e),t):wR(s.cachedRuntime)(e,t),runCallback:(e,t)=>void 0===s.cachedRuntime?wv(wz)(VX(s,e),t):wv(s.cachedRuntime)(e,t),runPromise:(e,t)=>void 0===s.cachedRuntime?wD(VX(s,e),t):wT(s.cachedRuntime)(e,t)});return s},V2=(e,t)=>V1(kf(kn(VU,V$,kd(VZ,ku(VQ,e)),FI(cR(qf,e=>e.concat(["x-uploadthing-api-key"])))),FF(KX(t)))),V3=Symbol("uploadthing-region-symbol"),V5=Symbol("uploadthing-custom-id-symbol");var V4="7.7.2";class V6 extends s9("uploadthing/AdapterArguments")(){}let V8=(e,t,r,i)=>{let n=V2(r.config?.fetch,r.config),s=Oe(()=>n.runtime().then(Jw)),a=(...t)=>uG(Oe(()=>n.runPromise(V7(r,i??"custom"))),gZ(V6,e(...t)));return async(...e)=>await s.pipe(Oo(a(...e)),Oo(t(...e)),gH("requestHandler"),n.runPromise)},V7=(e,t)=>k9(function*(){let r=yield*K0,i=yield*VW(e.router),n=e.config?.handleDaemonPromise?e.config.handleDaemonPromise:r?"void":"await";if(r&&"await"===n)return yield*new KR({code:"INVALID_SERVER_CONFIG",message:'handleDaemonPromise: "await" is forbidden in development.'});let s=k9(function*(){return yield*qW(i)}),a=k9(function*(){let{"uploadthing-hook":r,"x-uploadthing-package":i,"x-uploadthing-version":s}=yield*Js(La({"uploadthing-hook":KU.pipe(Le),"x-uploadthing-package":$R.pipe(Lt({default:()=>"unknown"})),"x-uploadthing-version":$R.pipe(Lt({default:()=>V4}))}));s!==V4&&(yield*gB("Client version mismatch. Things may not work as expected, please sync your versions to ensure compatibility.").pipe(gk({clientVersion:s,serverVersion:V4})));let{slug:a,actionType:o}=yield*H8(La({actionType:KL.pipe(Le),slug:$R})),l=e.router[a];if(!l){let e=`No file route found for slug ${a}`;return yield*gJ(e),yield*new KR({code:"NOT_FOUND",message:e})}let{body:u,fiber:c}=yield*Ku({actionType:o,uploadthingHook:r}).pipe(Kc({actionType:"upload",uploadthingHook:void 0},()=>Wr({uploadable:l,fePackage:i,beAdapter:t,slug:a})),Kc({actionType:void 0,uploadthingHook:"callback"},()=>We({uploadable:l,fePackage:i,beAdapter:t})),Kc({actionType:void 0,uploadthingHook:"error"},()=>V9({uploadable:l})),Kh(()=>u5({body:null,fiber:null})));return c&&(yield*gU("Running fiber as daemon").pipe(gk("handleDaemon",n)),"void"===n||("await"===n?yield*c.await:"function"==typeof n&&n(wD(c.await)))),yield*gU("Sending response").pipe(gk("body",u)),yield*qW(u)}).pipe(gF({ParseError:t=>qW(K4(new KR({code:"BAD_REQUEST",message:"Invalid input",cause:t.message}),e.router),{status:400}),UploadThingError:t=>qW(K4(t,e.router),{status:KT[t.code]})})),o=uG(qV("x-uploadthing-version",V4));return H0.pipe(H5("*",s),H4("*",a),H6(o))}).pipe(gH("createRequestHandler")),V9=e=>k9(function*(){let{uploadable:t}=e,r=yield*Jt,{apiKey:i}=yield*K1,n=yield*Kz((yield*r.text),r.headers["x-uploadthing-signature"]??null,i);if(yield*gU(`Signature verified: ${n}`),!n)return yield*gJ("Invalid signature"),yield*new KR({code:"BAD_REQUEST",message:"Invalid signature"});let s=yield*Ja(La({fileKey:$R,error:$R}));yield*gU("Handling error callback request with input:").pipe(gk("json",s));let a=yield*V6;return{body:null,fiber:yield*g5({try:async()=>t.onUploadError({...a,error:new KR({code:"UPLOAD_FAILED",message:`Upload failed for ${s.fileKey}: ${s.error}`}),fileKey:s.fileKey}),catch:e=>new KR({code:"INTERNAL_SERVER_ERROR",message:"Failed to run onUploadError",cause:e})}).pipe(g2(e=>gJ("Failed to run onUploadError. You probably shouldn't be throwing errors here.").pipe(gk("error",e)))).pipe(Or,vk)}}).pipe(gH("handleErrorRequest")),We=e=>k9(function*(){let{uploadable:t,fePackage:r,beAdapter:i}=e,n=yield*Jt,{apiKey:s}=yield*K1,a=yield*Kz((yield*n.text),n.headers["x-uploadthing-signature"]??null,s);if(yield*gU(`Signature verified: ${a}`),!a)return yield*gJ("Invalid signature"),yield*new KR({code:"BAD_REQUEST",message:"Invalid signature"});let o=yield*Ja(La({status:$R,file:KV,origin:$R,metadata:Lu({key:$R,value:$I})}));return yield*gU("Handling callback request with input:").pipe(gk("json",o)),{body:null,fiber:yield*k9(function*(){let e=yield*V6,n=yield*g5({try:async()=>t.onUploadComplete({...e,file:{...o.file,get url(){return K3("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),o.file.url},get appUrl(){return K3("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),o.file.appUrl}},metadata:o.metadata}),catch:e=>new KR({code:"INTERNAL_SERVER_ERROR",message:"Failed to run onUploadComplete. You probably shouldn't be throwing errors here.",cause:e})}),a={fileKey:o.file.key,callbackData:n??null};yield*gU("'onUploadComplete' callback finished. Sending response to UploadThing:").pipe(gk("callbackData",a));let l=(yield*Hm).pipe(Hj);yield*J1("/callback-result").pipe(Hn(o.origin),J9({"x-uploadthing-api-key":PQ(s),"x-uploadthing-version":V4,"x-uploadthing-be-adapter":i,"x-uploadthing-fe-package":r}),Hu(a),uM(l.execute),g2(VB("Failed to register callback result")),uM(Bs(KY)),u8(gL("Sent callback result to UploadThing")),Oi)}).pipe(Or,vk)}}).pipe(gH("handleCallbackRequest")),Wt=e=>k9(function*(){let{json:{files:t,input:r},uploadable:i}=e;yield*gU("Running middleware");let n=yield*V6,s=yield*g5({try:async()=>i.middleware({...n,input:r,files:t}),catch:e=>e instanceof KR?e:new KR({code:"INTERNAL_SERVER_ERROR",message:"Failed to run middleware",cause:e})});if(s[V5]&&s[V5].length!==t.length){let e=`Expected files override to have the same length as original files, got ${s[V5].length} but expected ${t.length}`;return yield*gJ(e),yield*new KR({code:"BAD_REQUEST",message:"Files override must have the same length as files",cause:e})}let a=yield*vv(t,(e,t)=>k9(function*(){let r=s[V5]?.[t];return r&&r.size!==e.size&&(yield*gB("File size mismatch. Reverting to original size")),{name:r?.name??e.name,size:e.size,type:e.type,customId:r?.customId,lastModified:r?.lastModified??Date.now()}}));return{metadata:s,filesWithCustomIds:a,preferredRegion:s[V3]}}).pipe(gH("runRouteMiddleware")),Wr=e=>k9(function*(){let t=(yield*Hm).pipe(Hj),{uploadable:r,fePackage:i,beAdapter:n,slug:s}=e,a=yield*Ja(KZ);yield*gU("Handling upload request").pipe(gk("json",a)),yield*gU("Parsing user input");let o=yield*g5({try:()=>(function(e){if("parseAsync"in e&&"function"==typeof e.parseAsync)return e.parseAsync;if($b(e))return t=>$g(e)(t).catch(e=>{throw new VJ({cause:v7(e[wO])})});if("~standard"in e)return async t=>{let r=await e["~standard"].validate(t);if(r.issues)throw new VJ({cause:r.issues});return r.value};throw Error("Invalid parser")})(r.inputParser)(a.input),catch:e=>new KR({code:"BAD_REQUEST",message:"Invalid input",cause:e})});yield*gU("Input parsed successfully").pipe(gk("input",o));let{metadata:l,filesWithCustomIds:u,preferredRegion:c}=yield*Wt({json:{input:o,files:a.files},uploadable:r});yield*gU("Parsing route config").pipe(gk("routerConfig",r.routerConfig));let h=yield*Kw(r.routerConfig).pipe(gI("InvalidRouteConfig",e=>new KR({code:"BAD_REQUEST",message:"Invalid route config",cause:e})));yield*gU("Route config parsed successfully").pipe(gk("routeConfig",h)),yield*gU("Validating files meet the config requirements").pipe(gk("files",a.files)),yield*VV(a.files,h).pipe(uZ(e=>new KR({code:"BAD_REQUEST",message:`Invalid config: ${e._tag}`,cause:"reason"in e?e.reason:e.message}))),yield*gU("Files validated.");let p=yield*vv(u,e=>uG(Kk(e,KI(h)),t=>({name:e.name,size:e.size,type:e.type||t,lastModified:e.lastModified,customId:e.customId,contentDisposition:h[t]?.contentDisposition??"inline",acl:h[t]?.acl}))).pipe(gF({InvalidFileType:e=>uO(e),UnknownFileType:e=>uO(e)})),f=r.routeOptions,{apiKey:d,appId:m}=yield*K1,g=yield*K2(c),b=yield*K0;yield*gU("Generating presigned URLs").pipe(gk("fileUploadRequests",p),gk("ingestUrl",g));let x=yield*vv(p,e=>k9(function*(){let t=yield*KP(e,m,f.getFileHashParts);return{url:yield*KD(`${g}/${t}`,d,{ttlInSeconds:f.presignedURLTTL,data:{"x-ut-identifier":m,"x-ut-file-name":e.name,"x-ut-file-size":e.size,"x-ut-file-type":e.type,"x-ut-slug":s,"x-ut-custom-id":e.customId,"x-ut-content-disposition":e.contentDisposition,"x-ut-acl":e.acl}}),key:t}}),{concurrency:"unbounded"}),y=yield*Jt,v=yield*Jl(y),S=yield*DO("callbackUrl").pipe(Dw(v.origin+v.pathname),uG(e=>J1(e).pipe(Hs("slug",s)))),_=J1("/route-metadata").pipe(Hn(g),J9({"x-uploadthing-api-key":PQ(d),"x-uploadthing-version":V4,"x-uploadthing-be-adapter":n,"x-uploadthing-fe-package":i}),Hu({fileKeys:x.map(({key:e})=>e),metadata:l,isDev:b,callbackUrl:S.url,callbackSlug:s,awaitServerData:f.awaitServerData??!0}),uM(t.execute)),w=Ou("handleDevStreamError")(function*(e,r){let s=LB(La({file:KV})),a=(yield*$m(s)(r)).file.key;yield*gJ("Failed to forward callback request from dev stream").pipe(gk({fileKey:a,error:e.message}));let o=yield*J1("/callback-result").pipe(Hn(g),J9({"x-uploadthing-api-key":PQ(d),"x-uploadthing-version":V4,"x-uploadthing-be-adapter":n,"x-uploadthing-fe-package":i}),Hu({fileKey:a,error:`Failed to forward callback request from dev stream: ${e.message}`}),uM(t.execute));yield*Vq("Reported callback error to UploadThing")(o)}),k=yield*uJ(b,{onTrue:()=>_.pipe(g1({onSuccess:Vq("Registered metadata",{mixin:"None"}),onFailure:VB("Failed to register metadata")}),H7,K6(KW,e=>S.pipe(J9({"uploadthing-hook":e.hook,"x-uploadthing-signature":e.signature}),Hl(qM(e.payload,"application/json")),t.execute,u8(Vq("Successfully forwarded callback request from dev stream")),gI("ResponseError",t=>w(t,e.payload)),gk(e),um,Or,Oi))),onFalse:()=>_.pipe(g1({onSuccess:Vq("Registered metadata"),onFailure:VB("Failed to register metadata")}),uM(Bs(KG)),Oi)}).pipe(vk),O=x.map((e,t)=>({url:e.url,key:e.key,name:p[t].name,customId:p[t].customId??null}));return yield*gq("Sending presigned URLs to client").pipe(gk("presignedUrls",O)),{body:O,fiber:k}}).pipe(gH("handleUploadAction")),Wi=(e=>(t,r)=>(function e(t={}){let r={$types:{},routerConfig:{image:{maxFileSize:"4MB"}},routeOptions:{awaitServerData:!0},inputParser:{parseAsync:()=>Promise.resolve(void 0),_input:void 0,_output:void 0},middleware:()=>({}),onUploadError:()=>{},onUploadComplete:()=>void 0,errorFormatter:t.errorFormatter??K5,...t};return{input:t=>e({...r,inputParser:t}),middleware:t=>e({...r,middleware:t}),onUploadComplete:e=>({...r,onUploadComplete:e}),onUploadError:t=>e({...r,onUploadError:t})}})({routerConfig:t,routeOptions:r??{},...e}))(),{GET:Wn,POST:Ws}=(e=>{let t=V8(e=>u5({req:e}),e=>u5(e),e,"nextjs-app");return{POST:t,GET:t}})({router:{propertyImageUploader:Wi({image:{maxFileSize:"8MB",maxFileCount:10}}).middleware(async({req:e})=>({uploadedBy:"property-system"})).onUploadComplete(async({metadata:e,file:t})=>(console.log("Upload complete for userId:",e.uploadedBy),console.log("file url",t.url),{uploadedBy:e.uploadedBy})),avatarUploader:Wi({image:{maxFileSize:"4MB",maxFileCount:1}}).middleware(async({req:e})=>({uploadedBy:"user-system"})).onUploadComplete(async({metadata:e,file:t})=>(console.log("Upload complete for userId:",e.uploadedBy),console.log("file url",t.url),{uploadedBy:e.uploadedBy}))}}),Wa=new M.AppRouteRouteModule({definition:{kind:z.RouteKind.APP_ROUTE,page:"/api/uploadthing/route",pathname:"/api/uploadthing",filename:"route",bundlePath:"app/api/uploadthing/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\api\\uploadthing\\route.ts",nextConfigOutput:"",userland:j}),{workAsyncStorage:Wo,workUnitAsyncStorage:Wl,serverHooks:Wu}=Wa;function Wc(){return(0,P.patchFetch)({workAsyncStorage:Wo,workUnitAsyncStorage:Wl})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[7719],()=>r(16593));module.exports=i})();