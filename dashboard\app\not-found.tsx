'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft } from 'lucide-react';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';

export default function NotFound() {
  const { language } = useSimpleLanguage();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-200 dark:text-gray-700">404</h1>
          <div className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
            {language === 'ar' ? 'الصفحة غير موجودة' : 'Page Not Found'}
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            {language === 'ar' 
              ? 'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.'
              : 'Sorry, the page you are looking for does not exist or has been moved.'
            }
          </p>
        </div>

        <div className="space-y-4">
          <Link href="/dashboard">
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
              <Home className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'العودة إلى لوحة التحكم' : 'Back to Dashboard'}
            </Button>
          </Link>
          
          <Button 
            variant="outline" 
            onClick={() => window.history.back()}
            className="w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'العودة للصفحة السابقة' : 'Go Back'}
          </Button>
        </div>

        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          {language === 'ar' 
            ? 'إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بالدعم الفني.'
            : 'If you think this is an error, please contact support.'
          }
        </div>
      </div>
    </div>
  );
}
