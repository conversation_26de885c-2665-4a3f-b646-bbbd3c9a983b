/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/sign-in/page";
exports.ids = ["app/sign-in/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"af095826e9f6\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWhtZWRcXERlc2t0b3BcXGNvZGVcXGJvb3RcXGRhc2hib2FyZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFmMDk1ODI2ZTlmNlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Cairo_arguments_subsets_latin_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"latin\",\"arabic\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\",\\\"arabic\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Cairo_arguments_subsets_latin_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Cairo_arguments_subsets_latin_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! i18next */ \"(rsc)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var _lib_i18n_settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/i18n/settings */ \"(rsc)/./lib/i18n/settings.ts\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(rsc)/./components/auth/auth-provider.tsx\");\n/* harmony import */ var _components_hydration_fix__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/hydration-fix */ \"(rsc)/./components/hydration-fix.tsx\");\n/* harmony import */ var _components_DarkModeProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/DarkModeProvider */ \"(rsc)/./components/DarkModeProvider.tsx\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"لوحة تحكم الذكاء الاصطناعي العقاري\",\n    description: \"لوحة تحكم إدارية لمساعد الذكاء الاصطناعي للعقارات عبر واتساب\"\n};\nasync function generateStaticParams() {\n    return _lib_i18n_settings__WEBPACK_IMPORTED_MODULE_5__.languages.map((lng)=>({\n            lng\n        }));\n}\nfunction RootLayout({ children, params: { lng = \"ar\" } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: lng,\n        dir: (0,i18next__WEBPACK_IMPORTED_MODULE_4__.dir)(lng),\n        suppressHydrationWarning: true,\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DarkModeProvider__WEBPACK_IMPORTED_MODULE_8__.DarkModeScript, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Cairo_arguments_subsets_latin_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_9___default().className),\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hydration_fix__WEBPACK_IMPORTED_MODULE_7__.HydrationFix, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DarkModeProvider__WEBPACK_IMPORTED_MODULE_8__.DarkModeProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_6__.AuthProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                                attribute: \"class\",\n                                defaultTheme: \"dark\",\n                                enableSystem: true,\n                                children: [\n                                    children,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/sign-in/page.tsx":
/*!******************************!*\
  !*** ./app/sign-in/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/DarkModeProvider.tsx":
/*!*****************************************!*\
  !*** ./components/DarkModeProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DarkModeProvider: () => (/* binding */ DarkModeProvider),
/* harmony export */   DarkModeScript: () => (/* binding */ DarkModeScript)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const DarkModeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DarkModeProvider() from the server but DarkModeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\DarkModeProvider.tsx",
"DarkModeProvider",
);const DarkModeScript = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DarkModeScript() from the server but DarkModeScript is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\DarkModeProvider.tsx",
"DarkModeScript",
);

/***/ }),

/***/ "(rsc)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\auth\\auth-provider.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./components/hydration-fix.tsx":
/*!**************************************!*\
  !*** ./components/hydration-fix.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HydrationFix: () => (/* binding */ HydrationFix)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const HydrationFix = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call HydrationFix() from the server but HydrationFix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\hydration-fix.tsx",
"HydrationFix",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./lib/i18n/settings.ts":
/*!******************************!*\
  !*** ./lib/i18n/settings.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   resources: () => (/* binding */ resources)\n/* harmony export */ });\nconst languages = [\n    \"ar\"\n];\nconst defaultLanguage = \"ar\" // Arabic only\n;\nconst resources = {\n    ar: {\n        translation: {\n            // Sidebar\n            \"sidebar.analytics\": \"التحليلات\",\n            \"sidebar.user\": \"لوحة التحكم\",\n            \"sidebar.clients\": \"العملاء\",\n            \"sidebar.messaging\": \"المراسلة\",\n            \"sidebar.marketing\": \"التسويق\",\n            \"sidebar.campaigns\": \"الحملات\",\n            \"sidebar.templates\": \"القوالب\",\n            \"sidebar.appointments\": \"المواعيد\",\n            \"sidebar.ai-chatbot\": \"الذكاء الاصطناعي\",\n            \"sidebar.users\": \"المستخدمين\",\n            \"sidebar.properties\": \"العقارات\",\n            \"sidebar.settings\": \"الإعدادات\",\n            \"sidebar.profile\": \"الملف الشخصي\",\n            // Dashboard\n            \"dashboard.title\": \"لوحة التحكم\",\n            \"dashboard.welcome\": \"أهلاً وسهلاً\",\n            \"dashboard.loading\": \"جاري التحميل...\",\n            \"dashboard.error\": \"حدث خطأ\",\n            \"dashboard.success\": \"تم بنجاح\",\n            // Authentication\n            \"auth.logout\": \"تسجيل الخروج\",\n            \"auth.logoutSuccess\": \"تم تسجيل الخروج بنجاح\",\n            \"auth.logoutFailed\": \"فشل في تسجيل الخروج\",\n            \"auth.redirectingToLogin\": \"جاري التوجيه إلى صفحة تسجيل الدخول\",\n            // Common actions\n            \"action.save\": \"حفظ\",\n            \"action.cancel\": \"إلغاء\",\n            \"action.delete\": \"حذف\",\n            \"action.edit\": \"تعديل\",\n            \"action.view\": \"عرض\",\n            \"action.create\": \"إنشاء\",\n            \"action.search\": \"بحث\",\n            \"action.filter\": \"تصفية\",\n            // Theme\n            \"theme.light\": \"فاتح\",\n            \"theme.dark\": \"داكن\",\n            \"theme.system\": \"النظام\",\n            // Properties\n            \"properties.title\": \"العقارات\",\n            \"properties.subtitle\": \"إدارة وإضافة العقارات الجديدة\",\n            \"properties.add\": \"إضافة عقار\",\n            \"properties.create\": \"إنشاء عقار جديد\",\n            \"properties.edit\": \"تعديل العقار\",\n            \"properties.delete\": \"حذف العقار\",\n            \"properties.view\": \"عرض العقار\",\n            \"properties.save\": \"حفظ العقار\",\n            \"properties.cancel\": \"إلغاء\",\n            \"properties.loading\": \"جاري التحميل...\",\n            \"properties.success\": \"تم حفظ العقار بنجاح\",\n            \"properties.error\": \"حدث خطأ أثناء حفظ العقار\",\n            // Property Form Fields\n            \"property.title\": \"عنوان العقار\",\n            \"property.title.placeholder\": \"أدخل عنوان العقار\",\n            \"property.description\": \"وصف العقار\",\n            \"property.description.placeholder\": \"أدخل وصف مفصل للعقار\",\n            \"property.price\": \"السعر\",\n            \"property.price.placeholder\": \"أدخل سعر العقار\",\n            \"property.currency\": \"العملة\",\n            \"property.type\": \"نوع العقار\",\n            \"property.type.select\": \"اختر نوع العقار\",\n            \"property.status\": \"حالة العقار\",\n            \"property.status.select\": \"اختر حالة العقار\",\n            \"property.bedrooms\": \"عدد غرف النوم\",\n            \"property.bathrooms\": \"عدد دورات المياه\",\n            \"property.area\": \"المساحة\",\n            \"property.location\": \"الموقع\",\n            \"property.location.placeholder\": \"أدخل موقع العقار\",\n            \"property.address\": \"العنوان\",\n            \"property.address.placeholder\": \"أدخل العنوان التفصيلي\",\n            \"property.city\": \"المدينة\",\n            \"property.city.placeholder\": \"أدخل اسم المدينة\",\n            \"property.country\": \"الدولة\",\n            \"property.images\": \"صور العقار\",\n            \"property.features\": \"المميزات\",\n            \"property.amenities\": \"الخدمات\",\n            \"property.yearBuilt\": \"سنة البناء\",\n            \"property.parking\": \"مواقف السيارات\",\n            \"property.furnished\": \"مفروش\",\n            \"property.petFriendly\": \"يسمح بالحيوانات الأليفة\",\n            // Property Types\n            \"property.type.apartment\": \"شقة\",\n            \"property.type.villa\": \"فيلا\",\n            \"property.type.townhouse\": \"تاون هاوس\",\n            \"property.type.penthouse\": \"بنتهاوس\",\n            \"property.type.studio\": \"استوديو\",\n            \"property.type.office\": \"مكتب\",\n            \"property.type.shop\": \"محل تجاري\",\n            \"property.type.warehouse\": \"مستودع\",\n            \"property.type.land\": \"أرض\",\n            \"property.type.building\": \"مبنى\",\n            // Property Status\n            \"property.status.available\": \"متاح\",\n            \"property.status.sold\": \"مباع\",\n            \"property.status.rented\": \"مؤجر\",\n            \"property.status.pending\": \"قيد المراجعة\",\n            // Countries\n            \"country.uae\": \"الإمارات العربية المتحدة\",\n            \"country.saudi\": \"المملكة العربية السعودية\",\n            \"country.qatar\": \"قطر\",\n            \"country.kuwait\": \"الكويت\",\n            \"country.bahrain\": \"البحرين\",\n            \"country.oman\": \"عمان\",\n            // Validation messages\n            \"validation.required\": \"هذا الحقل مطلوب\",\n            \"validation.email\": \"يرجى إدخال بريد إلكتروني صحيح\",\n            \"validation.minLength\": \"يجب أن يكون الحد الأدنى {{min}} أحرف\",\n            \"validation.maxLength\": \"يجب أن يكون الحد الأقصى {{max}} أحرف\",\n            \"validation.number\": \"يرجى إدخال رقم صحيح\",\n            \"validation.positive\": \"يجب أن يكون الرقم أكبر من الصفر\",\n            // Image upload\n            \"images.upload\": \"رفع الصور\",\n            \"images.drag\": \"اسحب الصور هنا أو انقر للاختيار\",\n            \"images.formats\": \"صور حتى ٨ ميجابايت\",\n            \"images.uploading\": \"جاري رفع الصور...\",\n            \"images.success\": \"تم رفع الصور بنجاح\",\n            \"images.error\": \"خطأ في رفع الصور\",\n            \"images.remove\": \"حذف الصورة\",\n            \"images.preview\": \"معاينة الصورة\",\n            \"images.fileType\": \"يرجى اختيار ملفات صور فقط\",\n            \"images.fileSize\": \"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت\"\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/i18n/settings.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsign-in%2Fpage&page=%2Fsign-in%2Fpage&appPaths=%2Fsign-in%2Fpage&pagePath=private-next-app-dir%2Fsign-in%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsign-in%2Fpage&page=%2Fsign-in%2Fpage&appPaths=%2Fsign-in%2Fpage&pagePath=private-next-app-dir%2Fsign-in%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sign-in/page.tsx */ \"(rsc)/./app/sign-in/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'sign-in',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/sign-in/page\",\n        pathname: \"/sign-in\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsign-in%2Fpage&page=%2Fsign-in%2Fpage&appPaths=%2Fsign-in%2Fpage&pagePath=private-next-app-dir%2Fsign-in%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5CDarkModeProvider.tsx%22%2C%22ids%22%3A%5B%22DarkModeScript%22%2C%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5CDarkModeProvider.tsx%22%2C%22ids%22%3A%5B%22DarkModeScript%22%2C%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/auth-provider.tsx */ \"(rsc)/./components/auth/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/DarkModeProvider.tsx */ \"(rsc)/./components/DarkModeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/hydration-fix.tsx */ \"(rsc)/./components/hydration-fix.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(rsc)/./components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5CDarkModeProvider.tsx%22%2C%22ids%22%3A%5B%22DarkModeScript%22%2C%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDYXBwJTVDJTVDbm90LWZvdW5kLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXlHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaG1lZFxcXFxEZXNrdG9wXFxcXGNvZGVcXFxcYm9vdFxcXFxkYXNoYm9hcmRcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Csign-in%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Csign-in%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sign-in/page.tsx */ \"(rsc)/./app/sign-in/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDYXBwJTVDJTVDc2lnbi1pbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBNkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFobWVkXFxcXERlc2t0b3BcXFxcY29kZVxcXFxib290XFxcXGRhc2hib2FyZFxcXFxhcHBcXFxcc2lnbi1pblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Csign-in%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(ssr)/./hooks/useSimpleLanguage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NotFound() {\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-9xl font-bold text-gray-200 dark:text-gray-700\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-semibold text-gray-900 dark:text-white mb-2\",\n                            children: language === 'ar' ? 'الصفحة غير موجودة' : 'Page Not Found'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-8\",\n                            children: language === 'ar' ? 'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.' : 'Sorry, the page you are looking for does not exist or has been moved.'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/dashboard\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    language === 'ar' ? 'العودة إلى لوحة التحكم' : 'Back to Dashboard'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.history.back(),\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                language === 'ar' ? 'العودة للصفحة السابقة' : 'Go Back'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-sm text-gray-500 dark:text-gray-400\",\n                    children: language === 'ar' ? 'إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بالدعم الفني.' : 'If you think this is an error, please contact support.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\not-found.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTZCO0FBQ21CO0FBQ0Q7QUFDZTtBQUUvQyxTQUFTSztJQUN0QixNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHRiwyRUFBaUJBO0lBRXRDLHFCQUNFLDhEQUFDRztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBc0Q7Ozs7OztzQ0FDcEUsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaRixhQUFhLE9BQU8sc0JBQXNCOzs7Ozs7c0NBRTdDLDhEQUFDSTs0QkFBRUYsV0FBVTtzQ0FDVkYsYUFBYSxPQUNWLHlEQUNBOzs7Ozs7Ozs7Ozs7OEJBS1IsOERBQUNDO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1Isa0RBQUlBOzRCQUFDVyxNQUFLO3NDQUNULDRFQUFDVix5REFBTUE7Z0NBQUNPLFdBQVU7O2tEQUNoQiw4REFBQ04sMEZBQUlBO3dDQUFDTSxXQUFVOzs7Ozs7b0NBQ2ZGLGFBQWEsT0FBTywyQkFBMkI7Ozs7Ozs7Ozs7OztzQ0FJcEQsOERBQUNMLHlEQUFNQTs0QkFDTFcsU0FBUTs0QkFDUkMsU0FBUyxJQUFNQyxPQUFPQyxPQUFPLENBQUNDLElBQUk7NEJBQ2xDUixXQUFVOzs4Q0FFViw4REFBQ0wsMEZBQVNBO29DQUFDSyxXQUFVOzs7Ozs7Z0NBQ3BCRixhQUFhLE9BQU8sMEJBQTBCOzs7Ozs7Ozs7Ozs7OzhCQUluRCw4REFBQ0M7b0JBQUlDLFdBQVU7OEJBQ1pGLGFBQWEsT0FDVix5REFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNZCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxhcHBcXG5vdC1mb3VuZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBIb21lLCBBcnJvd0xlZnQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlU2ltcGxlTGFuZ3VhZ2UgfSBmcm9tICdAL2hvb2tzL3VzZVNpbXBsZUxhbmd1YWdlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XG4gIGNvbnN0IHsgbGFuZ3VhZ2UgfSA9IHVzZVNpbXBsZUxhbmd1YWdlKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGRhcms6YmctZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHgtNFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCB3LWZ1bGwgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtOXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktMjAwIGRhcms6dGV4dC1ncmF5LTcwMFwiPjQwNDwvaDE+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTJcIj5cbiAgICAgICAgICAgIHtsYW5ndWFnZSA9PT0gJ2FyJyA/ICfYp9mE2LXZgdit2Kkg2LrZitixINmF2YjYrNmI2K/YqScgOiAnUGFnZSBOb3QgRm91bmQnfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLThcIj5cbiAgICAgICAgICAgIHtsYW5ndWFnZSA9PT0gJ2FyJyBcbiAgICAgICAgICAgICAgPyAn2LnYsNix2KfZi9iMINin2YTYtdmB2K3YqSDYp9mE2KrZiiDYqtio2K3YqyDYudmG2YfYpyDYutmK2LEg2YXZiNis2YjYr9ipINij2Ygg2KrZhSDZhtmC2YTZh9inLidcbiAgICAgICAgICAgICAgOiAnU29ycnksIHRoZSBwYWdlIHlvdSBhcmUgbG9va2luZyBmb3IgZG9lcyBub3QgZXhpc3Qgb3IgaGFzIGJlZW4gbW92ZWQuJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiPlxuICAgICAgICAgICAgPEJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICA8SG9tZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICB7bGFuZ3VhZ2UgPT09ICdhcicgPyAn2KfZhNi52YjYr9ipINil2YTZiSDZhNmI2K3YqSDYp9mE2KrYrdmD2YUnIDogJ0JhY2sgdG8gRGFzaGJvYXJkJ31cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICBcbiAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5oaXN0b3J5LmJhY2soKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAge2xhbmd1YWdlID09PSAnYXInID8gJ9in2YTYudmI2K/YqSDZhNmE2LXZgdit2Kkg2KfZhNiz2KfYqNmC2KknIDogJ0dvIEJhY2snfVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggdGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgIHtsYW5ndWFnZSA9PT0gJ2FyJyBcbiAgICAgICAgICAgID8gJ9il2LDYpyDZg9mG2Kog2KrYudiq2YLYryDYo9mGINmH2LDYpyDYrti32KPYjCDZitix2KzZiSDYp9mE2KfYqti12KfZhCDYqNin2YTYr9i52YUg2KfZhNmB2YbZii4nXG4gICAgICAgICAgICA6ICdJZiB5b3UgdGhpbmsgdGhpcyBpcyBhbiBlcnJvciwgcGxlYXNlIGNvbnRhY3Qgc3VwcG9ydC4nXG4gICAgICAgICAgfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJCdXR0b24iLCJIb21lIiwiQXJyb3dMZWZ0IiwidXNlU2ltcGxlTGFuZ3VhZ2UiLCJOb3RGb3VuZCIsImxhbmd1YWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiIsInZhcmlhbnQiLCJvbkNsaWNrIiwid2luZG93IiwiaGlzdG9yeSIsImJhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./app/sign-in/page.tsx":
/*!******************************!*\
  !*** ./app/sign-in/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n/client */ \"(ssr)/./lib/i18n/client.ts\");\n/* harmony import */ var _components_auth_sign_in_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/sign-in-form */ \"(ssr)/./components/auth/sign-in-form.tsx\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons */ \"(ssr)/./components/icons.tsx\");\n/* harmony import */ var _components_language_switcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/language-switcher */ \"(ssr)/./components/language-switcher.tsx\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-toggle */ \"(ssr)/./components/theme-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SignInContent() {\n    const { t } = (0,_lib_i18n_client__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container flex h-screen w-screen flex-col items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-2 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_4__.Icons.logo, {\n                            className: \"mx-auto h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold tracking-tight\",\n                            children: t(\"auth.welcomeBack\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: t(\"auth.signInToContinue\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_sign_in_form__WEBPACK_IMPORTED_MODULE_3__.SignInForm, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_language_switcher__WEBPACK_IMPORTED_MODULE_5__.LanguageSwitcher, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_6__.ThemeToggle, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction SignInPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignInContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/sign-in/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DarkModeProvider.tsx":
/*!*****************************************!*\
  !*** ./components/DarkModeProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DarkModeProvider: () => (/* binding */ DarkModeProvider),\n/* harmony export */   DarkModeScript: () => (/* binding */ DarkModeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ DarkModeProvider,DarkModeScript auto */ \n\n/**\n * Dark Mode Provider Component\n * Ensures dark mode is applied immediately on page load\n */ function DarkModeProvider({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DarkModeProvider.useEffect\": ()=>{\n            // Apply dark mode immediately\n            const applyDarkMode = {\n                \"DarkModeProvider.useEffect.applyDarkMode\": ()=>{\n                    // Check for saved theme preference, default to dark\n                    const savedTheme = localStorage.getItem('properties-theme') || 'dark';\n                    if (savedTheme === 'dark') {\n                        document.documentElement.classList.add('dark');\n                        document.documentElement.classList.remove('light');\n                    } else {\n                        document.documentElement.classList.add('light');\n                        document.documentElement.classList.remove('dark');\n                    }\n                    // Set color scheme\n                    document.documentElement.style.colorScheme = savedTheme;\n                }\n            }[\"DarkModeProvider.useEffect.applyDarkMode\"];\n            // Apply immediately\n            applyDarkMode();\n            // Also apply on storage change (for cross-tab sync)\n            const handleStorageChange = {\n                \"DarkModeProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === 'properties-theme') {\n                        applyDarkMode();\n                    }\n                }\n            }[\"DarkModeProvider.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"DarkModeProvider.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"DarkModeProvider.useEffect\"];\n        }\n    }[\"DarkModeProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\n * Script to inject dark mode before hydration\n * This prevents flash of light mode\n */ function DarkModeScript() {\n    const script = `\n    (function() {\n      try {\n        const theme = localStorage.getItem('properties-theme') || 'dark';\n        if (theme === 'dark') {\n          document.documentElement.classList.add('dark');\n          document.documentElement.style.colorScheme = 'dark';\n        } else {\n          document.documentElement.classList.add('light');\n          document.documentElement.style.colorScheme = 'light';\n        }\n      } catch (e) {\n        // Fallback to dark mode\n        document.documentElement.classList.add('dark');\n        document.documentElement.style.colorScheme = 'dark';\n      }\n    })();\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: script\n        },\n        suppressHydrationWarning: true\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\DarkModeProvider.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DarkModeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2F1dGgvYXV0aC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUQ7QUFHMUMsU0FBU0MsYUFBYSxFQUFFQyxRQUFRLEVBQTJCO0lBQ2hFLHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWhtZWRcXERlc2t0b3BcXGNvZGVcXGJvb3RcXGRhc2hib2FyZFxcY29tcG9uZW50c1xcYXV0aFxcYXV0aC1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tIFwicmVhY3RcIlxuXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/sign-in-form.tsx":
/*!******************************************!*\
  !*** ./components/auth/sign-in-form.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignInForm: () => (/* binding */ SignInForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _lib_i18n_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/i18n/client */ \"(ssr)/./lib/i18n/client.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons */ \"(ssr)/./components/icons.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ SignInForm auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n// Define the form schema\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_11__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_11__.string().email(),\n    password: zod__WEBPACK_IMPORTED_MODULE_11__.string().min(6)\n});\nfunction SignInForm() {\n    const { t } = (0,_lib_i18n_client__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Get the redirect URL from the query parameters or use the dashboard as default\n    const redirectUrl = searchParams.get(\"redirect_url\") || \"/dashboard/analytics\";\n    console.log(\"Redirect URL:\", redirectUrl);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(formSchema)\n    });\n    async function onSubmit(data) {\n        setIsLoading(true);\n        try {\n            // Use NextAuth's signIn function directly\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signIn)(\"credentials\", {\n                email: data.email,\n                password: data.password,\n                redirect: false,\n                callbackUrl: redirectUrl\n            });\n            if (result?.error) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(t(\"auth.invalidCredentials\"));\n                setIsLoading(false);\n                return;\n            }\n            // Show success message\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(t(\"auth.loginSuccessful\"));\n            // Redirect to the dashboard\n            router.push(redirectUrl);\n            router.refresh();\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(t(\"common.somethingWentWrong\"));\n            setIsLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                    htmlFor: \"email\",\n                                    children: t(\"auth.email\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                    id: \"email\",\n                                    type: \"email\",\n                                    placeholder: \"<EMAIL>\",\n                                    autoCapitalize: \"none\",\n                                    autoComplete: \"email\",\n                                    autoCorrect: \"off\",\n                                    disabled: isLoading,\n                                    ...register(\"email\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive\",\n                                    children: errors.email.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                            htmlFor: \"password\",\n                                            children: t(\"auth.password\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"link\",\n                                            className: \"px-0\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/forgot-password\",\n                                                children: t(\"auth.forgotPassword\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                    id: \"password\",\n                                    type: \"password\",\n                                    autoCapitalize: \"none\",\n                                    autoComplete: \"current-password\",\n                                    disabled: isLoading,\n                                    ...register(\"password\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive\",\n                                    children: errors.password.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            children: [\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_9__.Icons.spinner, {\n                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 27\n                                }, this),\n                                t(\"auth.signIn\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"w-full border-t\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center text-xs uppercase\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-card px-2 text-muted-foreground\",\n                            children: t(\"auth.orContinueWith\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        type: \"button\",\n                        disabled: isLoading,\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signIn)(\"github\", {\n                                callbackUrl: redirectUrl\n                            }),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_9__.Icons.gitHub, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            \"GitHub\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        type: \"button\",\n                        disabled: isLoading,\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signIn)(\"google\", {\n                                callbackUrl: redirectUrl\n                            }),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_9__.Icons.google, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            \"Google\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm\",\n                children: [\n                    t(\"auth.dontHaveAccount\"),\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"link\",\n                        className: \"px-0\",\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: `/sign-up${redirectUrl ? `?redirect_url=${encodeURIComponent(redirectUrl)}` : \"\"}`,\n                            children: t(\"auth.signUp\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\auth\\\\sign-in-form.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/sign-in-form.tsx\n");

/***/ }),

/***/ "(ssr)/./components/hydration-fix.tsx":
/*!**************************************!*\
  !*** ./components/hydration-fix.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HydrationFix: () => (/* binding */ HydrationFix)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ HydrationFix auto */ \nfunction HydrationFix() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"HydrationFix.useEffect\": ()=>{\n            // Remove the cz-shortcut-listen attribute from the body\n            const body = document.querySelector(\"body\");\n            if (body && body.hasAttribute(\"cz-shortcut-listen\")) {\n                body.removeAttribute(\"cz-shortcut-listen\");\n            }\n        }\n    }[\"HydrationFix.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2h5ZHJhdGlvbi1maXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7OztrRUFFaUM7QUFFMUIsU0FBU0M7SUFDZEQsZ0RBQVNBO2tDQUFDO1lBQ1Isd0RBQXdEO1lBQ3hELE1BQU1FLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztZQUNwQyxJQUFJRixRQUFRQSxLQUFLRyxZQUFZLENBQUMsdUJBQXVCO2dCQUNuREgsS0FBS0ksZUFBZSxDQUFDO1lBQ3ZCO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxjb21wb25lbnRzXFxoeWRyYXRpb24tZml4LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxuXG5leHBvcnQgZnVuY3Rpb24gSHlkcmF0aW9uRml4KCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFJlbW92ZSB0aGUgY3otc2hvcnRjdXQtbGlzdGVuIGF0dHJpYnV0ZSBmcm9tIHRoZSBib2R5XG4gICAgY29uc3QgYm9keSA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoXCJib2R5XCIpXG4gICAgaWYgKGJvZHkgJiYgYm9keS5oYXNBdHRyaWJ1dGUoXCJjei1zaG9ydGN1dC1saXN0ZW5cIikpIHtcbiAgICAgIGJvZHkucmVtb3ZlQXR0cmlidXRlKFwiY3otc2hvcnRjdXQtbGlzdGVuXCIpXG4gICAgfVxuICB9LCBbXSlcblxuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkh5ZHJhdGlvbkZpeCIsImJvZHkiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJoYXNBdHRyaWJ1dGUiLCJyZW1vdmVBdHRyaWJ1dGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/hydration-fix.tsx\n");

/***/ }),

/***/ "(ssr)/./components/icons.tsx":
/*!******************************!*\
  !*** ./components/icons.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icons: () => (/* binding */ Icons)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/command.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pizza.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun-medium.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Check,ChevronLeft,ChevronRight,Command,CreditCard,File,FileText,HelpCircle,Image,Laptop,Loader2,Moon,MoreVertical,Pizza,Plus,Settings,SunMedium,Trash,Twitter,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Github,Home,LogOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Github,Home,LogOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Github,Home,LogOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Github,Home,LogOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n\n\n\nconst Icons = {\n    logo: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    close: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    spinner: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    chevronLeft: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    chevronRight: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    trash: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    settings: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    user: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    add: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    warning: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    arrowRight: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    help: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    pizza: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    sun: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    moon: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    laptop: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    gitHub: _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    twitter: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    check: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    more: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    page: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    media: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    post: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    billing: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    ellipsis: _barrel_optimize_names_AlertTriangle_ArrowRight_Check_ChevronLeft_ChevronRight_Command_CreditCard_File_FileText_HelpCircle_Image_Laptop_Loader2_Moon_MoreVertical_Pizza_Plus_Settings_SunMedium_Trash_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    google: ({ ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            \"aria-hidden\": \"true\",\n            focusable: \"false\",\n            \"data-prefix\": \"fab\",\n            \"data-icon\": \"google\",\n            role: \"img\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 488 512\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"currentColor\",\n                d: \"M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\icons.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\icons.tsx\",\n            lineNumber: 59,\n            columnNumber: 5\n        }, undefined),\n    home: _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    logOut: _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n    circle: _barrel_optimize_names_Circle_Github_Home_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/icons.tsx\n");

/***/ }),

/***/ "(ssr)/./components/language-switcher.tsx":
/*!******************************************!*\
  !*** ./components/language-switcher.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: () => (/* binding */ LanguageSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/i18n/client */ \"(ssr)/./lib/i18n/client.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher auto */ \n\n\n\nfunction LanguageSwitcher() {\n    const { i18n } = (0,_lib_i18n_client__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const changeLanguage = (lng)=>{\n        i18n.changeLanguage(lng);\n        document.documentElement.dir = lng === \"ar\" ? \"rtl\" : \"ltr\";\n    };\n    const toggleLanguage = ()=>{\n        const currentLng = i18n.language;\n        const newLng = currentLng === \"ar\" ? \"en\" : \"ar\" // Default to Arabic\n        ;\n        changeLanguage(newLng);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: toggleLanguage,\n        title: i18n.language === \"ar\" ? \"Switch to English\" : \"Switch to Arabic\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\language-switcher.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\language-switcher.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/language-switcher.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWhtZWRcXERlc2t0b3BcXGNvZGVcXGJvb3RcXGRhc2hib2FyZFxcY29tcG9uZW50c1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyLFxuICB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyxcbn0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-toggle.tsx":
/*!*************************************!*\
  !*** ./components/theme-toggle.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \n\n\n\n\nfunction ThemeToggle() {\n    const { setTheme, theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const toggleTheme = ()=>{\n        setTheme(theme === \"light\" ? \"dark\" : \"light\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: toggleTheme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\theme-toggle.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\theme-toggle.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\theme-toggle.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\theme-toggle.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXRvZ2dsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU4QjtBQUNRO0FBQ1M7QUFDUDtBQUVqQyxTQUFTSztJQUNkLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxLQUFLLEVBQUUsR0FBR04scURBQVFBO0lBRXBDLE1BQU1PLGNBQWM7UUFDbEJGLFNBQVNDLFVBQVUsVUFBVSxTQUFTO0lBQ3hDO0lBRUEscUJBQ0UsOERBQUNMLHlEQUFNQTtRQUFDTyxTQUFRO1FBQVFDLE1BQUs7UUFBT0MsU0FBU0g7OzBCQUMzQyw4REFBQ0osb0ZBQUdBO2dCQUFDUSxXQUFVOzs7Ozs7MEJBQ2YsOERBQUNULG9GQUFJQTtnQkFBQ1MsV0FBVTs7Ozs7OzBCQUNoQiw4REFBQ0M7Z0JBQUtELFdBQVU7MEJBQVU7Ozs7Ozs7Ozs7OztBQUdoQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxjb21wb25lbnRzXFx0aGVtZS10b2dnbGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBNb29uLCBTdW4gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lVG9nZ2xlKCkge1xuICBjb25zdCB7IHNldFRoZW1lLCB0aGVtZSB9ID0gdXNlVGhlbWUoKVxuXG4gIGNvbnN0IHRvZ2dsZVRoZW1lID0gKCkgPT4ge1xuICAgIHNldFRoZW1lKHRoZW1lID09PSBcImxpZ2h0XCIgPyBcImRhcmtcIiA6IFwibGlnaHRcIilcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIG9uQ2xpY2s9e3RvZ2dsZVRoZW1lfT5cbiAgICAgIDxTdW4gY2xhc3NOYW1lPVwiaC00IHctNCByb3RhdGUtMCBzY2FsZS0xMDAgdHJhbnNpdGlvbi1hbGwgZGFyazotcm90YXRlLTkwIGRhcms6c2NhbGUtMFwiIC8+XG4gICAgICA8TW9vbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBoLTQgdy00IHJvdGF0ZS05MCBzY2FsZS0wIHRyYW5zaXRpb24tYWxsIGRhcms6cm90YXRlLTAgZGFyazpzY2FsZS0xMDBcIiAvPlxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPlRvZ2dsZSB0aGVtZTwvc3Bhbj5cbiAgICA8L0J1dHRvbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlVGhlbWUiLCJCdXR0b24iLCJNb29uIiwiU3VuIiwiVGhlbWVUb2dnbGUiLCJzZXRUaGVtZSIsInRoZW1lIiwidG9nZ2xlVGhlbWUiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFobWVkXFxEZXNrdG9wXFxjb2RlXFxib290XFxkYXNoYm9hcmRcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWhtZWRcXERlc2t0b3BcXGNvZGVcXGJvb3RcXGRhc2hib2FyZFxcY29tcG9uZW50c1xcdWlcXGxhYmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxuKVxuXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+ICZcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGxhYmVsVmFyaWFudHM+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMYWJlbFByaW1pdGl2ZSIsImN2YSIsImNuIiwibGFiZWxWYXJpYW50cyIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(ssr)/./lib/cookieCleanup.ts\");\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ \n\n/**\n * Arabic-only language hook for Properties system\n * Supports Arabic language with RTL text direction\n */ function useSimpleLanguage() {\n    const [language] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar'); // Arabic only\n    // Initialize Arabic interface with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Clean up cookies first\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.cleanupCookies)();\n            // Set Arabic language preference\n            localStorage.setItem('properties-language', 'ar');\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🏠 Arabic Properties system initialized');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Set Arabic document properties\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Update document properties for Arabic\n            document.documentElement.lang = 'ar';\n            document.documentElement.dir = 'rtl';\n            // Update CSS classes for Arabic\n            document.documentElement.className = 'rtl arabic-interface';\n            // Set Arabic fonts\n            document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🌐 Arabic language interface active');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Translation function for Arabic with direct mapping\n    const translations = {\n        'properties.create': 'إنشاء عقار جديد',\n        'properties.subtitle': 'إدارة وإضافة العقارات الجديدة',\n        'properties.save': 'حفظ العقار',\n        'properties.cancel': 'إلغاء',\n        'properties.loading': 'جاري التحميل...',\n        'properties.success': 'تم حفظ العقار بنجاح',\n        'properties.error': 'حدث خطأ أثناء حفظ العقار',\n        'property.title': 'عنوان العقار',\n        'property.title.placeholder': 'أدخل عنوان العقار',\n        'property.description': 'وصف العقار',\n        'property.description.placeholder': 'أدخل وصف مفصل للعقار',\n        'property.price': 'السعر',\n        'property.price.placeholder': 'أدخل سعر العقار',\n        'property.type': 'نوع العقار',\n        'property.type.select': 'اختر نوع العقار',\n        'property.status': 'حالة العقار',\n        'property.status.select': 'اختر حالة العقار',\n        'property.bedrooms': 'عدد غرف النوم',\n        'property.bathrooms': 'عدد دورات المياه',\n        'property.area': 'المساحة',\n        'property.location': 'الموقع',\n        'property.location.placeholder': 'أدخل موقع العقار',\n        'property.address': 'العنوان',\n        'property.address.placeholder': 'أدخل العنوان التفصيلي',\n        'property.city': 'المدينة',\n        'property.city.placeholder': 'أدخل اسم المدينة',\n        'property.country': 'الدولة',\n        'property.images': 'صور العقار',\n        'property.yearBuilt': 'سنة البناء',\n        'property.parking': 'مواقف السيارات',\n        'property.furnished': 'مفروش',\n        'property.petFriendly': 'يسمح بالحيوانات الأليفة',\n        'property.type.apartment': 'شقة',\n        'property.type.villa': 'فيلا',\n        'property.type.townhouse': 'تاون هاوس',\n        'property.type.penthouse': 'بنتهاوس',\n        'property.type.studio': 'استوديو',\n        'property.type.office': 'مكتب',\n        'property.type.shop': 'محل تجاري',\n        'property.type.warehouse': 'مستودع',\n        'property.type.land': 'أرض',\n        'property.type.building': 'مبنى',\n        'property.status.available': 'متاح',\n        'property.status.sold': 'مباع',\n        'property.status.rented': 'مؤجر',\n        'property.status.pending': 'قيد المراجعة',\n        'country.uae': 'الإمارات العربية المتحدة',\n        'country.saudi': 'المملكة العربية السعودية',\n        'country.qatar': 'قطر',\n        'country.kuwait': 'الكويت',\n        'country.bahrain': 'البحرين',\n        'country.oman': 'عمان',\n        // Currencies\n        'currency.sar': 'ريال سعودي',\n        'currency.aed': 'درهم إماراتي',\n        'currency.usd': 'دولار أمريكي',\n        'currency.eur': 'يورو',\n        'currency.gbp': 'جنيه إسترليني',\n        'images.drag': 'اسحب الصور هنا أو انقر للاختيار',\n        'images.formats': 'صور حتى ٨ ميجابايت',\n        'images.uploading': 'جاري رفع الصور...',\n        'images.success': 'تم رفع الصور بنجاح',\n        'images.error': 'خطأ في رفع الصور',\n        'images.remove': 'حذف الصورة',\n        'images.preview': 'معاينة الصورة',\n        'images.fileType': 'يرجى اختيار ملفات صور فقط',\n        'images.fileSize': 'حجم الصورة يجب أن يكون أقل من ٨ ميجابايت',\n        'validation.required': 'هذا الحقل مطلوب',\n        'validation.positive': 'يجب أن يكون الرقم أكبر من الصفر',\n        // Dashboard general\n        'dashboard.title': 'لوحة التحكم',\n        'dashboard.welcome': 'أهلاً وسهلاً',\n        'dashboard.loading': 'جاري التحميل...',\n        'dashboard.error': 'حدث خطأ',\n        'dashboard.success': 'تم بنجاح',\n        'dashboard.save': 'حفظ',\n        'dashboard.cancel': 'إلغاء',\n        'dashboard.delete': 'حذف',\n        'dashboard.edit': 'تعديل',\n        'dashboard.view': 'عرض',\n        'dashboard.create': 'إنشاء',\n        'dashboard.search': 'بحث',\n        'dashboard.filter': 'تصفية',\n        'dashboard.export': 'تصدير',\n        'dashboard.import': 'استيراد',\n        'dashboard.refresh': 'تحديث',\n        'dashboard.settings': 'الإعدادات',\n        'dashboard.profile': 'الملف الشخصي',\n        'dashboard.logout': 'تسجيل الخروج',\n        // Navigation\n        'nav.dashboard': 'الرئيسية',\n        'nav.analytics': 'التحليلات',\n        'nav.properties': 'العقارات',\n        'nav.clients': 'العملاء',\n        'nav.appointments': 'المواعيد',\n        'nav.marketing': 'التسويق',\n        'nav.campaigns': 'الحملات',\n        'nav.templates': 'القوالب',\n        'nav.messaging': 'المراسلة',\n        'nav.ai-chatbot': 'الذكاء الاصطناعي',\n        'nav.users': 'المستخدمين',\n        'nav.settings': 'الإعدادات',\n        // Common actions\n        'action.add': 'إضافة',\n        'action.remove': 'إزالة',\n        'action.update': 'تحديث',\n        'action.confirm': 'تأكيد',\n        'action.close': 'إغلاق',\n        'action.back': 'رجوع',\n        'action.next': 'التالي',\n        'action.previous': 'السابق',\n        'action.submit': 'إرسال',\n        'action.reset': 'إعادة تعيين',\n        // Status messages\n        'status.loading': 'جاري التحميل...',\n        'status.saving': 'جاري الحفظ...',\n        'status.deleting': 'جاري الحذف...',\n        'status.updating': 'جاري التحديث...',\n        'status.success': 'تم بنجاح',\n        'status.error': 'حدث خطأ',\n        'status.warning': 'تحذير',\n        'status.info': 'معلومات',\n        // User roles\n        'role.admin': 'مدير',\n        'role.agent': 'وكيل',\n        'role.client': 'عميل',\n        'role.user': 'مستخدم',\n        // Theme\n        'theme.light': 'فاتح',\n        'theme.dark': 'داكن',\n        'theme.system': 'النظام',\n        // Analytics\n        'analytics.dashboard': 'لوحة التحليلات',\n        'analytics.overview': 'نظرة عامة',\n        'analytics.performance': 'نظرة عامة على الأداء',\n        'analytics.clients': 'العملاء',\n        'analytics.clientAcquisition': 'اكتساب العملاء',\n        'analytics.campaigns': 'الحملات',\n        'analytics.campaignPerformance': 'أداء الحملات',\n        'analytics.properties': 'العقارات',\n        'analytics.propertyEngagement': 'تفاعل العقارات',\n        // Data Dashboard\n        'data.dashboard': 'لوحة قاعدة البيانات',\n        'data.description': 'عرض وإدارة جميع البيانات من قاعدة البيانات الخلفية',\n        'data.clients': 'العملاء',\n        'data.messages': 'الرسائل',\n        'data.campaigns': 'الحملات',\n        'data.qaPairs': 'أزواج الأسئلة والأجوبة',\n        // Language\n        'language.filter': 'تصفية حسب اللغة',\n        'language.select': 'اختر اللغة',\n        'language.all': 'جميع اللغات',\n        'language.arabic': 'العربية',\n        'language.english': 'الإنجليزية',\n        // UI Components\n        'ui.loading': 'جاري التحميل...',\n        'ui.saving': 'جاري الحفظ...',\n        'ui.saved': 'تم الحفظ',\n        'ui.error': 'حدث خطأ',\n        'ui.success': 'تم بنجاح',\n        'ui.warning': 'تحذير',\n        'ui.info': 'معلومات',\n        'ui.confirm': 'تأكيد',\n        'ui.cancel': 'إلغاء',\n        'ui.delete': 'حذف',\n        'ui.edit': 'تعديل',\n        'ui.view': 'عرض',\n        'ui.add': 'إضافة',\n        'ui.remove': 'إزالة',\n        'ui.update': 'تحديث',\n        'ui.refresh': 'تحديث',\n        'ui.search': 'بحث',\n        'ui.filter': 'تصفية',\n        'ui.sort': 'ترتيب',\n        'ui.export': 'تصدير',\n        'ui.import': 'استيراد',\n        'ui.upload': 'رفع',\n        'ui.download': 'تحميل',\n        'ui.print': 'طباعة',\n        'ui.share': 'مشاركة',\n        'ui.copy': 'نسخ',\n        'ui.paste': 'لصق',\n        'ui.cut': 'قص',\n        'ui.undo': 'تراجع',\n        'ui.redo': 'إعادة',\n        'ui.select': 'اختيار',\n        'ui.selectAll': 'اختيار الكل',\n        'ui.clear': 'مسح',\n        'ui.reset': 'إعادة تعيين',\n        'ui.apply': 'تطبيق',\n        'ui.submit': 'إرسال',\n        'ui.send': 'إرسال',\n        'ui.receive': 'استقبال',\n        'ui.open': 'فتح',\n        'ui.close': 'إغلاق',\n        'ui.minimize': 'تصغير',\n        'ui.maximize': 'تكبير',\n        'ui.expand': 'توسيع',\n        'ui.collapse': 'طي',\n        'ui.show': 'إظهار',\n        'ui.hide': 'إخفاء',\n        'ui.enable': 'تفعيل',\n        'ui.disable': 'تعطيل',\n        'ui.activate': 'تنشيط',\n        'ui.deactivate': 'إلغاء التنشيط',\n        'ui.connect': 'اتصال',\n        'ui.disconnect': 'قطع الاتصال',\n        'ui.online': 'متصل',\n        'ui.offline': 'غير متصل',\n        'ui.available': 'متاح',\n        'ui.unavailable': 'غير متاح',\n        'ui.active': 'نشط',\n        'ui.inactive': 'غير نشط',\n        'ui.pending': 'في الانتظار',\n        'ui.completed': 'مكتمل',\n        'ui.failed': 'فشل',\n        'ui.cancelled': 'ملغي',\n        'ui.draft': 'مسودة',\n        'ui.published': 'منشور',\n        'ui.archived': 'مؤرشف',\n        'ui.deleted': 'محذوف',\n        'ui.new': 'جديد',\n        'ui.old': 'قديم',\n        'ui.recent': 'حديث',\n        'ui.popular': 'شائع',\n        'ui.featured': 'مميز',\n        'ui.recommended': 'موصى به',\n        'ui.favorite': 'مفضل',\n        'ui.bookmark': 'إشارة مرجعية',\n        'ui.like': 'إعجاب',\n        'ui.dislike': 'عدم إعجاب',\n        'ui.comment': 'تعليق',\n        'ui.reply': 'رد',\n        'ui.forward': 'إعادة توجيه',\n        'ui.back': 'رجوع',\n        'ui.next': 'التالي',\n        'ui.previous': 'السابق',\n        'ui.first': 'الأول',\n        'ui.last': 'الأخير',\n        'ui.page': 'صفحة',\n        'ui.of': 'من',\n        'ui.total': 'المجموع',\n        'ui.count': 'العدد',\n        'ui.items': 'عناصر',\n        'ui.results': 'النتائج',\n        'ui.found': 'تم العثور على',\n        'ui.notFound': 'لم يتم العثور على',\n        'ui.empty': 'فارغ',\n        'ui.full': 'ممتلئ',\n        'ui.all': 'الكل',\n        'ui.none': 'لا شيء',\n        'ui.some': 'بعض',\n        'ui.many': 'كثير',\n        'ui.few': 'قليل',\n        'ui.more': 'المزيد',\n        'ui.less': 'أقل',\n        'ui.most': 'الأكثر',\n        'ui.least': 'الأقل',\n        'ui.best': 'الأفضل',\n        'ui.worst': 'الأسوأ',\n        'ui.good': 'جيد',\n        'ui.bad': 'سيء',\n        'ui.excellent': 'ممتاز',\n        'ui.poor': 'ضعيف',\n        'ui.high': 'عالي',\n        'ui.low': 'منخفض',\n        'ui.medium': 'متوسط',\n        'ui.large': 'كبير',\n        'ui.small': 'صغير',\n        'ui.big': 'كبير',\n        'ui.tiny': 'صغير جداً',\n        'ui.huge': 'ضخم',\n        'ui.normal': 'عادي',\n        'ui.special': 'خاص',\n        'ui.custom': 'مخصص',\n        'ui.default': 'افتراضي',\n        'ui.auto': 'تلقائي',\n        'ui.manual': 'يدوي',\n        'ui.automatic': 'تلقائي',\n        'ui.optional': 'اختياري',\n        'ui.required': 'مطلوب',\n        'ui.mandatory': 'إجباري',\n        'ui.forbidden': 'محظور',\n        'ui.allowed': 'مسموح',\n        'ui.denied': 'مرفوض',\n        'ui.granted': 'ممنوح',\n        'ui.public': 'عام',\n        'ui.private': 'خاص',\n        'ui.protected': 'محمي',\n        'ui.secure': 'آمن',\n        'ui.unsafe': 'غير آمن',\n        'ui.safe': 'آمن',\n        'ui.dangerous': 'خطير',\n        'ui.warning': 'تحذير',\n        'ui.caution': 'احتياط',\n        'ui.notice': 'إشعار',\n        'ui.alert': 'تنبيه',\n        'ui.notification': 'إشعار',\n        'ui.message': 'رسالة',\n        'ui.email': 'بريد إلكتروني',\n        'ui.phone': 'هاتف',\n        'ui.address': 'عنوان',\n        'ui.location': 'موقع',\n        'ui.date': 'تاريخ',\n        'ui.time': 'وقت',\n        'ui.datetime': 'التاريخ والوقت',\n        'ui.today': 'اليوم',\n        'ui.yesterday': 'أمس',\n        'ui.tomorrow': 'غداً',\n        'ui.now': 'الآن',\n        'ui.later': 'لاحقاً',\n        'ui.soon': 'قريباً',\n        'ui.never': 'أبداً',\n        'ui.always': 'دائماً',\n        'ui.sometimes': 'أحياناً',\n        'ui.often': 'غالباً',\n        'ui.rarely': 'نادراً',\n        'ui.frequently': 'بكثرة',\n        'ui.occasionally': 'أحياناً',\n        'ui.daily': 'يومياً',\n        'ui.weekly': 'أسبوعياً',\n        'ui.monthly': 'شهرياً',\n        'ui.yearly': 'سنوياً',\n        'ui.hourly': 'كل ساعة',\n        'ui.minute': 'دقيقة',\n        'ui.second': 'ثانية',\n        'ui.hour': 'ساعة',\n        'ui.day': 'يوم',\n        'ui.week': 'أسبوع',\n        'ui.month': 'شهر',\n        'ui.year': 'سنة',\n        'ui.century': 'قرن',\n        'ui.decade': 'عقد',\n        'ui.quarter': 'ربع',\n        'ui.semester': 'فصل دراسي',\n        'ui.season': 'موسم',\n        'ui.spring': 'ربيع',\n        'ui.summer': 'صيف',\n        'ui.autumn': 'خريف',\n        'ui.winter': 'شتاء',\n        'ui.morning': 'صباح',\n        'ui.afternoon': 'بعد الظهر',\n        'ui.evening': 'مساء',\n        'ui.night': 'ليل',\n        'ui.midnight': 'منتصف الليل',\n        'ui.noon': 'ظهر',\n        'ui.dawn': 'فجر',\n        'ui.dusk': 'غسق',\n        'ui.sunrise': 'شروق الشمس',\n        'ui.sunset': 'غروب الشمس'\n    };\n    const t = (key)=>{\n        const translation = translations[key];\n        if (translation) {\n            return translation;\n        }\n        // If no translation found, return the key without the prefix for debugging\n        console.warn(`Missing translation for: ${key}`);\n        return key;\n    };\n    return {\n        language,\n        setLanguage: ()=>{},\n        isRTL: true,\n        isArabic: true,\n        isEnglish: false,\n        t\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useSimpleLanguage.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/cookieCleanup.ts":
/*!******************************!*\
  !*** ./lib/cookieCleanup.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupCookies: () => (/* binding */ cleanupCookies),\n/* harmony export */   initializeArabicEnvironment: () => (/* binding */ initializeArabicEnvironment),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   safeLog: () => (/* binding */ safeLog)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ cleanupCookies,initializeArabicEnvironment,isDevelopment,safeLog auto */ /**\n * Cookie cleanup utility for Arabic Properties system\n * Removes unnecessary cookies and fixes session issues\n */ function cleanupCookies() {\n    if (true) return;\n    // List of cookies to remove\n    const cookiesToRemove = [\n        '__next_hmr_refresh_hash__',\n        '__clerk_db_jwt_NFfxy5s4',\n        '__refresh_NFfxy5s4',\n        '__session_NFfxy5s4',\n        '__client_uat_NFfxy5s4',\n        '__clerk_db_jwt_TYLMw0H7',\n        '__refresh_TYLMw0H7',\n        '__session_TYLMw0H7',\n        '__client_uat_TYLMw0H7',\n        '__clerk_db_jwt',\n        '__clerk_db_jwt_kCaGdcWF',\n        '__client_uat_kCaGdcWF',\n        '__client_uat',\n        'NEXT_LOCALE',\n        'authjs.csrf-token',\n        'authjs.callback-url'\n    ];\n    // Remove each cookie\n    cookiesToRemove.forEach((cookieName)=>{\n        // Remove from current domain\n        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n        // Remove from all possible domains\n        const domains = [\n            window.location.hostname,\n            `.${window.location.hostname}`,\n            'localhost',\n            '.localhost'\n        ];\n        domains.forEach((domain)=>{\n            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain};`;\n        });\n    });\n    // Set Arabic language preference\n    document.cookie = 'language=ar; path=/; max-age=31536000'; // 1 year\n    console.log('🧹 Cookies cleaned up for Arabic Properties system');\n}\n/**\n * Initialize Arabic-only environment\n */ function initializeArabicEnvironment() {\n    if (true) return;\n    // Clean up cookies first\n    cleanupCookies();\n    // Set document language and direction\n    document.documentElement.lang = 'ar';\n    document.documentElement.dir = 'rtl';\n    document.documentElement.className = 'rtl arabic-interface';\n    // Set Arabic fonts\n    document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n    // Add Arabic CSS class to body\n    document.body.classList.add('lang-ar', 'rtl-container');\n    console.log('🌟 Arabic environment initialized');\n}\n/**\n * Check if running in development mode\n */ function isDevelopment() {\n    return \"development\" === 'development';\n}\n/**\n * Safe console log for production\n */ function safeLog(message, ...args) {\n    if (isDevelopment()) {\n        console.log(message, ...args);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/cookieCleanup.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/client.ts":
/*!****************************!*\
  !*** ./lib/i18n/client.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./settings */ \"(ssr)/./lib/i18n/settings.ts\");\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n\n\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    lng: \"ar\",\n    fallbackLng: \"ar\",\n    resources: _settings__WEBPACK_IMPORTED_MODULE_2__.resources,\n    interpolation: {\n        escapeValue: false\n    }\n});\nfunction useTranslation() {\n    return (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi9jbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztvRUFFNkI7QUFDd0Q7QUFDL0M7QUFFdENBLG1EQUFXLENBQUNDLDJEQUFnQkEsRUFBRUssSUFBSSxDQUFDO0lBQ2pDQyxLQUFLO0lBQ0xDLGFBQWE7SUFDYkosU0FBU0Esa0RBQUFBO0lBQ1RLLGVBQWU7UUFDYkMsYUFBYTtJQUNmO0FBQ0Y7QUFFTyxTQUFTUjtJQUNkLE9BQU9DLDZEQUFpQkE7QUFDMUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWhtZWRcXERlc2t0b3BcXGNvZGVcXGJvb3RcXGRhc2hib2FyZFxcbGliXFxpMThuXFxjbGllbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IGkxOG5leHQgZnJvbSBcImkxOG5leHRcIlxuaW1wb3J0IHsgaW5pdFJlYWN0STE4bmV4dCwgdXNlVHJhbnNsYXRpb24gYXMgdXNlVHJhbnNsYXRpb25PcmcgfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiXG5pbXBvcnQgeyByZXNvdXJjZXMgfSBmcm9tIFwiLi9zZXR0aW5nc1wiXG5cbmkxOG5leHQudXNlKGluaXRSZWFjdEkxOG5leHQpLmluaXQoe1xuICBsbmc6IFwiYXJcIixcbiAgZmFsbGJhY2tMbmc6IFwiYXJcIixcbiAgcmVzb3VyY2VzLFxuICBpbnRlcnBvbGF0aW9uOiB7XG4gICAgZXNjYXBlVmFsdWU6IGZhbHNlLFxuICB9LFxufSlcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVRyYW5zbGF0aW9uKCkge1xuICByZXR1cm4gdXNlVHJhbnNsYXRpb25PcmcoKVxufVxuIl0sIm5hbWVzIjpbImkxOG5leHQiLCJpbml0UmVhY3RJMThuZXh0IiwidXNlVHJhbnNsYXRpb24iLCJ1c2VUcmFuc2xhdGlvbk9yZyIsInJlc291cmNlcyIsInVzZSIsImluaXQiLCJsbmciLCJmYWxsYmFja0xuZyIsImludGVycG9sYXRpb24iLCJlc2NhcGVWYWx1ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/settings.ts":
/*!******************************!*\
  !*** ./lib/i18n/settings.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   resources: () => (/* binding */ resources)\n/* harmony export */ });\nconst languages = [\n    \"ar\"\n];\nconst defaultLanguage = \"ar\" // Arabic only\n;\nconst resources = {\n    ar: {\n        translation: {\n            // Sidebar\n            \"sidebar.analytics\": \"التحليلات\",\n            \"sidebar.user\": \"لوحة التحكم\",\n            \"sidebar.clients\": \"العملاء\",\n            \"sidebar.messaging\": \"المراسلة\",\n            \"sidebar.marketing\": \"التسويق\",\n            \"sidebar.campaigns\": \"الحملات\",\n            \"sidebar.templates\": \"القوالب\",\n            \"sidebar.appointments\": \"المواعيد\",\n            \"sidebar.ai-chatbot\": \"الذكاء الاصطناعي\",\n            \"sidebar.users\": \"المستخدمين\",\n            \"sidebar.properties\": \"العقارات\",\n            \"sidebar.settings\": \"الإعدادات\",\n            \"sidebar.profile\": \"الملف الشخصي\",\n            // Dashboard\n            \"dashboard.title\": \"لوحة التحكم\",\n            \"dashboard.welcome\": \"أهلاً وسهلاً\",\n            \"dashboard.loading\": \"جاري التحميل...\",\n            \"dashboard.error\": \"حدث خطأ\",\n            \"dashboard.success\": \"تم بنجاح\",\n            // Authentication\n            \"auth.logout\": \"تسجيل الخروج\",\n            \"auth.logoutSuccess\": \"تم تسجيل الخروج بنجاح\",\n            \"auth.logoutFailed\": \"فشل في تسجيل الخروج\",\n            \"auth.redirectingToLogin\": \"جاري التوجيه إلى صفحة تسجيل الدخول\",\n            // Common actions\n            \"action.save\": \"حفظ\",\n            \"action.cancel\": \"إلغاء\",\n            \"action.delete\": \"حذف\",\n            \"action.edit\": \"تعديل\",\n            \"action.view\": \"عرض\",\n            \"action.create\": \"إنشاء\",\n            \"action.search\": \"بحث\",\n            \"action.filter\": \"تصفية\",\n            // Theme\n            \"theme.light\": \"فاتح\",\n            \"theme.dark\": \"داكن\",\n            \"theme.system\": \"النظام\",\n            // Properties\n            \"properties.title\": \"العقارات\",\n            \"properties.subtitle\": \"إدارة وإضافة العقارات الجديدة\",\n            \"properties.add\": \"إضافة عقار\",\n            \"properties.create\": \"إنشاء عقار جديد\",\n            \"properties.edit\": \"تعديل العقار\",\n            \"properties.delete\": \"حذف العقار\",\n            \"properties.view\": \"عرض العقار\",\n            \"properties.save\": \"حفظ العقار\",\n            \"properties.cancel\": \"إلغاء\",\n            \"properties.loading\": \"جاري التحميل...\",\n            \"properties.success\": \"تم حفظ العقار بنجاح\",\n            \"properties.error\": \"حدث خطأ أثناء حفظ العقار\",\n            // Property Form Fields\n            \"property.title\": \"عنوان العقار\",\n            \"property.title.placeholder\": \"أدخل عنوان العقار\",\n            \"property.description\": \"وصف العقار\",\n            \"property.description.placeholder\": \"أدخل وصف مفصل للعقار\",\n            \"property.price\": \"السعر\",\n            \"property.price.placeholder\": \"أدخل سعر العقار\",\n            \"property.currency\": \"العملة\",\n            \"property.type\": \"نوع العقار\",\n            \"property.type.select\": \"اختر نوع العقار\",\n            \"property.status\": \"حالة العقار\",\n            \"property.status.select\": \"اختر حالة العقار\",\n            \"property.bedrooms\": \"عدد غرف النوم\",\n            \"property.bathrooms\": \"عدد دورات المياه\",\n            \"property.area\": \"المساحة\",\n            \"property.location\": \"الموقع\",\n            \"property.location.placeholder\": \"أدخل موقع العقار\",\n            \"property.address\": \"العنوان\",\n            \"property.address.placeholder\": \"أدخل العنوان التفصيلي\",\n            \"property.city\": \"المدينة\",\n            \"property.city.placeholder\": \"أدخل اسم المدينة\",\n            \"property.country\": \"الدولة\",\n            \"property.images\": \"صور العقار\",\n            \"property.features\": \"المميزات\",\n            \"property.amenities\": \"الخدمات\",\n            \"property.yearBuilt\": \"سنة البناء\",\n            \"property.parking\": \"مواقف السيارات\",\n            \"property.furnished\": \"مفروش\",\n            \"property.petFriendly\": \"يسمح بالحيوانات الأليفة\",\n            // Property Types\n            \"property.type.apartment\": \"شقة\",\n            \"property.type.villa\": \"فيلا\",\n            \"property.type.townhouse\": \"تاون هاوس\",\n            \"property.type.penthouse\": \"بنتهاوس\",\n            \"property.type.studio\": \"استوديو\",\n            \"property.type.office\": \"مكتب\",\n            \"property.type.shop\": \"محل تجاري\",\n            \"property.type.warehouse\": \"مستودع\",\n            \"property.type.land\": \"أرض\",\n            \"property.type.building\": \"مبنى\",\n            // Property Status\n            \"property.status.available\": \"متاح\",\n            \"property.status.sold\": \"مباع\",\n            \"property.status.rented\": \"مؤجر\",\n            \"property.status.pending\": \"قيد المراجعة\",\n            // Countries\n            \"country.uae\": \"الإمارات العربية المتحدة\",\n            \"country.saudi\": \"المملكة العربية السعودية\",\n            \"country.qatar\": \"قطر\",\n            \"country.kuwait\": \"الكويت\",\n            \"country.bahrain\": \"البحرين\",\n            \"country.oman\": \"عمان\",\n            // Validation messages\n            \"validation.required\": \"هذا الحقل مطلوب\",\n            \"validation.email\": \"يرجى إدخال بريد إلكتروني صحيح\",\n            \"validation.minLength\": \"يجب أن يكون الحد الأدنى {{min}} أحرف\",\n            \"validation.maxLength\": \"يجب أن يكون الحد الأقصى {{max}} أحرف\",\n            \"validation.number\": \"يرجى إدخال رقم صحيح\",\n            \"validation.positive\": \"يجب أن يكون الرقم أكبر من الصفر\",\n            // Image upload\n            \"images.upload\": \"رفع الصور\",\n            \"images.drag\": \"اسحب الصور هنا أو انقر للاختيار\",\n            \"images.formats\": \"صور حتى ٨ ميجابايت\",\n            \"images.uploading\": \"جاري رفع الصور...\",\n            \"images.success\": \"تم رفع الصور بنجاح\",\n            \"images.error\": \"خطأ في رفع الصور\",\n            \"images.remove\": \"حذف الصورة\",\n            \"images.preview\": \"معاينة الصورة\",\n            \"images.fileType\": \"يرجى اختيار ملفات صور فقط\",\n            \"images.fileSize\": \"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت\"\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/settings.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFobWVkXFxEZXNrdG9wXFxjb2RlXFxib290XFxkYXNoYm9hcmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5CDarkModeProvider.tsx%22%2C%22ids%22%3A%5B%22DarkModeScript%22%2C%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5CDarkModeProvider.tsx%22%2C%22ids%22%3A%5B%22DarkModeScript%22%2C%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/auth-provider.tsx */ \"(ssr)/./components/auth/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/DarkModeProvider.tsx */ \"(ssr)/./components/DarkModeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/hydration-fix.tsx */ \"(ssr)/./components/hydration-fix.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5CDarkModeProvider.tsx%22%2C%22ids%22%3A%5B%22DarkModeScript%22%2C%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDYXBwJTVDJTVDbm90LWZvdW5kLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXlHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaG1lZFxcXFxEZXNrdG9wXFxcXGNvZGVcXFxcYm9vdFxcXFxkYXNoYm9hcmRcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Csign-in%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Csign-in%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sign-in/page.tsx */ \"(ssr)/./app/sign-in/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDYXBwJTVDJTVDc2lnbi1pbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBNkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFobWVkXFxcXERlc2t0b3BcXFxcY29kZVxcXFxib290XFxcXGRhc2hib2FyZFxcXFxhcHBcXFxcc2lnbi1pblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Csign-in%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWhtZWQlNUMlNUNEZXNrdG9wJTVDJTVDY29kZSU1QyU1Q2Jvb3QlNUMlNUNkYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBaG1lZCU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlJTVDJTVDYm9vdCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBaG1lZCU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlJTVDJTVDYm9vdCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWhtZWQlNUMlNUNEZXNrdG9wJTVDJTVDY29kZSU1QyU1Q2Jvb3QlNUMlNUNkYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGUlNUMlNUNib290JTVDJTVDZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBaG1lZCU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlJTVDJTVDYm9vdCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUFtSjtBQUNuSjtBQUNBLDBPQUFzSjtBQUN0SjtBQUNBLDBPQUFzSjtBQUN0SjtBQUNBLG9SQUE0SztBQUM1SztBQUNBLHdPQUFxSjtBQUNySjtBQUNBLDRQQUFnSztBQUNoSztBQUNBLGtRQUFtSztBQUNuSztBQUNBLHNRQUFvSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWhtZWRcXFxcRGVza3RvcFxcXFxjb2RlXFxcXGJvb3RcXFxcZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFobWVkXFxcXERlc2t0b3BcXFxcY29kZVxcXFxib290XFxcXGRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaG1lZFxcXFxEZXNrdG9wXFxcXGNvZGVcXFxcYm9vdFxcXFxkYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWhtZWRcXFxcRGVza3RvcFxcXFxjb2RlXFxcXGJvb3RcXFxcZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFobWVkXFxcXERlc2t0b3BcXFxcY29kZVxcXFxib290XFxcXGRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFobWVkXFxcXERlc2t0b3BcXFxcY29kZVxcXFxib290XFxcXGRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaG1lZFxcXFxEZXNrdG9wXFxcXGNvZGVcXFxcYm9vdFxcXFxkYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWhtZWRcXFxcRGVza3RvcFxcXFxjb2RlXFxcXGJvb3RcXFxcZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/@radix-ui","vendor-chunks/i18next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/react-i18next","vendor-chunks/next-themes","vendor-chunks/class-variance-authority","vendor-chunks/html-parse-stringify","vendor-chunks/clsx","vendor-chunks/void-elements","vendor-chunks/zod","vendor-chunks/react-hook-form","vendor-chunks/@hookform","vendor-chunks/sonner"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsign-in%2Fpage&page=%2Fsign-in%2Fpage&appPaths=%2Fsign-in%2Fpage&pagePath=private-next-app-dir%2Fsign-in%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();