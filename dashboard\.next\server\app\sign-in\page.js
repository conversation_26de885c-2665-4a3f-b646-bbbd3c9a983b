(()=>{var e={};e.id=6502,e.ids=[6502],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7489:(e,r,t)=>{"use strict";t.d(r,{c:()=>n});var s=t(60687),a=t(68082),i=t(24934),o=t(11437);function n(){let{i18n:e}=(0,a.B)(),r=r=>{e.changeLanguage(r),document.documentElement.dir="ar"===r?"rtl":"ltr"};return(0,s.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>{r("en"===e.language?"ar":"en")},title:"en"===e.language?"Switch to Arabic":"Switch to English",children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12484:(e,r,t)=>{"use strict";t.d(r,{ES:()=>a,eo:()=>s});let s=["ar"],a={ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"الذكاء الاصطناعي","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","properties.title":"العقارات","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.add":"إضافة عقار","properties.create":"إنشاء عقار جديد","properties.edit":"تعديل العقار","properties.delete":"حذف العقار","properties.view":"عرض العقار","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.currency":"العملة","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.features":"المميزات","property.amenities":"الخدمات","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","validation.required":"هذا الحقل مطلوب","validation.email":"يرجى إدخال بريد إلكتروني صحيح","validation.minLength":"يجب أن يكون الحد الأدنى {{min}} أحرف","validation.maxLength":"يجب أن يكون الحد الأقصى {{max}} أحرف","validation.number":"يرجى إدخال رقم صحيح","validation.positive":"يجب أن يكون الرقم أكبر من الصفر","images.upload":"رفع الصور","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت"}}}},15125:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20384:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>p});var s=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let p={children:["",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15125)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/sign-in/page",pathname:"/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,r,t)=>{"use strict";t.d(r,{J:()=>p});var s=t(60687),a=t(43210),i=t(78148),o=t(24224),n=t(96241);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(l(),e),...r}));p.displayName=i.b.displayName},51229:(e,r,t)=>{"use strict";t.d(r,{F:()=>S});var s=t(60687),a=t(85397),i=t(11860),o=t(41862),n=t(47033),l=t(14952),p=t(96362),d=t(84027),c=t(58869),u=t(96474),m=t(43649),h=t(70334),g=t(65668),x=t(47282),f=t(29104),y=t(363),b=t(34410),v=t(72575),j=t(13964),w=t(81904),A=t(78464),k=t(9005),N=t(10022),C=t(85778),P=t(62157),_=t(32192),R=t(40083),U=t(65822);let S={logo:a.A,close:i.A,spinner:o.A,chevronLeft:n.A,chevronRight:l.A,trash:p.A,settings:d.A,user:c.A,add:u.A,warning:m.A,arrowRight:h.A,help:g.A,pizza:x.A,sun:f.A,moon:y.A,laptop:b.A,gitHub:P.A,twitter:v.A,check:j.A,more:w.A,page:A.A,media:k.A,post:N.A,billing:C.A,ellipsis:w.A,google:({...e})=>(0,s.jsx)("svg",{"aria-hidden":"true",focusable:"false","data-prefix":"fab","data-icon":"google",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 488 512",...e,children:(0,s.jsx)("path",{fill:"currentColor",d:"M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"})}),home:_.A,logOut:R.A,circle:U.A}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68082:(e,r,t)=>{"use strict";t.d(r,{B:()=>o});var s=t(46755),a=t(16457),i=t(12484);function o(){return(0,a.Bd)()}s.Ay.use(a.r9).init({lng:"en",fallbackLng:"en",resources:i.ES,interpolation:{escapeValue:!1}})},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(60687),a=t(43210),i=t(96241);let o=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));o.displayName="Input"},81410:(e,r,t)=>{Promise.resolve().then(t.bind(t,15125))},90215:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var s=t(60687),a=t(43210),i=t(68082),o=t(16189),n=t(99208),l=t(27605),p=t(63442),d=t(45880),c=t(24934),u=t(68988),m=t(39390),h=t(51229),g=t(52581);let x=d.Ik({email:d.Yj().email(),password:d.Yj().min(6)});function f(){let{t:e}=(0,i.B)(),r=(0,o.useRouter)(),t=(0,o.useSearchParams)().get("redirect_url")||"/dashboard/analytics";console.log("Redirect URL:",t);let[d,f]=(0,a.useState)(!1),{register:y,handleSubmit:b,formState:{errors:v}}=(0,l.mN)({resolver:(0,p.u)(x)});async function j(s){f(!0);try{let a=await (0,n.Jv)("credentials",{email:s.email,password:s.password,redirect:!1,callbackUrl:t});if(a?.error){g.oR.error(e("auth.invalidCredentials")),f(!1);return}g.oR.success(e("auth.loginSuccessful")),r.push(t),r.refresh()}catch(r){console.error("Sign in error:",r),g.oR.error(e("common.somethingWentWrong")),f(!1)}}return(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsx)("form",{onSubmit:b(j),children:(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(m.J,{htmlFor:"email",children:e("auth.email")}),(0,s.jsx)(u.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:d,...y("email")}),v.email&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:v.email.message})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(m.J,{htmlFor:"password",children:e("auth.password")}),(0,s.jsx)(c.$,{variant:"link",className:"px-0",asChild:!0,children:(0,s.jsx)("a",{href:"/forgot-password",children:e("auth.forgotPassword")})})]}),(0,s.jsx)(u.p,{id:"password",type:"password",autoCapitalize:"none",autoComplete:"current-password",disabled:d,...y("password")}),v.password&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:v.password.message})]}),(0,s.jsxs)(c.$,{type:"submit",disabled:d,children:[d&&(0,s.jsx)(h.F.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),e("auth.signIn")]})]})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("span",{className:"w-full border-t"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,s.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:e("auth.orContinueWith")})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)(c.$,{variant:"outline",type:"button",disabled:d,onClick:()=>(0,n.Jv)("github",{callbackUrl:t}),children:[(0,s.jsx)(h.F.gitHub,{className:"mr-2 h-4 w-4"}),"GitHub"]}),(0,s.jsxs)(c.$,{variant:"outline",type:"button",disabled:d,onClick:()=>(0,n.Jv)("google",{callbackUrl:t}),children:[(0,s.jsx)(h.F.google,{className:"mr-2 h-4 w-4"}),"Google"]})]}),(0,s.jsxs)("div",{className:"text-center text-sm",children:[e("auth.dontHaveAccount")," ",(0,s.jsx)(c.$,{variant:"link",className:"px-0",asChild:!0,children:(0,s.jsx)("a",{href:`/sign-up${t?`?redirect_url=${encodeURIComponent(t)}`:""}`,children:e("auth.signUp")})})]})]})}var y=t(7489),b=t(99563);function v(){let{t:e}=(0,i.B)();return(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,s.jsx)(h.F.logo,{className:"mx-auto h-6 w-6"}),(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:e("auth.welcomeBack")}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e("auth.signInToContinue")})]}),(0,s.jsx)(f,{}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(y.c,{}),(0,s.jsx)(b.U,{})]})]})})}function j(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}),children:(0,s.jsx)(v,{})})}},99562:(e,r,t)=>{Promise.resolve().then(t.bind(t,90215))},99563:(e,r,t)=>{"use strict";t.d(r,{U:()=>l});var s=t(60687);t(43210);var a=t(10218),i=t(24934),o=t(21134),n=t(363);function l(){let{setTheme:e,theme:r}=(0,a.D)();return(0,s.jsxs)(i.$,{variant:"ghost",size:"icon",onClick:()=>{e("light"===r?"dark":"light")},children:[(0,s.jsx)(o.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(n.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,8157,3903,3517,8058,4088],()=>t(20384));module.exports=s})();