"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./services/propertyService.ts":
/*!*************************************!*\
  !*** ./services/propertyService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createProperty: () => (/* binding */ createProperty),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deleteProperty: () => (/* binding */ deleteProperty),\n/* harmony export */   getProperties: () => (/* binding */ getProperties),\n/* harmony export */   getPropertyById: () => (/* binding */ getPropertyById),\n/* harmony export */   propertyService: () => (/* binding */ propertyService),\n/* harmony export */   updateProperty: () => (/* binding */ updateProperty)\n/* harmony export */ });\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n\n/**\n * Creates a new property by sending data to the API\n * @param propertyData The property data to send\n * @returns The created property data\n */ const createProperty = async (propertyData)=>{\n    try {\n        // Clean the data before sending\n        const cleanedData = {\n            ...propertyData,\n            // Ensure arrays are properly initialized\n            images: propertyData.images || [],\n            features: propertyData.features || [],\n            featuresAr: propertyData.featuresAr || [],\n            amenities: propertyData.amenities || [],\n            amenitiesAr: propertyData.amenitiesAr || [],\n            // Set defaults for optional fields\n            currency: propertyData.currency || 'USD',\n            country: propertyData.country || 'UAE',\n            status: propertyData.status || 'AVAILABLE',\n            isActive: propertyData.isActive !== undefined ? propertyData.isActive : true,\n            isFeatured: propertyData.isFeatured || false\n        };\n        // Send as JSON using our API client\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/properties', cleanedData);\n        return response.data || response;\n    } catch (error) {\n        console.error('Error creating property:', error);\n        throw error;\n    }\n};\n/**\n * Fetches all properties from the API\n * @returns List of properties\n */ const getProperties = async ()=>{\n    try {\n        return await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/properties');\n    } catch (error) {\n        console.error('Error fetching properties:', error);\n        throw error;\n    }\n};\n/**\n * Fetches a single property by ID\n * @param id The property ID\n * @returns The property data\n */ const getPropertyById = async (id)=>{\n    try {\n        return await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/properties/\".concat(id));\n    } catch (error) {\n        // Handle 404 errors specifically\n        if (error.status === 404 || error.isNotFound) {\n            console.log(\"Property with ID \".concat(id, \" not found in database (404)\"));\n            const notFoundError = new Error(\"Property not found: \".concat(id));\n            notFoundError.status = 404;\n            notFoundError.isNotFound = true;\n            throw notFoundError;\n        }\n        console.error(\"Error fetching property with ID \".concat(id, \":\"), error);\n        throw error;\n    }\n};\n/**\n * Updates an existing property\n * @param id The property ID\n * @param propertyData The updated property data\n * @returns The updated property data\n */ const updateProperty = async (id, propertyData)=>{\n    try {\n        // If there are images, we need to use FormData\n        if (propertyData.images && propertyData.images.length > 0) {\n            const formData = new FormData();\n            // Add all property data to the form\n            Object.entries(propertyData).forEach((param)=>{\n                let [key, value] = param;\n                if (key !== 'images') {\n                    formData.append(key, String(value));\n                }\n            });\n            // Add images to the form\n            Array.from(propertyData.images).forEach((image)=>{\n                formData.append(\"images\", image);\n            });\n            // Send the request to the API using our API client\n            return await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].upload(\"/properties/\".concat(id), formData);\n        } else {\n            // If no images, send as JSON using our API client\n            return await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/properties/\".concat(id), propertyData);\n        }\n    } catch (error) {\n        console.error(\"Error updating property with ID \".concat(id, \":\"), error);\n        throw error;\n    }\n};\n/**\n * Deletes a property\n * @param id The property ID\n */ const deleteProperty = async (id)=>{\n    try {\n        await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/properties/\".concat(id));\n    } catch (error) {\n        console.error(\"Error deleting property with ID \".concat(id, \":\"), error);\n        throw error;\n    }\n};\n// Default export for the property service\nconst propertyService = {\n    createProperty,\n    getProperties,\n    getPropertyById,\n    updateProperty,\n    deleteProperty\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (propertyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/propertyService.ts\n"));

/***/ })

});