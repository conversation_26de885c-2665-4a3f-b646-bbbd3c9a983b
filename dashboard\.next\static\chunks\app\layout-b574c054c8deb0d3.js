(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1168:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(95155),o=r(45493);function n(e){let{children:t}=e;return(0,s.jsx)(o.CP,{children:t})}},19324:()=>{},23685:(e,t,r)=>{"use strict";r.d(t,{HydrationFix:()=>o});var s=r(12115);function o(){return(0,s.useEffect)(()=>{let e=document.querySelector("body");e&&e.hasAttribute("cz-shortcut-listen")&&e.removeAttribute("cz-shortcut-listen")},[]),null}},46633:(e,t,r)=>{"use strict";r.d(t,{DarkModeProvider:()=>n,DarkModeScript:()=>a});var s=r(95155),o=r(12115);function n(e){let{children:t}=e;return(0,o.useEffect)(()=>{let e=()=>{let e=localStorage.getItem("properties-theme")||"dark";"dark"===e?(document.documentElement.classList.add("dark"),document.documentElement.classList.remove("light")):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark")),document.documentElement.style.colorScheme=e};e();let t=t=>{"properties-theme"===t.key&&e()};return window.addEventListener("storage",t),()=>{window.removeEventListener("storage",t)}},[]),(0,s.jsx)(s.Fragment,{children:t})}function a(){return(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n    (function() {\n      try {\n        const theme = localStorage.getItem('properties-theme') || 'dark';\n        if (theme === 'dark') {\n          document.documentElement.classList.add('dark');\n          document.documentElement.style.colorScheme = 'dark';\n        } else {\n          document.documentElement.classList.add('light');\n          document.documentElement.style.colorScheme = 'light';\n        }\n      } catch (e) {\n        // Fallback to dark mode\n        document.documentElement.classList.add('dark');\n        document.documentElement.style.colorScheme = 'dark';\n      }\n    })();\n  "},suppressHydrationWarning:!0})}},53580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m});var s=r(12115);let o=0,n=new Map,a=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],c={toasts:[]};function l(e){c=d(c,e),i.forEach(e=>{e(c)})}function u(e){let{...t}=e,r=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>l({type:"DISMISS_TOAST",toastId:r});return l({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,t]=s.useState(c);return s.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(52596),o=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,s.$)(t))}},65038:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>g});var s=r(95155),o=r(53580),n=r(12115),a=r(26621),d=r(74466),i=r(54416),c=r(53999);let l=a.Kq,u=n.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(a.LM,{ref:t,className:(0,c.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});u.displayName=a.LM.displayName;let m=(0,d.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=n.forwardRef((e,t)=>{let{className:r,variant:o,...n}=e;return(0,s.jsx)(a.bL,{ref:t,className:(0,c.cn)(m({variant:o}),r),...n})});f.displayName=a.bL.displayName,n.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(a.rc,{ref:t,className:(0,c.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...o})}).displayName=a.rc.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(a.bm,{ref:t,className:(0,c.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});p.displayName=a.bm.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(a.hE,{ref:t,className:(0,c.cn)("text-sm font-semibold",r),...o})});v.displayName=a.hE.displayName;let h=n.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(a.VY,{ref:t,className:(0,c.cn)("text-sm opacity-90",r),...o})});function g(){let{toasts:e}=(0,o.dj)();return(0,s.jsxs)(l,{children:[e.map(function(e){let{id:t,title:r,description:o,action:n,...a}=e;return(0,s.jsxs)(f,{...a,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(v,{children:r}),o&&(0,s.jsx)(h,{children:o})]}),n,(0,s.jsx)(p,{})]},t)}),(0,s.jsx)(u,{})]})}h.displayName=a.VY.displayName},95993:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,19324,23)),Promise.resolve().then(r.bind(r,1168)),Promise.resolve().then(r.bind(r,46633)),Promise.resolve().then(r.bind(r,23685)),Promise.resolve().then(r.bind(r,99304)),Promise.resolve().then(r.bind(r,65038)),Promise.resolve().then(r.t.bind(r,27272,23))},99304:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(95155);r(12115);var o=r(51362);function n(e){let{children:t,...r}=e;return(0,s.jsx)(o.N,{...r,children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9536,2533,7525,4277,6071,5493,4300,8441,1684,7358],()=>t(95993)),_N_E=e.O()}]);