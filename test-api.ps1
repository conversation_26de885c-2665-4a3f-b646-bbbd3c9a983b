# Test Property API CRUD Operations

$baseUrl = "http://localhost:5000/api/v1/properties"

# Test data for creating a property
$propertyData = @{
    title = "Test Property from API"
    titleAr = "عقار تجريبي من API"
    description = "This is a test property created via API to verify CRUD operations"
    descriptionAr = "هذا عقار تجريبي تم إنشاؤه عبر API للتحقق من عمليات CRUD"
    price = 250000
    currency = "USD"
    type = "APARTMENT"
    status = "AVAILABLE"
    bedrooms = 2
    bathrooms = 2
    area = 120.5
    location = "Downtown Dubai"
    locationAr = "وسط دبي"
    address = "123 Sheikh Zayed Road"
    addressAr = "123 شارع الشيخ زايد"
    city = "Dubai"
    cityAr = "دبي"
    country = "UAE"
    countryAr = "الإمارات العربية المتحدة"
    latitude = 25.2048
    longitude = 55.2708
    images = @("https://example.com/image1.jpg", "https://example.com/image2.jpg")
    features = @("Balcony", "Parking", "Swimming Pool")
    featuresAr = @("شرفة", "موقف سيارات", "مسبح")
    amenities = @("Gym", "Security", "Elevator")
    amenitiesAr = @("صالة رياضية", "أمن", "مصعد")
    yearBuilt = 2020
    parking = 1
    furnished = $true
    petFriendly = $false
    utilities = "All utilities included"
    utilitiesAr = "جميع المرافق مشمولة"
    contactInfo = "Contact: +971-50-123-4567"
    isActive = $true
    isFeatured = $false
}

# Convert to JSON
$jsonData = $propertyData | ConvertTo-Json -Depth 10

Write-Host "Testing Property API CRUD Operations..." -ForegroundColor Green

# Test 1: Create Property (POST)
Write-Host "`n1. Testing CREATE (POST)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Body $jsonData -ContentType "application/json"
    $result = $response.Content | ConvertFrom-Json
    $newPropertyId = $result.data.id
    Write-Host "✅ CREATE Success: Property created with ID: $newPropertyId" -ForegroundColor Green
    Write-Host "Response: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ CREATE Failed: $($_.Exception.Message)" -ForegroundColor Red
    $newPropertyId = $null
}

# Test 2: Read Property (GET by ID)
if ($newPropertyId) {
    Write-Host "`n2. Testing READ (GET by ID)..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/$newPropertyId" -Method GET
        $result = $response.Content | ConvertFrom-Json
        Write-Host "✅ READ Success: Retrieved property: $($result.data.title)" -ForegroundColor Green
        Write-Host "Response: $($response.StatusCode)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ READ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 3: Update Property (PUT)
if ($newPropertyId) {
    Write-Host "`n3. Testing UPDATE (PUT)..." -ForegroundColor Yellow
    $updateData = @{
        title = "Updated Test Property"
        price = 275000
        isFeatured = $true
    }
    $updateJson = $updateData | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/$newPropertyId" -Method PUT -Body $updateJson -ContentType "application/json"
        $result = $response.Content | ConvertFrom-Json
        Write-Host "✅ UPDATE Success: Property updated: $($result.data.title)" -ForegroundColor Green
        Write-Host "Response: $($response.StatusCode)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ UPDATE Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 4: List Properties (GET all)
Write-Host "`n4. Testing LIST (GET all)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method GET
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ LIST Success: Found $($result.data.total) properties" -ForegroundColor Green
    Write-Host "Response: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ LIST Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Get Stats
Write-Host "`n5. Testing STATS (GET stats)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/stats" -Method GET
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ STATS Success: Total properties: $($result.data.total)" -ForegroundColor Green
    Write-Host "Response: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ STATS Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Delete Property (DELETE)
if ($newPropertyId) {
    Write-Host "`n6. Testing DELETE..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/$newPropertyId" -Method DELETE
        Write-Host "✅ DELETE Success: Property deleted" -ForegroundColor Green
        Write-Host "Response: $($response.StatusCode)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ DELETE Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 API Testing Complete!" -ForegroundColor Green
