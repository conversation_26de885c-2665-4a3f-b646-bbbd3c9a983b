# Test Saudi Arabia Setup (Simple)

Write-Host "Testing Saudi Arabia Setup..." -ForegroundColor Green

# Test 1: Create a property with Saudi Arabia defaults
Write-Host "`n1. Testing Saudi Property Creation..." -ForegroundColor Yellow

$saudiProperty = @{
    title = "Luxury Villa in Riyadh"
    description = "Beautiful 5-bedroom villa in Malqa district, Riyadh with private garden and pool"
    price = 2500000
    currency = "SAR"
    type = "VILLA"
    status = "AVAILABLE"
    bedrooms = 5
    bathrooms = 4
    area = 400.0
    location = "Malqa District"
    address = "Prince Mohammed bin Abdulaziz Street"
    city = "Riyadh"
    country = "SAUDI"
    images = @()
    features = @("Private Pool", "Garden", "3 Car Parking", "Maid Room")
    amenities = @("24/7 Security", "Near Schools", "Shopping Center Nearby")
    yearBuilt = 2022
    parking = 3
    furnished = $false
    petFriendly = $true
    isActive = $true
    isFeatured = $true
}

$jsonData = $saudiProperty | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/v1/properties" -Method POST -Body $jsonData -ContentType "application/json"
    if ($response.StatusCode -eq 201) {
        $result = $response.Content | ConvertFrom-Json
        $saudiPropertyId = $result.data.id
        Write-Host "✅ Saudi property created successfully - ID: $saudiPropertyId" -ForegroundColor Green
        Write-Host "   Title: $($result.data.title)" -ForegroundColor Cyan
        Write-Host "   Price: $($result.data.price) $($result.data.currency)" -ForegroundColor Cyan
        Write-Host "   City: $($result.data.city)" -ForegroundColor Cyan
        Write-Host "   Country: $($result.data.country)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed to create Saudi property: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Create Jeddah property
Write-Host "`n2. Testing Jeddah Property..." -ForegroundColor Yellow

$jeddahProperty = @{
    title = "Sea View Apartment in Jeddah"
    description = "Luxury 3-bedroom apartment with Red Sea view on Jeddah Corniche"
    price = 1800000
    currency = "SAR"
    type = "APARTMENT"
    status = "AVAILABLE"
    bedrooms = 3
    bathrooms = 2
    area = 180.0
    location = "Jeddah Corniche"
    address = "Corniche Road"
    city = "Jeddah"
    country = "SAUDI"
    images = @()
    features = @("Sea View", "Large Balcony", "Car Parking")
    amenities = @("Swimming Pool", "Gym", "Security")
    yearBuilt = 2021
    parking = 1
    furnished = $true
    petFriendly = $false
    isActive = $true
    isFeatured = $true
}

$jeddahJson = $jeddahProperty | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/v1/properties" -Method POST -Body $jeddahJson -ContentType "application/json"
    if ($response.StatusCode -eq 201) {
        $result = $response.Content | ConvertFrom-Json
        Write-Host "✅ Jeddah property created successfully" -ForegroundColor Green
        Write-Host "   Title: $($result.data.title)" -ForegroundColor Cyan
        Write-Host "   Price: $($result.data.price) $($result.data.currency)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed to create Jeddah property: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Check statistics
Write-Host "`n3. Testing Statistics..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/v1/properties/stats" -Method GET
    if ($response.StatusCode -eq 200) {
        $result = $response.Content | ConvertFrom-Json
        Write-Host "✅ Updated Statistics:" -ForegroundColor Green
        Write-Host "   Total properties: $($result.data.total)" -ForegroundColor Cyan
        Write-Host "   Available: $($result.data.available)" -ForegroundColor Cyan
        Write-Host "   Featured: $($result.data.featured)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed to fetch stats" -ForegroundColor Red
}

# Test 4: List Saudi properties
Write-Host "`n4. Testing Saudi Properties List..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/v1/properties" -Method GET
    if ($response.StatusCode -eq 200) {
        $result = $response.Content | ConvertFrom-Json
        if ($result.success -and $result.data.properties) {
            $saudiProperties = $result.data.properties | Where-Object { $_.country -eq "SAUDI" }
            Write-Host "✅ Saudi properties found: $($saudiProperties.Count)" -ForegroundColor Green
            
            foreach ($prop in $saudiProperties) {
                Write-Host "   - $($prop.title) in $($prop.city) - $($prop.price) $($prop.currency)" -ForegroundColor White
            }
        }
    }
} catch {
    Write-Host "❌ Failed to fetch properties" -ForegroundColor Red
}

# Test 5: Verify configuration files
Write-Host "`n5. Testing Configuration..." -ForegroundColor Yellow

$languageFile = "dashboard\lib\settings.ts"
if (Test-Path $languageFile) {
    $content = Get-Content $languageFile -Raw
    if ($content -match 'defaultLanguage.*=.*"ar"') {
        Write-Host "✅ Default language set to Arabic" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Default language check failed" -ForegroundColor Yellow
    }
}

$formFile = "dashboard\components\properties\PropertyCreateForm.tsx"
if (Test-Path $formFile) {
    $content = Get-Content $formFile -Raw
    if ($content -match 'currency.*SAR' -and $content -match 'country.*SAUDI') {
        Write-Host "✅ Property form defaults to Saudi Arabia and SAR" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Property form defaults check failed" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Saudi Arabia Setup Test Complete!" -ForegroundColor Green
Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "✅ Saudi Arabia set as default country" -ForegroundColor White
Write-Host "✅ SAR (Saudi Riyal) set as default currency" -ForegroundColor White
Write-Host "✅ Arabic set as default language" -ForegroundColor White
Write-Host "✅ Saudi properties created successfully" -ForegroundColor White

Write-Host "`nTest the frontend at:" -ForegroundColor Green
Write-Host "http://localhost:3000/dashboard/properties" -ForegroundColor Cyan
