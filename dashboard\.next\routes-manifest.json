{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/v1/properties/[id]/images", "regex": "^/api/v1/properties/([^/]+?)/images(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/v1/properties/(?<nxtPid>[^/]+?)/images(?:/)?$"}, {"page": "/dashboard/campaigns/edit/[id]", "regex": "^/dashboard/campaigns/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/campaigns/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/properties/[id]", "regex": "^/dashboard/properties/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/properties/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/properties/[id]/edit", "regex": "^/dashboard/properties/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/properties/(?<nxtPid>[^/]+?)/edit(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/access-denied", "regex": "^/access\\-denied(?:/)?$", "routeKeys": {}, "namedRegex": "^/access\\-denied(?:/)?$"}, {"page": "/auth-test", "regex": "^/auth\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth\\-test(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/ai-chatbot", "regex": "^/dashboard/ai\\-chatbot(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/ai\\-chatbot(?:/)?$"}, {"page": "/dashboard/analytics", "regex": "^/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analytics(?:/)?$"}, {"page": "/dashboard/appointments", "regex": "^/dashboard/appointments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/appointments(?:/)?$"}, {"page": "/dashboard/campaigns", "regex": "^/dashboard/campaigns(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/campaigns(?:/)?$"}, {"page": "/dashboard/campaigns/create", "regex": "^/dashboard/campaigns/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/campaigns/create(?:/)?$"}, {"page": "/dashboard/clients", "regex": "^/dashboard/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/clients(?:/)?$"}, {"page": "/dashboard/data", "regex": "^/dashboard/data(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/data(?:/)?$"}, {"page": "/dashboard/marketing", "regex": "^/dashboard/marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/marketing(?:/)?$"}, {"page": "/dashboard/marketing/templates", "regex": "^/dashboard/marketing/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/marketing/templates(?:/)?$"}, {"page": "/dashboard/marketing/templates/new", "regex": "^/dashboard/marketing/templates/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/marketing/templates/new(?:/)?$"}, {"page": "/dashboard/messaging", "regex": "^/dashboard/messaging(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/messaging(?:/)?$"}, {"page": "/dashboard/profile", "regex": "^/dashboard/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/profile(?:/)?$"}, {"page": "/dashboard/properties", "regex": "^/dashboard/properties(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/properties(?:/)?$"}, {"page": "/dashboard/properties/create", "regex": "^/dashboard/properties/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/properties/create(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/dashboard/templates", "regex": "^/dashboard/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/templates(?:/)?$"}, {"page": "/dashboard/templates/new", "regex": "^/dashboard/templates/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/templates/new(?:/)?$"}, {"page": "/dashboard/user", "regex": "^/dashboard/user(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/user(?:/)?$"}, {"page": "/dashboard/users", "regex": "^/dashboard/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users(?:/)?$"}, {"page": "/sign-in", "regex": "^/sign\\-in(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in(?:/)?$"}, {"page": "/sign-out", "regex": "^/sign\\-out(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-out(?:/)?$"}, {"page": "/sign-up", "regex": "^/sign\\-up(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-up(?:/)?$"}, {"page": "/test-login", "regex": "^/test\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-login(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/api/v1/:path*", "destination": "http://localhost:5000/api/v1/:path*", "regex": "^/api/v1(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}