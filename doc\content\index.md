---
title: توثيق نظام Boot للذكاء الاصطناعي العقاري
description: توثيق شامل لتطبيق Boot للذكاء الاصطناعي العقاري
category: نظرة عامة
order: 0
---

# توثيق نظام Boot للذكاء الاصطناعي العقاري

مرحباً بك في التوثيق الشامل لتطبيق Boot للذكاء الاصطناعي العقاري. هذا التوثيق مصمم لمساعدة المطورين على فهم قاعدة الكود والهندسة المعمارية وأنماط التطوير المستخدمة في التطبيق.

## التركيز على السوق السعودي

تم تحسين النظام خصيصاً للسوق السعودي:

- **🇸🇦 اللغة العربية كلغة أساسية**: واجهة كاملة باللغة العربية مع دعم RTL
- **🏛️ المملكة العربية السعودية كدولة افتراضية**: جميع النماذج تبدأ بالسعودية
- **💰 الريال السعودي كعملة افتراضية**: SAR كعملة أساسية
- **🏙️ المدن السعودية**: قائمة شاملة بـ15 مدينة سعودية رئيسية
- **🎨 التكيف الثقافي**: تصميم يتناسب مع السوق السعودي

## Documentation Structure

The documentation is organized into the following sections:

1. **API Reference**: Complete reference for all API endpoints
2. **Frontend Pages**: Documentation for all pages in the application
3. **React Hooks**: Documentation for all custom React hooks
4. **Database Models**: Documentation for all database models and relationships
5. **Utility Functions**: Documentation for key utility functions and helper methods
6. **State Management**: Documentation for state management approaches
7. **Advanced API Guide**: In-depth technical guide to the API implementation
8. **Frontend Development Guide**: Comprehensive guide to the frontend architecture
9. **WhatsApp Integration**: Guide to integrating with the WhatsApp Business API

## Getting Started

If you're new to the Boot application, we recommend starting with the following sections:

1. [API Reference](./api-reference.md) - To understand the available endpoints
2. [Frontend Pages](./frontend-pages.md) - To understand the application structure
3. [Database Models](./database-models.md) - To understand the data model

## المميزات الرئيسية

يتضمن تطبيق Boot المميزات الرئيسية التالية:

1. **إدارة العملاء**: إضافة وتعديل وإدارة العملاء
2. **إدارة الحملات**: إنشاء وإرسال الحملات التسويقية
3. **إدارة القوالب**: إنشاء وإدارة قوالب الرسائل
4. **تكامل واتساب**: إرسال الرسائل عبر واتساب
5. **التحليلات**: تتبع تسليم الرسائل وتفاعل العملاء
6. **إدارة العقارات**: نظام شامل لإدارة العقارات مع دعم السوق السعودي
7. **الدعم متعدد اللغات**: دعم اللغة العربية كلغة أساسية مع الإنجليزية
8. **الوضع الداكن**: واجهة عصرية بالثيم الداكن

## Architecture Overview

The Boot application follows a modern web application architecture:

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Next.js        │      │  Node.js API    │      │  PostgreSQL     │
│  Frontend       │◄────►│  Backend        │◄────►│  Database       │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

### Frontend

- **Framework**: Next.js with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Context and React Query
- **Form Handling**: React Hook Form with Zod validation

### Backend

- **Framework**: Node.js with Express
- **Language**: TypeScript
- **Database ORM**: Prisma
- **Authentication**: JWT-based authentication
- **API Documentation**: OpenAPI (Swagger)

### Database

- **Database**: PostgreSQL
- **Schema Management**: Prisma Schema
- **Migrations**: Prisma Migrations

## Development Workflow

### Setting Up the Development Environment

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```
4. Start the development server:
   ```bash
   npm run dev
   ```

### Code Structure

The codebase follows a structured organization:

```
boot/
├── app/                  # Next.js App Router
│   ├── api/              # API routes
│   ├── auth/             # Authentication pages
│   ├── clients/          # Client management pages
│   ├── campaigns/        # Campaign management pages
│   ├── templates/        # Template management pages
│   └── settings/         # Settings pages
├── components/           # React components
│   ├── ui/               # UI components
│   ├── layout/           # Layout components
│   ├── clients/          # Client-related components
│   ├── campaigns/        # Campaign-related components
│   └── templates/        # Template-related components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and constants
├── prisma/               # Prisma schema and migrations
│   ├── schema.prisma     # Database schema
│   └── migrations/       # Database migrations
├── public/               # Static assets
└── styles/               # Global styles
```

### Coding Standards

The Boot application follows these coding standards:

1. **TypeScript**: Use TypeScript for type safety
2. **ESLint**: Follow ESLint rules for code quality
3. **Prettier**: Use Prettier for code formatting
4. **Component Structure**: Follow a consistent component structure
5. **Naming Conventions**: Use descriptive names for variables, functions, and components
6. **Error Handling**: Implement proper error handling
7. **Testing**: Write tests for critical functionality

## API Overview

The Boot API provides the following endpoints:

- **Authentication**: `/api/auth/*` - User authentication and registration
- **Clients**: `/api/clients/*` - Client management
- **Campaigns**: `/api/campaigns/*` - Campaign management
- **Templates**: `/api/templates/*` - Template management
- **Messages**: `/api/messages/*` - Message management
- **Analytics**: `/api/analytics/*` - Analytics data

For a complete reference, see the [API Reference](./api-reference.md).

## Frontend Overview

The Boot frontend includes the following main sections:

- **Dashboard**: Overview of key metrics and recent activity
- **Clients**: Client management interface
- **Campaigns**: Campaign creation and management
- **Templates**: Template creation and management
- **Settings**: Application settings

For detailed information about each page, see the [Frontend Pages](./frontend-pages.md) documentation.

## Database Overview

The Boot database includes the following main models:

- **User**: Application users
- **Client**: Clients who receive messages
- **Campaign**: Marketing campaigns
- **Template**: Message templates
- **Message**: Messages sent to clients

For detailed information about each model, see the [Database Models](./database-models.md) documentation.

## State Management Overview

The Boot application uses a hybrid approach to state management:

- **React Context**: For global application state
- **React Query**: For server state management
- **Local Component State**: For UI-specific state

For detailed information about state management, see the [State Management](./state-management.md) documentation.

## الدعم متعدد اللغات

يدعم تطبيق Boot عدة لغات:

- **العربية**: اللغة الافتراضية مع دعم كامل للكتابة من اليمين إلى اليسار (RTL)
- **الإنجليزية**: لغة ثانوية للمستخدمين الدوليين

إعدادات اللغة تُدار من خلال `LanguageProvider` context.

### مميزات الدعم العربي

- **🔤 خطوط عربية جميلة**: Cairo, Noto Sans Arabic, Tajawal
- **↔️ دعم RTL كامل**: تخطيط صحيح من اليمين إلى اليسار
- **🎨 واجهة عربية أصيلة**: تصميم يتناسب مع الثقافة العربية
- **🏛️ محتوى سعودي**: مدن ومناطق المملكة العربية السعودية
- **💰 عملات محلية**: الريال السعودي والعملات الخليجية

## WhatsApp Integration

The Boot application integrates with the WhatsApp Business API to send messages to clients. For detailed information about this integration, see the [WhatsApp Integration](./whatsapp-integration.md) documentation.

## Contributing

To contribute to the Boot application:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests for your changes
5. Submit a pull request

## Troubleshooting

If you encounter issues with the Boot application, check the following:

1. **Environment Variables**: Ensure all required environment variables are set
2. **Database Connection**: Verify the database connection is working
3. **API Endpoints**: Check API responses for error messages
4. **Console Errors**: Look for errors in the browser console
5. **Server Logs**: Check server logs for backend errors

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp)

## Conclusion

This documentation provides a comprehensive overview of the Boot application. For specific details about any aspect of the application, refer to the relevant section of the documentation.

If you have any questions or need further assistance, please contact the development team.
