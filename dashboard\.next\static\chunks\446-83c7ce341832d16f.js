(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[446],{6721:()=>{},31886:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(23464);class s{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let r=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),r.data}catch(t){if(t.response){let r=t.response.status;if(console.log("API Client: Request to ".concat(e," failed with status ").concat(r)),404===r){let t=Error("Resource not found: ".concat(e));throw t.status=404,t.isNotFound=!0,t}}throw t}}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let r=await this.client.delete(e,t);if(204===r.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return r.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async upload(e,t,r){let o={...r,headers:{...null==r?void 0:r.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,o)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=o.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let a=new s},45300:(e,t,r)=>{"use strict";function o(){["__next_hmr_refresh_hash__","__clerk_db_jwt_NFfxy5s4","__refresh_NFfxy5s4","__session_NFfxy5s4","__client_uat_NFfxy5s4","__clerk_db_jwt_TYLMw0H7","__refresh_TYLMw0H7","__session_TYLMw0H7","__client_uat_TYLMw0H7","__clerk_db_jwt","__clerk_db_jwt_kCaGdcWF","__client_uat_kCaGdcWF","__client_uat","NEXT_LOCALE","authjs.csrf-token","authjs.callback-url"].forEach(e=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"),[window.location.hostname,".".concat(window.location.hostname),"localhost",".localhost"].forEach(t=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=").concat(t,";")})}),document.cookie="language=ar; path=/; max-age=31536000",console.log("\uD83E\uDDF9 Cookies cleaned up for Arabic Properties system")}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o]}r.d(t,{Ei:()=>s,ss:()=>o})},53580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>d});var o=r(12115);let s=0,a=new Map,n=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],p={toasts:[]};function l(e){p=i(p,e),c.forEach(e=>{e(p)})}function u(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),o=()=>l({type:"DISMISS_TOAST",toastId:r});return l({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||o()}}}),{id:r,dismiss:o,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function d(){let[e,t]=o.useState(p);return o.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var o=r(52596),s=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,o.$)(t))}},78137:(e,t,r)=>{"use strict";r.d(t,{Jf:()=>s});var o=r(31886);let s={createProperty:async e=>{try{let t={...e,images:e.images||[],features:e.features||[],featuresAr:e.featuresAr||[],amenities:e.amenities||[],amenitiesAr:e.amenitiesAr||[],currency:e.currency||"USD",country:e.country||"UAE",status:e.status||"AVAILABLE",isActive:void 0===e.isActive||e.isActive,isFeatured:e.isFeatured||!1},r=await o.A.post("/properties",t);return r.data||r}catch(e){throw console.error("Error creating property:",e),e}},getProperties:async()=>{try{return await o.A.get("/properties")}catch(e){throw console.error("Error fetching properties:",e),e}},getPropertyById:async e=>{try{return await o.A.get("/properties/".concat(e))}catch(t){if(404===t.status||t.isNotFound){console.log("Property with ID ".concat(e," not found in database (404)"));let t=Error("Property not found: ".concat(e));throw t.status=404,t.isNotFound=!0,t}throw console.error("Error fetching property with ID ".concat(e,":"),t),t}},updateProperty:async(e,t)=>{try{if(!t.images||!(t.images.length>0))return await o.A.put("/properties/".concat(e),t);{let r=new FormData;return Object.entries(t).forEach(e=>{let[t,o]=e;"images"!==t&&r.append(t,String(o))}),Array.from(t.images).forEach((e,t)=>{r.append("images",e)}),await o.A.upload("/properties/".concat(e),r)}}catch(t){throw console.error("Error updating property with ID ".concat(e,":"),t),t}},deleteProperty:async e=>{try{await o.A.delete("/properties/".concat(e))}catch(t){throw console.error("Error deleting property with ID ".concat(e,":"),t),t}}}},86132:(e,t,r)=>{"use strict";r.d(t,{Y:()=>a});var o=r(12115),s=r(45300);function a(){let[e]=(0,o.useState)("ar");(0,o.useEffect)(()=>{(0,s.ss)(),localStorage.setItem("properties-language","ar"),(0,s.Ei)("\uD83C\uDFE0 Arabic Properties system initialized")},[]),(0,o.useEffect)(()=>{document.documentElement.lang="ar",document.documentElement.dir="rtl",document.documentElement.className="rtl arabic-interface",document.body.style.fontFamily="'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif",(0,s.Ei)("\uD83C\uDF10 Arabic language interface active")},[]);let t={"properties.create":"إنشاء عقار جديد","properties.subtitle":"إدارة وإضافة العقارات الجديدة","properties.save":"حفظ العقار","properties.cancel":"إلغاء","properties.loading":"جاري التحميل...","properties.success":"تم حفظ العقار بنجاح","properties.error":"حدث خطأ أثناء حفظ العقار","property.title":"عنوان العقار","property.title.placeholder":"أدخل عنوان العقار","property.description":"وصف العقار","property.description.placeholder":"أدخل وصف مفصل للعقار","property.price":"السعر","property.price.placeholder":"أدخل سعر العقار","property.type":"نوع العقار","property.type.select":"اختر نوع العقار","property.status":"حالة العقار","property.status.select":"اختر حالة العقار","property.bedrooms":"عدد غرف النوم","property.bathrooms":"عدد دورات المياه","property.area":"المساحة","property.location":"الموقع","property.location.placeholder":"أدخل موقع العقار","property.address":"العنوان","property.address.placeholder":"أدخل العنوان التفصيلي","property.city":"المدينة","property.city.placeholder":"أدخل اسم المدينة","property.country":"الدولة","property.images":"صور العقار","property.yearBuilt":"سنة البناء","property.parking":"مواقف السيارات","property.furnished":"مفروش","property.petFriendly":"يسمح بالحيوانات الأليفة","property.type.apartment":"شقة","property.type.villa":"فيلا","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.studio":"استوديو","property.type.office":"مكتب","property.type.shop":"محل تجاري","property.type.warehouse":"مستودع","property.type.land":"أرض","property.type.building":"مبنى","property.status.available":"متاح","property.status.sold":"مباع","property.status.rented":"مؤجر","property.status.pending":"قيد المراجعة","country.uae":"الإمارات العربية المتحدة","country.saudi":"المملكة العربية السعودية","country.qatar":"قطر","country.kuwait":"الكويت","country.bahrain":"البحرين","country.oman":"عمان","images.drag":"اسحب الصور هنا أو انقر للاختيار","images.formats":"صور حتى ٨ ميجابايت","images.uploading":"جاري رفع الصور...","images.success":"تم رفع الصور بنجاح","images.error":"خطأ في رفع الصور","images.remove":"حذف الصورة","images.preview":"معاينة الصورة","images.fileType":"يرجى اختيار ملفات صور فقط","images.fileSize":"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت","validation.required":"هذا الحقل مطلوب","validation.positive":"يجب أن يكون الرقم أكبر من الصفر"};return{language:e,setLanguage:()=>{},isRTL:!0,isArabic:!0,isEnglish:!1,t:e=>{let r=t[e];return r||(console.warn("Missing translation for: ".concat(e)),e)}}}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>p,r:()=>c});var o=r(95155),s=r(12115),a=r(99708),n=r(74466),i=r(53999);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=s.forwardRef((e,t)=>{let{className:r,variant:s,size:n,asChild:p=!1,...l}=e,u=p?a.DX:"button";return(0,o.jsx)(u,{className:(0,i.cn)(c({variant:s,size:n,className:r})),ref:t,...l})});p.displayName="Button"}}]);