"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx":
/*!************************************************!*\
  !*** ./app/dashboard/properties/[id]/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertyShowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/properties/PropertyShowComponent */ \"(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction PropertyShowPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const propertyId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyShowPage.useEffect\": ()=>{\n            if (propertyId) {\n                console.log('Component mounted, property ID:', propertyId);\n                fetchProperty();\n            }\n        }\n    }[\"PropertyShowPage.useEffect\"], [\n        propertyId\n    ]);\n    const fetchProperty = async ()=>{\n        try {\n            setIsLoading(true);\n            console.log('Fetching property:', propertyId);\n            const response = await _services_propertyService__WEBPACK_IMPORTED_MODULE_3__.propertyService.getPropertyById(propertyId);\n            console.log('API response received:', response);\n            // Handle both direct property data and nested response structure\n            let propertyData = null;\n            if (response && response.success && response.data) {\n                // Nested response structure: {success: true, data: {...}}\n                propertyData = response.data;\n            } else if (response && response.id) {\n                // Direct property data: {...}\n                propertyData = response;\n            }\n            if (propertyData && propertyData.id) {\n                setProperty(propertyData);\n                console.log('Property loaded successfully from API:', propertyData.title);\n            } else {\n                console.log('No property data received from API');\n                setProperty(null);\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Error fetching property:', error);\n            const status = error.status || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status);\n            if (status === 404) {\n                console.log(\"Property \".concat(propertyId, \" not found in database (404)\"));\n                toast({\n                    title: 'العقار غير موجود',\n                    description: 'لم يتم العثور على العقار المطلوب',\n                    variant: 'destructive'\n                });\n            } else if (error.message && error.message.includes('Network Error')) {\n                console.log('Network error - backend server not available');\n                toast({\n                    title: 'خطأ في الاتصال',\n                    description: 'لا يمكن الاتصال بالخادم. يرجى المحاولة مرة أخرى.',\n                    variant: 'destructive'\n                });\n            } else {\n                console.log('API error:', status || error.message);\n                toast({\n                    title: 'خطأ في تحميل العقار',\n                    description: 'حدث خطأ أثناء تحميل بيانات العقار',\n                    variant: 'destructive'\n                });\n            }\n            setProperty(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/properties/\".concat(propertyId, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {\n            return;\n        }\n        try {\n            setIsDeleting(true);\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_3__.propertyService.deleteProperty(propertyId);\n            toast({\n                title: 'تم حذف العقار',\n                description: 'تم حذف العقار بنجاح'\n            });\n            router.push('/dashboard/properties');\n        } catch (error) {\n            console.error('Error deleting property:', error);\n            toast({\n                title: 'خطأ في حذف العقار',\n                description: 'حدث خطأ أثناء حذف العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/dashboard/properties');\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[400px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-3 text-lg\",\n                            children: \"جاري تحميل العقار...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-400 mb-4\",\n                            children: \"العقار غير موجود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"لم يتم العثور على العقار المطلوب\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: handleBack,\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة إلى قائمة العقارات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleBack,\n                                        variant: \"ghost\",\n                                        className: \"text-gray-400 hover:text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة إلى قائمة العقارات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"arabic-heading text-3xl font-bold text-white\",\n                                        children: property.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mt-2\",\n                                        children: [\n                                            property.location,\n                                            \" • \",\n                                            property.city,\n                                            \" • \",\n                                            property.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleEdit,\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تعديل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                        children: [\n                                            isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_4__.PropertyShowComponent, {\n                    property: property\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowPage, \"q2flJxCB5cD/rp1Y/gC/rgW0uns=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = PropertyShowPage;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx\n"));

/***/ })

});