"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/edit/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyCreateForm.tsx":
/*!******************************************************!*\
  !*** ./components/properties/PropertyCreateForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyCreateForm: () => (/* binding */ PropertyCreateForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/uploadthing */ \"(app-pages-browser)/./lib/uploadthing.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyCreateForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyCreateForm(param) {\n    let { onSuccess, onCancel, initialData, isEdit = false, propertyId } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // UploadThing hook for property images\n    const { startUpload, isUploading } = (0,_lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__.useUploadThing)(\"propertyImageUploader\", {\n        onClientUploadComplete: {\n            \"PropertyCreateForm.useUploadThing\": (res)=>{\n                if (res) {\n                    const newImageUrls = res.map({\n                        \"PropertyCreateForm.useUploadThing.newImageUrls\": (file)=>file.url\n                    }[\"PropertyCreateForm.useUploadThing.newImageUrls\"]);\n                    setFormData({\n                        \"PropertyCreateForm.useUploadThing\": (prev)=>({\n                                ...prev,\n                                images: [\n                                    ...prev.images,\n                                    ...newImageUrls\n                                ]\n                            })\n                    }[\"PropertyCreateForm.useUploadThing\"]);\n                    toast({\n                        title: t('images.success'),\n                        description: t('images.success')\n                    });\n                }\n            }\n        }[\"PropertyCreateForm.useUploadThing\"],\n        onUploadError: {\n            \"PropertyCreateForm.useUploadThing\": (error)=>{\n                toast({\n                    title: t('images.error'),\n                    description: t('images.error'),\n                    variant: 'destructive'\n                });\n            }\n        }[\"PropertyCreateForm.useUploadThing\"]\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: (initialData === null || initialData === void 0 ? void 0 : initialData.title) || '',\n        description: (initialData === null || initialData === void 0 ? void 0 : initialData.description) || '',\n        price: (initialData === null || initialData === void 0 ? void 0 : initialData.price) || '',\n        currency: (initialData === null || initialData === void 0 ? void 0 : initialData.currency) || 'SAR',\n        type: (initialData === null || initialData === void 0 ? void 0 : initialData.type) || '',\n        status: (initialData === null || initialData === void 0 ? void 0 : initialData.status) || 'AVAILABLE',\n        bedrooms: (initialData === null || initialData === void 0 ? void 0 : initialData.bedrooms) || '',\n        bathrooms: (initialData === null || initialData === void 0 ? void 0 : initialData.bathrooms) || '',\n        area: (initialData === null || initialData === void 0 ? void 0 : initialData.area) || '',\n        location: (initialData === null || initialData === void 0 ? void 0 : initialData.location) || '',\n        address: (initialData === null || initialData === void 0 ? void 0 : initialData.address) || '',\n        city: (initialData === null || initialData === void 0 ? void 0 : initialData.city) || '',\n        country: (initialData === null || initialData === void 0 ? void 0 : initialData.country) || 'SAUDI',\n        images: (initialData === null || initialData === void 0 ? void 0 : initialData.images) || [],\n        features: (initialData === null || initialData === void 0 ? void 0 : initialData.features) || [],\n        amenities: (initialData === null || initialData === void 0 ? void 0 : initialData.amenities) || [],\n        yearBuilt: (initialData === null || initialData === void 0 ? void 0 : initialData.yearBuilt) || '',\n        parking: (initialData === null || initialData === void 0 ? void 0 : initialData.parking) || '',\n        furnished: (initialData === null || initialData === void 0 ? void 0 : initialData.furnished) || false,\n        petFriendly: (initialData === null || initialData === void 0 ? void 0 : initialData.petFriendly) || false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleInputChange]\": (field, value)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                        ...prev,\n                        [field]: value\n                    })\n            }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            // Clear error when user starts typing\n            if (errors[field]) {\n                setErrors({\n                    \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                            ...prev,\n                            [field]: ''\n                        })\n                }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleInputChange]\"], [\n        errors\n    ]);\n    // Handle image file selection\n    const handleImageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleImageChange]\": (files)=>{\n            if (!files) return;\n            const validFiles = [];\n            Array.from(files).forEach({\n                \"PropertyCreateForm.useCallback[handleImageChange]\": (file)=>{\n                    // Validate file type\n                    if (!file.type.startsWith('image/')) {\n                        toast({\n                            title: t('images.error'),\n                            description: t('images.fileType'),\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    // Validate file size (8MB max for UploadThing)\n                    if (file.size > 8 * 1024 * 1024) {\n                        toast({\n                            title: t('images.error'),\n                            description: t('images.fileSize'),\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    validFiles.push(file);\n                }\n            }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            if (validFiles.length > 0) {\n                setSelectedFiles({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>[\n                            ...prev,\n                            ...validFiles\n                        ]\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n                // Start upload immediately\n                startUpload(validFiles);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleImageChange]\"], [\n        toast,\n        t,\n        startUpload\n    ]);\n    // Remove image\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[removeImage]\": (index)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>({\n                        ...prev,\n                        images: prev.images.filter({\n                            \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                        }[\"PropertyCreateForm.useCallback[removeImage]\"])\n                    })\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n        }\n    }[\"PropertyCreateForm.useCallback[removeImage]\"], []);\n    // Handle drag and drop\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"PropertyCreateForm.useCallback[handleDragOver]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleImageChange(e.dataTransfer.files);\n        }\n    }[\"PropertyCreateForm.useCallback[handleDrop]\"], [\n        handleImageChange\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.title.trim()) newErrors.title = t('validation.required');\n        if (!formData.description.trim()) newErrors.description = t('validation.required');\n        if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive');\n        if (formData.price && formData.price > 100000000000) newErrors.price = 'السعر كبير جداً';\n        if (!formData.type) newErrors.type = t('validation.required');\n        if (!formData.location.trim()) newErrors.location = t('validation.required');\n        if (!formData.address.trim()) newErrors.address = t('validation.required');\n        if (!formData.city.trim()) newErrors.city = t('validation.required');\n        if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive');\n        if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive');\n        if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive');\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: t('properties.error'),\n                description: t('validation.required'),\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: Number(formData.price),\n                currency: formData.currency,\n                type: formData.type,\n                status: formData.status,\n                bedrooms: Number(formData.bedrooms),\n                bathrooms: Number(formData.bathrooms),\n                area: Number(formData.area),\n                location: formData.location,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                images: formData.images,\n                features: formData.features,\n                amenities: formData.amenities,\n                yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,\n                parking: formData.parking ? Number(formData.parking) : undefined,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly\n            };\n            if (isEdit && propertyId) {\n                await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.updateProperty(propertyId, propertyData);\n            } else {\n                await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.createProperty(propertyData);\n            }\n            toast({\n                title: t('properties.success'),\n                description: isEdit ? 'تم تحديث العقار بنجاح' : t('properties.success')\n            });\n            onSuccess();\n        } catch (error) {\n            console.error('Error creating property:', error);\n            toast({\n                title: t('properties.error'),\n                description: t('properties.error'),\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const propertyTypes = [\n        {\n            value: 'APARTMENT',\n            label: t('property.type.apartment')\n        },\n        {\n            value: 'VILLA',\n            label: t('property.type.villa')\n        },\n        {\n            value: 'TOWNHOUSE',\n            label: t('property.type.townhouse')\n        },\n        {\n            value: 'PENTHOUSE',\n            label: t('property.type.penthouse')\n        },\n        {\n            value: 'STUDIO',\n            label: t('property.type.studio')\n        },\n        {\n            value: 'OFFICE',\n            label: t('property.type.office')\n        },\n        {\n            value: 'SHOP',\n            label: t('property.type.shop')\n        },\n        {\n            value: 'WAREHOUSE',\n            label: t('property.type.warehouse')\n        },\n        {\n            value: 'LAND',\n            label: t('property.type.land')\n        },\n        {\n            value: 'BUILDING',\n            label: t('property.type.building')\n        }\n    ];\n    const propertyStatuses = [\n        {\n            value: 'AVAILABLE',\n            label: t('property.status.available')\n        },\n        {\n            value: 'SOLD',\n            label: t('property.status.sold')\n        },\n        {\n            value: 'RENTED',\n            label: t('property.status.rented')\n        },\n        {\n            value: 'PENDING',\n            label: t('property.status.pending')\n        }\n    ];\n    const countries = [\n        {\n            value: 'SAUDI',\n            label: t('country.saudi')\n        },\n        {\n            value: 'UAE',\n            label: t('country.uae')\n        },\n        {\n            value: 'QATAR',\n            label: t('country.qatar')\n        },\n        {\n            value: 'KUWAIT',\n            label: t('country.kuwait')\n        },\n        {\n            value: 'BAHRAIN',\n            label: t('country.bahrain')\n        },\n        {\n            value: 'OMAN',\n            label: t('country.oman')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"المعلومات الأساسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.title'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.title ? 'error' : ''),\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange('title', e.target.value),\n                                        placeholder: t('property.title.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.price'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                className: \"form-input \".concat(errors.price ? 'error' : '', \" flex-1\"),\n                                                type: \"number\",\n                                                value: formData.price,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value ? Number(e.target.value) : '';\n                                                    // Limit price to reasonable maximum (100 billion)\n                                                    if (typeof value === 'number' && value > 100000000000) {\n                                                        return; // Don't update if price is too high\n                                                    }\n                                                    handleInputChange('price', value);\n                                                },\n                                                placeholder: t('property.price.placeholder'),\n                                                min: \"0\",\n                                                max: \"100000000000\",\n                                                dir: \"rtl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: formData.currency,\n                                                onValueChange: (value)=>handleInputChange('currency', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: \"form-select w-32\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"SAR\",\n                                                                children: \"ريال سعودي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AED\",\n                                                                children: \"درهم إماراتي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"USD\",\n                                                                children: \"دولار أمريكي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"EUR\",\n                                                                children: \"يورو\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"GBP\",\n                                                                children: \"جنيه إسترليني\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.type'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.type,\n                                        onValueChange: (value)=>handleInputChange('type', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select \".concat(errors.type ? 'error' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: t('property.type.select')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: type.value,\n                                                        children: type.label\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.status')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.status,\n                                        onValueChange: (value)=>handleInputChange('status', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: t('property.status.select')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyStatuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: status.value,\n                                                        children: status.label\n                                                    }, status.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"تفاصيل العقار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bedrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bedrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bedrooms,\n                                        onChange: (e)=>handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bedrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bathrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bathrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bathrooms,\n                                        onChange: (e)=>handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bathrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.area'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.area ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.area,\n                                        onChange: (e)=>handleInputChange('area', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.area\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.yearBuilt')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.yearBuilt,\n                                        onChange: (e)=>handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٢٠٢٤\",\n                                        min: \"1900\",\n                                        max: \"2030\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.parking')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.parking,\n                                        onChange: (e)=>handleInputChange('parking', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"٠\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"معلومات الموقع\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.location'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.location ? 'error' : ''),\n                                        value: formData.location,\n                                        onChange: (e)=>handleInputChange('location', e.target.value),\n                                        placeholder: t('property.location.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.location\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.address'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.address ? 'error' : ''),\n                                        value: formData.address,\n                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                        placeholder: t('property.address.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.city'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.city ? 'error' : ''),\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                        placeholder: t('property.city.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.city\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.country')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.country,\n                                        onValueChange: (value)=>handleInputChange('country', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: country.value,\n                                                        children: country.label\n                                                    }, country.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 448,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"الوصف\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                className: \"form-label\",\n                                children: [\n                                    t('property.description'),\n                                    \" *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                className: \"form-textarea \".concat(errors.description ? 'error' : ''),\n                                value: formData.description,\n                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                placeholder: t('property.description.placeholder'),\n                                dir: \"rtl\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-error\",\n                                children: errors.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 34\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: t('property.images')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"image-upload-area \".concat(isUploading ? 'loading' : ''),\n                                onDragOver: handleDragOver,\n                                onDrop: handleDrop,\n                                onClick: ()=>{\n                                    const input = document.createElement('input');\n                                    input.type = 'file';\n                                    input.multiple = true;\n                                    input.accept = 'image/*';\n                                    input.onchange = (e)=>{\n                                        const target = e.target;\n                                        handleImageChange(target.files);\n                                    };\n                                    input.click();\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-medium text-gray-300 mb-2\",\n                                            children: t('images.drag')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: t('images.formats')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, this),\n                                        isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"loading-spinner\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: t('images.uploading')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, this),\n                            formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                children: formData.images.map((url, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-800 rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: url,\n                                                    alt: \"\".concat(t('images.preview'), \" \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>removeImage(index),\n                                                className: \"absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                title: t('images.remove'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"خيارات إضافية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"furnished\",\n                                        checked: formData.furnished,\n                                        onChange: (e)=>handleInputChange('furnished', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"furnished\",\n                                        className: \"form-label\",\n                                        children: t('property.furnished')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"petFriendly\",\n                                        checked: formData.petFriendly,\n                                        onChange: (e)=>handleInputChange('petFriendly', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"petFriendly\",\n                                        className: \"form-label\",\n                                        children: t('property.petFriendly')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 593,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 flex-row-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading || isUploading,\n                        className: \"btn-primary\",\n                        children: [\n                            (isLoading || isUploading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 42\n                            }, this),\n                            isLoading || isUploading ? t('properties.loading') : t('properties.save')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        onClick: onCancel,\n                        disabled: isLoading || isUploading,\n                        className: \"btn-secondary\",\n                        children: t('properties.cancel')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 626,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyCreateForm, \"iX5sOBcOi6AYfiHUyEcXAg0msx8=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _lib_uploadthing__WEBPACK_IMPORTED_MODULE_10__.useUploadThing\n    ];\n});\n_c = PropertyCreateForm;\nvar _c;\n$RefreshReg$(_c, \"PropertyCreateForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\n"));

/***/ })

});